package com.amadeus.airbi.json2star.common.addons.base.mapping

import com.amadeus.airbi.json2star.common.config.ResizeParams
import com.amadeus.airbi.json2star.common.resize.ResizeLogic.DoResize
import com.amadeus.airbi.json2star.common.testfwk.SimpleSpec

class MappingResizeLogicSpec extends SimpleSpec {

  describe("MappingResizeLogic") {

    // scalastyle:off magic.number
    val resizeParams = ResizeParams(
      trafficPeakThreshold = 1000,
      numWorkersDefault = 2,
      numWorkersPitHisto = 4,
      numWorkersTrafficPeak = 6,
      numWorkersOptimize = 8,
      numWorkersSfPush = 1
    )
    // scalastyle:on

    it("should ask for default cluster size if no Pit Histo and no Traffic Peak") {
      val resize = new MappingResizeLogic(oldVersionsCount = 0, newRecords = 100).shouldResize(resizeParams)
      resize shouldBe a[DoResize]
      resize.asInstanceOf[DoResize].numWorkers shouldBe (resizeParams.numWorkersDefault)
    }

    it("should ask for traffic peak cluster size if Traffic Peak") {
      val resize = new MappingResizeLogic(oldVersionsCount = 0, newRecords = 10000).shouldResize(resizeParams)
      resize shouldBe a[DoResize]
      resize.asInstanceOf[DoResize].numWorkers shouldBe (resizeParams.numWorkersTrafficPeak)
    }

    it("should ask for Pit Histo cluster size in case of Pit Histo branch") {
      val resize = new MappingResizeLogic(oldVersionsCount = 10, newRecords = 100).shouldResize(resizeParams)
      resize shouldBe a[DoResize]
      resize.asInstanceOf[DoResize].numWorkers shouldBe (resizeParams.numWorkersPitHisto)
    }

    it("should ask for max between Pit Histo and Traffic Peak cluster size if both situations arise") {
      val resize = new MappingResizeLogic(oldVersionsCount = 10, newRecords = 10000).shouldResize(resizeParams)
      resize shouldBe a[DoResize]
      val maxWorkers = math.max(resizeParams.numWorkersPitHisto, resizeParams.numWorkersTrafficPeak)
      resize.asInstanceOf[DoResize].numWorkers shouldBe (maxWorkers)
    }
  }

}
