import com.databricks.dbutils_v1.DBUtilsHolder.dbutils

import java.time.OffsetDateTime
import scala.collection.mutable.ListBuffer
import com.amadeus.airbi.json2star.common.validation.config.{CheckType,ValidationConf, ValidationConfig, ValidationRecord}

//WIDGETS


val currentTimestamp = OffsetDateTime.now().toString
val task = dbutils.notebook().getContext().notebookPath.get.split("/").last
var failedTables: ListBuffer[String] = ListBuffer()
var countTables = 0

val exclusions = dbutils.widgets.get("exclusions")
val exclusionList = exclusions.toUpperCase().split(';')
val vConfig = ValidationConf.apply(dbutils)
val dbType = "STAR SCHEMA"

spark.catalog.setCurrentDatabase(vConfig.appConfig.common.outputDatabase)

spark.catalog
  .listTables()
  .collect()
  .foreach(table => {
    if (!exclusionList.contains(table.name.toUpperCase())) {
      val count = spark.sql("SELECT count(*) as count FROM " + table.name + ";").select("count").first().get(0)
      println(table.name + " " + count)
      if (count == 0) {
        failedTables += table.name
      }
      countTables += 1
    }
  })

val testRecord = Seq(
  ValidationRecord(
    vConfig.appConfig.common.domain,
    vConfig.appConfig.common.domainVersion,
    vConfig.appConfig.common.shard,
    vConfig.phase,
    currentTimestamp,
    task,
    "Check count of tables of the " + dbType + " database ",
    failedTables.length == 0,
    failedTables.length,
    countTables,
    failedTables.length.toFloat / countTables,
    failedTables.mkString(",")
  )
)
val df = testRecord.toDF()

df.withColumn("fail_ratio", $"fail_ratio".cast("Decimal(3,2)"))
  .write
  .insertInto(s"${vConfig.validationDatabase}.${vConfig.validationTable}")