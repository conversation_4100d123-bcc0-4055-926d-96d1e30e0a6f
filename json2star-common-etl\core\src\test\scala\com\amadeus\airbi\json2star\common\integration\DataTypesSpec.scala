package com.amadeus.airbi.json2star.common.integration

import com.amadeus.airbi.json2star.common.app.Json2StarApp
import com.amadeus.airbi.json2star.common.app.Json2StarApp.readMappingConfig
import com.amadeus.airbi.rawvault.common.testfwk.Json2StarSpec

import java.sql.{Date, Timestamp}

class DataTypesSpec extends Json2StarSpec {
  val modelFile = "datasets/data_types/mapping.conf"
  val dataDir = "datasets/data_types/data/"
  val TableNames: List[String] = getTableNames(modelFile)

  override def beforeAll: Unit = {
    super.beforeAll
    createTables(modelFile)
  }
  override def beforeEach: Unit = {
    cleanTables(TableNames)
  }

  "the engine" should "extract fields according to the output types" in withStrTmpDir { tmp =>
    val rc = rootConfig(dataDir, inputDatabase, isLight = isLight, checkpointPath = Some(tmp))
    val model = readMappingConfig(modelFile)
    Json2StarApp.run(spark, rc, model)
    val tableName :: Nil = TableNames
    val df = getTableContent(tableName)
    val actualRows = df.collect().toList.map(r => r.toSeq.toList)
    val expectedRow = List(
      "hashM(OP27AX-2022-05-22)", // RESERVATION_ID
      1, // VERSION
      1, // VERSION_INT
      "1", // VERSION_STRING
      Date.valueOf("2022-05-22"), // PNR_CREATION_DATE
      Timestamp.valueOf("2022-05-22 21:31:00.0"), // PNR_CREATION_TIMESTAMP
      // ID_BINARY bugged
      "hashM(OP27AX-2022-05-22)", // ID_BINARY_STRING
      true, // IS_ACTIVE_BOOLEAN
      1L, // VERSION_LONG
      1.0f, // VERSION_FLOAT
      """{"office":{"id":"NYCQ","i":3386},"login":{"i":"WB"}}""", // OWNER_JSON_STRING
      null, // DATE_BEGIN
      null, // DATE_END
      true, // IS_LAST_VERSION
      Timestamp.valueOf("2018-01-01 00:00:00.0") // LOAD_DATE
    )
    actualRows shouldBe List(expectedRow)

    // the json is well formatted and queriable
    val parsed = df.selectExpr("get_json_object(OWNER_JSON_STRING, '$.office.id')").collect().map(r => r.getString(0))
    parsed.toList shouldBe List("NYCQ")
  }
}
