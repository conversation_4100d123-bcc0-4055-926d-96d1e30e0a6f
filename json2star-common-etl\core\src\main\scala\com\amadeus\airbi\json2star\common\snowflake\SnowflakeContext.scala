package com.amadeus.airbi.json2star.common.snowflake

import com.amadeus.airbi.json2star.common.config.{AppConfig, SnowflakeParams}
import com.amadeus.airbi.json2star.common.resize.DatabricksUtils
import com.databricks.dbutils_v1.DBUtilsHolder.dbutils
import net.snowflake.spark.snowflake.{SnowflakeUtils, Utils}
import net.snowflake.spark.snowflake.Utils.SNOWFLAKE_SOURCE_NAME
import org.apache.spark.sql.streaming.StreamingQuery
import org.apache.spark.sql.{DataFrame, SparkSession}

import java.sql.ResultSet

/** This class contains the context used to do operations using Snowflake
  * It is extracted mainly for testability reasons
  *
  * @param spark Spark session used for the mirror operations
  * @param sfParams Snowflake parameters configured from the application configuration
  * @param srcDeltaDatabase source Delta Database to mirror
  */
class SnowflakeContext(
  spark: SparkSession,
  sfParams: SnowflakeParams,
  srcDeltaDatabase: String,
  val databricksUtils: DatabricksUtils,
  val appConfig: AppConfig,
  val sfQueryRunner: SnowflakeQueryRunner = SnowflakeQueryRunner
) {
  val sfOptions: Map[String, String] = sfParams.getSnowflakeOptions(dbutils)
  val sfDatabase: String = sfOptions("sfDatabase")
  val sfSchema: String = sfOptions("sfSchema")
  val displayEvents: Boolean = sfParams.displayEvents
  val stagingRetentionDays: Int = sfParams.stagingRetentionDays

  // Root Config for the SF Push Library
  private val sfPushRootConfig: SnowflakeRootConfig = SnowflakeRootConfig(
    sparkOptions = sfParams.sparkConf,
    streamOptions = sfParams.streamOptions,
    mirror = Mirror(srcDeltaDatabase),
    outputConnectorOptions = sfOptions,
    sink = SfSink(sfParams.stream.sink.checkpointLocation, sfParams.stream.sink.trigger)
  )

  private val sfPushConnector: SnowflakeConnector = SnowflakeConnector(sfPushRootConfig, spark, sfQueryRunner)

  /** Run a show tables of the snowflake database
    *
    * @return a spark Dataframe containing all tables information
    */
  def runShowTables(): DataFrame = {
    val tables = spark.sqlContext.read
      .format(SNOWFLAKE_SOURCE_NAME)
      .options(sfOptions)
      .option("query", s"SELECT * FROM ${sfDatabase}.INFORMATION_SCHEMA.TABLES")
      .load()
    tables
  }

  /** Run a Snowflake query using the JDBC connector
    *
    * @param query a SQL query
    * @return a ResultSet of the query
    */
  def runQuery(query: String): ResultSet = {
    val res = Utils.runQuery(sfOptions, query)
    SnowflakeUtils.printResultSet(res)
    res
  }

  /** Run the mirror of a table using the SF Push connector
    * A mirror operation consists of a copy done using a Spark Streaming Query with these sequential steps:
    * - create transient table in Snowflake
    * - copy from Databricks Delta to Snowflake
    * - run Snowflake Merge query in Snowflake
    *
    * The Spark Streaming Query is started and finishes when the Snowflake query is completed
    * The Spark Streaming Spark Query has its own checkpoint for this specific table
    *
    * @param tableName   table name to mirror
    * @param primaryKeys primary keys of the table
    * @param isCloned if the table is a cloned table
    * @return a started Spark Streaming Query
    */
  def runMirror(tableName: String, primaryKeys: Set[String], isCloned: Boolean): StreamingQuery = {
    sfPushConnector.mirrorTable(tableName, primaryKeys, isCloned)
  }

}
