package com.amadeus.airbi.json2star.common.addons.base.mapping

import com.amadeus.airbi.json2star.common.addons.base.mapping.MappingPipeline.TableMapping
import com.amadeus.airbi.rawvault.common.config.{ColumnConfig, ColumnType}
import com.typesafe.scalalogging.Logger
import org.apache.spark.sql.catalyst.encoders.RowEncoder
import org.apache.spark.sql.types.{DataTypes, StructField, StructType}
import org.apache.spark.sql.{DataFrame, Dataset, Row}
import org.slf4j.LoggerFactory

import java.sql.{Date, Timestamp}
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import scala.util.{Failure, Success, Try}

object TablesToDf {

  lazy val logger: Logger = Logger(LoggerFactory.getLogger(getClass.getName))

  /** Transforms the dataset of tables (where only items related to one specific table are present)
    * into a dataframe matching that table schema, and apply all the expressions.
    *
    * @param cfg   mapping configuration for the table
    * @param table the dataset of tables, containing only items related to one single table (the one whose schema is passed)
    * @return a dataframe for the table with applied expressions
    */
  def tablesToDFWithExpressions(cfg: TableMapping, table: Dataset[Table]): DataFrame = {
    ExpressionManager.applyPreExpressions(
      tablesToDF(cfg.sparkSchema, cfg.conf.allOrderedColumns)(table),
      cfg.conf.allOrderedColumns, cfg.rootConfig.etl.common.shard
    ).na.drop(cfg.conf.allOrderedColumnsWithLoadDate.filter(_.isMandatory).map(_.name) )

  }

  /** Transforms the dataset of tables (where only items related to one specific table are present)
    * into a dataframe matching that table schema.
    *
    * @param schema  the schema of the table we are transforming to dataframe
    * @param columns the configuration for the different column of the schema
    * @param table   the dataset of tables, containing only items related to one single table (the one whose schema is passed)
    * @return a dataframe for the table
    */
  def tablesToDF(schema: StructType, columns: Seq[ColumnConfig])(table: Dataset[Table]): DataFrame = {
    val cols = columns.map(c => c.name -> c).toMap
    //TODO : industrialize this quick patch (trouble when trying to use HashXS to generate a longID)
    val schm: StructType = schema.copy(fields =
      schema.fields.map(f =>
        if (
          cols.contains(f.name) && cols(f.name).expr.toList.nonEmpty && cols(f.name).columnType != ColumnType.strColumn
        ) {
          logger.trace(s"tablesToDF : changing  '${f.name}' type because of  '${cols(f.name).expr}'")
          StructField(f.name, DataTypes.StringType)
        } else {
          f
        }
      )
    )
    table.flatMap(tableRows(schm, _))(RowEncoder(schm))
  }

  private def tableRows(schema: StructType, table: Table): Seq[Row] = {
    def getFieldValue[T](value: String, f: StructField)(conversion: String => T): Any = {
      if (f.nullable) {
        Option(value).map(conversion).orNull
      } else {
        conversion(value)
      }
    }

    table.rows.map { row =>
      // Cast the table columns in accordance with the schema
      Row.fromSeq({
        (schema.fields zip row).map { case (fields, row) =>
          val parseAttempt = Try {
            fields.dataType match {
              case DataTypes.IntegerType => getFieldValue(row, fields)(_.toInt)
              case DataTypes.LongType => getFieldValue(row, fields)(_.toLong)
              case DataTypes.FloatType => getFieldValue(row, fields)(_.toFloat)
              case DataTypes.TimestampType =>
                getFieldValue(row, fields)(f =>
                  Timestamp.valueOf(LocalDateTime.parse(f, DateTimeFormatter.ISO_DATE_TIME))
                )
              case DataTypes.DateType =>
                getFieldValue(row, fields)(f => Date.valueOf("[0-9]{4}-[0-9]{2}-[0-9]{2}".r.findFirstIn(f).get))
              case DataTypes.BinaryType => getFieldValue(row, fields)(_.getBytes("UTF-8"))
              case DataTypes.BooleanType => getFieldValue(row, fields)(_.toBoolean)
              case DataTypes.StringType => getFieldValue(row, fields)(identity)
              case _ =>
                throw new IllegalArgumentException(
                  s"Cannot perform Table->Row on unsupported type: '${fields.dataType}'"
                )
            }
          }
          parseAttempt match {
            case Success(v) =>
              v
            case Failure(f) =>
              logger.trace(s"Failed to parse '${fields.name}' in '$row': '${f.getMessage}'")
              null
          }
        }
      })
    }
  }

}
