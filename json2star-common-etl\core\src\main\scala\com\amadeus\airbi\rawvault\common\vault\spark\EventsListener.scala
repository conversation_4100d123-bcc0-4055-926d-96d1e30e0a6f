package com.amadeus.airbi.rawvault.common.vault.spark
import com.amadeus.airbi.json2star.common.config.ProcessingParams
import com.amadeus.airbi.rawvault.common.vault.spark.EventsListener.{JobRef, StageStats}

import java.util.concurrent.ConcurrentHashMap
import org.apache.spark.scheduler.{SparkListenerJobEnd, SparkListenerJobStart, SparkListenerStageCompleted, StageInfo}

import java.util.Properties

/**
  * This listener displays in the Spark Driver STDOUT some
  * relevant information about the application, including:
  * - spilled tasks
  */
class EventsListener(params: ProcessingParams) extends org.apache.spark.scheduler.SparkListener {

  // Maps to keep job + stages information until job is completed
  val jobIdToName = new ConcurrentHashMap[Int, JobRef]()
  val stageIdToStats = new ConcurrentHashMap[Int, StageStats]()

  private def display(s: String): Unit = {
    println(s) // scalastyle:ignore
  }

  def report(): Unit = { }

  override def onJobStart(jobStart: SparkListenerJobStart): Unit = {
    jobIdToName.put(jobStart.jobId, JobRef.from(jobStart.stageInfos, jobStart.properties))
  }

  override def onStageCompleted(stageCompleted: SparkListenerStageCompleted): Unit = {
    stageIdToStats.put(stageCompleted.stageInfo.stageId, StageStats.from(stageCompleted.stageInfo))
  }

  override def onJobEnd(jobEnd: SparkListenerJobEnd): Unit = {
    val jobId = jobEnd.jobId
    val jobDesc = jobIdToName.get(jobId)
    val stagesIdAndStats = jobDesc.stages.map { sd =>
      (sd, Option(stageIdToStats.get(sd.id)))
    }
    val totalExecCpuTimeSec = stagesIdAndStats.collect{case (_, Some(stgStats)) => stgStats.execCpuSecs}.sum

    val spillMb = stagesIdAndStats.collect{case (_, Some(stgStats)) => stgStats.spillMb}.flatten.sum
    val spillReport = if (spillMb != 0) s"SPILL_MB=$spillMb" else ""

    val header =
      s"JOB ID=${jobEnd.jobId} GROUP='${jobDesc.group}' NAME='${jobDesc.name}' SQL_ID=${jobDesc.sqlId} ${spillReport}"
    val jobStats =
      s"STAGES=${jobDesc.stages.size} TOTAL_CPU_SEC=${totalExecCpuTimeSec}"
    val stagesStats = if (!params.displaySparkStageLevelEvents) "" else "\n" + stagesIdAndStats.map {
      case (id, stgStats) => s"- STAGE JOB=${jobId} ${id}: ${stgStats.mkString}"
    }.mkString("\n")

    display(s"$header $jobStats $stagesStats")

    // Keep maps small
    jobIdToName.remove(jobId)
    jobDesc.stages.foreach(sd => stageIdToStats.remove(sd.id))
  }
}

object EventsListener {

  // Copied from org.apache.spark.context to keep them in this package
  private val SPARK_JOB_DESCRIPTION = "spark.job.description"
  private val SPARK_JOB_GROUP_ID = "spark.jobGroup.id"
  private val SPARK_SQL_EXECUTION_ID = "spark.sql.execution.id"

  case class StageRef(id: Int, nroTasks: Int) {
    override def toString(): String = s"ID=${id} TASKS=${nroTasks}"
  }
  case class JobRef(name: String, group: String, sqlId: String, stages: Seq[StageRef])

  object JobRef {
    def from(stageInfos: Seq[StageInfo], properties: Properties): JobRef = {
      val d = sanitize(properties.getProperty(SPARK_JOB_DESCRIPTION))
      val g = sanitize(properties.getProperty(SPARK_JOB_GROUP_ID))
      val i = properties.getProperty(SPARK_SQL_EXECUTION_ID)
      val s = stageInfos.map(i => StageRef(i.stageId, i.numTasks)).sortBy(_.id)
      JobRef(d, g, i, s)
    }
  }

  case class StageStats(
    inputReadMb: Long,
    outputWriteMb: Long,
    shuffleReadMb: Long,
    shuffleWriteMb: Long,
    execCpuSecs: Long,
    spillMb: Option[Long],
    attempt: Int
  ) {
    override def toString(): String = {
      val spillRep = spillMb.map(i => s"SPILL_MB=$i").mkString
      val attemptRep = if (attempt != 0) s"$attempt" else ""
      s"READ_MB=${inputReadMb} WRITE_MB=${outputWriteMb} SHUFFLE_READ_MB=${shuffleReadMb}" +
        s"SHUFFLE_WRITE_MB=${shuffleWriteMb} EXEC_CPU_SECS=${execCpuSecs} $spillRep $attemptRep"
    }
  }

  object StageStats {
    def from(s: StageInfo): StageStats = {
      val spillMb = if (s.taskMetrics.memoryBytesSpilled > 0) {
        Some(s.taskMetrics.memoryBytesSpilled / 1024 / 1024)
      } else {
        None
      }
      StageStats(
        inputReadMb = s.taskMetrics.inputMetrics.bytesRead / 1024 / 1024,
        outputWriteMb = s.taskMetrics.outputMetrics.bytesWritten / 1024 / 1024,
        shuffleReadMb = s.taskMetrics.shuffleReadMetrics.totalBytesRead / 1024 / 1024,
        shuffleWriteMb = s.taskMetrics.shuffleWriteMetrics.bytesWritten / 1024 / 1024,
        execCpuSecs = s.taskMetrics.executorCpuTime / 1024 / 1024 / 1024,
        spillMb = spillMb,
        attempt = s.attemptNumber()
      )
    }
  }

  private def sanitize(s: String): String = {
    s"$s".replace('\n', ' ')
  }

}