package com.amadeus.airbi.json2star.common.views.generators

import com.amadeus.airbi.json2star.common.views.executors.SnowflakeConfig
import com.amadeus.airbi.json2star.common.views.generators.SchemaGenerator.Statements
import com.amadeus.airbi.json2star.common.{ColumnDef, Schema, TableDef}
import com.amadeus.airbi.rawvault.common.config.ColumnType.ColumnType

/** It generates the SQL DDL statements to copy Snowflake Tables from the previous version's schema,
  * in case of partial reprocessing
  */
class SnowflakeTableCopySchemaGenerator(c: SnowflakeConfig) extends SchemaGenerator {
  assert(
    c.previousDbSchema.isDefined && c.tablesToReprocess.isDefined,
    "previousDbSchema and tablesToReprocess must be defined in SnowflakeConfig"
  )

  private[views] def toTableType(columnType: ColumnType): String = {
    throw new UnsupportedOperationException(s"No support for column-level methods in ${this.getClass.getName}")
  }

  private[views] def toColumnDDL(col: ColumnDef): Seq[String] = {
    throw new UnsupportedOperationException(s"No support for column-level methods in ${this.getClass.getName}")
  }

  private[views] def createInternalTable: Boolean = false

  /** Generate the SQL statement to copy a table from the previous version's database
    *
    * @param conf     a TableDef
    * @param database database name
    * @param options  configurable parameters for delta table
    * @return a init SQL
    */
  def toCreateTableSql(
    conf: TableDef,
    database: String,
    options: Map[String, String]
  ): Option[Statements] = {
    val s = conf.schema
    s.kind match {
      case Schema.Materialized => toCreateTableSql(tableDef = s, database = database, options = options)
      case Schema.View(_) => None // No need to copy views
    }
  }

  private[views] def toCreateTableSql(
    tableDef: Schema,
    database: String,
    options: Map[String, String]
  ): Option[Statements] = {
    val tablesToReprocess = c.tablesToReprocess.get.map(_.trim.toUpperCase())
    tablesToReprocess.contains(tableDef.name) match {
      case true => None // No copy needed for tables to reprocess
      case false =>
        val previousDb = c.previousDbSchema.get
        Some(Statements(s"CREATE OR REPLACE TABLE ${database}.${tableDef.name} CLONE ${previousDb}.${tableDef.name};"))
    }
  }
}
