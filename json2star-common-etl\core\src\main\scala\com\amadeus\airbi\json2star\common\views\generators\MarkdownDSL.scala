package com.amadeus.airbi.json2star.common.views.generators

object MarkdownDSL {
  case class DocElem(key: String, content: String)
  type Row = List[DocElem]
  type Rows = List[List[DocElem]]

  /** Implicit converter methods to generate table in Markdown (.md)
    * @param rows a list of rows
    */
  implicit class Converters(rows: Rows) {

    def replace(str: String): String = {
      str
        .replace("*", "\\*")
        .replace("|", "\\|")
        .replace("\n", " ")
    }

    def toTableMd: String = {
      val Delim = "----"
      val NewLine = "\n"
      val EmptyMsg = "No Table is available"
      if (rows.isEmpty) { EmptyMsg }
      else {
        val headerRow = rows.head.map(_.key)
        val sepRow = headerRow.map(i => Delim)
        val contentRows = rows.map(r => r.map(docElem => replace(docElem.content)))

        val allRows = headerRow +: sepRow +: contentRows
        allRows
          .map(r => "| " + r.mkString(" | ") + " |")
          .mkString(NewLine) + NewLine
      }
    }
  }

}
