package com.amadeus.airbi.json2star.common.snowflake

import com.databricks.dbutils_v1.DBUtilsHolder.dbutils
import org.json4s.DefaultFormats
import org.json4s.jackson.Serialization.{write => asJson}
import pureconfig._
import pureconfig.generic.auto._

case class SnowflakeRootConfig private(
                                 sparkOptions: Map[String, String],
                                 streamOptions: Map[String, String],
                                 mirror: Mirror,
                                 outputConnectorOptions: Map[String, String],
                                 sink: SfSink
                               )

object SnowflakeRootConfig {

  def fromArgs(args: Array[String]): SnowflakeRootConfig = {
    // Expected arguments:
    //   0 -> App conf file
    val confFile = if (args.length < 1) None else Some(args(0))
    SnowflakeRootConfig(confFile)

  }

  def apply(confFile: Option[String] = None): SnowflakeRootConfig = {
    // Reads the config file
    val appConfigSource = confFile match {
      case Some(confFile) =>
        val confFileSource = ConfigSource.file(confFile).recoverWith { case _ => ConfigSource.resources(confFile) }
        ConfigSource.default(confFileSource)
      case None => ConfigSource.default
    }
    val appConfig = appConfigSource.loadOrThrow[SnowflakeSettings]

    // Builds the Spark options
    val sparkOptions: Map[String, String] = appConfig.sparkConf.fold(Map[String, String]())(_.map { case (k, v) =>
      "spark." + k -> v
    })

    // Builds the Snowflake options
    val snowflakeOptions: Map[String, String] = appConfig.snowflakeConf
      // Retrieves actual values from secrets
      .map { case (k, v) =>
        v match {
          case Clear(value) => k -> value
          case Secret(secretName) => k -> dbutils.secrets.get(appConfig.azureConf.secretScope, secretName)
        }
      }
      // Converts keys from "xxx" format to "sfXxx" format
      .map {
        case kv @ ("pem_private_key", _) => kv
        case (k, v) => ("sf" + k.capitalize) -> v
      }+ ( "query_tag" -> asJson(appConfig.queryTag.getOrElse(Map()))(DefaultFormats))

    SnowflakeRootConfig(
      sparkOptions = sparkOptions,
      streamOptions = appConfig.streamOptions.getOrElse(Map()),
      mirror = appConfig.mirror,
      outputConnectorOptions = snowflakeOptions,
      sink = appConfig.sink
    )
  }
}