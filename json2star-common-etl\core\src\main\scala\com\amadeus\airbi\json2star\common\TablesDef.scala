package com.amadeus.airbi.json2star.common

import com.amadeus.airbi.json2star.common.addons.base.AddonConfig
import com.amadeus.airbi.json2star.common.addons.stackable.StackableAddons
import com.amadeus.airbi.rawvault.common.application.config.TablesConfig

case class TablesDef(
  tables: List[TableDef]
) {
  lazy val factTables: List[TableDef] = tables.filter(t => t.schema.isFact)
  lazy val dimTables: List[TableDef] = tables.filter(t => t.schema.isDim)
  lazy val assoTables: List[TableDef] = tables.filter(t => t.schema.isAsso)
  lazy val otherTables: List[TableDef] = tables.filterNot(t => t.schema.isFact || t.schema.isDim || t.schema.isAsso)
}

object TablesDef {
  def consolidate(tablesConf: TablesConfig): TablesDef = TablesDef(
    tables = tablesConf.tables.map { t =>
      // base addon validation
      TableDef.AllAddons.foreach { a =>
        val config: Option[_ <: AddonConfig] = a.getConfig(t)
        config.foreach { _ =>
          a.validate(tablesConf, t)
        }
      }
      // stackable addon validation
      StackableAddons.validate(tablesConf, t)
      new TableDef(general = tablesConf, table = t)
    }
  )
}
