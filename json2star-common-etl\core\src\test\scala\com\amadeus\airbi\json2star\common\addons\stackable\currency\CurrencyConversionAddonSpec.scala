package com.amadeus.airbi.json2star.common.addons.stackable.currency

import com.amadeus.airbi.json2star.common.addons.base.mapping.Mapping
import com.amadeus.airbi.json2star.common.addons.stackable.{StackableAddonValidationException, StackableAddons}
import com.amadeus.airbi.json2star.common.app.Json2StarApp.readMappingConfig
import com.amadeus.airbi.json2star.common.testfwk.CurrencyTestSpec
import com.amadeus.airbi.json2star.common.{MappingType, Origin, TablesDef}
import com.amadeus.airbi.rawvault.common.application.config.TablesConfig
import com.amadeus.airbi.rawvault.common.config.ColumnMetadataRule.Replace
import com.amadeus.airbi.rawvault.common.config.ColumnMetadataValue
import com.amadeus.airbi.rawvault.common.testfwk.Json2StarSpec
import org.apache.spark.sql.Row
import org.apache.spark.sql.types._

import java.sql.Date

class CurrencyConversionAddonSpec extends Json2StarSpec with CurrencyTestSpec {

  override def beforeAll: Unit = {
    super.beforeAll
    createXrtData(
      spark,
      inputDatabase,
      "/datasets/currency_conversion/data/input/exchange_rates/xrt.json",
      "refDataDataBase",
      "exchange_rates"
    )
  }

  override def afterAll: Unit = {
    super.afterAll()
    supprXrtData(
      spark,
      inputDatabase,
      "/datasets/currency_conversion/data/input/exchange_rates/xrt.json",
      "refDataDataBase"
    )
  }

  "CurrencyConversionAddon" should "only be compatible with Mapping" in {
    CurrencyConversionAddon.getCompatibleBaseAddons shouldBe List(Mapping)
  }

  def metadataValue(s: String): ColumnMetadataValue = ColumnMetadataValue(s, Replace)

  it should "enrich the schema metadata" in {
    val mappingFile = "datasets/currency_conversion/simplified_dcsbag.conf"
    val tableName = "FACT_1_CURRENCY_CONVERSION_HISTO"
    val mapping: TablesConfig = readMappingConfig(mappingFile)
    val tableOk = mapping.tables.filter(_.name == tableName).head
    val conversionConfig =
      tableOk.stackableAddons.filter(c => StackableAddons.stackableAddonName(c).contains("currency")).head
    val tableDefs = TablesDef.consolidate(mapping)
    val inputSchema = tableDefs.tables.filter(_.schema.name == tableName).head.schema
    val enrichedSchemaMetadata =
      CurrencyConversionAddon.enrichSchemaMetadata(mapping, tableOk, conversionConfig, inputSchema)

    enrichedSchemaMetadata.description shouldBe None
    enrichedSchemaMetadata.columnsMetadata.size shouldBe 2

    enrichedSchemaMetadata.columnsMetadata("RATE_AMOUNT").origins shouldBe
      Some(
        List(
          Origin(
            "$.mainResource.current.image.excessCharges[*].calculations[*].items[*].rate.total",
            None,
            None,
            MappingType
          )
        )
      )
    enrichedSchemaMetadata.columnsMetadata("RATE_AMOUNT").meta.get.description.get shouldBe
      metadataValue(
        "Conversion of RATE_AMOUNT_ORIGINAL from the unit defined in RATE_CURRENCY_ORIGINAL to the one defined in RATE_CURRENCY at the date EXCHANGE_RATE_DATE_TAKEN closest to the one defined in EXCHANGE_RATE_DATE_NEEDED"
      )

    enrichedSchemaMetadata.columnsMetadata("RATE_CURRENCY").origins shouldBe
      Some(
        List(
          Origin(
            "$.mainResource.current.image.excessCharges[*].calculations[*].items[*].rate.currency",
            None,
            None,
            MappingType
          )
        )
      )
    enrichedSchemaMetadata.columnsMetadata("RATE_CURRENCY").meta.get.description.get shouldBe
      metadataValue("Currency of RATE_AMOUNT")

  }

  it should "validate its configuration: valid config should be ok" in {
    val mappingFile = "datasets/currency_conversion/simplified_dcsbag.conf"
    val mapping: TablesConfig = readMappingConfig(mappingFile)
    val tables = mapping.copy(tables = mapping.tables.filter(_.name == "FACT_1_CURRENCY_CONVERSION_HISTO"))
    val tableOk = tables.tables.head
    val conversionConfig =
      tableOk.stackableAddons.filter(c => StackableAddons.stackableAddonName(c).contains("currency")).head

    // it must not throw
    CurrencyConversionAddon.validate(tables, tableOk, conversionConfig)
  }

  it should "validate its configuration: fail if base addon is not Mapping" in {
    val mappingFile = "datasets/currency_conversion/currency_fail_mapping.conf"
    val mapping: TablesConfig = readMappingConfig(mappingFile)
    val tables = mapping.copy(tables = mapping.tables.filter(_.name == "FACT_FAIL"))
    val tableKo = tables.tables.head
    val conversionConfig =
      tableKo.stackableAddons.filter(c => StackableAddons.stackableAddonName(c).contains("currency")).head
    // it must throw because not mapping
    assertThrows[StackableAddonValidationException](
      CurrencyConversionAddon.validate(tables, tableKo, conversionConfig)
    )
  }

  it should "validate its configuration: fail if any of the input columns is not defined" in {
    val mappingFile = "datasets/currency_conversion/currency_fail_mapping.conf"
    val mapping: TablesConfig = readMappingConfig(mappingFile)
    val tables = mapping.copy(tables = mapping.tables.filter(_.name == "FACT_FAIL_HISTO"))
    val tableKo = tables.tables.head
    val conversionConfig =
      tableKo.stackableAddons.filter(c => StackableAddons.stackableAddonName(c).contains("currency")).head
    // it must throw because column missing
    assertThrows[StackableAddonValidationException](
      CurrencyConversionAddon.validate(tables, tableKo, conversionConfig)
    )
  }

  it should "throw an exception if home currency is not defined or not supported" in {
    val mappingFile = "datasets/currency_conversion/simplified_dcsbag.conf"
    val mapping: TablesConfig = readMappingConfig(mappingFile)
    val tables = mapping.copy(tables = mapping.tables.filter(_.name == "FACT_1_CURRENCY_CONVERSION_HISTO"))
    val tableOk = tables.tables.head
    val conversionConfig =
      tableOk.stackableAddons.filter(c => StackableAddons.stackableAddonName(c).contains("currency")).head
    val schema = StructType(
      List(
        StructField("CHARGE_AMOUNT", FloatType),
        StructField("CHARGE_CURRENCY", StringType),
        StructField("CHARGE_AMOUNT_ORIGINAL", FloatType),
        StructField("CHARGE_CURRENCY_ORIGINAL", StringType),
        StructField("CHARGE_EXCHANGE_RATE_DATE_NEEDED", DateType),
        StructField("RATE_AMOUNT", FloatType),
        StructField("RATE_CURRENCY", StringType),
        StructField("RATE_AMOUNT_ORIGINAL", FloatType),
        StructField("RATE_CURRENCY_ORIGINAL", StringType),
        StructField("RATE_EXCHANGE_RATE_DATE_NEEDED", DateType),
        StructField("EXCHANGE_RATE_DATE_TAKEN", DateType)
      )
    )
    val values = Seq(null, null, 2.0f, "USD", null, null, 1.0f, "USD", Date.valueOf("2023-08-29"), null)
    val input = spark.createDataFrame(spark.sparkContext.parallelize(Seq(Row.fromSeq(values))), schema)

    val rc1 = rootConfig(
      datadir = "",
      inputDatabase,
      homeCurrencyParams = None,
      isLight = isLight
    )
    assertThrows[StackableAddonValidationException](
      CurrencyConversionAddon.enrichBatch(input, conversionConfig, rc1) // scalastyle:off
    )

  }

  it should "not enrich the table rows if home currency information is wrong" in {
    val mappingFile = "datasets/currency_conversion/simplified_dcsbag.conf"
    val mapping: TablesConfig = readMappingConfig(mappingFile)
    val tables = mapping.copy(tables = mapping.tables.filter(_.name == "FACT_1_CURRENCY_CONVERSION_HISTO"))
    val tableOk = tables.tables.head
    val conversionConfig =
      tableOk.stackableAddons.filter(c => StackableAddons.stackableAddonName(c).contains("currency")).head

    val schema = StructType(
      List(
        StructField("CHARGE_AMOUNT", FloatType),
        StructField("CHARGE_CURRENCY", StringType),
        StructField("CHARGE_AMOUNT_ORIGINAL", FloatType),
        StructField("CHARGE_CURRENCY_ORIGINAL", StringType),
        StructField("CHARGE_EXCHANGE_RATE_DATE_NEEDED", DateType),
        StructField("RATE_AMOUNT", FloatType),
        StructField("RATE_CURRENCY", StringType),
        StructField("RATE_AMOUNT_ORIGINAL", FloatType),
        StructField("RATE_CURRENCY_ORIGINAL", StringType),
        StructField("RATE_EXCHANGE_RATE_DATE_NEEDED", DateType),
        StructField("RATE_EXCHANGE_RATE_DATE_TAKEN", DateType),
        StructField("CHARGE_EXCHANGE_RATE_DATE_TAKEN", DateType)
      )
    )
    val values = Seq(null, null, 2.0f, "USD", null, null, 1.0f, "USD", Date.valueOf("2023-08-29"), null, null)
    val input = spark.createDataFrame(spark.sparkContext.parallelize(Seq(Row.fromSeq(values))), schema)

    val rc2 = rootConfigWithHomeCurrencyParams(inputDatabase, "UNKNOWN_CURRENCY", isLight = DefaultIsLight)

    assertThrows[StackableAddonValidationException](
      CurrencyConversionAddon.enrichBatch(input, conversionConfig, rc2) // scalastyle:off
    )

  }

  it should "enrich the dataframe" in {
    val mappingFile = "datasets/currency_conversion/simplified_dcsbag.conf"
    val mapping: TablesConfig = readMappingConfig(mappingFile)
    val tables = mapping.copy(tables = mapping.tables.filter(_.name == "FACT_2_CURRENCY_CONVERSIONS_HISTO"))
    val tableOk = tables.tables.head
    val conversionConfig =
      tableOk.stackableAddons.filter(c => StackableAddons.stackableAddonName(c).contains("currency")).head

    val schema = StructType(
      List(
        StructField("CHARGE_AMOUNT", FloatType),
        StructField("CHARGE_CURRENCY", StringType),
        StructField("CHARGE_AMOUNT_ORIGINAL", FloatType),
        StructField("CHARGE_CURRENCY_ORIGINAL", StringType),
        StructField("CHARGE_EXCHANGE_RATE_DATE_NEEDED", DateType),
        StructField("RATE_AMOUNT", FloatType),
        StructField("RATE_CURRENCY", StringType),
        StructField("RATE_AMOUNT_ORIGINAL", FloatType),
        StructField("RATE_CURRENCY_ORIGINAL", StringType),
        StructField("RATE_EXCHANGE_RATE_DATE_NEEDED", DateType),
        StructField("RATE_EXCHANGE_RATE_DATE_TAKEN", DateType),
        StructField("CHARGE_EXCHANGE_RATE_DATE_TAKEN", DateType)
      )
    )
    val values = Seq(
      null,
      null,
      2.0f,
      "USD",
      Date.valueOf("2023-08-27"),
      null,
      null,
      1.0f,
      "USD",
      Date.valueOf("2023-08-27"),
      null,
      null
    )
    val rows = spark.createDataFrame(spark.sparkContext.parallelize(Seq(Row.fromSeq(values))), schema)

    val expectedValues = Seq(
      1.8450471f,
      "EUR",
      2.0f,
      "USD",
      Date.valueOf("2023-08-27"),
      0.92252356f,
      "EUR",
      1.0f,
      "USD",
      Date.valueOf("2023-08-27"),
      Date.valueOf("2023-08-26"),
      Date.valueOf("2023-08-26")
    )
    val expected = spark.createDataFrame(spark.sparkContext.parallelize(Seq(Row.fromSeq(expectedValues))), schema)

    val rc = rootConfigWithHomeCurrencyParams(inputDatabase, "EUR", isLight = DefaultIsLight)

    val actual = CurrencyConversionAddon.enrichBatch(rows, conversionConfig, rc) // scalastyle:off
    assert(actual.collectAsList() === expected.collectAsList())

  }

  it should "should not enrich the dataframe if ORIGIN CURRENCY UNKNOWN" in {
    val mappingFile = "datasets/currency_conversion/simplified_dcsbag.conf"
    val mapping: TablesConfig = readMappingConfig(mappingFile)
    val tables = mapping.copy(tables = mapping.tables.filter(_.name == "FACT_2_CURRENCY_CONVERSIONS_HISTO"))
    val tableOk = tables.tables.head
    val conversionConfig =
      tableOk.stackableAddons.filter(c => StackableAddons.stackableAddonName(c).contains("currency")).head

    val schema = StructType(
      List(
        StructField("CHARGE_AMOUNT", FloatType),
        StructField("CHARGE_CURRENCY", StringType),
        StructField("CHARGE_AMOUNT_ORIGINAL", FloatType),
        StructField("CHARGE_CURRENCY_ORIGINAL", StringType),
        StructField("CHARGE_EXCHANGE_RATE_DATE_NEEDED", DateType),
        StructField("RATE_AMOUNT", FloatType),
        StructField("RATE_CURRENCY", StringType),
        StructField("RATE_AMOUNT_ORIGINAL", FloatType),
        StructField("RATE_CURRENCY_ORIGINAL", StringType),
        StructField("RATE_EXCHANGE_RATE_DATE_NEEDED", DateType),
        StructField("RATE_EXCHANGE_RATE_DATE_TAKEN", DateType),
        StructField("CHARGE_EXCHANGE_RATE_DATE_TAKEN", DateType)
      )
    )
    val values = Seq(
      null,
      null,
      2.0f,
      "TATA",
      Date.valueOf("2023-08-27"),
      null,
      null,
      1.0f,
      "USD",
      Date.valueOf("2023-08-27"),
      null,
      null
    )
    val rows = spark.createDataFrame(spark.sparkContext.parallelize(Seq(Row.fromSeq(values))), schema)

    val expectedValues = Seq(
      null,
      null,
      2.0f,
      "TATA",
      Date.valueOf("2023-08-27"),
      0.92252356f,
      "EUR",
      1.0f,
      "USD",
      Date.valueOf("2023-08-27"),
      Date.valueOf("2023-08-26"),
      null
    )
    val expected = spark.createDataFrame(spark.sparkContext.parallelize(Seq(Row.fromSeq(expectedValues))), schema)

    val rc = rootConfigWithHomeCurrencyParams(inputDatabase, "EUR", isLight = DefaultIsLight)

    val actual = CurrencyConversionAddon.enrichBatch(rows, conversionConfig, rc) // scalastyle:off
    assert(actual.collectAsList() === expected.collectAsList())

  }

  it should "should enrich the dataframe if 2 different ORIGIN CURRENCY " in {
    val mappingFile = "datasets/currency_conversion/simplified_dcsbag.conf"
    val mapping: TablesConfig = readMappingConfig(mappingFile)
    val tables = mapping.copy(tables = mapping.tables.filter(_.name == "FACT_2_CURRENCY_CONVERSIONS_HISTO"))
    val tableOk = tables.tables.head
    val conversionConfig =
      tableOk.stackableAddons.filter(c => StackableAddons.stackableAddonName(c).contains("currency")).head

    val schema = StructType(
      List(
        StructField("CHARGE_AMOUNT", FloatType),
        StructField("CHARGE_CURRENCY", StringType),
        StructField("CHARGE_AMOUNT_ORIGINAL", FloatType),
        StructField("CHARGE_CURRENCY_ORIGINAL", StringType),
        StructField("CHARGE_EXCHANGE_RATE_DATE_NEEDED", DateType),
        StructField("RATE_AMOUNT", FloatType),
        StructField("RATE_CURRENCY", StringType),
        StructField("RATE_AMOUNT_ORIGINAL", FloatType),
        StructField("RATE_CURRENCY_ORIGINAL", StringType),
        StructField("RATE_EXCHANGE_RATE_DATE_NEEDED", DateType),
        StructField("RATE_EXCHANGE_RATE_DATE_TAKEN", DateType),
        StructField("CHARGE_EXCHANGE_RATE_DATE_TAKEN", DateType)
      )
    )
    val values = Seq(
      null,
      null,
      2.0f,
      "FAKE",
      Date.valueOf("2023-08-27"),
      null,
      null,
      1.0f,
      "USD",
      Date.valueOf("2023-08-27"),
      null,
      null
    )
    val rows = spark.createDataFrame(spark.sparkContext.parallelize(Seq(Row.fromSeq(values))), schema)

    val expectedValues = Seq(
      0.6666667f,
      "EUR",
      2.0f,
      "FAKE",
      Date.valueOf("2023-08-27"),
      0.92252356f,
      "EUR",
      1.0f,
      "USD",
      Date.valueOf("2023-08-27"),
      Date.valueOf("2023-08-26"),
      Date.valueOf("2023-08-22")
    )
    val expected = spark.createDataFrame(spark.sparkContext.parallelize(Seq(Row.fromSeq(expectedValues))), schema)

    val rc = rootConfigWithHomeCurrencyParams(inputDatabase, "EUR", isLight = DefaultIsLight)
    val actual = CurrencyConversionAddon.enrichBatch(rows, conversionConfig, rc) // scalastyle:off
    assert(actual.collectAsList() === expected.collectAsList())

  }

  it should "should enrich the dataframe if  ORIGIN CURRENCY == HOME_CURRENCY" in {
    val mappingFile = "datasets/currency_conversion/simplified_dcsbag.conf"
    val mapping: TablesConfig = readMappingConfig(mappingFile)
    val tables = mapping.copy(tables = mapping.tables.filter(_.name == "FACT_2_CURRENCY_CONVERSIONS_HISTO"))
    val tableOk = tables.tables.head
    val conversionConfig =
      tableOk.stackableAddons.filter(c => StackableAddons.stackableAddonName(c).contains("currency")).head

    val schema = StructType(
      List(
        StructField("CHARGE_AMOUNT", FloatType),
        StructField("CHARGE_CURRENCY", StringType),
        StructField("CHARGE_AMOUNT_ORIGINAL", FloatType),
        StructField("CHARGE_CURRENCY_ORIGINAL", StringType),
        StructField("CHARGE_EXCHANGE_RATE_DATE_NEEDED", DateType),
        StructField("RATE_AMOUNT", FloatType),
        StructField("RATE_CURRENCY", StringType),
        StructField("RATE_AMOUNT_ORIGINAL", FloatType),
        StructField("RATE_CURRENCY_ORIGINAL", StringType),
        StructField("RATE_EXCHANGE_RATE_DATE_NEEDED", DateType),
        StructField("RATE_EXCHANGE_RATE_DATE_TAKEN", DateType),
        StructField("CHARGE_EXCHANGE_RATE_DATE_TAKEN", DateType)
      )
    )
    val values = Seq(
      null,
      null,
      2.0f,
      "EUR",
      Date.valueOf("2023-08-27"),
      null,
      null,
      1.0f,
      "USD",
      Date.valueOf("2023-08-27"),
      null,
      null
    )
    val rows = spark.createDataFrame(spark.sparkContext.parallelize(Seq(Row.fromSeq(values))), schema)

    val expectedValues = Seq(
      2.0f,
      "EUR",
      2.0f,
      "EUR",
      Date.valueOf("2023-08-27"),
      0.92252356f,
      "EUR",
      1.0f,
      "USD",
      Date.valueOf("2023-08-27"),
      Date.valueOf("2023-08-26"),
      null
    )
    val expected = spark.createDataFrame(spark.sparkContext.parallelize(Seq(Row.fromSeq(expectedValues))), schema)

    val rc = rootConfigWithHomeCurrencyParams(inputDatabase, "EUR", isLight = DefaultIsLight)
    val actual = CurrencyConversionAddon.enrichBatch(rows, conversionConfig, rc) // scalastyle:off
    assert(actual.collectAsList() === expected.collectAsList())

  }

  it should "apply the conversion when RATE_AMOUNT_ORIGINAL is all numeric" in {
    runEnrichBatchWithFact3AndCheckResult(
      "USD",
      Map(
        "1.0" -> (null, 0.92252356f),
        "1" -> (null, 0.92252356f),
        "1.50612E7" -> (null, 1.3894312e7f),
        "15061200" -> (null, 1.3894312e7f),
        "0" -> (null, 0.0f)
      )
    )
  }

  it should "apply the conversion when RATE_AMOUNT_ORIGINAL is incremental value <price>A" in {
    runEnrichBatchWithFact3AndCheckResult(
      "USD",
      Map(
        "1.0A" -> ("A", 0.92252356f),
        "1A" -> ("A", 0.92252356f)
      )
    )
  }

  it should "set 0 to the converted result when RATE_AMOUNT_ORIGINAL is No Additional Cost - original currency set" in {
    runEnrichBatchWithFact3AndCheckResult(
      "USD",
      Map(
        "NO_ADC" -> ("NO_ADC", 0.0f),
        "NO ADC" -> ("NO ADC", 0.0f),
        "NOADC" -> ("NOADC", 0.0f),
        "FREE" -> ("FREE", 0.0f),
        "NOFARE" -> ("NOFARE", 0.0f),
        "EXEMPT" -> ("EXEMPT", 0.0f)
      )
    )
  }

  it should "set 0 to the converted result when RATE_AMOUNT_ORIGINAL is No Additional Cost - original currency not set (null)" in {
    runEnrichBatchWithFact3AndCheckResult(
      null,
      Map(
        "NO_ADC" -> ("NO_ADC", 0.0f),
        "NO ADC" -> ("NO ADC", 0.0f),
        "NOADC" -> ("NOADC", 0.0f),
        "FREE" -> ("FREE", 0.0f),
        "NOFARE" -> ("NOFARE", 0.0f),
        "EXEMPT" -> ("EXEMPT", 0.0f)
      )
    )
  }

  it should "set null to the converted result when RATE_AMOUNT_ORIGINAL is an unknown literal" in {
    runEnrichBatchWithFact3AndCheckResult(
      "USD",
      Map(
        "UNKNOWN" -> (null, null)
      )
    )
  }

  it should "set null to the converted result when RATE_AMOUNT_ORIGINAL is IT or BT" in {
    runEnrichBatchWithFact3AndCheckResult(
      "USD",
      Map(
        "IT" -> (null, null),
        "BT" -> (null, null)
      )
    )
  }

  /** Run CurrencyConversionAddon.enrichBatch on the table FACT_3_CURRENCY_CONVERSIONS_HISTO.
    * @param currencyOriginal the original currency
    * @param testCasesMap Map[originalValue, (expectedExtraPartValue, expectedConvertedValue)]
    */
  private def runEnrichBatchWithFact3AndCheckResult(
    currencyOriginal: String,
    testCasesMap: Map[String, (String, Any)]
  ): Unit = {
    val mappingFile = "datasets/currency_conversion/simplified_dcsbag.conf"
    val mapping: TablesConfig = readMappingConfig(mappingFile)
    val tables = mapping.copy(tables = mapping.tables.filter(_.name == "FACT_3_CURRENCY_CONVERSIONS_HISTO"))
    val tableOk = tables.tables.head
    val conversionConfig =
      tableOk.stackableAddons.filter(c => StackableAddons.stackableAddonName(c).contains("currency")).head

    CurrencyConversionAddon.validate(mapping, tableOk, conversionConfig)

    val schema = StructType(
      List(
        StructField("RATE_AMOUNT", FloatType),
        StructField("RATE_CURRENCY", StringType),
        StructField("RATE_AMOUNT_ORIGINAL", StringType),
        StructField("RATE_AMOUNT_ADDITIONAL_PART", StringType),
        StructField("RATE_CURRENCY_ORIGINAL", StringType),
        StructField("RATE_EXCHANGE_RATE_DATE_NEEDED", DateType),
        StructField("RATE_EXCHANGE_RATE_DATE_TAKEN", DateType)
      )
    )

    testCasesMap.foreach { case (originalValue, (expectedExtraPartValue, expectedConvertedValue)) =>
      val values = Seq(
        null,
        null,
        originalValue,
        null,
        currencyOriginal,
        Date.valueOf("2023-08-27"),
        null
      )

      val expectedValues = Seq(
        expectedConvertedValue,
        if (currencyOriginal != null) "EUR" else null, // case when the original currency is not set
        originalValue,
        expectedExtraPartValue,
        currencyOriginal,
        Date.valueOf("2023-08-27"),
        if (currencyOriginal != null) Date.valueOf("2023-08-26") else null // case when the original currency is not set
      )

      val rows = spark.createDataFrame(spark.sparkContext.parallelize(Seq(Row.fromSeq(values))), schema)

      val expected =
        spark.createDataFrame(spark.sparkContext.parallelize(Seq(Row.fromSeq(expectedValues))), schema)

      val rc = rootConfigWithHomeCurrencyParams(inputDatabase, "EUR", isLight = DefaultIsLight)

      val actual = CurrencyConversionAddon.enrichBatch(rows, conversionConfig, rc)
      assert(actual.collectAsList() === expected.collectAsList())
    }
  }
}
