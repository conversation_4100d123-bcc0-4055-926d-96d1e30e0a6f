package com.amadeus.airbi.json2star.common.validation.config

import com.amadeus.airbi.json2star.common.config.AppConfig
import com.databricks.dbutils_v1.DBUtilsV1
import org.apache.hadoop.shaded.com.ctc.wstx.util.DataUtil.Integer

case class ValidationConfig(
  validationDatabase: String,
  validationTablename: String,
  domain: String,
  domainVersion: String,
  customer: String,
  phase: String,
  currentTimestamp: String,
  daysBack: Int
)
case class ValidationConf(
  appConfig: AppConfig,
  validationDatabase: String,
  validationTable: String,
  validationDaysBack: Int = 1,
  checkType: String,
  phase: String,
  inputFeed : String
)
object ValidationConf {

  /** Generate a config for ValidationExecutor from Databricks Utils (widgets, secrets)
    * For notebook deployment
    *
    * @param dbutils the Databricks utils
    * @return a config
    */
  def apply(dbutils: DBUtilsV1): ValidationConf = {
    ValidationConf(
      appConfig = AppConfig.apply(dbutils.widgets.get("appConfigFile")),
      validationDatabase = dbutils.widgets.get("valDatabase"),
      validationTable = dbutils.widgets.get("valTableName"),
      validationDaysBack = dbutils.widgets.get("daysBack").toInt,
      checkType = dbutils.widgets.get("testType"),
      phase = dbutils.widgets.get("phase"),
      inputFeed = try {
        dbutils.widgets.get("inputFeed")
      }  catch {
        case _: Exception => ""
      }
    )
  }

  /** Generate a config for SnowflakeSchemaExecutor from Command-Line Arguments
    * For app deployment:
    * Usage: APP appConfigFile validationDatabase validationTable validationDaysBack checkType phase inputFeed
    *
    * @param args the args array
    * @return a config
    */
  def apply(args: Array[String]): ValidationConf = {
    // scalastyle:off magic.number
    val ac = AppConfig(args(0))
    ValidationConf(
      appConfig = ac,
      validationDatabase = args(1),
      validationTable = args(2),
      validationDaysBack = args(3).toInt ,
      checkType = args(4).toUpperCase,
      phase = args(5).toUpperCase,
      inputFeed = args(6)
    )
  }
  // scalastyle:on magic.number

}
