package com.amadeus.airbi.json2star.common.config

import pureconfig.error.ConfigReaderFailures
import pureconfig.{ConfigObjectCursor, ConfigReader, ConfigSource}

/**
  * Item of Pureconfig configuration whose parsing is to be delayed until actual use
  *
  * This allows for decoupling of configuration parsing between core engine and anything else (like addons).
  * In other words, it allows to avoid upfront parsing of all the configuration (which means the engine knows too
  * much about all the addons for instance), delegating parsing of certain sections with type Unparsed to other
  * configuration consumers (like addons).
  *
  * @param c internal representation of the configuration
  */
case class Unparsed(c: ConfigObjectCursor) {
  override def toString: String = s"Unparsed(${c.objValue.toConfig.toString})"
  /**
    * Parse the configuration as a given type
    * @tparam T the type to be used
    * @return the result of the parsing
    */
  def parseAs[T: ConfigReader]: ConfigReader.Result[T] = {
    val reader = implicitly[ConfigReader[T]]
    reader.from(c)
  }
}

object Unparsed {
  implicit val unparsedReader = ConfigReader.fromCursor[Unparsed](_.asObjectCursor.map(Unparsed.apply))

  /**
    * Create an Unparsed from an HOCON string (for testing purposes)
    */
  def from(str: String): Unparsed = {
    val configObjectCursorAttempt = for {
      c <- ConfigSource.string(str).cursor()
      oc <- c.asObjectCursor
    } yield (oc)

    val objectCursor = configObjectCursorAttempt match {
      case Right(v) => v
      case Left(e) => throw new IllegalArgumentException(s"Failed to parse 'Unparsed' string '$str': '${e.prettyPrint()}'")
    }
    Unparsed(objectCursor)
  }
}
