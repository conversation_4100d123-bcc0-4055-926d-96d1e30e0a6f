{"version": "[VERSION]", "path": "output/path", "shard": "[SHARD]", "schemaName": "DEFAULT_DB.db", "status": "ACTIVATED", "tables": [{"name": "FACT_RESERVATION_HISTO", "type": "FACT", "description": "It contains information related to the booking on PNR-level.", "gdprZone": "red", "kind": "MATERIALIZED", "columns": [{"name": "RESERVATION_ID", "description": "", "type": "STRING", "primaryKey": true, "gdprZone": "green", "piiType": "", "fkRelationships": [], "sourcePaths": ["hashM($.mainResource.current.image.id)"]}, {"name": "POINT_OF_SALE_ID", "description": "Hashed foreign key", "type": "STRING", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": [{"tableName": "DIM_POINT_OF_SALE", "columnName": "POINT_OF_SALE_ID"}], "sourcePaths": ["hashM($.mainResource.current.image.owner.office.id - $.mainResource.current.image.owner.office.iataNumber - $.mainResource.current.image.owner.office.systemCode - $.mainResource.current.image.owner.office.agentType - $.mainResource.current.image.owner.login.cityCode - $.mainResource.current.image.owner.login.countryCode - $.mainResource.current.image.owner.login.numericSign - $.mainResource.current.image.owner.login.initials - $.mainResource.current.image.owner.login.dutyCode)"]}, {"name": "POINT_OF_SALE_OWNER_ID", "description": "Hashed foreign key", "type": "STRING", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": [{"tableName": "DIM_POINT_OF_SALE", "columnName": "POINT_OF_SALE_ID"}], "sourcePaths": ["hashM($.mainResource.current.image.owner.office.id - $.mainResource.current.image.owner.office.iataNumber - $.mainResource.current.image.owner.office.systemCode - $.mainResource.current.image.owner.office.agentType - $.mainResource.current.image.owner.login.cityCode - $.mainResource.current.image.owner.login.countryCode - $.mainResource.current.image.owner.login.numericSign - $.mainResource.current.image.owner.login.initials - $.mainResource.current.image.owner.login.dutyCode)"]}, {"name": "POINT_OF_SALE_CREATION_ID", "description": "Hashed foreign key", "type": "STRING", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": [{"tableName": "DIM_POINT_OF_SALE", "columnName": "POINT_OF_SALE_ID"}], "sourcePaths": ["hashM($.mainResource.current.image.creation.pointOfSale.office.id - $.mainResource.current.image.creation.pointOfSale.office.iataNumber - $.mainResource.current.image.creation.pointOfSale.office.systemCode - $.mainResource.current.image.creation.pointOfSale.office.agentType - $.mainResource.current.image.creation.pointOfSale.login.cityCode - $.mainResource.current.image.creation.pointOfSale.login.countryCode - $.mainResource.current.image.creation.pointOfSale.login.numericSign - $.mainResource.current.image.creation.pointOfSale.login.initials - $.mainResource.current.image.creation.pointOfSale.login.dutyCode)"]}, {"name": "POINT_OF_SALE_LAST_UPDATE_ID", "description": "Hashed foreign key", "type": "STRING", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": [{"tableName": "DIM_POINT_OF_SALE", "columnName": "POINT_OF_SALE_ID"}, {"tableName": "DUMMY", "columnName": "DUMMY"}], "sourcePaths": ["hashM($.mainResource.current.image.lastModification.pointOfSale.office.id - $.mainResource.current.image.lastModification.pointOfSale.office.iataNumber - $.mainResource.current.image.lastModification.pointOfSale.office.systemCode - $.mainResource.current.image.lastModification.pointOfSale.office.agentType - $.mainResource.current.image.lastModification.pointOfSale.login.cityCode - $.mainResource.current.image.lastModification.pointOfSale.login.countryCode - $.mainResource.current.image.lastModification.pointOfSale.login.numericSign - $.mainResource.current.image.lastModification.pointOfSale.login.initials - $.mainResource.current.image.lastModification.pointOfSale.login.dutyCode)"]}, {"name": "MY_TABLE_CODE", "description": "", "type": "STRING", "primaryKey": false, "gdprZone": "red", "piiType": "", "fkRelationships": [], "sourcePaths": ["$.mainResource.current.image.table.id"]}, {"name": "TABLE", "description": "", "type": "STRING", "primaryKey": false, "piiType": "", "fkRelationships": [], "sourcePaths": ["$.mainResource.current.image.table.id"]}, {"name": "COLUMN_NAME_A", "description": "", "type": "STRING", "primaryKey": false, "piiType": "", "fkRelationships": [], "sourcePaths": ["$.mainResource.current.image.table.id"]}, {"name": "VERSION", "description": "", "type": "STRING", "primaryKey": true, "gdprZone": "green", "piiType": "", "fkRelationships": [{"tableName": "FACT_A_HISTO", "columnName": "A_ID"}, {"tableName": "FACT_B_HISTO", "columnName": "B_ID"}], "sourcePaths": ["$.mainResource.current.image.version"]}, {"name": "FACT_A_ID", "description": "Hashed foreign key", "type": "STRING", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": [{"tableName": "FACT_A_HISTO", "columnName": "A_ID"}], "sourcePaths": ["hashM($.mainResource.current.image.ida2)"]}, {"name": "FACT_B_ID", "description": "Hashed foreign key", "type": "STRING", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": [{"tableName": "FACT_B_HISTO", "columnName": "B_ID"}], "sourcePaths": ["hashM($.mainResource.current.image.idb2)"]}, {"name": "PNR_CREATION_DATE", "description": "", "type": "TIMESTAMP", "primaryKey": false, "piiType": "", "fkRelationships": [], "sourcePaths": ["$.mainResource.current.image.creation.dateTime"]}, {"name": "DATE_BEGIN", "description": "", "type": "TIMESTAMP", "primaryKey": false, "piiType": "", "fkRelationships": [], "sourcePaths": ["$.mainResource.current.image.lastModification.dateTime"]}, {"name": "DATE_END", "description": "", "type": "TIMESTAMP", "primaryKey": false, "piiType": "", "fkRelationships": []}, {"name": "IS_LAST_VERSION", "description": "", "type": "BOOLEAN", "primaryKey": false, "piiType": "", "fkRelationships": []}, {"name": "MY_TABLE_CODE_ID", "description": "Hashed Foreign Key", "type": "BIGINT", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": [{"schemaName": "PNR_DATABASE_NAME", "tableName": "DIM_TABLE", "columnName": "TABLE_ID"}], "sourcePaths": ["hashXS($.mainResource.current.image.table.id)"]}, {"name": "TABLE_ID", "description": "Hashed Foreign Key", "type": "BIGINT", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": [{"tableName": "DIM_TABLE", "columnName": "TABLE_ID"}], "sourcePaths": ["hashXS($.mainResource.current.image.table.id)"]}, {"name": "COLUMN_ID_A", "description": "Hashed Foreign Key", "type": "STRING", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": [], "sourcePaths": ["hashXS($.mainResource.current.image.table.id)"]}, {"name": "LOAD_DATE", "description": "Technical field: date/time UTC when the source message was received in the Amadeus Big Data Platform", "type": "TIMESTAMP", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": []}]}, {"name": "FACT_RESERVATION", "type": "FACT", "description": "Latest view of FACT_RESERVATION_HISTO", "gdprZone": "red", "kind": "VIEW", "query": "SELECT RESERVATION_ID,POINT_OF_SALE_ID,POINT_OF_SALE_OWNER_ID,POINT_OF_SALE_CREATION_ID,POINT_OF_SALE_LAST_UPDATE_ID,MY_TABLE_CODE,T<PERSON><PERSON>,COLUMN_NAME_A,VERSI<PERSON>,FACT_A_ID,FACT_B_ID,PNR_CREATION_DATE,MY_TABLE_CODE_ID,TABLE_ID,COLUMN_ID_A,LOAD_DATE FROM FACT_RESERVATION_HISTO WHERE IS_LAST_VERSION=true", "columns": [{"name": "RESERVATION_ID", "description": "", "type": "STRING", "primaryKey": true, "gdprZone": "green", "piiType": "", "fkRelationships": [], "sourcePaths": ["hashM($.mainResource.current.image.id)"]}, {"name": "POINT_OF_SALE_ID", "description": "Hashed foreign key", "type": "STRING", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": [{"tableName": "DIM_POINT_OF_SALE", "columnName": "POINT_OF_SALE_ID"}], "sourcePaths": ["hashM($.mainResource.current.image.owner.office.id - $.mainResource.current.image.owner.office.iataNumber - $.mainResource.current.image.owner.office.systemCode - $.mainResource.current.image.owner.office.agentType - $.mainResource.current.image.owner.login.cityCode - $.mainResource.current.image.owner.login.countryCode - $.mainResource.current.image.owner.login.numericSign - $.mainResource.current.image.owner.login.initials - $.mainResource.current.image.owner.login.dutyCode)"]}, {"name": "POINT_OF_SALE_OWNER_ID", "description": "Hashed foreign key", "type": "STRING", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": [{"tableName": "DIM_POINT_OF_SALE", "columnName": "POINT_OF_SALE_ID"}], "sourcePaths": ["hashM($.mainResource.current.image.owner.office.id - $.mainResource.current.image.owner.office.iataNumber - $.mainResource.current.image.owner.office.systemCode - $.mainResource.current.image.owner.office.agentType - $.mainResource.current.image.owner.login.cityCode - $.mainResource.current.image.owner.login.countryCode - $.mainResource.current.image.owner.login.numericSign - $.mainResource.current.image.owner.login.initials - $.mainResource.current.image.owner.login.dutyCode)"]}, {"name": "POINT_OF_SALE_CREATION_ID", "description": "Hashed foreign key", "type": "STRING", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": [{"tableName": "DIM_POINT_OF_SALE", "columnName": "POINT_OF_SALE_ID"}], "sourcePaths": ["hashM($.mainResource.current.image.creation.pointOfSale.office.id - $.mainResource.current.image.creation.pointOfSale.office.iataNumber - $.mainResource.current.image.creation.pointOfSale.office.systemCode - $.mainResource.current.image.creation.pointOfSale.office.agentType - $.mainResource.current.image.creation.pointOfSale.login.cityCode - $.mainResource.current.image.creation.pointOfSale.login.countryCode - $.mainResource.current.image.creation.pointOfSale.login.numericSign - $.mainResource.current.image.creation.pointOfSale.login.initials - $.mainResource.current.image.creation.pointOfSale.login.dutyCode)"]}, {"name": "POINT_OF_SALE_LAST_UPDATE_ID", "description": "Hashed foreign key", "type": "STRING", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": [{"tableName": "DIM_POINT_OF_SALE", "columnName": "POINT_OF_SALE_ID"}, {"tableName": "DUMMY", "columnName": "DUMMY"}], "sourcePaths": ["hashM($.mainResource.current.image.lastModification.pointOfSale.office.id - $.mainResource.current.image.lastModification.pointOfSale.office.iataNumber - $.mainResource.current.image.lastModification.pointOfSale.office.systemCode - $.mainResource.current.image.lastModification.pointOfSale.office.agentType - $.mainResource.current.image.lastModification.pointOfSale.login.cityCode - $.mainResource.current.image.lastModification.pointOfSale.login.countryCode - $.mainResource.current.image.lastModification.pointOfSale.login.numericSign - $.mainResource.current.image.lastModification.pointOfSale.login.initials - $.mainResource.current.image.lastModification.pointOfSale.login.dutyCode)"]}, {"name": "MY_TABLE_CODE", "description": "", "type": "STRING", "primaryKey": false, "gdprZone": "red", "piiType": "", "fkRelationships": [], "sourcePaths": ["$.mainResource.current.image.table.id"]}, {"name": "TABLE", "description": "", "type": "STRING", "primaryKey": false, "piiType": "", "fkRelationships": [], "sourcePaths": ["$.mainResource.current.image.table.id"]}, {"name": "COLUMN_NAME_A", "description": "", "type": "STRING", "primaryKey": false, "piiType": "", "fkRelationships": [], "sourcePaths": ["$.mainResource.current.image.table.id"]}, {"name": "VERSION", "description": "", "type": "STRING", "primaryKey": true, "gdprZone": "green", "piiType": "", "fkRelationships": [{"tableName": "FACT_A", "columnName": "A_ID"}, {"tableName": "FACT_B", "columnName": "B_ID"}], "sourcePaths": ["$.mainResource.current.image.version"]}, {"name": "FACT_A_ID", "description": "Hashed foreign key", "type": "STRING", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": [{"tableName": "FACT_A", "columnName": "A_ID"}], "sourcePaths": ["hashM($.mainResource.current.image.ida2)"]}, {"name": "FACT_B_ID", "description": "Hashed foreign key", "type": "STRING", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": [{"tableName": "FACT_B", "columnName": "B_ID"}], "sourcePaths": ["hashM($.mainResource.current.image.idb2)"]}, {"name": "PNR_CREATION_DATE", "description": "", "type": "TIMESTAMP", "primaryKey": false, "piiType": "", "fkRelationships": [], "sourcePaths": ["$.mainResource.current.image.creation.dateTime"]}, {"name": "MY_TABLE_CODE_ID", "description": "Hashed Foreign Key", "type": "BIGINT", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": [{"schemaName": "PNR_DATABASE_NAME", "tableName": "DIM_TABLE", "columnName": "TABLE_ID"}], "sourcePaths": ["hashXS($.mainResource.current.image.table.id)"]}, {"name": "TABLE_ID", "description": "Hashed Foreign Key", "type": "BIGINT", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": [{"tableName": "DIM_TABLE", "columnName": "TABLE_ID"}], "sourcePaths": ["hashXS($.mainResource.current.image.table.id)"]}, {"name": "COLUMN_ID_A", "description": "Hashed Foreign Key", "type": "STRING", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": [], "sourcePaths": ["hashXS($.mainResource.current.image.table.id)"]}, {"name": "LOAD_DATE", "description": "Technical field: date/time UTC when the source message was received in the Amadeus Big Data Platform", "type": "TIMESTAMP", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": []}]}, {"name": "DIM_POINT_OF_SALE", "type": "DIM", "description": "", "gdprZone": "green", "kind": "MATERIALIZED", "columns": [{"name": "POINT_OF_SALE_ID", "description": "", "type": "STRING", "primaryKey": true, "gdprZone": "green", "piiType": "", "fkRelationships": [], "sourcePaths": ["$.dummy.id"]}, {"name": "OFFICE_IATA_NUMBER", "description": "", "type": "STRING", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": [], "sourcePaths": ["$.dummy.iataNumber"]}, {"name": "OFFICE_SYSTEM_CODE", "description": "", "type": "STRING", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": [], "sourcePaths": ["$.dummy.systemCode"]}, {"name": "OFFICE_AGENT_TYPE", "description": "", "type": "STRING", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": [], "sourcePaths": ["$.dummy.agentType"]}, {"name": "LOAD_DATE", "description": "Technical field: date/time UTC when the source message was received in the Amadeus Big Data Platform", "type": "TIMESTAMP", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": []}]}, {"name": "FACT_A", "type": "FACT", "description": "Latest view of FACT_A_HISTO", "gdprZone": "green", "kind": "VIEW", "query": "SELECT VERSION,FACT_A_ID,LOAD_DATE FROM FACT_A_HISTO WHERE IS_LAST_VERSION=true", "columns": [{"name": "VERSION", "description": "", "type": "STRING", "primaryKey": true, "gdprZone": "green", "piiType": "", "fkRelationships": [], "sourcePaths": ["$.dummy.version"]}, {"name": "FACT_A_ID", "description": "", "type": "STRING", "primaryKey": true, "gdprZone": "green", "piiType": "", "fkRelationships": [], "sourcePaths": ["$.dummy.id2"]}, {"name": "LOAD_DATE", "description": "Technical field: date/time UTC when the source message was received in the Amadeus Big Data Platform", "type": "TIMESTAMP", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": []}]}, {"name": "FACT_A_HISTO", "type": "FACT", "description": "", "gdprZone": "", "kind": "MATERIALIZED", "columns": [{"name": "VERSION", "description": "", "type": "STRING", "primaryKey": true, "gdprZone": "green", "piiType": "", "fkRelationships": [], "sourcePaths": ["$.dummy.version"]}, {"name": "FACT_A_ID", "description": "", "type": "STRING", "primaryKey": true, "gdprZone": "green", "piiType": "", "fkRelationships": [], "sourcePaths": ["$.dummy.id2"]}, {"name": "DATE_BEGIN", "description": "", "type": "TIMESTAMP", "primaryKey": false, "piiType": "", "fkRelationships": [], "sourcePaths": ["$.dummy.lastModification.dateTime"]}, {"name": "DATE_END", "description": "", "type": "TIMESTAMP", "primaryKey": false, "piiType": "", "fkRelationships": []}, {"name": "IS_LAST_VERSION", "description": "", "type": "BOOLEAN", "primaryKey": false, "piiType": "", "fkRelationships": []}, {"name": "LOAD_DATE", "description": "Technical field: date/time UTC when the source message was received in the Amadeus Big Data Platform", "type": "TIMESTAMP", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": []}]}, {"name": "FACT_B", "type": "FACT", "description": "Latest view of FACT_B_HISTO", "gdprZone": "green", "kind": "VIEW", "query": "SELECT VERSION,FACT_B_ID,LOAD_DATE FROM FACT_B_HISTO WHERE IS_LAST_VERSION=true", "columns": [{"name": "VERSION", "description": "", "type": "STRING", "primaryKey": true, "gdprZone": "green", "piiType": "", "fkRelationships": [], "sourcePaths": ["$.dummy.version"]}, {"name": "FACT_B_ID", "description": "", "type": "STRING", "primaryKey": true, "gdprZone": "green", "piiType": "", "fkRelationships": [], "sourcePaths": ["$.dummy.id2"]}, {"name": "LOAD_DATE", "description": "Technical field: date/time UTC when the source message was received in the Amadeus Big Data Platform", "type": "TIMESTAMP", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": []}]}, {"name": "FACT_B_HISTO", "type": "FACT", "description": "", "gdprZone": "", "kind": "MATERIALIZED", "columns": [{"name": "VERSION", "description": "", "type": "STRING", "primaryKey": true, "gdprZone": "green", "piiType": "", "fkRelationships": [], "sourcePaths": ["$.dummy.version"]}, {"name": "FACT_B_ID", "description": "", "type": "STRING", "primaryKey": true, "gdprZone": "green", "piiType": "", "fkRelationships": [], "sourcePaths": ["$.dummy.id2"]}, {"name": "DATE_BEGIN", "description": "", "type": "TIMESTAMP", "primaryKey": false, "piiType": "", "fkRelationships": [], "sourcePaths": ["$.dummy.lastModification.dateTime"]}, {"name": "DATE_END", "description": "", "type": "TIMESTAMP", "primaryKey": false, "piiType": "", "fkRelationships": []}, {"name": "IS_LAST_VERSION", "description": "", "type": "BOOLEAN", "primaryKey": false, "piiType": "", "fkRelationships": []}, {"name": "LOAD_DATE", "description": "Technical field: date/time UTC when the source message was received in the Amadeus Big Data Platform", "type": "TIMESTAMP", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": []}]}, {"name": "DIM_TABLE", "type": "DIM", "description": "", "gdprZone": "green", "kind": "MATERIALIZED", "columns": [{"name": "TABLE_ID", "description": "", "type": "BIGINT", "primaryKey": true, "gdprZone": "green", "piiType": "", "fkRelationships": [], "sourcePaths": ["hashXS($.dummy.table.id)"]}, {"name": "TABLE_CODE", "description": "", "type": "STRING", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": [], "sourcePaths": ["$.dummy.table.id"]}, {"name": "LOAD_DATE", "description": "Technical field: date/time UTC when the source message was received in the Amadeus Big Data Platform", "type": "TIMESTAMP", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": []}]}]}