package com.amadeus.airbi.json2star.common.addons.base.mapping

import com.amadeus.airbi.datalake.common.spark.DefaultLocalSparkInstance
import com.amadeus.airbi.json2star.common.app.Json2StarApp.readMappingConfig
import com.amadeus.airbi.json2star.common.app.{Json2StarApp, TemporaryTable}
import com.amadeus.airbi.json2star.common.resize.DefaultDatabricksUtils
import com.amadeus.airbi.rawvault.common.testfwk._
import com.amadeus.airbi.rawvault.common.testfwk.snowflake.SnowflakeSpec
import com.jayway.jsonpath.DocumentContext
import org.apache.commons.io.FileUtils
import org.apache.spark.sql.functions.col
import org.apache.spark.sql.streaming.StreamingQueryException
import org.scalatest.tags.Slow

import java.io.File
import java.nio.file.FileSystems
import scala.reflect.io.Directory

/**
 * These scenarios check the failure-recovery mechanism of the Json2Star application in the context of the Master-Secondary PIT processing
 * where the Master table is updated first, then the Secondary tables are updated.
 * The update of tables is done within a Spark Streaming Query
 * Each table is updated with a separate MERGE query - each query is atomic
 * The checkpoint of the Spark Streaming Query is updated after that all tables are updated.
 * In case of failure, the Spark Streaming Query is re-run on the same input data, but tables can have been already updated.
 * For this reason, the update process must be resilient to intermediate failures and idempotent for already updated tables.
 *
 * Given the Database with these tables:
 * - 1 Master Table M
 * - 2 Secondary Table S1, S2
 *
 * Test Scenarios
 * 1) Master-Secondary PIT (default data)     failure after master       - Merge 1 "light" is used to upsert tables
 * 2) Master-Secondary PIT (default data)     failure after secondary    - Merge 1 "light" is used to upsert tables
 * 3) Master-Secondary PIT (replay past data) failure after master       - Merge 2 "histo" is used to upsert tables
 *
 * SCENARIO 1: failure happens between master-secondary when processing new data (default update)
 * Steps:
 * - The Master table M is successfully updated
 * - The Secondary table S2 is successfully updated
 * - The Secondary table S1 update fails and table is not updated
 * The recovery run must update correctly S1 and keep M correct
 *
 * The following cases are tested according to the data in the master/secondary tables:
 * - case 1 - tables have no latest, no histo partitions
 * - case 2 - tables have latest, no histo partitions
 * - case 3 - tables have latest and histo partitions
 *
 * SCENARIO 2: failure happens between secondary-secondary table when processing new data (default update)
 * Tables are
 * - 1 Master Table M
 * - 2 Secondary Table (S1, S2)
 *
 * Steps:
 * - The Master table M is successfully updated
 * - The Secondary table S1 is successfully updated
 * - The Secondary table S2 fails and the table is not updated
 * The recovery run must update correctly S2 and keep M, S1 correct
 * Note: considers only the case 2 (latest, no histo partitions) to reduce tests and their execution times
 *
 * SCENARIO 3: failure happens between master-secondary when processing 'replay' data (past update)
 * It uses the Merge 2 to handle old versions
 * Note: considers only the case 2 (latest, no histo partitions) to reduce tests and their execution times
 *
 * SCENARIO 4: failure happens between secondary-secondary when processing 'replay' data (past update)
 * It uses the Merge 2 to handle old versions
 * Note: considers only the case 2 (latest, no histo partitions) to reduce tests and their execution times
 */
@Slow
class IntegrationRevisedPitFailureRecoverySpec extends Json2StarSpec with JsonCustomizer {

  val srcTestPath = s"src/test/resources/datasets/revised_pit"
  val mappingFile = s"$srcTestPath/revised_pit_mapping.conf"
  val tableNames: Seq[String] = getTableNames(mappingFile)

  val MockNumWorkers = 5

  override def beforeAll: Unit = {
    super.beforeAll
    createTables(mappingFile)
  }

  override def beforeEach: Unit = {
    cleanTables(tableNames)
  }

  // Flag to write input files in test resource folder from the JSON template using the custom generator
  val CreateInputFlag = true

  // Flag to write expected resources in test resource folder from the generated table
  override val RewriteExpectedForFailingTests: Boolean = false

  // as multiple batches, the test light mode can't be used
  override def isLight: Boolean = false

  def expectedResultsPath(scenario: String, batch: String): String = {
    s"$srcTestPath/${scenario}/data/batch${batch}/expected_results/"
  }

  def inputPath(scenario: String, batch: String): String = {
    s"datasets/revised_pit/${scenario}/data/batch${batch}/input/"
  }

  val DisplayMainEventsParam: Boolean = true

  val AKey = "KEY A"
  // functional date (last modification date field of the document)
  val Date1 = "2023-08-30T08:35:00Z"
  val Date2 = "2023-08-30T10:41:00Z"
  val Date3 = "2023-09-02T12:30:00Z"

  // technical date (load date of event message)
  val LoadDate1 = "08-31-2023"
  val LoadDate2 = "09-03-2023"
  val LoadDate3 = "09-04-2023"

  private def check(scenario: String, batch: String, dbSuffix: Option[String] = None, createExpected: Boolean = false): Unit = {
    checkTablesContent(
      tableNames,
      expectedResultsPath(scenario, batch),
      createExpected = createExpected,
      dbSuffix = dbSuffix
    )
  }

  private def getVersion(table: String): Int = {
    val maxVersion = spark.sql(s"describe history $table")
      .selectExpr("max(version) as max_version")
      .collect()
      .head
      .getLong(0)
      .toInt
    maxVersion
  }

  private def assertEmpty(table: String): Unit = {
    val numRows = spark.sql(s"select * from $table").count()
    assert(numRows == 0, s"Expected empty table $table, but found $numRows rows")
  }

  private def assertNoDupsLatest(table: String, keyCol: String): Unit = {
    val numKeysWithDups = spark.sql(s"select $keyCol, count(*) from $table where IS_LAST_ENVELOP = true group by $keyCol having count(*) > 1").count()
    assert(numKeysWithDups == 0, s"Expected no duplicates in $table, but found $numKeysWithDups keys with dups")
  }

  private def assertEquality(table: String, beforeVersion: Int, afterVersion: Int): Unit = {
    val path = spark.sql(s"describe detail $table").select("location").collect().head.getString(0)
    val before = spark.read
      .format("delta")
      .option("versionAsOf", beforeVersion)
      .load(path)
    val after = spark.read
      .format("delta")
      .option("versionAsOf", afterVersion)
      .load(path)
    val columns = before.columns.map(col)
    DataFrameComparison.compareData(table, before.sort(columns: _*), after.sort(columns: _*))
  }

  // disable formatter on test cases to keep one line for each generated test input file
  // @formatter:off
  "Master-Secondary PIT - Scenario 1 default data - failure between master-secondary tables" should
    "be resilient to failure in Secondary Merge 1 for case 1 (no latest, no histo)" taggedAs(SlowTestTag) in withTmpDir { tmp =>
    /**
     * Scenario: failure in secondary table merge 1
     * Expected: data is correctly updated in master and secondary tables - only 1 version with is_last=true
     * - J2S App Run 1:
     *  - Master Merge is ok (merge 1)
     *  - Secondary Merge fails (merge 1)
     * - Re-run J2S App Run 2:
     *  - Master Merge is ok    - (merge 2) idempotent
     *  - Secondary Merge is ok - (merge 2)
     */
    val Scenario = "s11-merge_failure_rerun"
    /** Steps
     * - 1. Add constraint to secondary table --> master table is ok, but secondary table will fail
     * - 2. Process Batch 1 (master merge 1 ok, will fail on merge 1 of secondary table, because of constraint violation)
     * - 3. Remove constraint to secondary table --> master table is ok, but secondary table will fail
     * - 4. Process Batch 1 again (should work) - duplicata ???
     * - 5. Perform the check
     */

    /** batch 1 - failure **/
    inputFile(Scenario, batch = "1", key = AKey, version = "1", beginDate = Date1, loadDate = LoadDate1, removeProduct0 = false, createInput = CreateInputFlag)
    // add constraint to all secondary tables - so no secondary tables is updated
    spark.sql("ALTER TABLE FACT_SECONDARY_1_HISTO ADD CONSTRAINT CHK_A CHECK (ENVELOP_NB != 1);")
    spark.sql("ALTER TABLE FACT_SECONDARY_2_HISTO ADD CONSTRAINT CHK_A CHECK (ENVELOP_NB != 1);")

    intercept[StreamingQueryException](
      runBatch(batch = "1", inputPath(Scenario, batch = "1"))
    )

    assertEmpty("FACT_SECONDARY_1_HISTO")
    assertEmpty("FACT_SECONDARY_2_HISTO")
    assertNoTemporaryTable()
    check(Scenario, batch = "1")

    /** batch 1 - retry ok **/
    // remove all constraints
    spark.sql("ALTER TABLE FACT_SECONDARY_1_HISTO DROP CONSTRAINT CHK_A;")
    spark.sql("ALTER TABLE FACT_SECONDARY_2_HISTO DROP CONSTRAINT CHK_A;")

    val beforeVerM = getVersion("FACT_MASTER_HISTO")
    runBatch(batch = "1", inputPath(Scenario, batch = "1"))
    val afterVerM = getVersion("FACT_MASTER_HISTO")

    assertEquality("FACT_MASTER_HISTO", beforeVerM, afterVerM)
    assertNoDupsLatest("FACT_MASTER_HISTO", "RESERVATION_ID")
    assertNoDupsLatest("FACT_SECONDARY_1_HISTO", "AIR_SEGMENT_ID")
    assertNoDupsLatest("FACT_SECONDARY_2_HISTO", "AIR_SEGMENT_PAX_ID")
    check(Scenario, batch = "1-rerun")
  }

  it should "be resilient to failure in Secondary Merge 1 for case 2 (latest, no histo)" taggedAs(SlowTestTag) in {
    // case 2 - known key             - Master Latest, Histo is empty
    val Scenario = "s12-merge_failure_rerun"

    /** batch 1 - ok **/
    inputFile(Scenario, batch = "1", key = AKey, version = "1", beginDate = Date1, loadDate = LoadDate1, removeProduct0 = false, createInput = CreateInputFlag)
    runBatch(batch = "1", inputPath(Scenario, batch = "1"))
    check(Scenario, batch = "1")

    /** batch 2 - failure **/
    val beforeVerS1 = getVersion("FACT_SECONDARY_1_HISTO")
    inputFile(Scenario, batch = "2", key = AKey, version = "2", beginDate = Date2, loadDate = LoadDate2, removeProduct0 = false, createInput = CreateInputFlag)
    // add constraints to all secondary tables - so no secondary tables is updated
    spark.sql("ALTER TABLE FACT_SECONDARY_1_HISTO ADD CONSTRAINT CHK_A CHECK (ENVELOP_NB != 2);")
    spark.sql("ALTER TABLE FACT_SECONDARY_2_HISTO ADD CONSTRAINT CHK_A CHECK (ENVELOP_NB != 2);")

    intercept[StreamingQueryException](
      runBatch(batch = "2", inputPath(Scenario, batch = "2"))
    )
    val afterVerS1 = getVersion("FACT_SECONDARY_1_HISTO")
    // check no update for secondary table --> because of failure
    assertEquality("FACT_SECONDARY_1_HISTO", beforeVerS1, afterVerS1)
    check(Scenario, batch = "2")

    /** batch 2 - retry ok **/
    val beforeVerM = getVersion("FACT_MASTER_HISTO")
    // remove all constraints
    spark.sql("ALTER TABLE FACT_SECONDARY_1_HISTO DROP CONSTRAINT CHK_A;")
    spark.sql("ALTER TABLE FACT_SECONDARY_2_HISTO DROP CONSTRAINT CHK_A;")

    runBatch(batch = "2", inputPath(Scenario, batch = "2"))
    val afterVerM = getVersion("FACT_MASTER_HISTO")

    // verify idempotency for master table
    assertEquality("FACT_MASTER_HISTO", beforeVerM, afterVerM)
    assertNoDupsLatest("FACT_MASTER_HISTO", "RESERVATION_ID")
    assertNoDupsLatest("FACT_SECONDARY_1_HISTO", "AIR_SEGMENT_ID")
    assertNoDupsLatest("FACT_SECONDARY_2_HISTO", "AIR_SEGMENT_PAX_ID")

    check(Scenario, batch = "2-rerun")
  }

  it should "be resilient to failure in Secondary Merge 1 for case 3 (latest, histo)" taggedAs(SlowTestTag) in {
    // case 3 - known key with histo  - Master Latest, Histo is not empty
    val Scenario = "s13-merge_failure_rerun"

    /** batch 1 - ok **/
    // insert in latest
    inputFile(Scenario, batch = "1", key = AKey, version = "1", beginDate = Date1, loadDate = LoadDate1, removeProduct0 = false, createInput = CreateInputFlag)
    runBatch(batch = "1", inputPath(Scenario, batch = "1"))
    check(Scenario, batch = "1")

    /** batch 2 - ok **/
    // update latest and insert in histo
    inputFile(Scenario, batch = "2", key = AKey, version = "2", beginDate = Date2, loadDate = LoadDate2, removeProduct0 = false, createInput = CreateInputFlag)
    runBatch(batch = "2", inputPath(Scenario, batch = "2"))
    check(Scenario, batch = "2")

    /** batch 3 - failure **/
    // update latest and update histo - but fails
    val beforeVersS1 = getVersion("FACT_SECONDARY_1_HISTO")
    // add constraints to all secondary tables - so no secondary tables is updated
    spark.sql("ALTER TABLE FACT_SECONDARY_1_HISTO ADD CONSTRAINT CHK_A CHECK (ENVELOP_NB != 3);")
    spark.sql("ALTER TABLE FACT_SECONDARY_2_HISTO ADD CONSTRAINT CHK_A CHECK (ENVELOP_NB != 3);")

    inputFile(Scenario, batch = "3", key = AKey, version = "3", beginDate = Date3, loadDate = LoadDate3, removeProduct0 = false, createInput = CreateInputFlag)
    intercept[StreamingQueryException](
      runBatch(batch = "3", inputPath(Scenario, batch = "3"))
    )
    val afterVersS1 = getVersion("FACT_SECONDARY_1_HISTO")
    assertEquality("FACT_SECONDARY_1_HISTO", beforeVersS1, afterVersS1)
    check(Scenario, batch = "3")

    /** batch 3 - retry ok **/
    val beforeVersM = getVersion("FACT_MASTER_HISTO")
    // remove all constraints
    spark.sql("ALTER TABLE FACT_SECONDARY_1_HISTO DROP CONSTRAINT CHK_A;")
    spark.sql("ALTER TABLE FACT_SECONDARY_2_HISTO DROP CONSTRAINT CHK_A;")

    runBatch(batch = "3", inputPath(Scenario, batch = "3"))
    val afterVersM = getVersion("FACT_MASTER_HISTO")

    assertEquality("FACT_MASTER_HISTO", beforeVersM, afterVersM)
    assertNoDupsLatest("FACT_MASTER_HISTO", "RESERVATION_ID")
    assertNoDupsLatest("FACT_SECONDARY_1_HISTO", "AIR_SEGMENT_ID")
    assertNoDupsLatest("FACT_SECONDARY_2_HISTO", "AIR_SEGMENT_PAX_ID")
    check(Scenario, batch = "3-rerun")
  }

  "Master-Secondary PIT - Scenario 2 default data - failure between secondary tables" should
    "be resilient to failure in Secondary Merge 1 for case 2 (latest, no histo)" taggedAs(SlowTestTag) in {
    // case 2 - known key             - Master Latest, Histo is empty
    val Scenario = "s14-merge_failure_rerun"

    /** batch 1 - ok **/
    inputFile(Scenario, batch = "1", key = AKey, version = "1", beginDate = Date1, loadDate = LoadDate1, removeProduct0 = false, createInput = CreateInputFlag)
    runBatch(batch = "1", inputPath(Scenario, batch = "1"))
    check(Scenario, batch = "1")

    /** batch 2 - failure **/
    val beforeVerS2 = getVersion("FACT_SECONDARY_2_HISTO")
    inputFile(Scenario, batch = "2", key = AKey, version = "2", beginDate = Date2, loadDate = LoadDate2, removeProduct0 = false, createInput = CreateInputFlag)
    // add constraints only to 2 secondary table - so the first secondary table is updated
    spark.sql("ALTER TABLE FACT_SECONDARY_2_HISTO ADD CONSTRAINT CHK_A CHECK (ENVELOP_NB != 2);")
    intercept[StreamingQueryException](
      runBatch(batch = "2", inputPath(Scenario, batch = "2"))
    )
    val afterVerS2 = getVersion("FACT_SECONDARY_2_HISTO")
    // check no update for secondary table --> because of failure
    assertEquality("FACT_SECONDARY_2_HISTO", beforeVerS2, afterVerS2)
    check(Scenario, batch = "2")

    /** batch 2 - retry ok **/
    val beforeVerM = getVersion("FACT_MASTER_HISTO")
    val beforeVerS1 = getVersion("FACT_SECONDARY_1_HISTO")
    spark.sql("ALTER TABLE FACT_SECONDARY_2_HISTO DROP CONSTRAINT CHK_A;")
    runBatch(batch = "2", inputPath(Scenario, batch = "2"))
    val afterVerM = getVersion("FACT_MASTER_HISTO")
    val afterVerS1 = getVersion("FACT_SECONDARY_1_HISTO")

    // verify idempotency
    assertEquality("FACT_MASTER_HISTO", beforeVerM, afterVerM)
    assertEquality("FACT_SECONDARY_1_HISTO", beforeVerS1, afterVerS1)
    // verify no dups
    assertNoDupsLatest("FACT_MASTER_HISTO", "RESERVATION_ID")
    assertNoDupsLatest("FACT_SECONDARY_1_HISTO", "AIR_SEGMENT_ID")
    assertNoDupsLatest("FACT_SECONDARY_2_HISTO", "AIR_SEGMENT_PAX_ID")

    check(Scenario, batch = "2-rerun")
  }

  "Master-Secondary PIT - Scenario 3 replay past data" should
    "compute PIT and be resilient to intermediate merge failures (on merge 2 master table) - case 2 (latest, no histo)" taggedAs(SlowTestTag) in {
    val Scenario = "s10-merge_failure"
    /** Steps:
     * - 1. Add constraint to master table (it won't be respected by old data in master table in batch 2)
     * - 2. Process Batch 1 (will work)
     * - 3. Process Batch 2 (will fail on merge 2 of master table, because of constraint violation)
     * - 4. Remove constraint
     * - 5. Process Batch 2 again (should work)
     * - 6. Perform the check
     */

    inputFile(Scenario, batch = "1", key = AKey, version = "2", beginDate = Date2, loadDate = LoadDate2, removeProduct0 = true, createInput = CreateInputFlag)
    inputFile(Scenario, batch = "2", key = AKey, version = "1", beginDate = Date1, loadDate = LoadDate1, removeProduct0 = false,createInput = CreateInputFlag) // misses
    inputFile(Scenario, batch = "2", key = AKey, version = "3", beginDate = Date3, loadDate = LoadDate2, removeProduct0 = false,createInput = CreateInputFlag)

    spark.sql("ALTER TABLE FACT_MASTER_HISTO ADD CONSTRAINT CHK_A CHECK (ENVELOP_NB != 1);")

    runBatch(batch = "1", inputPath(Scenario, batch = "1"))
    check(Scenario, batch = "1")

    intercept[StreamingQueryException](
      runBatch(batch = "2", inputPath(Scenario, batch = "2"))
    )

    // check that no tmp table is left even in case of failure
    assertNoTemporaryTable()

    spark.sql("ALTER TABLE FACT_MASTER_HISTO DROP CONSTRAINT CHK_A;")

    runBatch(batch = "2", inputPath(Scenario, batch = "2"))
    check(Scenario, batch = "2")
  }

  "Master-Secondary PIT - Scenario 4 replay past data" should
    "compute PIT and be resilient to intermediate merge failures (on merge 2 sec. table) - case 2 (latest, no histo)" taggedAs(SlowTestTag) in {
    // Note: this test case would fail if we do the "selective" merge in the merge 2 on the master table
    // (e.g. merging into the table only rows that are not already there)
    val Scenario = "s10-merge_failure"
    // merge 2 is to handle old versions
    /** Steps
     * - 1. Add constraint to secondary table (it won't be respected by old data in secondary table in batch 2)
     * - 2. Process Batch 1 (will work)
     * - 3. Process Batch 2 (will fail on merge 2 of secondary table, because of constraint violation)
     * - 4. Remove constraint
     * - 5. Process Batch 2 again (should work)
     * - 6. Perform the check
     */

    inputFile(Scenario, batch = "1", key = AKey, version = "2", beginDate = Date2, loadDate = LoadDate2, removeProduct0 = true, createInput = CreateInputFlag)
    inputFile(Scenario, batch = "2", key = AKey, version = "1", beginDate = Date1, loadDate = LoadDate1, removeProduct0 = false, createInput = CreateInputFlag) // misses
    inputFile(Scenario, batch = "2", key = AKey, version = "3", beginDate = Date3, loadDate = LoadDate2, removeProduct0 = false, createInput = CreateInputFlag)

    spark.sql("ALTER TABLE FACT_SECONDARY_1_HISTO ADD CONSTRAINT CHK_A CHECK (ENVELOP_NB != 1);")

    // resize config (batch1: nominal, batch2: pit histo, batch2-recovery: pit histo)
    val resizeCfg = ResizeConfigContext.create(spark)
    // RESIZE check - initial cluster size
    resizeCfg.dbxUtils.getRunningNumWorkers shouldBe resizeCfg.resizeParams.numWorkersDefault

    runBatch(batch = "1", inputPath(Scenario, batch = "1"), resizeCfg = Some(resizeCfg))
    check(Scenario, batch = "1")

    // RESIZE check - no change in cluster size
    resizeCfg.dbxUtils.getRunningNumWorkers shouldBe resizeCfg.resizeParams.numWorkersDefault

    intercept[StreamingQueryException](
      runBatch(batch = "2", inputPath(Scenario, batch = "2"), resizeCfg = Some(resizeCfg))
    )

    // RESIZE check - pit histo
    resizeCfg.dbxUtils.getRunningNumWorkers shouldBe resizeCfg.resizeParams.numWorkersPitHisto

    // check that no tmp table is left even in case of failure
    assertNoTemporaryTable()

    spark.sql("ALTER TABLE FACT_SECONDARY_1_HISTO DROP CONSTRAINT CHK_A;")

    runBatch(batch = "2", inputPath(Scenario, batch = "2"), resizeCfg = Some(resizeCfg))
    check(Scenario, batch = "2")

    // RESIZE check - pit histo
    resizeCfg.dbxUtils.getRunningNumWorkers shouldBe resizeCfg.resizeParams.numWorkersPitHisto
  }

  //@formatter:on

  private def assertNoTemporaryTable(): Unit = {
    val temporaryTables = spark
      .sql(s"show tables in ${TemporaryTable.TmpTablesDB} like '${TemporaryTable.TmpTablePrefix}*'")
      .select("tableName")
      .collect()
      .map(_.getAs[String]("tableName"))
    temporaryTables shouldBe Array.empty
  }

  private def inputFile(
                         scenario: String,
                         batch: String,
                         version: String, // 1
                         beginDate: String, // 2022-08-31T08:35:00Z
                         removeProduct0: Boolean = false,
                         loadDate: String = "01-01-2018",
                         key: String = "OP27AX-2022-05-22",
                         createInput: Boolean
                       ): Unit = {
    if (createInput) {
      val jsonTemplate = "src/test/resources/datasets/revised_pit/template.json"

      def customize(d: DocumentContext): JsonAsString = {
        d
          .set(".mainResource.current.image.version", version)
          .set(".mainResource.current.image.lastModification.dateTime", beginDate)
        if (removeProduct0) {
          d.delete(".mainResource.current.image.products[0]")
        }
        // replace the ID with the key used for the functional scenario
        val testKey = scenario.split("-").head
        d.jsonString().replaceAll("OP27AX-2022-05-22", s"${testKey}-${key}")
      }

      newJsonFile(
        jsonTemplate = jsonTemplate,
        targetDir = "src/test/resources/" + inputPath(scenario, batch),
        description = s"${key}-v${version}-${beginDate.replace(":", "-")}-${removeProduct0}",
        jsonCustomization = customize _,
        loadDate = loadDate
      )
    }
  }

  private def runBatch(
                        batch: String,
                        inputPath: String,
                        resizeCfg: Option[ResizeConfigContext] = None
                      ): Unit = {
    logger.info(s"Run batch: $batch")
    val baseRootConfig = rootConfig(inputPath, inputDatabase, displayMainEventsParam = DisplayMainEventsParam, isLight = isLight)
    val rc = resizeCfg.map(_.rootConfigWithResize(baseRootConfig)).getOrElse(baseRootConfig)
    val dbxUtils = resizeCfg.map(_.dbxUtils).getOrElse(DefaultDatabricksUtils)
    val checkpointLocation = rc.etl.stream.sink.checkpointLocation
    val directory = new Directory(new File(checkpointLocation))
    directory.deleteRecursively()
    val tablesConf = readMappingConfig(mappingFile)

    Json2StarApp.run(spark, rc, tablesConf, dbx = dbxUtils)
    // Note: for simple feed, we are simulating streaming running batches on different folders
    // so the checkpoint location MUST BE cleaned at each batch
    directory.deleteRecursively()
  }
}
