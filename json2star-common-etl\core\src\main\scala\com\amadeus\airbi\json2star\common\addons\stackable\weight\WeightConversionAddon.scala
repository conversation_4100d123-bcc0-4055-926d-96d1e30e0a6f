package com.amadeus.airbi.json2star.common.addons.stackable.weight

import com.amadeus.airbi.json2star.common.addons.base.Addon
import com.amadeus.airbi.json2star.common.addons.base.mapping.Mapping
import com.amadeus.airbi.json2star.common.addons.stackable._
import com.amadeus.airbi.json2star.common.extdata.{ExtData, ExtDataType}
import com.amadeus.airbi.json2star.common.{ColumnDef, Origin, Schema}
import com.amadeus.airbi.rawvault.common.RootConfig
import com.amadeus.airbi.rawvault.common.application.config.{MappingConfig, TableConfig, TablesConfig}
import com.amadeus.airbi.rawvault.common.config.ColumnMetadataRule.Replace
import com.amadeus.airbi.rawvault.common.config.{ColumnMetadata, ColumnMetadataValue}
import com.amadeus.airbi.json2star.common.addons.base.mapping.Table.KeyValueRow
import com.jayway.jsonpath.DocumentContext
import com.typesafe.scalalogging.Logger
import org.slf4j.LoggerFactory

import scala.util.Try

object WeightConversionAddon extends StackableAddon[WeightConversion] {

  lazy val logger: Logger = Logger(LoggerFactory.getLogger(getClass.getName))

  val Kilograms = "KILOGRAMS"
  val Pounds = "POUNDS"

  val ConversionTableKgLbs: Map[String, Map[String, Double]] = Map(
    Kilograms -> Map(Pounds -> 2.20462262185, Kilograms -> 1),
    Pounds -> Map(Kilograms -> 0.45359237, Pounds -> 1)
  )

  override def getCompatibleBaseAddons: List[Addon[_]] = List(Mapping)

  override def getRequiredExtData: List[ExtDataType] = List()

  /** Replace any existing description with a generated one.
    * Copy the rest of the metadata from src columns.
    * Leave the table description unchanged.
    */
  override def enrichSchemaMetadata(
    c: TablesConfig,
    t: TableConfig,
    addonConfig: StackableAddonConfig,
    schema: Schema
  ): EnrichedSchemaMetadata = {
    def m(col: Option[ColumnDef], d: String, o: Option[Seq[Origin]]): EnrichedColumnMetadata = {
      val descValue = Some(ColumnMetadataValue(d, Replace))
      val enrichedMeta = col
        .flatMap(_.meta)
        .map(m =>
          m.copy(
            description = descValue
          )
        )
        .orElse(Some(ColumnMetadata(description = descValue)))
      EnrichedColumnMetadata(meta = enrichedMeta, origins = o)
    }
    val config = getConfig(addonConfig)
    EnrichedSchemaMetadata(
      columnsMetadata = config.conversions.flatMap { cc =>
        val src = schema.columns.find(c => c.name == cc.srcCol)
        val srcUnit = schema.columns.find(c => c.name == cc.srcUnitCol)
        Seq(
          cc.dstCol -> m(
            src,
            s"Conversion of ${cc.srcCol} from the unit defined in ${cc.srcUnitCol} to the one defined in ${cc.dstUnitCol}",
            src.map(_.origins)
          ),
          cc.dstUnitCol -> m(srcUnit, s"Weight unit of ${cc.dstCol}", srcUnit.map(_.origins))
        )
      }.toMap
    )
  }

  private def convertColumn(
    r: List[KeyValueRow],
    c: WeightConversionColumnConfig,
    dstUnit: String
  ): List[KeyValueRow] = {
    r.map(row =>
      row
        .get(c.srcCol)
        .map(_ =>
          row + (c.dstCol -> convert(row.get(c.srcCol), row.get(c.srcUnitCol), dstUnit), c.dstUnitCol -> dstUnit)
        )
        .getOrElse(row)
    )
  }

  private def convert(srcValueOpt: Option[String], srcUnitOpt: Option[String], dstUnit: String): String = {
    val converted = for {
      srcValue <- srcValueOpt
      srcUnit <- srcUnitOpt
      src <- Try(srcValue.toDouble).toOption
      srcConversionMap <- ConversionTableKgLbs.get(srcUnit)
      rate <- srcConversionMap.get(dstUnit)
    } yield BigDecimal(src * rate).setScale(2, BigDecimal.RoundingMode.HALF_UP)
    val convertedStr = converted.map(_.toString)
    convertedStr match {
      case Some(str) => str
      case None =>
        logger.info(s"Weight conversion not performed ($srcValueOpt, $srcUnitOpt, $dstUnit)")
        null // scalastyle:ignore null
    }
  }

  private def valid(unit: String) = Set(Pounds, Kilograms).contains(unit)

  override def enrichTableRows(
    rows: List[KeyValueRow],
    addonConfig: StackableAddonConfig,
    jsonRoot: DocumentContext,
    rootConfig: RootConfig,
    extData: ExtData
  ): List[KeyValueRow] = {
    val config = getConfig(addonConfig)
    val homeWeightUnit = rootConfig.etl.common.homeWeightUnit
    homeWeightUnit match {
      case Some(dstUnit) if valid(dstUnit) => config.conversions.foldLeft(rows)((r, c) => convertColumn(r, c, dstUnit))
      case Some(dstUnit) if !valid(dstUnit) =>
        exception(s"Not supported home-weight-unit=$dstUnit in app configuration"); rows
      case None => exception("Missing home-weight-unit in app configuration"); rows
    }
  }

  override def validate(c: TablesConfig, t: TableConfig, addonConfig: StackableAddonConfig): Unit = {
    // base addon check
    t.mapping.getOrElse(exception(s"Base addon is not Mapping"))

    // column conversions check
    val config = getConfig(addonConfig)
    for {
      mapping <- t.mapping
    } yield checkColumns(config, mapping)
  }

  private def exception(msg: String): Unit = {
    throw new StackableAddonValidationException(s"[WeightConversionAddon] - $msg")
  }

  private def checkColumns(c: WeightConversion, mapping: MappingConfig): Unit = {
    // column presence
    c.conversions.foreach { cc =>
      val cols = mapping.columns.map(_.name).toSet
      val notPresent = Seq(cc.srcCol, cc.srcUnitCol, cc.dstCol, cc.dstUnitCol).filter(!cols.contains(_))
      if (notPresent.nonEmpty) {
        exception(
          s"Columns ${notPresent.mkString(", ")} are configured for weight conversion but not present in the mapping config"
        )
      }
    }
  }

}
