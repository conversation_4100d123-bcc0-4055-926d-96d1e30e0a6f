package com.amadeus.airbi.json2star.common.eventgrid

import com.amadeus.airbi.rawvault.common.RootConfig

import java.sql.Timestamp
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter

object JDFEventData {
  private val dtmFormatter = DateTimeFormatter.ISO_ZONED_DATE_TIME
  def timestampStr(t: Timestamp): String = {
    dtmFormatter.format(t.toLocalDateTime.atOffset(ZoneOffset.UTC))
  }

  def apply(
    conf: RootConfig,
    jobId: String,
    jobRunId: String,
    startTimestamp: Timestamp,
    endTimestamp: Timestamp
  ): JDFEventData = {
    JDFEventData(
      container = conf.etl.common.outputContainerName.get,
      version = conf.etl.common.domainVersion,
      schemaName = conf.etl.common.outputDatabase,
      domain = conf.etl.common.domain,
      jobId = jobId,
      jobRunId = jobRunId,
      startTimestamp = timestampStr(startTimestamp),
      endTimestamp = timestampStr(endTimestamp)
    )
  }

}

case class JDFEventData(
  container: String,
  version: String,
  schemaName: String,
  domain: String,
  jobId: String,
  jobRunId: String,
  startTimestamp: String,
  endTimestamp: String
)
