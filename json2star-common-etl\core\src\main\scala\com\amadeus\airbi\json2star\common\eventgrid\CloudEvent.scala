package com.amadeus.airbi.json2star.common.eventgrid

import java.time.format.DateTimeFormatter
import java.time.{Instant, ZoneOffset}

/** CloudEvents Schema
  * Refer to
  * https://learn.microsoft.com/en-us/azure/event-grid/cloud-event-schema
  * https://github.com/cloudevents/spec/blob/v1.0/json-format.md
  */
case class CloudEvent(
  specversion: String,
  `type`: String,
  source: String,
  id: String,
  datacontenttype: String,
  time: String,
  subject: String,
  // custom payload for the JDF event
  data: JDFEventData
)

object CloudEvent {
  val specversion = "1.0"
  val jdfEventType = "Amadeus.JDF.UpdateNotification"
  val datacontenttype = "application/json"

  def apply(subject: String, source: String, data: JDFEventData): CloudEvent = {
    val UUID = java.util.UUID.randomUUID().toString
    val timeFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSSSSSXXX")
    val formattedTime: String = Instant.now().atOffset(ZoneOffset.UTC).format(timeFormatter)

    CloudEvent(
      specversion = specversion,
      `type` = jdfEventType,
      source = source,
      id = UUID,
      datacontenttype = datacontenttype,
      time = formattedTime,
      subject = subject,
      data = data
    )
  }

}
