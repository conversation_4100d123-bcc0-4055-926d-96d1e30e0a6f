package com.amadeus.airbi.json2star.common.addons.stackable

import com.amadeus.airbi.json2star.common.Schema
import com.amadeus.airbi.json2star.common.addons.base.Addon
import com.amadeus.airbi.json2star.common.addons.stackable.EnrichedSchemaMetadata.NoEnrichment
import com.amadeus.airbi.json2star.common.extdata.{ExtData, ExtDataType}
import com.amadeus.airbi.rawvault.common.RootConfig
import com.amadeus.airbi.rawvault.common.application.config.{Merge, TableConfig, TablesConfig}
import com.amadeus.airbi.json2star.common.addons.base.mapping.Table.KeyValueRow
import com.jayway.jsonpath.DocumentContext
import org.apache.spark.sql.DataFrame

import scala.reflect.ClassTag

/** Stackable Addon
  *
  * A stackable addon is a feature that enriches an existing table (e.g., converting a currency).
  *
  * A stackable addon does not create a new table, but is plugged on top of an existing table
  * created by a base addon (cf. com.amadeus.airbi.json2star.common.addons.base.Addon).
  * A stackable addon has to define the base addons it can work with.
  *
  * A stackable addon does not modify the table schema.
  * All the columns needed by the addon are expected to be present (even if empty).
  * The stackable addon simply fills them.
  *
  * A stackable addon can enrich the schema metadata.
  *
  * A stackable addon can enrich the table in two places, defined by the methods:
  * - enrichTableRows()
  * - enrichBatch()
  *
  * Examples of stackable addons are:
  * - currency conversion
  * - weight conversion
  * - proration
  *
  * @tparam T the type of the configuration used to apply the stackable addon
  */
trait StackableAddon[T <: StackableAddonConfig] {

  /** Given a StackableAddonConfig, return its config with the right subtype T
    * If the passed config has the wrong runtime type, an exception is thrown
    *
    * @param addonConfig the StackableAddonConfig
    * @param tag the implicit ClassTag, needed to avoid type erasure of T in the pattern matching
    */
  def getConfig(addonConfig: StackableAddonConfig)(implicit tag: ClassTag[T]): T = {
    addonConfig match {
      case t: T => t
      case c => throw new IllegalStateException(s"Wrong config type passed to stackable addon: ${c} $tag")
    }
  }

  /** Get the list of base addons this stackable addon can work with
    *
    * WARNING: for the moment the stackable addons are only plugged into Mapping base addon.
    *
    * E.g. List(Mapping, Correlation)
    */
  def getCompatibleBaseAddons: List[Addon[_]]

  /** Get the list of external data types required for this stackable addon
    *
    * If no external data is needed, set an empty list
    * otherwise set the list of required data
    *
    * E.g. List(PorsType)
    *
    * Note:
    * The external data is loaded only once per each type in ExtData.load
    * Then it will be available in the enrichTableRows method (scala closure enrichment)
    * If no value is specified and then it is accessed in the enrichTableRows method, an exception will be raised
    */
  def getRequiredExtData: List[ExtDataType]

  def isCompatibleWith(addon: Addon[_]): Boolean = getCompatibleBaseAddons.contains(addon)

  /** Enriches the schema metadata with the modifications expected by this stackable addon.
    * The addon cannot change the columns names/order/types but only the table and column metadata.
    *
    * Note: if a stackable addon defines something (e.g. a column description) in the enriched metadata, it will
    * replace the same thing in the enriched Schema, so any concatenation with previous value have to be handled by the
    * addon itself if needed.
    *
    * @param c  the general raw configuration
    * @param t  the table raw configuration
    * @param addonConfig  the stackable addon configuration
    * @param schema the schema to enrich
    * @return the enriched table metadata (only the enriched should be present, not the whole schema)
    */
  def enrichSchemaMetadata(
    c: TablesConfig,
    t: TableConfig,
    addonConfig: StackableAddonConfig,
    schema: Schema
  ): EnrichedSchemaMetadata = NoEnrichment

  /** Enrichment done for mapping table after having extracted the table rows from the json document (scala closure)
    *
    * Only stackable addons working with Mapping base addon may redefine this.
    *
    * @param rows rows extracted from a json document for a given mapping table
    * @param addonConfig stackable addon configuration for the table
    * @param jsonRoot    json document
    * @param rootConfig root configuration for the app
    * @param extData     external data to the json document
    */
  def enrichTableRows(
    rows: List[KeyValueRow],
    addonConfig: StackableAddonConfig,
    jsonRoot: DocumentContext,
    rootConfig: RootConfig,
    extData: ExtData
  ): List[KeyValueRow] = rows

  /** Enrichment done before the merge
    *
    * @param b dataframe generated by a base addons and possibly enriched by previous stackable addons
    * @param addonConfig stackable addon configuration for the table
    * @param rootConfig  root Configuration
    * @return the enriched dataframe ready to be merged
    */
  def enrichBatch(b: DataFrame, addonConfig: StackableAddonConfig, rootConfig: RootConfig): DataFrame = b

  /** Validates the settings provided in this table for this stackable addon
    *
    * TODO: may evolve, for now issues are meant to be raised via exceptions, aggregated per table by StackableAddons
    *
    * @param c the general raw configuration
    * @param t the table raw configuration
    * @param addonConfig the stackable addon configuration
    */
  def validate(c: TablesConfig, t: TableConfig, addonConfig: StackableAddonConfig): Unit
}
