{
  hashMIdCheckEndNotNull: "if(element_at(split({0},'-'),array_size(split({0},'-'))) == '_', NULL,hashM({0}) )"
  "defaultComment": "A light mapping file for test",
  "ruleset": {
    "data-dictionary": {
      "json-to-yaml-paths": {
        # this rule is used to convert any path that starts with $.mainResource.current.image to DcsPassengerPush.processedDcsPassenger
        "$.mainResource.current.image": "DcsPassengerPush.processedDcsPassenger",
        # this rule is more specific - mismatch between DIH and JSON for the field mySecondSpecialSeat
        "$.mainResource.current.image.mySecondSpecialSeat": "DcsPassengerPush.processedDcsPassenger.differentSpecialSeat"
      }
    }
  },
  "tables": [
    {
      "name": "FACT_PASSENGER_HISTO_LIGHT",
      "mapping": {
        "merge": {
          "key-columns": ["PASSENGER_ID"],
          "if-dupe-take-higher": ["not used"]
        },
        "root-sources": [
          {
            "name": "pax", "rs": {"blocks": [{"base": "$.mainResource.current.image"}]}
          },
          {
            "name": "segDel", "rs": {"blocks": [{"base": "$.mainResource.current.image.segDel"}]}
          }
        ],
        "columns": [
          {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {
            "name": "COLUMN_A", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.specialSeat"}]},
            "meta": {
              "description": {"value": "CUSTOM DESCRIPTION FOR A", "rule": "replace"},
              "example": {"value": "CUSTOM EXAMPLE FOR A", "rule": "concat"},
              "pii-type": {"value": "CUSTOM PII TYPE FOR A", "rule": "concat"},
              "gdpr-zone": "red"
            }
          },
          {
            "name": "COLUMN_B", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.specialSeat"}]},
            "meta": {
              "description": {"value": "CUSTOM DESCRIPTION FOR B", "rule": "concat"},
              "example": {"value": "CUSTOM EXAMPLE FOR B", "rule": "replace"},
              "pii-type": {"value": "CUSTOM PII TYPE FOR B", "rule": "concat"},
              "gdpr-zone": "red"
            }
          },
          {
            "name": "COLUMN_C", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.specialSeat"}]},
            "meta": {
              "description": {"value": "CUSTOM DESCRIPTION FOR C", "rule": "concat"},
              "example": {"value": "CUSTOM EXAMPLE FOR C", "rule": "concat"},
              "pii-type": {"value": "CUSTOM PII TYPE FOR C", "rule": "replace"},
              "gdpr-zone": "red"
            }
          },
          {
            "name": "COLUMN_D", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.specialSeat[*].seatId"}]},
            "meta": {
              "example": {"value": "CUSTOM EXAMPLE FOR D", "rule": "concat"},
              "gdpr-zone": "orange"
            }
          },
          {
            "name": "COLUMN_E", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.specialSeat"}]}, "expr": ${hashMIdCheckEndNotNull},
            "meta": {
              "gdpr-zone": "green"
            }
          },
          {
            # This column is used to test a JSON path different from the dih path
            # mySpecialSeat is a JSON path that is not in the DIH yaml
            "name": "COLUMN_F", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.mySpecialSeat"}]},
            "meta": {
              "gdpr-zone": "green"
            }
          },
          {
            # This column is used to test a JSON path different from the dih path
            # mySpecialSeat2 is a JSON path that is not in the DIH yaml - BUT present in the rules conversion section
            "name": "COLUMN_G", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.mySecondSpecialSeat"}]},
            "meta": {
              "gdpr-zone": "green"
            }
          },
          {
            "name": "RELATES_TO", "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {"rs-name": "pax", "literal": "PASSENGER"},
                {"rs-name": "segDel", "literal": "SEGMENT_DELIVERY"}
              ]
            }
          }
        ],
        "pit": {
          "type": "master-pit-table",
          "master": {
            "pit-key": "not used",
            "pit-version": "not used",
            "valid-from": "not used",
            "valid-to": "not used",
            "is-last": "not used"
          }
        }
      }
    },
    {
      "name": "DIM_PASSENGER",
      "mapping": {
        "description": {"description": "A DIM table", "granularity": "1 test type"},
        "merge": {
          "key-columns": ["PASSENGER_TYPE_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [{"blocks": [{"base": "$.mainResource.current.image.travelers[*]"}]}],
        "columns": [
          {"name": "PASSENGER_TYPE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base":"$.passengerTypeCode"}]}, "expr": "hashS({0})",
            "meta": {"description": {"value": "Hash of the functional key (code)", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "PASSENGER_TYPE_CODE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base":"$.passengerTypeCode"}]}},
          {"name": "PASSENGER_TYPE_LABEL", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base":"$.passengerTypeCode"}]}},
          {"name": "RECORD_SOURCE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"literal" : "FROM_FEED_DATA"}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_PASSENGER_WITH_PREFILLER",
      "mapping": {
        "description": {"description": "A DIM table ", "granularity": "1 test type"},
        "merge": {
          "key-columns": ["PASSENGER_TYPE_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [{"blocks": [{"base": "$.mainResource.current.image.travelers[*]"}]}],
        "columns": [
          {"name": "PASSENGER_TYPE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base":"$.passengerTypeCode"}]}, "expr": "hashS({0})",
            "meta": {"description": {"value": "Hash of the functional key (code)", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "PASSENGER_TYPE_CODE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base":"$.passengerTypeCode"}]}},
          {"name": "PASSENGER_TYPE_LABEL", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base":"$.passengerTypeCode"}]}},
          {"name": "RECORD_SOURCE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"literal" : "FROM_FEED_DATA"}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      },
      "prefiller": [ {
        "data-source-key": "FROM_REF_DATA",
        "column-filler" : [
          {"dim-col" : "PASSENGER_TYPE_ID", "src-col" : "CODE"},
          {"dim-col" :  "PASSENGER_TYPE_CODE", "src-col" : "CODE"},
          {"dim-col" :  "PASSENGER_TYPE_LABEL", "src-col" : "NAME"}
        ]
      }
      ]
    }
  ]
}