package com.amadeus.airbi.json2star.common.addons.base.mapping.input.checkpoints

import com.amadeus.airbi.json2star.common.resize.DatabricksUtils

import java.time.LocalDate
import scala.util.{Success, Try}

object FileSystemDiscovery {

  /** Discover the partitions in a given base path.
    * Important: The assumption is that partitions are integer.
    * @param path a base path, according to the configured regex
    * @return a list of integer representing the partitions
    */
  def discoverPartitions(path: String, dbutils: DatabricksUtils): Seq[Int] = {
    val partitions = dbutils.listNames(path).sorted.map(_.toInt)
    if (partitions.isEmpty) throw new IllegalStateException(s"There must be at least one partition but none found: $path")
    partitions
  }

  /** Discover the start date for the feed in a given base path.
    * @param path a base path, according to the configured regex
    *             The following part of the path is supposed to have the format: /part/yyyy/MM/dd
    * @return the feed start date, or fail if the date could not be discovered
    */
  def discoverStartDate(path: String, dbutils: DatabricksUtils): LocalDate = {
    val date = for {
      partition <- dbutils.listNames(path).sorted.headOption
      year <- dbutils.listNames(s"$path/$partition").sorted.headOption
      month <- dbutils.listNames(s"$path/$partition/$year").sorted.headOption
      day <- dbutils.listNames(s"$path/$partition/$year/$month").sorted.headOption
    } yield LocalDate.of(year.toInt, month.toInt, day.toInt)
    date.getOrElse(throw new IllegalStateException(s"Could not discover start date for path: $path"))
  }

}
