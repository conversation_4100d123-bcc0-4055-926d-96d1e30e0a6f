package com.amadeus.airbi.json2star.common.validation.executors

import com.amadeus.airbi.json2star.common.TablesDef
import com.amadeus.airbi.json2star.common.validation.generators.ValidationForeignKeyGenerator
import com.amadeus.airbi.rawvault.common.application.config.ModelConfigLoader
import com.amadeus.airbi.json2star.common.validation.config.{ValidationConf, ValidationConfig}
import com.typesafe.scalalogging.Logger
import org.apache.spark.SparkConf
import org.apache.spark.sql.SparkSession
import org.slf4j.LoggerFactory

import java.time.OffsetDateTime

object ValidationSchemaExecutor {

  val DefaultRootLocation = "/user/hive/warehouse/TRASH"

  @transient
  lazy val logger: Logger = Logger(LoggerFactory.getLogger(getClass.getName))

  def main(args: Array[String]): Unit = {

    val vConfig = ValidationConf.apply(args)

    implicit val spark: SparkSession = SparkSession
      .builder()
      .config(
        new SparkConf()
          .set("spark.databricks.delta.properties.defaults.autoOptimize.optimizeWrite", "true")
          .set("spark.databricks.delta.properties.defaults.autoOptimize.autoCompact", "true")
      )
      .getOrCreate()

    run(
      spark,
      vConfig
    )
  }

  def run(
    spark: SparkSession,vConfig: ValidationConf,
    restrictedTableSet: List[String] = List.empty[String] // needed to optimize test run
  ): Unit = {
    val tablesDef = getTableList(vConfig.appConfig.modelConfFile, vConfig.appConfig.tablesSelectors, vConfig.appConfig.disabledStackableAddons,restrictedTableSet)

    val validParams = ValidationConfig(
      validationDatabase = vConfig.validationDatabase,
      validationTablename = vConfig.validationTable+ "_foreignKeys",
      domain = vConfig.appConfig.common.domain,
      domainVersion = vConfig.appConfig.common.domainVersion,
      customer = vConfig.appConfig.common.shard,
      phase = vConfig.phase,
      currentTimestamp = OffsetDateTime.now().toString,
      daysBack = vConfig.validationDaysBack
    )

    //In case table does not exist, create it. Consider database already exists.
    val tableSchema =
      "(domain STRING,domain_version STRING,customer String,phase String, foreign_key STRING, nb_rows BIGINT, ratio_of_foreign_key FLOAT, quality_of_foreign_key FLOAT, test_datetime TIMESTAMP)"
    val tableName = validParams.validationTablename
    val createTableQuery = s"CREATE TABLE IF NOT EXISTS ${validParams.validationDatabase}.${tableName} ${tableSchema}"
    spark.sql(createTableQuery)

    val domain = vConfig.appConfig.common.domain
    val queries = ValidationForeignKeyGenerator.toCreateValidationRequest(tablesDef, vConfig.appConfig.common.outputDatabase,validParams)
    if (queries.isEmpty || queries.exists(_.isEmpty)) {
      logger.error(s"Wrong SQL generated from the file ${vConfig.appConfig.modelConfFile}")
      logger.error(s"Wrong SQL content is ${queries.mkString("\n")}")
      throw new Exception("ERROR VALIDATION SQL GENERATOR")
    }

    queries.foreach { validationQuery =>
      logger.info(s"${getClass.getName} - Run Validation Query: ${validationQuery}")
      spark.sql(validationQuery)
    }
  }

  def getTableList(
    mappingConfFile: String,
    selectors: Set[String],
    disabledStackableAddons: Set[String],
    restrictedTableSet: List[String]
  ): TablesDef = {
    val originalMappingConf = ModelConfigLoader.defaultLoad(mappingConfFile, selectors, disabledStackableAddons)
    val mappingConfToUse = if (restrictedTableSet.isEmpty) {
      originalMappingConf
    } else {
      originalMappingConf.copy(tables = originalMappingConf.tables.filter(t => restrictedTableSet.contains(t.name)))
    }
    TablesDef.consolidate(mappingConfToUse)
  }
}
