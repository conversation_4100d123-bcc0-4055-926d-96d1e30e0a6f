package com.amadeus.airbi.json2star.common.views.generators

import com.amadeus.airbi.rawvault.common.config.ColumnType

object ColumnTypeDatabricks {
  implicit class ColumnTypeToDatabricks(ct: ColumnType.Value) {
    def toDatabricks: String = {
      ct match {
        case ColumnType.intColumn => "INT"
        case ColumnType.strColumn => "STRING"
        case ColumnType.dateColumn => "DATE"
        case ColumnType.timestampColumn => "TIMESTAMP"
        case ColumnType.binaryColumn => "BINARY"
        case ColumnType.booleanColumn => "BOOLEAN"
        case ColumnType.longColumn => "LONG"
        case ColumnType.floatColumn => "FLOAT"
      }
    }
  }


}
