package com.amadeus.airbi.json2star.common.app

import org.apache.spark.sql.{DataFrame, SparkSession}

import java.util.UUID

object TemporaryTable {

  val TmpTablesDB: String = "default"
  val TmpTablePrefix: String = "TMP_TABLE_"

  def temporaryTableName(prefix: String): String = {
    val u = UUID.randomUUID().toString
    val s = u.replace("-", "")

    s"$TmpTablePrefix${prefix}_${s}"
  }

  def toDefaultDeltaTable(df: DataFrame, name: String)(implicit spark: SparkSession): DataFrame = {
    df.write.format("delta").mode("overwrite").saveAsTable(s"$TmpTablesDB.$name")
    spark.table(s"$TmpTablesDB.$name")
  }

  def dropDefaultDeltaTable(name: String)(implicit spark: SparkSession): DataFrame = {
    spark.sql(s"DROP TABLE IF EXISTS $TmpTablesDB.$name")
  }

}
