package com.amadeus.airbi.rawvault.common.application.config

import scala.collection.Seq

case class DimPrefiller(
  dataSourceKey: String,
  columnFiller: Seq[ColumnFiller]
) {
  val ColumnFillerMap: Map[String, String] = columnFiller.map(r => r.dimCol -> r.srcCol).toMap
  def getByDimColKey(key: String): String = ColumnFillerMap(key)
}

case class ColumnFiller(
  dimCol: String,
  srcCol: String
)
