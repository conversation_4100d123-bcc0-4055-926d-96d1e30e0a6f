model-conf-file = views/sample_11/mapping.conf
stream {
  sink {
    checkpoint-location = "/a/path"
    trigger = "once"
    delta-options = {}
  }
}
common {
  domain = "DOMAIN"
  output-database = "DB_CUSTOMER_PHASE_FEED_VERSION"
  domain-version = "VERSION"
  output-path = "/output/path"
  shard = "CUSTOMER"
}
tables-selectors = [ "latest_selector" ]

snowflake-params {
  stream {
    sink {
      checkpoint-location = "/a/path"
      trigger = "availablenow"
      delta-options = {}
    }
  }
  stream-options {
    "maxFilesPerTrigger" = "40000"
  }
  azure-conf {
    secret-scope = ds-keyvault-secrets
  }
  snowflake-conf {
    url = {type = clear, value = snowflakecomputing.com}
    user = {type = clear, value = ROBOTIC_DEV}
    pem_private_key = {type = clear, value = dummy-value}
    role = {type = clear, value = APP_OWNER}
    database = {type = clear, value = DATA_CUSTOMER_PHASE}
    schema = {type = clear, value = DWH_DOMAIN_VERSION}
    warehouse = {type = clear, value = WAREHOUSE_DIHDLK_XS}
  }
}

partial-reprocessing-params {
  previous-version = "PREVIOUS_VERSION"
  tables-to-reprocess = ["INTERNAL_MY_TABLE"]
}