package com.amadeus.airbi.rawvault.common.application.config

import com.amadeus.airbi.rawvault.common.application.config.TablesConfig.JsonPathExpr
import com.typesafe.scalalogging.Logger
import org.slf4j.LoggerFactory

/** Model definition
  * @param tables of the model
  * @param ruleset optional ruleset with common rules for all tables
  */
case class TablesConfig(
  tables: List[TableConfig],
  ruleset: Option[Ruleset] = None
) {
  def tableByName(n: String): TableConfig = tables.filter(_.name == n) match {
    case Nil => throw new IllegalArgumentException(s"Table not found, name: '${n}'")
    case head :: Nil => head
    case _ => throw new IllegalArgumentException(s"Multiple tables found with name: '${n}'")
  }
  def selected(tablesSelectors: Set[String]): TablesConfig = {
    import TablesConfig._
    val selectedTables = tables.filter(_.isSelected(tablesSelectors))
    logger.info(s"Tables selectors: ${tablesSelectors}")
    tables.foreach(t =>
      logger.info(s"Table ${t.name} (selectors: ${t.tableSelectors}) selected: ${selectedTables.contains(t)}")
    )
    TablesConfig(selectedTables, ruleset)
  }

  val jsonToYamlPaths: Map[String, String] =
    ruleset.flatMap(_.dataDictionary.map(_.jsonToYamlPaths)).getOrElse(Map.empty)

  val mappingRecordFilter: Option[JsonPathExpr] = ruleset.flatMap(_.mapping.flatMap(_.recordFilter))

}

object TablesConfig {
  lazy val logger: Logger = Logger(LoggerFactory.getLogger(getClass.getName))
  type JsonPathExpr = String
}

case class Ruleset(
  dataDictionary: Option[DataDictionaryRule] = None,
  mapping: Option[MappingRule] = None
)
case class DataDictionaryRule(
  jsonToYamlPaths: Map[String, String]
)

/** Mapping Rule Config
  * @param recordFilter json path expression used to filter records (if the jsonpath evaluation on a
  *                     json record returns at least one element, the json record is considered eligible)
  */
case class MappingRule(
  recordFilter: Option[JsonPathExpr] = None
)
