package com.amadeus.airbi.json2star.common.config

import com.amadeus.airbi.json2star.common.config.AppConfig._
import com.amadeus.airbi.json2star.common.purge.PurgeParams
import pureconfig.ConfigSource
import pureconfig.generic.auto._

import java.net.URI

/** This class compiles the information needed to build views for this instance of the application
  *
  * It corresponds to the file application.conf that can be used by ETL, generators, etc.
  */
case class AppConfig(
  common: Common,
  modelConfFile: String, // global
  dihYamlFile: Option[String] = None, // global
  stream: Stream, // processing
  inputDatabases: Option[Map[String, String]] = None, // global
  processingParams: Option[ProcessingParams], // processing
  tablesSelectors: Set[String] = Set.empty[String], // global
  disabledStackableAddons: Set[String] = Set.empty, // global
  eventGridParams: Option[EventGridParams] = None,
  snowflakeParams: Option[SnowflakeParams] = None,
  workspaceSecrets: Option[Map[String, Secret]] = None,
  dimPrefillerParams: Option[DimPrefillerParams] = None,
  partialReprocessingParams: Option[PartialReprocessingParams] = None,
  purgeParams: Option[PurgeParams] = None
) {
  def validYamlFile: Option[String] = dihYamlFile.filter(f => f.contains(".yaml"))

  /** Get snowflake parameters
    *
    * @param jobId optional Databricks jobId to add in the query tag
    * @param runId optional Databricks runId to add in the query tag
    * @return SnowflakeParams
    */
  def assumeSnowflakeParams(jobId: Option[String], runId: Option[String]): SnowflakeParams = snowflakeParams match {
    case None => throw new IllegalArgumentException(s"The appConfigFile has missing parameters for the snowflake")
    case Some(s) =>
      (jobId, runId) match {
        // Add Databricks job_id and run_id to the Snowflake Query Tag
        case (Some(j), Some(r)) =>
          val enrichedQueryTag = s.queryTag + ("job_id" -> j, "run_id" -> r)
          val sfParams = s.copy(
            queryTag = enrichedQueryTag
          )
          sfParams
        case _ => s
      }

  }

  /** Get partial reprocessing parameters
    *
    * @return PartialReprocessingParams
    */
  def assumePartialReprocessingParams(): PartialReprocessingParams = partialReprocessingParams match {
    case None =>
      throw new IllegalArgumentException(s"The appConfigFile has missing parameters for the partial reprocessing")
    case Some(s) => s
  }

  /** Get purge parameters
    *
    * @return PurgeParams
    */
  def assumePurgeParams(): PurgeParams = purgeParams match {
    case None => throw new IllegalArgumentException(s"The appConfigFile has missing parameters for the purge")
    case Some(s) => s
  }

  /** Get refData Prefiller parameters
    *
    * @param jobId optional Databricks jobId to add in the query tag
    * @param runId optional Databricks runId to add in the query tag
    * @return SnowflakeParams
    */
  def assumeRefDataPrefillerParams(jobId: Option[String], runId: Option[String]): DimPrefillerParams =
    dimPrefillerParams match {
      case None =>
        throw new IllegalArgumentException(s"The appConfigFile has missing parameters for the refData Prefiller")
      case Some(s) => /*
      (jobId, runId) match {
        // Add Databricks job_id and run_id to the Snowflake Query Tag
        case (Some(j), Some(r)) =>
          val enrichedQueryTag = s.queryTag + ("job_id" -> j, "run_id" -> r)
          val sfParams = s.copy(
            queryTag = enrichedQueryTag
          )
          sfParams
        case _ => s
      }*/ s

    }
}

object AppConfig {

  /** Container for settings that are of general interest for different views (ETL, doc, ...)
    */
  case class Common(
    domain: String,
    outputDatabase: String,
    domainVersion: String,
    outputPath: String,
    shard: String,
    homeWeightUnit: Option[String] = None,
    homeCurrencyParams: Option[HomeCurrencyParams] = None,
    fatalStackableAddonsErrors: Boolean = true,
    prorationEnableMetadataColumn: Boolean = false,
    refDataOptdLocation: Option[String] = None
  ) {

    /** Get the container name from a abfss path if any
      * @return a container name if any
      */
    def outputContainerName: Option[String] = {
      val containerName = new URI(outputPath).getUserInfo
      if (containerName == null || containerName.isEmpty) None else Some(containerName)
    }
  }

  case class HomeCurrencyParams(
    database: String,
    tablename: String,
    currencyCode: String,
    noAdditionalCostWhitelist: Seq[String]
  )

  case class Sink(
    checkpointLocation: String,
    trigger: String,
    deltaOptions: Map[String, String]
  )

  case class Stream(sink: Sink)

  case class Secret(
    scope: String,
    key: String
  )

  def apply(confFile: String): AppConfig = {
    val confFileSource = ConfigSource.default(ConfigSource.file(confFile)).load[AppConfig]
    val confResourceSource = ConfigSource.default(ConfigSource.resources(confFile)).load[AppConfig]
    (confFileSource, confResourceSource) match {
      case (Right(f), _) => f
      case (_, Right(r)) => r
      case (Left(f), Left(r)) =>
        throw new IllegalArgumentException(
          s"Failed to load config file/resource '$confFile': file ${f.prettyPrint()}, resource ${r.prettyPrint()}"
        )
    }
  }

}
