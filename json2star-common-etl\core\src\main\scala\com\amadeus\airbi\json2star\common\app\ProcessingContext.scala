package com.amadeus.airbi.json2star.common.app

import com.amadeus.airbi.json2star.common.config.ProcessingParams

/** Context used to compute the star schema
  *
  * Note: eventually this class should be replaced by ProcessingParams completely, as no added value
  *
  * @param processingParams parameters used for the etl
  */
case class ProcessingContext(processingParams: ProcessingParams)

object ProcessingContext {

  val DefaultParallelism: Int = 3

}
