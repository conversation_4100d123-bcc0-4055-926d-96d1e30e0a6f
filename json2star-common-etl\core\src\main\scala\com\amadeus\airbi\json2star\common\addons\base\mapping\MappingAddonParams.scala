package com.amadeus.airbi.json2star.common.addons.base.mapping

import com.amadeus.airbi.json2star.common.addons.base.mapping.MappingAddonParams._
import com.amadeus.airbi.json2star.common.addons.base.mapping.input.DeltaInput.DeltaInputFormatConfig
import com.amadeus.airbi.json2star.common.addons.base.mapping.input._
import com.amadeus.airbi.json2star.common.resize.DatabricksUtils
import org.apache.spark.sql.SparkSession

import java.time.LocalDate

case class MappingAddonParams(
  inputFormat: Option[String],
  inputPaths: Seq[String],
  deltaConf: Option[DeltaInputFormatConfig] = None,
  autoloaderConf: Option[AutoloaderConfig] = None,
  enableTableMetrics: Boolean = false,
  streamOptions: Map[String, String] = Map.empty[String, String],
  enableBroadcastDistinctPitKeys: Boolean = false,
  enablePreMergeDeltaPersist: Boolean = false,
  enableRawDeltaPersist: Boolean = false,
  rawInputRepartition: Option[Int] = None
) {
  def positiveRawInputRepartition: Option[Int] = rawInputRepartition.filter(_ > 0)
  def asInputFormat(dbx: DatabricksUtils, spark: SparkSession): InputFormat = {
    def err(s: String) = throw new IllegalArgumentException(s)
    inputFormat match {
      case Some(InputFormatTest) =>
        JsonFileInput
      case Some(InputFormatDelta) =>
        val c = deltaConf.getOrElse(err(s"delta-conf is required for input format '${InputFormatDelta}'"))
        DeltaInput(c)
      case Some(InputFormatAvroAutoloader) =>
        val c = autoloaderConf.getOrElse(
          err(s"autoloader-conf is required for input format '${InputFormatAvroAutoloader}'")
        )
        AutoLoaderAvroInput(spark, dbx, c)
      case Some(InputFormatMDMJsonAutoloader) =>
        val c = autoloaderConf.getOrElse(
          err(s"autoloader-conf is required for input format '${InputFormatAvroAutoloader}'")
        )
        AutoLoaderMDMJsonInput(spark, c)
      case _ =>
        err(s"Provided input format '${inputFormat}' is not supported")
    }
  }
}

object MappingAddonParams {
  val InputFormatTest = "test" // json input files
  val InputFormatDelta = "delta" // delta table with json payload
  val InputFormatAvroAutoloader = "avro_autoloader" // avro files discovered with Azure autoloader
  val InputFormatMDMJsonAutoloader = "mdm_json_autoloader" // MDM json files discovered with Azure autoloader

  case class AutoloaderConfig(
    useNotifications: Boolean = false,
    includeExistingFiles: Boolean = true,
    backfillInterval: Option[String] = None,
    maxFilesPerTrigger: Option[Int] = None,
    maxBytesPerTrigger: Option[String] = None,
    startConfig: StartConfig
  )

  /** Input configuration for the start mode. It can be either a static or autoloader start.
    * If D is the date when the start configuration is saved in the checkpoint table:
    * - in static start mode: the batching is done programmatically until D-1, then the rest is done with autoloader
    *    (note that no autoloader processing is done in case an end date is specified)
    * - in autoloader start mode: all the batching is done with autoloader
    */
  sealed trait StartConfig
  case class StaticStart(
    inputPathRegex: String,
    numberOfDaysPerBatch: Int,
    startDate: Option[LocalDate],
    endDate: Option[LocalDate]
  ) extends StartConfig
  case class AutoloaderStart() extends StartConfig

}
