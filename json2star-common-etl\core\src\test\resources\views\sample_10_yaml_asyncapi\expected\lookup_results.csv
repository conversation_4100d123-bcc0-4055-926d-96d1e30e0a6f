table^column^jsonPath^transformedJsonPath^yamlPath^description^descriptionResult^example^exampleResult^piiType^piiTypeResult
FACT_ORDER_HISTO^INTERNAL_ZORDER^$.data.currentImage.id^""^OrderOrderChangeEvent.currentImage.id^Unique id of an Order.^Success^""^Empty^""^Empty
FACT_ORDER_HISTO^ORDER_ID^$.data.currentImage.id^hashM($.data.currentImage.id)^OrderOrderChangeEvent.currentImage.id^Unique id of an Order.^Success^""^Empty^""^Empty
FACT_ORDER_HISTO^REFERENCE_KEY^$.data.currentImage.id^""^OrderOrderChangeEvent.currentImage.id^Unique id of an Order.^Success^""^Empty^""^Empty
FACT_ORDER_HISTO^CREATION_USER_ORGANIZATION_CODE^$.data.currentImage.lifecycle.creation.user.organizationCode^""^OrderOrderChangeEvent.currentImage.lifecycle.creation.user.organizationCode^Organization currently used^Success^AC^Success^""^Empty
FACT_ORDER_HISTO^CREATION_USER_IATA_NUMBER^$.data.currentImage.lifecycle.creation.user.iataNumber^""^OrderOrderChangeEvent.currentImage.lifecycle.creation.user.iataNumber^8-digit IATA number^Success^""^Empty^""^Empty
FACT_ORDER_HISTO^CREATION_USER_LOGIN^$.data.currentImage.lifecycle.creation.user.login^""^OrderOrderChangeEvent.currentImage.lifecycle.creation.user.login^""^Missing^""^Missing^""^Missing
FACT_ORDER_HISTO^CREATION_LOCATION_IATA_CODE^$.data.currentImage.lifecycle.creation.location.iataCode^""^OrderOrderChangeEvent.currentImage.lifecycle.creation.location.iataCode^IATA location code^Success^PAR^Success^""^Empty
FACT_ORDER_HISTO^CREATION_LOCATION_COUNTRY_ISO2_CODE^$.data.currentImage.lifecycle.creation.location.address.countryCode^""^OrderOrderChangeEvent.currentImage.lifecycle.creation.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
FACT_ORDER_HISTO^UPDATE_USER_ORGANIZATION_CODE^$.data.currentImage.lifecycle.lastUpdate.user.organizationCode^""^OrderOrderChangeEvent.currentImage.lifecycle.lastUpdate.user.organizationCode^Organization currently used^Success^AC^Success^""^Empty
FACT_ORDER_HISTO^UPDATE_USER_IATA_NUMBER^$.data.currentImage.lifecycle.lastUpdate.user.iataNumber^""^OrderOrderChangeEvent.currentImage.lifecycle.lastUpdate.user.iataNumber^8-digit IATA number^Success^""^Empty^""^Empty
FACT_ORDER_HISTO^UPDATE_USER_LOGIN^$.data.currentImage.lifecycle.lastUpdate.user.login^""^OrderOrderChangeEvent.currentImage.lifecycle.lastUpdate.user.login^""^Missing^""^Missing^""^Missing
FACT_ORDER_HISTO^UPDATE_LOCATION_IATA_CODE^$.data.currentImage.lifecycle.lastUpdate.location.iataCode^""^OrderOrderChangeEvent.currentImage.lifecycle.lastUpdate.location.iataCode^IATA location code^Success^PAR^Success^""^Empty
FACT_ORDER_HISTO^UPDATE_LOCATION_COUNTRY_ISO2_CODE^$.data.currentImage.lifecycle.lastUpdate.location.address.countryCode^""^OrderOrderChangeEvent.currentImage.lifecycle.lastUpdate.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
FACT_ORDER_HISTO^CREATION_DATETIME_ORDER^$.data.currentImage.lifecycle.creation.dateTime^""^OrderOrderChangeEvent.currentImage.lifecycle.creation.dateTime^Date and time of the change occurence based on ISO-8601.^Success^""^Empty^""^Empty
FACT_ORDER_HISTO^VERSION_ORDER^$.data.currentImage.lifecycle.dataVersion^""^OrderOrderChangeEvent.currentImage.lifecycle.dataVersion^Version of the element. It starts at 1, each update/change increments the version by 1. Dotted notation is to be used when there is a rewrite of history.^Success^""^Empty^""^Empty
FACT_ORDER_HISTO^VALID_FROM^$.data.currentImage.lifecycle.lastUpdate.dateTime^""^OrderOrderChangeEvent.currentImage.lifecycle.lastUpdate.dateTime^Date and time of the change occurence based on ISO-8601.^Success^""^Empty^""^Empty
FACT_ORDER_ITEM_HISTO^INTERNAL_ZORDER^$.data.currentImage.id^""^OrderOrderChangeEvent.currentImage.id^Unique id of an Order.^Success^""^Empty^""^Empty
FACT_ORDER_ITEM_HISTO^ORDER_ITEM_ID^$.data.currentImage.orderItems[*].id^hashM($.data.currentImage.orderItems[*].id)^OrderOrderChangeEvent.currentImage.orderItems.id^Unique id of the item within an Order.^Success^""^Empty^""^Empty
FACT_ORDER_ITEM_HISTO^REFERENCE_KEY^$.data.currentImage.orderItems[*].id^""^OrderOrderChangeEvent.currentImage.orderItems.id^Unique id of the item within an Order.^Success^""^Empty^""^Empty
FACT_ORDER_ITEM_HISTO^STATUS^$.data.currentImage.orderItems[*].status^""^OrderOrderChangeEvent.currentImage.orderItems.status^Order Item status^Success^CONFIRMED|CANCELLED^Success^""^Empty
FACT_ORDER_ITEM_HISTO^CREATION_AGREEMENT_ITEM_PTR^$.data.currentImage.orderItems[*].createdByAgreementItemPtr^""^OrderOrderChangeEvent.currentImage.orderItems.createdByAgreementItemPtr^Link to another part of the resource, expressed as a JSON pointer starting from resource root. See https://datatracker.ietf.org/doc/html/rfc6901#section-5^Success^""^Empty^""^Empty
FACT_ORDER_ITEM_HISTO^CANCELLATION_AGREEMENT_ITEM_PTR^$.data.currentImage.orderItems[*].cancelledByAgreementItemPtr^""^OrderOrderChangeEvent.currentImage.orderItems.cancelledByAgreementItemPtr^Link to another part of the resource, expressed as a JSON pointer starting from resource root. See https://datatracker.ietf.org/doc/html/rfc6901#section-5^Success^""^Empty^""^Empty
FACT_ORDER_ITEM_HISTO^ORDER_ID^$.data.currentImage.id^hashM($.data.currentImage.id)^OrderOrderChangeEvent.currentImage.id^Unique id of an Order.^Success^""^Empty^""^Empty
FACT_ORDER_ITEM_HISTO^CREATION_AGREEMENT_ITEM_ID^$.data.currentImage.orderItems[*].createdByAgreementItemPtr^hashM($.data.currentImage.orderItems[*].createdByAgreementItemPtr)^OrderOrderChangeEvent.currentImage.orderItems.createdByAgreementItemPtr^Link to another part of the resource, expressed as a JSON pointer starting from resource root. See https://datatracker.ietf.org/doc/html/rfc6901#section-5^Success^""^Empty^""^Empty
FACT_ORDER_ITEM_HISTO^CANCELLATION_AGREEMENT_ITEM_ID^$.data.currentImage.orderItems[*].cancelledByAgreementItemPtr^hashM($.data.currentImage.orderItems[*].cancelledByAgreementItemPtr)^OrderOrderChangeEvent.currentImage.orderItems.cancelledByAgreementItemPtr^Link to another part of the resource, expressed as a JSON pointer starting from resource root. See https://datatracker.ietf.org/doc/html/rfc6901#section-5^Success^""^Empty^""^Empty
FACT_ORDER_ITEM_HISTO^VERSION_ORDER^$.data.currentImage.lifecycle.dataVersion^""^OrderOrderChangeEvent.currentImage.lifecycle.dataVersion^Version of the element. It starts at 1, each update/change increments the version by 1. Dotted notation is to be used when there is a rewrite of history.^Success^""^Empty^""^Empty
FACT_ORDER_ITEM_HISTO^VALID_FROM^$.data.currentImage.lifecycle.lastUpdate.dateTime^""^OrderOrderChangeEvent.currentImage.lifecycle.lastUpdate.dateTime^Date and time of the change occurence based on ISO-8601.^Success^""^Empty^""^Empty
FACT_ORDER_ITEM_SERVICE_HISTO^INTERNAL_ZORDER^$.data.currentImage.id^""^OrderOrderChangeEvent.currentImage.id^Unique id of an Order.^Success^""^Empty^""^Empty
FACT_ORDER_ITEM_SERVICE_HISTO^ORDER_ITEM_SERVICE_ID^$.data.currentImage.orderItems[*].services[*].id^hashM($.data.currentImage.orderItems[*].services[*].id)^OrderOrderChangeEvent.currentImage.orderItems.services.id^Id of the Fulfillment.^Success^""^Empty^""^Empty
FACT_ORDER_ITEM_SERVICE_HISTO^REFERENCE_KEY^$.data.currentImage.orderItems[*].services[*].id^""^OrderOrderChangeEvent.currentImage.orderItems.services.id^Id of the Fulfillment.^Success^""^Empty^""^Empty
FACT_ORDER_ITEM_SERVICE_HISTO^OFFER_IDENTIFIER^$.data.currentImage.orderItems[*].services[*].context.offerServiceItemRef.offerRef.id^""^OrderOrderChangeEvent.currentImage.orderItems.services.context.offerServiceItemRef.offerRef.id^Id of the related resource.^Success^""^Empty^""^Empty
FACT_ORDER_ITEM_SERVICE_HISTO^OFFER_HREF^$.data.currentImage.orderItems[*].services[*].context.offerServiceItemRef.offerRef.href^""^OrderOrderChangeEvent.currentImage.orderItems.services.context.offerServiceItemRef.offerRef.href^URI reference to the related source.^Success^""^Empty^""^Empty
FACT_ORDER_ITEM_SERVICE_HISTO^OFFER_ITEM_PTR^$.data.currentImage.orderItems[*].services[*].context.offerServiceItemRef.offerServiceItemPtr^""^OrderOrderChangeEvent.currentImage.orderItems.services.context.offerServiceItemRef.offerServiceItemPtr^Link to another part of the resource, expressed as a JSON pointer starting from resource root. See https://datatracker.ietf.org/doc/html/rfc6901#section-5^Success^""^Empty^""^Empty
FACT_ORDER_ITEM_SERVICE_HISTO^OFFER_ITEM_IDENTIFIER^$.data.currentImage.orderItems[*].services[*].context.offerServiceItemRef.offerServiceItemPtr^element_at(split($.data.currentImage.orderItems[*].services[*].context.offerServiceItemRef.offerServiceItemPtr,'/'),array_size(split($.data.currentImage.orderItems[*].services[*].context.offerServiceItemRef.offerServiceItemPtr,'/')))^OrderOrderChangeEvent.currentImage.orderItems.services.context.offerServiceItemRef.offerServiceItemPtr^Link to another part of the resource, expressed as a JSON pointer starting from resource root. See https://datatracker.ietf.org/doc/html/rfc6901#section-5^Success^""^Empty^""^Empty
FACT_ORDER_ITEM_SERVICE_HISTO^SERVICE_TYPE^$.data.currentImage.orderItems[*].services[*].type^""^OrderOrderChangeEvent.currentImage.orderItems.services.type^Type for the Fulfillment (Service/Payment).^Success^""^Empty^""^Empty
FACT_ORDER_ITEM_SERVICE_HISTO^SOLD_PRODUCT_REFERENCE_IDENTIFIER^$.data.currentImage.orderItems[*].services[*].context.soldProduct.productRef.id^""^OrderOrderChangeEvent.currentImage.orderItems.services.context.soldProduct.productRef.id^Id of the related resource.^Success^""^Empty^""^Empty
FACT_ORDER_ITEM_SERVICE_HISTO^SOLD_PRODUCT_INSTANCE_IDENTIFIER^$.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance.id^""^OrderOrderChangeEvent.currentImage.orderItems.services.context.soldProduct.productInstance.id^identifier of the product^Success^""^Empty^""^Empty
FACT_ORDER_ITEM_SERVICE_HISTO^OPENTICKET_PRIMARY_DOCUMENT_AIRLINE_CODE^$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenTicket.v1')].document.structuredPrimaryDocumentNumber.numericalAirlineCode^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.document.structuredPrimaryDocumentNumber.numericalAirlineCode^""^Missing^""^Missing^""^Missing
FACT_ORDER_ITEM_SERVICE_HISTO^OPENTICKET_PRIMARY_DOCUMENT_NUMBER^$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenTicket.v1')].document.structuredPrimaryDocumentNumber.number^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.document.structuredPrimaryDocumentNumber.number^""^Missing^""^Missing^""^Missing
FACT_ORDER_ITEM_SERVICE_HISTO^OPENTICKET_PRIMARY_DOCUMENT_ISSUANCE_DATETIME_LOCAL^$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenTicket.v1')].document.issuanceLocalDateTime^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.document.issuanceLocalDateTime^""^Missing^""^Missing^""^Missing
FACT_ORDER_ITEM_SERVICE_HISTO^OPENTICKET_PRIMARY_DOCUMENT_VERSION^$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenTicket.v1')].document.version^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.document.version^""^Missing^""^Missing^""^Missing
FACT_ORDER_ITEM_SERVICE_HISTO^OPENTICKET_PRIMARY_DOCUMENT_TYPE^$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenTicket.v1')].document.documentType^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.document.documentType^""^Missing^""^Missing^""^Missing
FACT_ORDER_ITEM_SERVICE_HISTO^OPENSALE_PRIMARY_DOCUMENT_AIRLINE_CODE^$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenSale.v1')].document.structuredPrimaryDocumentNumber.numericalAirlineCode^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.document.structuredPrimaryDocumentNumber.numericalAirlineCode^""^Missing^""^Missing^""^Missing
FACT_ORDER_ITEM_SERVICE_HISTO^OPENSALE_PRIMARY_DOCUMENT_NUMBER^$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenSale.v1')].document.structuredPrimaryDocumentNumber.number^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.document.structuredPrimaryDocumentNumber.number^""^Missing^""^Missing^""^Missing
FACT_ORDER_ITEM_SERVICE_HISTO^OPENSALE_PRIMARY_DOCUMENT_CHECKDIGIT^$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenSale.v1')].document.structuredPrimaryDocumentNumber.checkDigit^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.document.structuredPrimaryDocumentNumber.checkDigit^""^Missing^""^Missing^""^Missing
FACT_ORDER_ITEM_SERVICE_HISTO^OPENSALE_PRIMARY_DOCUMENT_ISSUANCE_DATETIME_LOCAL^$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenSale.v1')].document.issuanceLocalDateTime^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.document.issuanceLocalDateTime^""^Missing^""^Missing^""^Missing
FACT_ORDER_ITEM_SERVICE_HISTO^PNR_REFERENCE^$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_Pnr.v1')].pnr.reference^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.pnr.reference^""^Missing^""^Missing^""^Missing
FACT_ORDER_ITEM_SERVICE_HISTO^PNR_VERSION^$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_Pnr.v1')].pnr.version^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.pnr.version^""^Missing^""^Missing^""^Missing
FACT_ORDER_ITEM_SERVICE_HISTO^PNR_CREATION_DATETIME^$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_Pnr.v1')].pnr.creation.dateTime^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.pnr.creation.dateTime^""^Missing^""^Missing^""^Missing
FACT_ORDER_ITEM_SERVICE_HISTO^SOLD_PRODUCT_ID^$.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance.id^hashM($.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance.id)^OrderOrderChangeEvent.currentImage.orderItems.services.context.soldProduct.productInstance.id^identifier of the product^Success^""^Empty^""^Empty
FACT_ORDER_ITEM_SERVICE_HISTO^ORDER_ID^$.data.currentImage.id^hashM($.data.currentImage.id)^OrderOrderChangeEvent.currentImage.id^Unique id of an Order.^Success^""^Empty^""^Empty
FACT_ORDER_ITEM_SERVICE_HISTO^ORDER_ITEM_ID^$.data.currentImage.orderItems[*].id^hashM($.data.currentImage.orderItems[*].id)^OrderOrderChangeEvent.currentImage.orderItems.id^Unique id of the item within an Order.^Success^""^Empty^""^Empty
FACT_ORDER_ITEM_SERVICE_HISTO^OFFER_ID^$.data.currentImage.orderItems[*].services[*].context.offerServiceItemRef.offerRef.id^hashM($.data.currentImage.orderItems[*].services[*].context.offerServiceItemRef.offerRef.id)^OrderOrderChangeEvent.currentImage.orderItems.services.context.offerServiceItemRef.offerRef.id^Id of the related resource.^Success^""^Empty^""^Empty
FACT_ORDER_ITEM_SERVICE_HISTO^OFFER_ITEM_ID^$.data.currentImage.orderItems[*].services[*].context.offerServiceItemRef.offerServiceItemPtr^hashM($.data.currentImage.orderItems[*].services[*].context.offerServiceItemRef.offerServiceItemPtr)^OrderOrderChangeEvent.currentImage.orderItems.services.context.offerServiceItemRef.offerServiceItemPtr^Link to another part of the resource, expressed as a JSON pointer starting from resource root. See https://datatracker.ietf.org/doc/html/rfc6901#section-5^Success^""^Empty^""^Empty
FACT_ORDER_ITEM_SERVICE_HISTO^VERSION_ORDER^$.data.currentImage.lifecycle.dataVersion^""^OrderOrderChangeEvent.currentImage.lifecycle.dataVersion^Version of the element. It starts at 1, each update/change increments the version by 1. Dotted notation is to be used when there is a rewrite of history.^Success^""^Empty^""^Empty
FACT_ORDER_ITEM_SERVICE_HISTO^VALID_FROM^$.data.currentImage.lifecycle.lastUpdate.dateTime^""^OrderOrderChangeEvent.currentImage.lifecycle.lastUpdate.dateTime^Date and time of the change occurence based on ISO-8601.^Success^""^Empty^""^Empty
FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS_HISTO^INTERNAL_ZORDER^$.data.currentImage.id^""^OrderOrderChangeEvent.currentImage.id^Unique id of an Order.^Success^""^Empty^""^Empty
FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS_HISTO^ITEM_AIR_SEGMENT_BOOKING_ID^$.data.currentImage.orderItems[*].services[*].id^hashM($.data.currentImage.orderItems[*].services[*].id)^OrderOrderChangeEvent.currentImage.orderItems.services.id^Id of the Fulfillment.^Success^""^Empty^""^Empty
FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS_HISTO^REFERENCE_KEY^$.data.currentImage.orderItems[*].services[*].id^""^OrderOrderChangeEvent.currentImage.orderItems.services.id^Id of the Fulfillment.^Success^""^Empty^""^Empty
FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS_HISTO^STATUS^$.data.currentImage.orderItems[*].services[*].measure.status^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.status^"Fulfillment status for service item.
- NOT_STARTED : When Fulfillment is created from Order based flow  
- BOOK_SUBMITTED :  When booking request is sent to airline inventory system
- BOOK_ACCEPTED : When booking request has been accepted by airline inventory system
- BOOK_REJECTED : When booking request has been rejected by airline inventory system
- READY_TO_DELIVER : When the fulfillment is in a situation allowing the provider of this service liability to start the delivery
- DELIVERED : When the fulfillment has been delivered - Status received from airline delivery system
- CANCELLED : When the cancellation request has been accepted by the airline inventory system
- BOOK_WAITLISTED : When the booking is waitlisted
- CONTROLLED_BY_DELIVERY : When the fulfillment is being managed by the delivery system
- IN_PROGRESS : When the fulfillment is being delivered
- INFORMED : When the fulfillment is being managed by the service retailer"^Success^NOT_STARTED|BOOK_SUBMITTED|BOOK_ACCEPTED|BOOK_REJECTED|READY_TO_DELIVER|DELIVERED|CANCELLED|BOOK_WAITLISTED|CONTROLLED_BY_DELIVERY|IN_PROGRESS|INFORMED^Success^""^Empty
FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS_HISTO^SEGMENT_MKT_CARRIER^$.data.currentImage.orderItems[*].services[*].measure.product.productInstance[?(@.productType == 'airSegmentBookingClassProduct')].segment.marketing.flightDesignator.carrierCode^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.product.productInstance.segment.marketing.flightDesignator.carrierCode^Two letter IATA standard carrier code^Success^6X^Success^""^Empty
FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS_HISTO^SEGMENT_MKT_FLIGHT_NUMBER^$.data.currentImage.orderItems[*].services[*].measure.product.productInstance[?(@.productType == 'airSegmentBookingClassProduct')].segment.marketing.flightDesignator.flightNumber^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.product.productInstance.segment.marketing.flightDesignator.flightNumber^1-4 digit number^Success^555^Success^""^Empty
FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS_HISTO^SEGMENT_MKT_OPERATIONAL_SUFFIX^$.data.currentImage.orderItems[*].services[*].measure.product.productInstance[?(@.productType == 'airSegmentBookingClassProduct')].segment.marketing.flightDesignator.operationalSuffix^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.product.productInstance.segment.marketing.flightDesignator.operationalSuffix^the operational suffix^Success^A^Success^""^Empty
FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS_HISTO^SEGMENT_OPE_CARRIER^$.data.currentImage.orderItems[*].services[*].measure.product.productInstance[?(@.productType == 'airSegmentBookingClassProduct')].segment.operating.flightDesignator.flightNumber - $.data.currentImage.orderItems[*].services[*].measure.product.productInstance[?(@.productType == 'airSegmentBookingClassProduct')].segment.marketing.flightDesignator.carrierCode^if(element_at(split($.data.currentImage.orderItems[*].services[*].measure.product.productInstance[?(@.productType == 'airSegmentBookingClassProduct')].segment.operating.flightDesignator.flightNumber - $.data.currentImage.orderItems[*].services[*].measure.product.productInstance[?(@.productType == 'airSegmentBookingClassProduct')].segment.marketing.flightDesignator.carrierCode,'-'),1) == '_', element_at(split($.data.currentImage.orderItems[*].services[*].measure.product.productInstance[?(@.productType == 'airSegmentBookingClassProduct')].segment.operating.flightDesignator.flightNumber - $.data.currentImage.orderItems[*].services[*].measure.product.productInstance[?(@.productType == 'airSegmentBookingClassProduct')].segment.marketing.flightDesignator.carrierCode,'-'),2),element_at(split($.data.currentImage.orderItems[*].services[*].measure.product.productInstance[?(@.productType == 'airSegmentBookingClassProduct')].segment.operating.flightDesignator.flightNumber - $.data.currentImage.orderItems[*].services[*].measure.product.productInstance[?(@.productType == 'airSegmentBookingClassProduct')].segment.marketing.flightDesignator.carrierCode,'-'),1) )^OrderOrderChangeEvent.currentImage.orderItems.services.measure.product.productInstance.segment.operating.flightDesignator.flightNumber^1-4 digit number^Success^555^Success^""^Empty
FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS_HISTO^SEGMENT_OPE_FLIGHT_NUMBER^$.data.currentImage.orderItems[*].services[*].measure.product.productInstance[?(@.productType == 'airSegmentBookingClassProduct')].segment.operating.flightDesignator.flightNumber - $.data.currentImage.orderItems[*].services[*].measure.product.productInstance[?(@.productType == 'airSegmentBookingClassProduct')].segment.marketing.flightDesignator.flightNumber^if(element_at(split($.data.currentImage.orderItems[*].services[*].measure.product.productInstance[?(@.productType == 'airSegmentBookingClassProduct')].segment.operating.flightDesignator.flightNumber - $.data.currentImage.orderItems[*].services[*].measure.product.productInstance[?(@.productType == 'airSegmentBookingClassProduct')].segment.marketing.flightDesignator.flightNumber,'-'),1) == '_', element_at(split($.data.currentImage.orderItems[*].services[*].measure.product.productInstance[?(@.productType == 'airSegmentBookingClassProduct')].segment.operating.flightDesignator.flightNumber - $.data.currentImage.orderItems[*].services[*].measure.product.productInstance[?(@.productType == 'airSegmentBookingClassProduct')].segment.marketing.flightDesignator.flightNumber,'-'),2),element_at(split($.data.currentImage.orderItems[*].services[*].measure.product.productInstance[?(@.productType == 'airSegmentBookingClassProduct')].segment.operating.flightDesignator.flightNumber - $.data.currentImage.orderItems[*].services[*].measure.product.productInstance[?(@.productType == 'airSegmentBookingClassProduct')].segment.marketing.flightDesignator.flightNumber,'-'),1) )^OrderOrderChangeEvent.currentImage.orderItems.services.measure.product.productInstance.segment.operating.flightDesignator.flightNumber^1-4 digit number^Success^555^Success^""^Empty
FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS_HISTO^SEGMENT_OPE_OPERATIONAL_SUFFIX^$.data.currentImage.orderItems[*].services[*].measure.product.productInstance[?(@.productType == 'airSegmentBookingClassProduct')].segment.operating.flightDesignator.flightNumber - $.data.currentImage.orderItems[*].services[*].measure.product.productInstance[?(@.productType == 'airSegmentBookingClassProduct')].segment.marketing.flightDesignator.operationalSuffix^if(element_at(split($.data.currentImage.orderItems[*].services[*].measure.product.productInstance[?(@.productType == 'airSegmentBookingClassProduct')].segment.operating.flightDesignator.flightNumber - $.data.currentImage.orderItems[*].services[*].measure.product.productInstance[?(@.productType == 'airSegmentBookingClassProduct')].segment.marketing.flightDesignator.operationalSuffix,'-'),1) == '_', element_at(split($.data.currentImage.orderItems[*].services[*].measure.product.productInstance[?(@.productType == 'airSegmentBookingClassProduct')].segment.operating.flightDesignator.flightNumber - $.data.currentImage.orderItems[*].services[*].measure.product.productInstance[?(@.productType == 'airSegmentBookingClassProduct')].segment.marketing.flightDesignator.operationalSuffix,'-'),2),element_at(split($.data.currentImage.orderItems[*].services[*].measure.product.productInstance[?(@.productType == 'airSegmentBookingClassProduct')].segment.operating.flightDesignator.flightNumber - $.data.currentImage.orderItems[*].services[*].measure.product.productInstance[?(@.productType == 'airSegmentBookingClassProduct')].segment.marketing.flightDesignator.operationalSuffix,'-'),1) )^OrderOrderChangeEvent.currentImage.orderItems.services.measure.product.productInstance.segment.operating.flightDesignator.flightNumber^1-4 digit number^Success^555^Success^""^Empty
FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS_HISTO^SEGMENT_DEPARTURE_AIRPORT^$.data.currentImage.orderItems[*].services[*].measure.product.productInstance[?(@.productType == 'airSegmentBookingClassProduct')].segment.departure.iataCode^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.product.productInstance.segment.departure.iataCode^IATA Airport code^Success^JFK^Success^""^Empty
FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS_HISTO^SEGMENT_DEPARTURE_DATETIME_LOCAL^$.data.currentImage.orderItems[*].services[*].measure.product.productInstance[?(@.productType == 'airSegmentBookingClassProduct')].segment.departure.localDateTime^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.product.productInstance.segment.departure.localDateTime^Local dateTime of the departure or arrival^Success^2020-01-13T17:09:00 at the &quot;IATA airport code&quot;^Success^""^Empty
FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS_HISTO^SEGMENT_ARRIVAL_AIRPORT^$.data.currentImage.orderItems[*].services[*].measure.product.productInstance[?(@.productType == 'airSegmentBookingClassProduct')].segment.arrival.iataCode^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.product.productInstance.segment.arrival.iataCode^IATA Airport code^Success^JFK^Success^""^Empty
FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS_HISTO^SEGMENT_ARRIVAL_DATETIME_LOCAL^$.data.currentImage.orderItems[*].services[*].measure.product.productInstance[?(@.productType == 'airSegmentBookingClassProduct')].segment.arrival.localDateTime^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.product.productInstance.segment.arrival.localDateTime^Local dateTime of the departure or arrival^Success^2020-01-13T17:09:00 at the &quot;IATA airport code&quot;^Success^""^Empty
FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS_HISTO^CABIN^$.data.currentImage.orderItems[*].services[*].measure.product.productInstance[?(@.productType == 'airSegmentBookingClassProduct')].cabin.code^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.product.productInstance.cabin.code^cabin code filed in inventory for the sold seat^Success^""^Empty^""^Empty
FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS_HISTO^BOOKING_CLASS^$.data.currentImage.orderItems[*].services[*].measure.product.productInstance[?(@.productType == 'airSegmentBookingClassProduct')].cabin.code^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.product.productInstance.cabin.code^cabin code filed in inventory for the sold seat^Success^""^Empty^""^Empty
FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS_HISTO^OPENTICKET_PRIMARY_DOCUMENT_AIRLINE_CODE^$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenTicket.v1')].document.structuredPrimaryDocumentNumber.numericalAirlineCode^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.document.structuredPrimaryDocumentNumber.numericalAirlineCode^""^Missing^""^Missing^""^Missing
FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS_HISTO^OPENTICKET_PRIMARY_DOCUMENT_NUMBER^$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenTicket.v1')].document.structuredPrimaryDocumentNumber.number^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.document.structuredPrimaryDocumentNumber.number^""^Missing^""^Missing^""^Missing
FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS_HISTO^OPENTICKET_PRIMARY_DOCUMENT_ISSUANCE_DATETIME_LOCAL^$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenTicket.v1')].document.issuanceLocalDateTime^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.document.issuanceLocalDateTime^""^Missing^""^Missing^""^Missing
FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS_HISTO^OPENTICKET_PRIMARY_DOCUMENT_VERSION^$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenTicket.v1')].document.version^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.document.version^""^Missing^""^Missing^""^Missing
FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS_HISTO^OPENTICKET_PRIMARY_DOCUMENT_TYPE^$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenTicket.v1')].document.documentType^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.document.documentType^""^Missing^""^Missing^""^Missing
FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS_HISTO^OPENSALE_PRIMARY_DOCUMENT_AIRLINE_CODE^$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenSale.v1')].document.structuredPrimaryDocumentNumber.numericalAirlineCode^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.document.structuredPrimaryDocumentNumber.numericalAirlineCode^""^Missing^""^Missing^""^Missing
FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS_HISTO^OPENSALE_PRIMARY_DOCUMENT_NUMBER^$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenSale.v1')].document.structuredPrimaryDocumentNumber.number^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.document.structuredPrimaryDocumentNumber.number^""^Missing^""^Missing^""^Missing
FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS_HISTO^OPENSALE_PRIMARY_DOCUMENT_CHECKDIGIT^$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenSale.v1')].document.structuredPrimaryDocumentNumber.checkDigit^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.document.structuredPrimaryDocumentNumber.checkDigit^""^Missing^""^Missing^""^Missing
FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS_HISTO^OPENSALE_PRIMARY_DOCUMENT_ISSUANCE_DATETIME_LOCAL^$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenSale.v1')].document.issuanceLocalDateTime^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.document.issuanceLocalDateTime^""^Missing^""^Missing^""^Missing
FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS_HISTO^PNR_REFERENCE^$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_Pnr.v1')].pnr.reference^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.pnr.reference^""^Missing^""^Missing^""^Missing
FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS_HISTO^PNR_VERSION^$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_Pnr.v1')].pnr.version^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.pnr.version^""^Missing^""^Missing^""^Missing
FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS_HISTO^PNR_CREATION_DATETIME^$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_Pnr.v1')].pnr.creation.dateTime^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.pnr.creation.dateTime^""^Missing^""^Missing^""^Missing
FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS_HISTO^OFFER_IDENTIFIER^$.data.currentImage.orderItems[*].services[*].context.offerServiceItemRef.offerRef.id^""^OrderOrderChangeEvent.currentImage.orderItems.services.context.offerServiceItemRef.offerRef.id^Id of the related resource.^Success^""^Empty^""^Empty
FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS_HISTO^OFFER_HREF^$.data.currentImage.orderItems[*].services[*].context.offerServiceItemRef.offerRef.href^""^OrderOrderChangeEvent.currentImage.orderItems.services.context.offerServiceItemRef.offerRef.href^URI reference to the related source.^Success^""^Empty^""^Empty
FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS_HISTO^OFFER_ITEM_PTR^$.data.currentImage.orderItems[*].services[*].context.offerServiceItemRef.offerServiceItemPtr^""^OrderOrderChangeEvent.currentImage.orderItems.services.context.offerServiceItemRef.offerServiceItemPtr^Link to another part of the resource, expressed as a JSON pointer starting from resource root. See https://datatracker.ietf.org/doc/html/rfc6901#section-5^Success^""^Empty^""^Empty
FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS_HISTO^OFFER_ITEM_IDENTIFIER^$.data.currentImage.orderItems[*].services[*].context.offerServiceItemRef.offerServiceItemPtr^element_at(split($.data.currentImage.orderItems[*].services[*].context.offerServiceItemRef.offerServiceItemPtr,'/'),array_size(split($.data.currentImage.orderItems[*].services[*].context.offerServiceItemRef.offerServiceItemPtr,'/')))^OrderOrderChangeEvent.currentImage.orderItems.services.context.offerServiceItemRef.offerServiceItemPtr^Link to another part of the resource, expressed as a JSON pointer starting from resource root. See https://datatracker.ietf.org/doc/html/rfc6901#section-5^Success^""^Empty^""^Empty
FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS_HISTO^ITEM_SERVICE_TYPE^$.data.currentImage.orderItems[*].services[*].type^""^OrderOrderChangeEvent.currentImage.orderItems.services.type^Type for the Fulfillment (Service/Payment).^Success^""^Empty^""^Empty
FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS_HISTO^SOLD_PRODUCT_REFERENCE_IDENTIFIER^$.data.currentImage.orderItems[*].services[*].context.soldProduct.productRef.id^""^OrderOrderChangeEvent.currentImage.orderItems.services.context.soldProduct.productRef.id^Id of the related resource.^Success^""^Empty^""^Empty
FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS_HISTO^SOLD_PRODUCT_INSTANCE_IDENTIFIER^$.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance.id^""^OrderOrderChangeEvent.currentImage.orderItems.services.context.soldProduct.productInstance.id^identifier of the product^Success^""^Empty^""^Empty
FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS_HISTO^SOLD_PRODUCT_ID^$.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance.id^hashM($.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance.id)^OrderOrderChangeEvent.currentImage.orderItems.services.context.soldProduct.productInstance.id^identifier of the product^Success^""^Empty^""^Empty
FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS_HISTO^ORDER_ID^$.data.currentImage.id^hashM($.data.currentImage.id)^OrderOrderChangeEvent.currentImage.id^Unique id of an Order.^Success^""^Empty^""^Empty
FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS_HISTO^ORDER_ITEM_ID^$.data.currentImage.orderItems[*].id^hashM($.data.currentImage.orderItems[*].id)^OrderOrderChangeEvent.currentImage.orderItems.id^Unique id of the item within an Order.^Success^""^Empty^""^Empty
FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS_HISTO^OFFER_ID^$.data.currentImage.orderItems[*].services[*].context.offerServiceItemRef.offerRef.id^hashM($.data.currentImage.orderItems[*].services[*].context.offerServiceItemRef.offerRef.id)^OrderOrderChangeEvent.currentImage.orderItems.services.context.offerServiceItemRef.offerRef.id^Id of the related resource.^Success^""^Empty^""^Empty
FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS_HISTO^OFFER_ITEM_ID^$.data.currentImage.orderItems[*].services[*].context.offerServiceItemRef.offerServiceItemPtr^hashM($.data.currentImage.orderItems[*].services[*].context.offerServiceItemRef.offerServiceItemPtr)^OrderOrderChangeEvent.currentImage.orderItems.services.context.offerServiceItemRef.offerServiceItemPtr^Link to another part of the resource, expressed as a JSON pointer starting from resource root. See https://datatracker.ietf.org/doc/html/rfc6901#section-5^Success^""^Empty^""^Empty
FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS_HISTO^VERSION_ORDER^$.data.currentImage.lifecycle.dataVersion^""^OrderOrderChangeEvent.currentImage.lifecycle.dataVersion^Version of the element. It starts at 1, each update/change increments the version by 1. Dotted notation is to be used when there is a rewrite of history.^Success^""^Empty^""^Empty
FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS_HISTO^VALID_FROM^$.data.currentImage.lifecycle.lastUpdate.dateTime^""^OrderOrderChangeEvent.currentImage.lifecycle.lastUpdate.dateTime^Date and time of the change occurence based on ISO-8601.^Success^""^Empty^""^Empty
FACT_ITEM_SERVICE_STAKEHOLDER_HISTO^INTERNAL_ZORDER^$.data.currentImage.id^""^OrderOrderChangeEvent.currentImage.id^Unique id of an Order.^Success^""^Empty^""^Empty
FACT_ITEM_SERVICE_STAKEHOLDER_HISTO^ITEM_SERVICE_STAKEHOLDER_ID^$.data.currentImage.orderItems[*].services[*].id - $.data.currentImage.orderItems[*].services[*].measure.stakeholders[*].personalData.personalDataRef.id^hashM($.data.currentImage.orderItems[*].services[*].id - $.data.currentImage.orderItems[*].services[*].measure.stakeholders[*].personalData.personalDataRef.id)^OrderOrderChangeEvent.currentImage.orderItems.services.id^Id of the Fulfillment.^Success^""^Empty^""^Empty
FACT_ITEM_SERVICE_STAKEHOLDER_HISTO^REFERENCE_KEY^$.data.currentImage.orderItems[*].services[*].id - $.data.currentImage.orderItems[*].services[*].measure.stakeholders[*].personalData.personalDataRef.id^""^OrderOrderChangeEvent.currentImage.orderItems.services.id^Id of the Fulfillment.^Success^""^Empty^""^Empty
FACT_ITEM_SERVICE_STAKEHOLDER_HISTO^ITEM_SERVICE_IDENTIFIER^$.data.currentImage.orderItems[*].services[*].id^""^OrderOrderChangeEvent.currentImage.orderItems.services.id^Id of the Fulfillment.^Success^""^Empty^""^Empty
FACT_ITEM_SERVICE_STAKEHOLDER_HISTO^STAKEHOLDER_IDENTIFIER^$.data.currentImage.orderItems[*].services[*].measure.stakeholders[*].personalData.personalDataRef.id^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.stakeholders.personalData.personalDataRef.id^Id of the related resource.^Success^""^Empty^""^Empty
FACT_ITEM_SERVICE_STAKEHOLDER_HISTO^STATUS^$.data.currentImage.orderItems[*].services[*].measure.status^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.status^"Fulfillment status for service item.
- NOT_STARTED : When Fulfillment is created from Order based flow  
- BOOK_SUBMITTED :  When booking request is sent to airline inventory system
- BOOK_ACCEPTED : When booking request has been accepted by airline inventory system
- BOOK_REJECTED : When booking request has been rejected by airline inventory system
- READY_TO_DELIVER : When the fulfillment is in a situation allowing the provider of this service liability to start the delivery
- DELIVERED : When the fulfillment has been delivered - Status received from airline delivery system
- CANCELLED : When the cancellation request has been accepted by the airline inventory system
- BOOK_WAITLISTED : When the booking is waitlisted
- CONTROLLED_BY_DELIVERY : When the fulfillment is being managed by the delivery system
- IN_PROGRESS : When the fulfillment is being delivered
- INFORMED : When the fulfillment is being managed by the service retailer"^Success^NOT_STARTED|BOOK_SUBMITTED|BOOK_ACCEPTED|BOOK_REJECTED|READY_TO_DELIVER|DELIVERED|CANCELLED|BOOK_WAITLISTED|CONTROLLED_BY_DELIVERY|IN_PROGRESS|INFORMED^Success^""^Empty
FACT_ITEM_SERVICE_STAKEHOLDER_HISTO^BASELINE_SERVICE_CODE^$.data.currentImage.orderItems[*].services[*].measure.product.productInstance[?(@.productType == 'serviceProduct')].baselineInformation.ssrCode^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.product.productInstance.baselineInformation.ssrCode^""^Missing^""^Missing^""^Missing
FACT_ITEM_SERVICE_STAKEHOLDER_HISTO^EXECUTION_SERVICE_CODE^$.data.currentImage.orderItems[*].services[*].measure.product.productInstance[?(@.productType == 'serviceProduct')].baselineInformation.ssrCode^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.product.productInstance.baselineInformation.ssrCode^""^Missing^""^Missing^""^Missing
FACT_ITEM_SERVICE_STAKEHOLDER_HISTO^OPENTICKET_PRIMARY_DOCUMENT_AIRLINE_CODE^$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenTicket.v1')].document.structuredPrimaryDocumentNumber.numericalAirlineCode^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.document.structuredPrimaryDocumentNumber.numericalAirlineCode^""^Missing^""^Missing^""^Missing
FACT_ITEM_SERVICE_STAKEHOLDER_HISTO^OPENTICKET_PRIMARY_DOCUMENT_NUMBER^$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenTicket.v1')].document.structuredPrimaryDocumentNumber.number^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.document.structuredPrimaryDocumentNumber.number^""^Missing^""^Missing^""^Missing
FACT_ITEM_SERVICE_STAKEHOLDER_HISTO^OPENTICKET_PRIMARY_DOCUMENT_ISSUANCE_DATETIME_LOCAL^$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenTicket.v1')].document.issuanceLocalDateTime^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.document.issuanceLocalDateTime^""^Missing^""^Missing^""^Missing
FACT_ITEM_SERVICE_STAKEHOLDER_HISTO^OPENTICKET_PRIMARY_DOCUMENT_VERSION^$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenTicket.v1')].document.version^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.document.version^""^Missing^""^Missing^""^Missing
FACT_ITEM_SERVICE_STAKEHOLDER_HISTO^OPENTICKET_PRIMARY_DOCUMENT_TYPE^$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenTicket.v1')].document.documentType^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.document.documentType^""^Missing^""^Missing^""^Missing
FACT_ITEM_SERVICE_STAKEHOLDER_HISTO^OPENSALE_PRIMARY_DOCUMENT_AIRLINE_CODE^$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenSale.v1')].document.structuredPrimaryDocumentNumber.numericalAirlineCode^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.document.structuredPrimaryDocumentNumber.numericalAirlineCode^""^Missing^""^Missing^""^Missing
FACT_ITEM_SERVICE_STAKEHOLDER_HISTO^OPENSALE_PRIMARY_DOCUMENT_NUMBER^$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenSale.v1')].document.structuredPrimaryDocumentNumber.number^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.document.structuredPrimaryDocumentNumber.number^""^Missing^""^Missing^""^Missing
FACT_ITEM_SERVICE_STAKEHOLDER_HISTO^OPENSALE_PRIMARY_DOCUMENT_CHECKDIGIT^$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenSale.v1')].document.structuredPrimaryDocumentNumber.checkDigit^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.document.structuredPrimaryDocumentNumber.checkDigit^""^Missing^""^Missing^""^Missing
FACT_ITEM_SERVICE_STAKEHOLDER_HISTO^OPENSALE_PRIMARY_DOCUMENT_ISSUANCE_DATETIME_LOCAL^$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenSale.v1')].document.issuanceLocalDateTime^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.document.issuanceLocalDateTime^""^Missing^""^Missing^""^Missing
FACT_ITEM_SERVICE_STAKEHOLDER_HISTO^PNR_REFERENCE^$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_Pnr.v1')].pnr.reference^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.pnr.reference^""^Missing^""^Missing^""^Missing
FACT_ITEM_SERVICE_STAKEHOLDER_HISTO^PNR_VERSION^$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_Pnr.v1')].pnr.version^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.pnr.version^""^Missing^""^Missing^""^Missing
FACT_ITEM_SERVICE_STAKEHOLDER_HISTO^PNR_CREATION_DATETIME^$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_Pnr.v1')].pnr.creation.dateTime^""^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.pnr.creation.dateTime^""^Missing^""^Missing^""^Missing
FACT_ITEM_SERVICE_STAKEHOLDER_HISTO^OFFER_IDENTIFIER^$.data.currentImage.orderItems[*].services[*].context.offerServiceItemRef.offerRef.id^""^OrderOrderChangeEvent.currentImage.orderItems.services.context.offerServiceItemRef.offerRef.id^Id of the related resource.^Success^""^Empty^""^Empty
FACT_ITEM_SERVICE_STAKEHOLDER_HISTO^OFFER_HREF^$.data.currentImage.orderItems[*].services[*].context.offerServiceItemRef.offerRef.href^""^OrderOrderChangeEvent.currentImage.orderItems.services.context.offerServiceItemRef.offerRef.href^URI reference to the related source.^Success^""^Empty^""^Empty
FACT_ITEM_SERVICE_STAKEHOLDER_HISTO^OFFER_ITEM_PTR^$.data.currentImage.orderItems[*].services[*].context.offerServiceItemRef.offerServiceItemPtr^""^OrderOrderChangeEvent.currentImage.orderItems.services.context.offerServiceItemRef.offerServiceItemPtr^Link to another part of the resource, expressed as a JSON pointer starting from resource root. See https://datatracker.ietf.org/doc/html/rfc6901#section-5^Success^""^Empty^""^Empty
FACT_ITEM_SERVICE_STAKEHOLDER_HISTO^OFFER_ITEM_IDENTIFIER^$.data.currentImage.orderItems[*].services[*].context.offerServiceItemRef.offerServiceItemPtr^element_at(split($.data.currentImage.orderItems[*].services[*].context.offerServiceItemRef.offerServiceItemPtr,'/'),array_size(split($.data.currentImage.orderItems[*].services[*].context.offerServiceItemRef.offerServiceItemPtr,'/')))^OrderOrderChangeEvent.currentImage.orderItems.services.context.offerServiceItemRef.offerServiceItemPtr^Link to another part of the resource, expressed as a JSON pointer starting from resource root. See https://datatracker.ietf.org/doc/html/rfc6901#section-5^Success^""^Empty^""^Empty
FACT_ITEM_SERVICE_STAKEHOLDER_HISTO^ITEM_SERVICE_TYPE^$.data.currentImage.orderItems[*].services[*].type^""^OrderOrderChangeEvent.currentImage.orderItems.services.type^Type for the Fulfillment (Service/Payment).^Success^""^Empty^""^Empty
FACT_ITEM_SERVICE_STAKEHOLDER_HISTO^SOLD_PRODUCT_REFERENCE_IDENTIFIER^$.data.currentImage.orderItems[*].services[*].context.soldProduct.productRef.id^""^OrderOrderChangeEvent.currentImage.orderItems.services.context.soldProduct.productRef.id^Id of the related resource.^Success^""^Empty^""^Empty
FACT_ITEM_SERVICE_STAKEHOLDER_HISTO^SOLD_PRODUCT_INSTANCE_IDENTIFIER^$.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance.id^""^OrderOrderChangeEvent.currentImage.orderItems.services.context.soldProduct.productInstance.id^identifier of the product^Success^""^Empty^""^Empty
FACT_ITEM_SERVICE_STAKEHOLDER_HISTO^SOLD_PRODUCT_ID^$.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance.id^hashM($.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance.id)^OrderOrderChangeEvent.currentImage.orderItems.services.context.soldProduct.productInstance.id^identifier of the product^Success^""^Empty^""^Empty
FACT_ITEM_SERVICE_STAKEHOLDER_HISTO^ORDER_ID^$.data.currentImage.id^hashM($.data.currentImage.id)^OrderOrderChangeEvent.currentImage.id^Unique id of an Order.^Success^""^Empty^""^Empty
FACT_ITEM_SERVICE_STAKEHOLDER_HISTO^ORDER_ITEM_ID^$.data.currentImage.orderItems[*].id^hashM($.data.currentImage.orderItems[*].id)^OrderOrderChangeEvent.currentImage.orderItems.id^Unique id of the item within an Order.^Success^""^Empty^""^Empty
FACT_ITEM_SERVICE_STAKEHOLDER_HISTO^OFFER_ID^$.data.currentImage.orderItems[*].services[*].context.offerServiceItemRef.offerRef.id^hashM($.data.currentImage.orderItems[*].services[*].context.offerServiceItemRef.offerRef.id)^OrderOrderChangeEvent.currentImage.orderItems.services.context.offerServiceItemRef.offerRef.id^Id of the related resource.^Success^""^Empty^""^Empty
FACT_ITEM_SERVICE_STAKEHOLDER_HISTO^OFFER_ITEM_ID^$.data.currentImage.orderItems[*].services[*].context.offerServiceItemRef.offerServiceItemPtr^hashM($.data.currentImage.orderItems[*].services[*].context.offerServiceItemRef.offerServiceItemPtr)^OrderOrderChangeEvent.currentImage.orderItems.services.context.offerServiceItemRef.offerServiceItemPtr^Link to another part of the resource, expressed as a JSON pointer starting from resource root. See https://datatracker.ietf.org/doc/html/rfc6901#section-5^Success^""^Empty^""^Empty
FACT_ITEM_SERVICE_STAKEHOLDER_HISTO^VERSION_ORDER^$.data.currentImage.lifecycle.dataVersion^""^OrderOrderChangeEvent.currentImage.lifecycle.dataVersion^Version of the element. It starts at 1, each update/change increments the version by 1. Dotted notation is to be used when there is a rewrite of history.^Success^""^Empty^""^Empty
FACT_ITEM_SERVICE_STAKEHOLDER_HISTO^VALID_FROM^$.data.currentImage.lifecycle.lastUpdate.dateTime^""^OrderOrderChangeEvent.currentImage.lifecycle.lastUpdate.dateTime^Date and time of the change occurence based on ISO-8601.^Success^""^Empty^""^Empty
FACT_SOLD_AIR_LEG_CABIN_PRODUCT_HISTO^INTERNAL_ZORDER^$.data.currentImage.id^""^OrderOrderChangeEvent.currentImage.id^Unique id of an Order.^Success^""^Empty^""^Empty
FACT_SOLD_AIR_LEG_CABIN_PRODUCT_HISTO^SOLD_AIR_LEG_CABIN_PRODUCT_ID^$.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].id^hashM($.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].id)^OrderOrderChangeEvent.currentImage.orderItems.services.context.soldProduct.productInstance.id^identifier of the product^Success^""^Empty^""^Empty
FACT_SOLD_AIR_LEG_CABIN_PRODUCT_HISTO^REFERENCE_KEY^$.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].id^""^OrderOrderChangeEvent.currentImage.orderItems.services.context.soldProduct.productInstance.id^identifier of the product^Success^""^Empty^""^Empty
FACT_SOLD_AIR_LEG_CABIN_PRODUCT_HISTO^PRODUCT_TYPE^$.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].productType^""^OrderOrderChangeEvent.currentImage.orderItems.services.context.soldProduct.productInstance.productType^"The Product Type is the general category of the product. Each Product Type has a dedicated datamodel.

The Product type value acts as a discriminator to select the right concrete product model. Therefore, it is expected to match the name of one of the models present in this specification."^Success^""^Empty^""^Empty
FACT_SOLD_AIR_LEG_CABIN_PRODUCT_HISTO^MKT_CARRIER^$.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].marketingFlightDesignator.carrierCode^""^OrderOrderChangeEvent.currentImage.orderItems.services.context.soldProduct.productInstance.marketingFlightDesignator.carrierCode^Two letter IATA standard carrier code^Success^6X^Success^""^Empty
FACT_SOLD_AIR_LEG_CABIN_PRODUCT_HISTO^MKT_FLIGHT_NUMBER^$.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].marketingFlightDesignator.flightNumber^""^OrderOrderChangeEvent.currentImage.orderItems.services.context.soldProduct.productInstance.marketingFlightDesignator.flightNumber^1-4 digit number^Success^555^Success^""^Empty
FACT_SOLD_AIR_LEG_CABIN_PRODUCT_HISTO^MKT_OPERATIONAL_SUFFIX^$.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].marketingFlightDesignator.operationalSuffix^""^OrderOrderChangeEvent.currentImage.orderItems.services.context.soldProduct.productInstance.marketingFlightDesignator.operationalSuffix^the operational suffix^Success^A^Success^""^Empty
FACT_SOLD_AIR_LEG_CABIN_PRODUCT_HISTO^OPE_CARRIER^$.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].operatingFlightDesignator.flightNumber -  - $.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].marketingFlightDesignator.carrierCode^if(element_at(split($.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].operatingFlightDesignator.flightNumber -  - $.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].marketingFlightDesignator.carrierCode,'-'),1) == '_', if(element_at(split($.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].operatingFlightDesignator.flightNumber -  - $.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].marketingFlightDesignator.carrierCode,'-'),3) == '_', null, element_at(split($.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].operatingFlightDesignator.flightNumber -  - $.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].marketingFlightDesignator.carrierCode,'-'),3)), if(element_at(split($.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].operatingFlightDesignator.flightNumber -  - $.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].marketingFlightDesignator.carrierCode,'-'),2) == '_', null, element_at(split($.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].operatingFlightDesignator.flightNumber -  - $.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].marketingFlightDesignator.carrierCode,'-'),2)) )^OrderOrderChangeEvent.currentImage.orderItems.services.context.soldProduct.productInstance.operatingFlightDesignator.flightNumber^1-4 digit number^Success^555^Success^""^Empty
FACT_SOLD_AIR_LEG_CABIN_PRODUCT_HISTO^OPE_FLIGHT_NUMBER^$.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].operatingFlightDesignator.flightNumber -  - $.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].marketingFlightDesignator.flightNumber^if(element_at(split($.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].operatingFlightDesignator.flightNumber -  - $.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].marketingFlightDesignator.flightNumber,'-'),1) == '_', if(element_at(split($.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].operatingFlightDesignator.flightNumber -  - $.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].marketingFlightDesignator.flightNumber,'-'),3) == '_', null, element_at(split($.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].operatingFlightDesignator.flightNumber -  - $.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].marketingFlightDesignator.flightNumber,'-'),3)), if(element_at(split($.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].operatingFlightDesignator.flightNumber -  - $.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].marketingFlightDesignator.flightNumber,'-'),2) == '_', null, element_at(split($.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].operatingFlightDesignator.flightNumber -  - $.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].marketingFlightDesignator.flightNumber,'-'),2)) )^OrderOrderChangeEvent.currentImage.orderItems.services.context.soldProduct.productInstance.operatingFlightDesignator.flightNumber^1-4 digit number^Success^555^Success^""^Empty
FACT_SOLD_AIR_LEG_CABIN_PRODUCT_HISTO^OPE_OPERATIONAL_SUFFIX^$.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].operatingFlightDesignator.flightNumber -  - $.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].marketingFlightDesignator.operationalSuffix^if(element_at(split($.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].operatingFlightDesignator.flightNumber -  - $.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].marketingFlightDesignator.operationalSuffix,'-'),1) == '_', if(element_at(split($.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].operatingFlightDesignator.flightNumber -  - $.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].marketingFlightDesignator.operationalSuffix,'-'),3) == '_', null, element_at(split($.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].operatingFlightDesignator.flightNumber -  - $.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].marketingFlightDesignator.operationalSuffix,'-'),3)), if(element_at(split($.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].operatingFlightDesignator.flightNumber -  - $.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].marketingFlightDesignator.operationalSuffix,'-'),2) == '_', null, element_at(split($.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].operatingFlightDesignator.flightNumber -  - $.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].marketingFlightDesignator.operationalSuffix,'-'),2)) )^OrderOrderChangeEvent.currentImage.orderItems.services.context.soldProduct.productInstance.operatingFlightDesignator.flightNumber^1-4 digit number^Success^555^Success^""^Empty
FACT_SOLD_AIR_LEG_CABIN_PRODUCT_HISTO^CABIN^$.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].cabin.code^""^OrderOrderChangeEvent.currentImage.orderItems.services.context.soldProduct.productInstance.cabin.code^cabin code filed in inventory for the sold seat^Success^""^Empty^""^Empty
FACT_SOLD_AIR_LEG_CABIN_PRODUCT_HISTO^LEG_DEPARTURE_AIRPORT^$.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].leg.boardPointIataCode^""^Leg\.v1.boardPointIataCode^IATA code of the leg's departure airport^Success^GVA^Success^""^Empty
FACT_SOLD_AIR_LEG_CABIN_PRODUCT_HISTO^LEG_DEPARTURE_DATETIME_LOCAL^$.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].leg.scheduledDepartureDateTime^""^Leg\.v1.scheduledDepartureDateTime^scheduled departure date and time of the Leg in local time, ISO format yyyy-mm-ddThh:mm:ss^Success^2018-09-17T12:40:00^Success^""^Empty
FACT_SOLD_AIR_LEG_CABIN_PRODUCT_HISTO^LEG_ARRIVAL_AIRPORT^$.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].leg.offPointIataCode^""^Leg\.v1.offPointIataCode^IATA code of the leg's arrival airport^Success^YUL^Success^""^Empty
FACT_SOLD_AIR_LEG_CABIN_PRODUCT_HISTO^LEG_ARRIVAL_DATETIME_LOCAL^$.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].leg.scheduledArrivalDateTime^""^Leg\.v1.scheduledArrivalDateTime^scheduled arrival date and time of the Leg in local time, ISO format yyyy-mm-ddThh:mm:ss.^Success^2018-09-17T14:45:00^Success^""^Empty
FACT_SOLD_AIR_LEG_CABIN_PRODUCT_HISTO^LEG_DEIS_CODE^$.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].leg.deis[*].code^""^Leg\.v1.deis.code^DEI code^Success^505^Success^""^Empty
FACT_SOLD_AIR_LEG_CABIN_PRODUCT_HISTO^LEG_DEIS_VALUE^$.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].leg.deis[*].value^""^DEI\.v2.\value^DEI value from a existing Reference to IATA SSIM definition, in case multi values related to same code: '/' is a separator between several DEI Value.^Success^ET^Success^""^Empty
FACT_SOLD_AIR_LEG_CABIN_PRODUCT_HISTO^LEG_AIRCRAFT_TYPE^$.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].leg.aircraftEquipment.aircraftType^""^Leg\.v1.aircraftEquipment.aircraftType^aircraft type (ex 320, 777, ...)^Success^""^Empty^""^Empty
FACT_SOLD_AIR_LEG_CABIN_PRODUCT_HISTO^EXECUTION_ITINERARY_ORDER^$.data.currentImage.orderItems[*].services[*].context.soldProduct.serviceItemExecution.itineraryOrder^""^OrderOrderChangeEvent.currentImage.orderItems.services.context.soldProduct.serviceItemExecution.itineraryOrder^The order/rank of the leg at itinerary level^Success^""^Empty^""^Empty
FACT_SOLD_AIR_LEG_CABIN_PRODUCT_HISTO^EXECUTION_BOOKING_CLASS^$.data.currentImage.orderItems[*].services[*].context.soldProduct.serviceItemExecution.bookingClass^""^OrderOrderChangeEvent.currentImage.orderItems.services.context.soldProduct.serviceItemExecution.bookingClass^Booking classe is a marketing partition of the airline and is designated by a single letter code: F, J, C, Y, R. Price/fare rules are associated to this class, which leads to price variation over the time. Primary RBD (ATPCo standard notation), Booked Class, BKC (Amadeus standard notation) ^Success^""^Empty^""^Empty
FACT_SOLD_AIR_LEG_CABIN_PRODUCT_HISTO^EXECUTION_FLIGHT_STATUS^$.data.currentImage.orderItems[*].services[*].context.soldProduct.serviceItemExecution.flightStatus^""^OrderOrderChangeEvent.currentImage.orderItems.services.context.soldProduct.serviceItemExecution.flightStatus^""^Missing^""^Missing^""^Missing
FACT_SOLD_AIR_LEG_CABIN_PRODUCT_HISTO^EXECUTION_VALIDATING_CARRIER_REF^$.data.currentImage.orderItems[*].services[*].context.soldProduct.serviceItemExecution.validatingCarrierRef^""^OrderOrderChangeEvent.currentImage.orderItems.services.context.soldProduct.serviceItemExecution.validatingCarrierRef^""^Missing^""^Missing^""^Missing
FACT_SOLD_AIR_LEG_CABIN_PRODUCT_HISTO^EXECUTION_FLIGHT_CONNECTION_QUALIFIER^$.data.currentImage.orderItems[*].services[*].context.soldProduct.serviceItemExecution.flightConnectionQualifier^""^OrderOrderChangeEvent.currentImage.orderItems.services.context.soldProduct.serviceItemExecution.flightConnectionQualifier^""^Missing^""^Missing^""^Missing
FACT_SOLD_AIR_LEG_CABIN_PRODUCT_HISTO^EXECUTION_SEGMENT_ELAPSED_FLYING_TIME^$.data.currentImage.orderItems[*].services[*].context.soldProduct.serviceItemExecution.segmentElapsedFlyingTime^""^OrderOrderChangeEvent.currentImage.orderItems.services.context.soldProduct.serviceItemExecution.segmentElapsedFlyingTime^""^Missing^""^Missing^""^Missing
FACT_SOLD_AIR_LEG_CABIN_PRODUCT_HISTO^ORDER_ID^$.data.currentImage.id^hashM($.data.currentImage.id)^OrderOrderChangeEvent.currentImage.id^Unique id of an Order.^Success^""^Empty^""^Empty
FACT_SOLD_AIR_LEG_CABIN_PRODUCT_HISTO^VERSION_ORDER^$.data.currentImage.lifecycle.dataVersion^""^OrderOrderChangeEvent.currentImage.lifecycle.dataVersion^Version of the element. It starts at 1, each update/change increments the version by 1. Dotted notation is to be used when there is a rewrite of history.^Success^""^Empty^""^Empty
FACT_SOLD_AIR_LEG_CABIN_PRODUCT_HISTO^VALID_FROM^$.data.currentImage.lifecycle.lastUpdate.dateTime^""^OrderOrderChangeEvent.currentImage.lifecycle.lastUpdate.dateTime^Date and time of the change occurence based on ISO-8601.^Success^""^Empty^""^Empty
FACT_ORDER_SOLD_PRODUCT_SERVICE_HISTO^INTERNAL_ZORDER^$.data.currentImage.id^""^OrderOrderChangeEvent.currentImage.id^Unique id of an Order.^Success^""^Empty^""^Empty
FACT_ORDER_SOLD_PRODUCT_SERVICE_HISTO^ORDER_SOLD_PRODUCT_SERVICE_ID^$.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'serviceProduct')].id^hashM($.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'serviceProduct')].id)^OrderOrderChangeEvent.currentImage.orderItems.services.context.soldProduct.productInstance.id^identifier of the product^Success^""^Empty^""^Empty
FACT_ORDER_SOLD_PRODUCT_SERVICE_HISTO^REFERENCE_KEY^$.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'serviceProduct')].id^""^OrderOrderChangeEvent.currentImage.orderItems.services.context.soldProduct.productInstance.id^identifier of the product^Success^""^Empty^""^Empty
FACT_ORDER_SOLD_PRODUCT_SERVICE_HISTO^PRODUCT_TYPE^$.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'serviceProduct')].productType^""^OrderOrderChangeEvent.currentImage.orderItems.services.context.soldProduct.productInstance.productType^"The Product Type is the general category of the product. Each Product Type has a dedicated datamodel.

The Product type value acts as a discriminator to select the right concrete product model. Therefore, it is expected to match the name of one of the models present in this specification."^Success^""^Empty^""^Empty
FACT_ORDER_SOLD_PRODUCT_SERVICE_HISTO^ORDER_ID^$.data.currentImage.id^hashM($.data.currentImage.id)^OrderOrderChangeEvent.currentImage.id^Unique id of an Order.^Success^""^Empty^""^Empty
FACT_ORDER_SOLD_PRODUCT_SERVICE_HISTO^VERSION_ORDER^$.data.currentImage.lifecycle.dataVersion^""^OrderOrderChangeEvent.currentImage.lifecycle.dataVersion^Version of the element. It starts at 1, each update/change increments the version by 1. Dotted notation is to be used when there is a rewrite of history.^Success^""^Empty^""^Empty
FACT_ORDER_SOLD_PRODUCT_SERVICE_HISTO^VALID_FROM^$.data.currentImage.lifecycle.lastUpdate.dateTime^""^OrderOrderChangeEvent.currentImage.lifecycle.lastUpdate.dateTime^Date and time of the change occurence based on ISO-8601.^Success^""^Empty^""^Empty
FACT_STAKEHOLDER_HISTO^INTERNAL_ZORDER^$.data.currentImage.id^""^OrderOrderChangeEvent.currentImage.id^Unique id of an Order.^Success^""^Empty^""^Empty
FACT_STAKEHOLDER_HISTO^STAKEHOLDER_ID^$.data.currentImage.stakeholders[*].id^hashM($.data.currentImage.stakeholders[*].id)^OrderOrderChangeEvent.currentImage.stakeholders.id^Id of the Stakeholder in the related Order.^Success^""^Empty^""^Empty
FACT_STAKEHOLDER_HISTO^REFERENCE_KEY^$.data.currentImage.stakeholders[*].id^""^OrderOrderChangeEvent.currentImage.stakeholders.id^Id of the Stakeholder in the related Order.^Success^""^Empty^""^Empty
FACT_STAKEHOLDER_HISTO^ROLE^$.data.currentImage.stakeholders[*].role^""^OrderOrderChangeEvent.currentImage.stakeholders.role^Stakeholder role for the related Order^Success^SELLER|TRAVELER|BUYER|PAYER|RETAILER|AGGREGATOR^Success^""^Empty
FACT_STAKEHOLDER_HISTO^PERSONAL_DATA_REF_IDENTIFIER^$.data.currentImage.stakeholders[*].personalDataRef.id^""^OrderServiceFulfillmentStakeholderPersonalData.personalDataRef.id^Id of the related resource.^Success^""^Empty^""^Empty
FACT_STAKEHOLDER_HISTO^PERSONAL_DATA_REF_HREF^$.data.currentImage.stakeholders[*].personalDataRef.href^""^OrderServiceFulfillmentStakeholderPersonalData.personalDataRef.href^URI reference to the related source.^Success^""^Empty^""^Empty
FACT_STAKEHOLDER_HISTO^ORDER_ID^$.data.currentImage.id^hashM($.data.currentImage.id)^OrderOrderChangeEvent.currentImage.id^Unique id of an Order.^Success^""^Empty^""^Empty
FACT_STAKEHOLDER_HISTO^VERSION_ORDER^$.data.currentImage.lifecycle.dataVersion^""^OrderOrderChangeEvent.currentImage.lifecycle.dataVersion^Version of the element. It starts at 1, each update/change increments the version by 1. Dotted notation is to be used when there is a rewrite of history.^Success^""^Empty^""^Empty
FACT_STAKEHOLDER_HISTO^VALID_FROM^$.data.currentImage.lifecycle.lastUpdate.dateTime^""^OrderOrderChangeEvent.currentImage.lifecycle.lastUpdate.dateTime^Date and time of the change occurence based on ISO-8601.^Success^""^Empty^""^Empty
FACT_AGREEMENT_ITEM_HISTO^INTERNAL_ZORDER^$.data.currentImage.agreements[*].lifecycle.creation.dateTime - $.data.currentImage.agreements[*].id^""^OrderOrderChangeEvent.currentImage.agreements.lifecycle.creation.dateTime^Date and time of the change occurence based on ISO-8601.^Success^""^Empty^""^Empty
FACT_AGREEMENT_ITEM_HISTO^AGREEMENT_ITEM_ID^$.data.currentImage.agreements[*].id - $.data.currentImage.agreements[*].items[*].id^hashM($.data.currentImage.agreements[*].id - $.data.currentImage.agreements[*].items[*].id)^OrderOrderChangeEvent.currentImage.agreements.id^Id of the Agreement, unique within an Order.^Success^""^Empty^""^Empty
FACT_AGREEMENT_ITEM_HISTO^REFERENCE_KEY^$.data.currentImage.agreements[*].id - $.data.currentImage.agreements[*].items[*].id^""^OrderOrderChangeEvent.currentImage.agreements.id^Id of the Agreement, unique within an Order.^Success^""^Empty^""^Empty
FACT_AGREEMENT_ITEM_HISTO^AGREEMENT_IDENTIFIER^$.data.currentImage.agreements[*].id^""^OrderOrderChangeEvent.currentImage.agreements.id^Id of the Agreement, unique within an Order.^Success^""^Empty^""^Empty
FACT_AGREEMENT_ITEM_HISTO^AGREEMENT_STATUS^$.data.currentImage.agreements[*].status^""^OrderOrderChangeEvent.currentImage.agreements.status^"Agreement status
ACTIVE -&gt; Agreement is applicable.
REVOKED -&gt; Agreement is no more applicable due to terms and conditions no longer valid."^Success^ACTIVE|REVOKED^Success^""^Empty
FACT_AGREEMENT_ITEM_HISTO^AGREEMENT_ORIGIN^$.data.currentImage.agreements[*].origin^""^OrderOrderChangeEvent.currentImage.agreements.origin^"Agreement origin
ORDER -&gt; Agreement has been created from an Offer/Order native touchpoint.
RECONSTRUCTION -&gt; Agreement has been created from a PSS message going through smartbridge."^Success^ORDER|RECONSTRUCTION^Success^""^Empty
FACT_AGREEMENT_ITEM_HISTO^ORDER_ID^$.data.currentImage.id^hashM($.data.currentImage.id)^OrderOrderChangeEvent.currentImage.id^Unique id of an Order.^Success^""^Empty^""^Empty
FACT_AGREEMENT_ITEM_HISTO^OFFER_ID^$.data.currentImage.agreements[*].items[*].offerItemRef.offerRef.href^hashM($.data.currentImage.agreements[*].items[*].offerItemRef.offerRef.href)^OrderOrderChangeEvent.currentImage.agreements.items.offerItemRef.offerRef.href^URI reference to the related source.^Success^""^Empty^""^Empty
FACT_AGREEMENT_ITEM_HISTO^OFFER_STAKEHOLDER_ID^$.data.currentImage.agreements[*].items[*].stakeholderPtrByOfferStakeholderId^hashM($.data.currentImage.agreements[*].items[*].stakeholderPtrByOfferStakeholderId)^OrderOrderChangeEvent.currentImage.agreements.items.stakeholderPtrByOfferStakeholderId^JSON pointer reference to the stakeholder id of the offer model. This will be used to match stakeholders between offer and order.^Success^""^Empty^""^Empty
FACT_AGREEMENT_ITEM_HISTO^SELLER_STAKEHOLDER_ID^$.data.currentImage.agreements[*].sellerPtr^hashM($.data.currentImage.agreements[*].sellerPtr)^OrderOrderChangeEvent.currentImage.agreements.sellerPtr^Link to another part of the resource, expressed as a JSON pointer starting from resource root. See https://datatracker.ietf.org/doc/html/rfc6901#section-5^Success^""^Empty^""^Empty
FACT_AGREEMENT_ITEM_HISTO^CREATION_DATETIME_AGREEMENT^$.data.currentImage.agreements[*].lifecycle.creation.dateTime^""^OrderOrderChangeEvent.currentImage.agreements.lifecycle.creation.dateTime^Date and time of the change occurence based on ISO-8601.^Success^""^Empty^""^Empty
FACT_AGREEMENT_ITEM_HISTO^VERSION_AGREEMENT^$.data.currentImage.agreements[*].lifecycle.dataVersion^""^OrderOrderChangeEvent.currentImage.agreements.lifecycle.dataVersion^Version of the element. It starts at 1, each update/change increments the version by 1. Dotted notation is to be used when there is a rewrite of history.^Success^""^Empty^""^Empty
FACT_AGREEMENT_ITEM_HISTO^VALID_FROM^$.data.currentImage.agreements[*].lifecycle.lastUpdate.dateTime^""^OrderOrderChangeEvent.currentImage.agreements.lifecycle.lastUpdate.dateTime^Date and time of the change occurence based on ISO-8601.^Success^""^Empty^""^Empty
DIM_USER^USER_ID^$.data.currentImage.lifecycle.creation.user.organizationCode - $.data.currentImage.lifecycle.creation.user.iataNumber^hashS($.data.currentImage.lifecycle.creation.user.organizationCode - $.data.currentImage.lifecycle.creation.user.iataNumber)^OrderOrderChangeEvent.currentImage.lifecycle.creation.user.organizationCode^Organization currently used^Success^AC^Success^""^Empty
DIM_USER^USER_ID^$.data.currentImage.lifecycle.lastUpdate.user.organizationCode - $.data.currentImage.lifecycle.lastUpdate.user.iataNumber^hashS($.data.currentImage.lifecycle.lastUpdate.user.organizationCode - $.data.currentImage.lifecycle.lastUpdate.user.iataNumber)^OrderOrderChangeEvent.currentImage.lifecycle.lastUpdate.user.organizationCode^Organization currently used^Success^AC^Success^""^Empty
DIM_USER^USER_ID^$.data.currentImage.agreements[*].lifecycle.creation.user.organizationCode - $.data.currentImage.agreements[*].lifecycle.creation.user.iataNumber^hashS($.data.currentImage.agreements[*].lifecycle.creation.user.organizationCode - $.data.currentImage.agreements[*].lifecycle.creation.user.iataNumber)^OrderOrderChangeEvent.currentImage.agreements.lifecycle.creation.user.organizationCode^Organization currently used^Success^AC^Success^""^Empty
DIM_USER^USER_ID^$.data.currentImage.agreements[*].lifecycle.lastUpdate.user.organizationCode - $.data.currentImage.agreements[*].lifecycle.lastUpdate.user.iataNumber^hashS($.data.currentImage.agreements[*].lifecycle.lastUpdate.user.organizationCode - $.data.currentImage.agreements[*].lifecycle.lastUpdate.user.iataNumber)^OrderOrderChangeEvent.currentImage.agreements.lifecycle.lastUpdate.user.organizationCode^Organization currently used^Success^AC^Success^""^Empty
DIM_USER^USER_ID^$.data.currentImage.orderItems[*].services[*].lifecycle.creation.user.organizationCode - $.data.currentImage.orderItems[*].services[*].lifecycle.creation.user.iataNumber^hashS($.data.currentImage.orderItems[*].services[*].lifecycle.creation.user.organizationCode - $.data.currentImage.orderItems[*].services[*].lifecycle.creation.user.iataNumber)^OrderOrderChangeEvent.currentImage.orderItems.services.lifecycle.creation.user.organizationCode^Organization currently used^Success^AC^Success^""^Empty
DIM_USER^USER_ID^$.data.currentImage.orderItems[*].services[*].lifecycle.lastUpdate.user.organizationCode - $.data.currentImage.orderItems[*].services[*].lifecycle.lastUpdate.user.iataNumber^hashS($.data.currentImage.orderItems[*].services[*].lifecycle.lastUpdate.user.organizationCode - $.data.currentImage.orderItems[*].services[*].lifecycle.lastUpdate.user.iataNumber)^OrderOrderChangeEvent.currentImage.orderItems.services.lifecycle.lastUpdate.user.organizationCode^Organization currently used^Success^AC^Success^""^Empty
DIM_USER^USER_ID^$.data.currentImage.payments[*].lifecycle.creation.user.organizationCode - $.data.currentImage.payments[*].lifecycle.creation.user.iataNumber^hashS($.data.currentImage.payments[*].lifecycle.creation.user.organizationCode - $.data.currentImage.payments[*].lifecycle.creation.user.iataNumber)^OrderOrderChangeEvent.currentImage.payments.lifecycle.creation.user.organizationCode^Organization currently used^Success^AC^Success^""^Empty
DIM_USER^USER_ID^$.data.currentImage.payments[*].lifecycle.lastUpdate.user.organizationCode - $.data.currentImage.payments[*].lifecycle.lastUpdate.user.iataNumber^hashS($.data.currentImage.payments[*].lifecycle.lastUpdate.user.organizationCode - $.data.currentImage.payments[*].lifecycle.lastUpdate.user.iataNumber)^OrderOrderChangeEvent.currentImage.payments.lifecycle.lastUpdate.user.organizationCode^Organization currently used^Success^AC^Success^""^Empty
DIM_USER^ORGANIZATION_CODE^$.data.currentImage.lifecycle.creation.user.organizationCode^""^OrderOrderChangeEvent.currentImage.lifecycle.creation.user.organizationCode^Organization currently used^Success^AC^Success^""^Empty
DIM_USER^ORGANIZATION_CODE^$.data.currentImage.lifecycle.lastUpdate.user.organizationCode^""^OrderOrderChangeEvent.currentImage.lifecycle.lastUpdate.user.organizationCode^Organization currently used^Success^AC^Success^""^Empty
DIM_USER^ORGANIZATION_CODE^$.data.currentImage.agreements[*].lifecycle.creation.user.organizationCode^""^OrderOrderChangeEvent.currentImage.agreements.lifecycle.creation.user.organizationCode^Organization currently used^Success^AC^Success^""^Empty
DIM_USER^ORGANIZATION_CODE^$.data.currentImage.agreements[*].lifecycle.lastUpdate.user.organizationCode^""^OrderOrderChangeEvent.currentImage.agreements.lifecycle.lastUpdate.user.organizationCode^Organization currently used^Success^AC^Success^""^Empty
DIM_USER^ORGANIZATION_CODE^$.data.currentImage.orderItems[*].services[*].lifecycle.creation.user.organizationCode^""^OrderOrderChangeEvent.currentImage.orderItems.services.lifecycle.creation.user.organizationCode^Organization currently used^Success^AC^Success^""^Empty
DIM_USER^ORGANIZATION_CODE^$.data.currentImage.orderItems[*].services[*].lifecycle.lastUpdate.user.organizationCode^""^OrderOrderChangeEvent.currentImage.orderItems.services.lifecycle.lastUpdate.user.organizationCode^Organization currently used^Success^AC^Success^""^Empty
DIM_USER^ORGANIZATION_CODE^$.data.currentImage.payments[*].lifecycle.creation.user.organizationCode^""^OrderOrderChangeEvent.currentImage.payments.lifecycle.creation.user.organizationCode^Organization currently used^Success^AC^Success^""^Empty
DIM_USER^ORGANIZATION_CODE^$.data.currentImage.payments[*].lifecycle.lastUpdate.user.organizationCode^""^OrderOrderChangeEvent.currentImage.payments.lifecycle.lastUpdate.user.organizationCode^Organization currently used^Success^AC^Success^""^Empty
DIM_USER^IATA_NUMBER^$.data.currentImage.lifecycle.creation.user.iataNumber^""^OrderOrderChangeEvent.currentImage.lifecycle.creation.user.iataNumber^8-digit IATA number^Success^""^Empty^""^Empty
DIM_USER^IATA_NUMBER^$.data.currentImage.lifecycle.lastUpdate.user.iataNumber^""^OrderOrderChangeEvent.currentImage.lifecycle.lastUpdate.user.iataNumber^8-digit IATA number^Success^""^Empty^""^Empty
DIM_USER^IATA_NUMBER^$.data.currentImage.agreements[*].lifecycle.creation.user.iataNumber^""^OrderOrderChangeEvent.currentImage.agreements.lifecycle.creation.user.iataNumber^8-digit IATA number^Success^""^Empty^""^Empty
DIM_USER^IATA_NUMBER^$.data.currentImage.agreements[*].lifecycle.lastUpdate.user.iataNumber^""^OrderOrderChangeEvent.currentImage.agreements.lifecycle.lastUpdate.user.iataNumber^8-digit IATA number^Success^""^Empty^""^Empty
DIM_USER^IATA_NUMBER^$.data.currentImage.orderItems[*].services[*].lifecycle.creation.user.iataNumber^""^OrderOrderChangeEvent.currentImage.orderItems.services.lifecycle.creation.user.iataNumber^8-digit IATA number^Success^""^Empty^""^Empty
DIM_USER^IATA_NUMBER^$.data.currentImage.orderItems[*].services[*].lifecycle.lastUpdate.user.iataNumber^""^OrderOrderChangeEvent.currentImage.orderItems.services.lifecycle.lastUpdate.user.iataNumber^8-digit IATA number^Success^""^Empty^""^Empty
DIM_USER^IATA_NUMBER^$.data.currentImage.payments[*].lifecycle.creation.user.iataNumber^""^OrderOrderChangeEvent.currentImage.payments.lifecycle.creation.user.iataNumber^8-digit IATA number^Success^""^Empty^""^Empty
DIM_USER^IATA_NUMBER^$.data.currentImage.payments[*].lifecycle.lastUpdate.user.iataNumber^""^OrderOrderChangeEvent.currentImage.payments.lifecycle.lastUpdate.user.iataNumber^8-digit IATA number^Success^""^Empty^""^Empty
DIM_USER^ORGANIZATION_ID^$.data.currentImage.lifecycle.creation.user.organizationCode^hashS($.data.currentImage.lifecycle.creation.user.organizationCode)^OrderOrderChangeEvent.currentImage.lifecycle.creation.user.organizationCode^Organization currently used^Success^AC^Success^""^Empty
DIM_USER^ORGANIZATION_ID^$.data.currentImage.lifecycle.lastUpdate.user.organizationCode^hashS($.data.currentImage.lifecycle.lastUpdate.user.organizationCode)^OrderOrderChangeEvent.currentImage.lifecycle.lastUpdate.user.organizationCode^Organization currently used^Success^AC^Success^""^Empty
DIM_USER^ORGANIZATION_ID^$.data.currentImage.agreements[*].lifecycle.creation.user.organizationCode^hashS($.data.currentImage.agreements[*].lifecycle.creation.user.organizationCode)^OrderOrderChangeEvent.currentImage.agreements.lifecycle.creation.user.organizationCode^Organization currently used^Success^AC^Success^""^Empty
DIM_USER^ORGANIZATION_ID^$.data.currentImage.agreements[*].lifecycle.lastUpdate.user.organizationCode^hashS($.data.currentImage.agreements[*].lifecycle.lastUpdate.user.organizationCode)^OrderOrderChangeEvent.currentImage.agreements.lifecycle.lastUpdate.user.organizationCode^Organization currently used^Success^AC^Success^""^Empty
DIM_USER^ORGANIZATION_ID^$.data.currentImage.orderItems[*].services[*].lifecycle.creation.user.organizationCode^hashS($.data.currentImage.orderItems[*].services[*].lifecycle.creation.user.organizationCode)^OrderOrderChangeEvent.currentImage.orderItems.services.lifecycle.creation.user.organizationCode^Organization currently used^Success^AC^Success^""^Empty
DIM_USER^ORGANIZATION_ID^$.data.currentImage.orderItems[*].services[*].lifecycle.lastUpdate.user.organizationCode^hashS($.data.currentImage.orderItems[*].services[*].lifecycle.lastUpdate.user.organizationCode)^OrderOrderChangeEvent.currentImage.orderItems.services.lifecycle.lastUpdate.user.organizationCode^Organization currently used^Success^AC^Success^""^Empty
DIM_USER^ORGANIZATION_ID^$.data.currentImage.payments[*].lifecycle.creation.user.organizationCode^hashS($.data.currentImage.payments[*].lifecycle.creation.user.organizationCode)^OrderOrderChangeEvent.currentImage.payments.lifecycle.creation.user.organizationCode^Organization currently used^Success^AC^Success^""^Empty
DIM_USER^ORGANIZATION_ID^$.data.currentImage.payments[*].lifecycle.lastUpdate.user.organizationCode^hashS($.data.currentImage.payments[*].lifecycle.lastUpdate.user.organizationCode)^OrderOrderChangeEvent.currentImage.payments.lifecycle.lastUpdate.user.organizationCode^Organization currently used^Success^AC^Success^""^Empty
DIM_LOCATION^LOCATION_ID^$.data.currentImage.lifecycle.creation.location.iataCode^hashS($.data.currentImage.lifecycle.creation.location.iataCode)^OrderOrderChangeEvent.currentImage.lifecycle.creation.location.iataCode^IATA location code^Success^PAR^Success^""^Empty
DIM_LOCATION^LOCATION_ID^$.data.currentImage.lifecycle.lastUpdate.location.iataCode^hashS($.data.currentImage.lifecycle.lastUpdate.location.iataCode)^OrderOrderChangeEvent.currentImage.lifecycle.lastUpdate.location.iataCode^IATA location code^Success^PAR^Success^""^Empty
DIM_LOCATION^LOCATION_ID^$.data.currentImage.agreements.lifecycle.creation.location.iataCode^hashS($.data.currentImage.agreements.lifecycle.creation.location.iataCode)^OrderOrderChangeEvent.currentImage.agreements.lifecycle.creation.location.iataCode^IATA location code^Success^PAR^Success^""^Empty
DIM_LOCATION^LOCATION_ID^$.data.currentImage.agreements.lifecycle.lastUpdate.location.iataCode^hashS($.data.currentImage.agreements.lifecycle.lastUpdate.location.iataCode)^OrderOrderChangeEvent.currentImage.agreements.lifecycle.lastUpdate.location.iataCode^IATA location code^Success^PAR^Success^""^Empty
DIM_LOCATION^LOCATION_ID^$.data.currentImage.orderItems[*].lifecycle.creation.location.iataCode^hashS($.data.currentImage.orderItems[*].lifecycle.creation.location.iataCode)^OrderOrderLifecycle.creation.location.iataCode^IATA location code^Success^PAR^Success^""^Empty
DIM_LOCATION^LOCATION_ID^$.data.currentImage.orderItems[*].lifecycle.lastUpdate.location.iataCode^hashS($.data.currentImage.orderItems[*].lifecycle.lastUpdate.location.iataCode)^OrderOrderLifecycle.lastUpdate.location.iataCode^IATA location code^Success^PAR^Success^""^Empty
DIM_LOCATION^LOCATION_ID^$.data.currentImage.orderItems[*].services[*].lifecycle.creation.location.iataCode^hashS($.data.currentImage.orderItems[*].services[*].lifecycle.creation.location.iataCode)^OrderOrderChangeEvent.currentImage.orderItems.services.lifecycle.creation.location.iataCode^IATA location code^Success^PAR^Success^""^Empty
DIM_LOCATION^LOCATION_ID^$.data.currentImage.orderItems[*].services[*].lifecycle.lastUpdate.location.iataCode^hashS($.data.currentImage.orderItems[*].services[*].lifecycle.lastUpdate.location.iataCode)^OrderOrderChangeEvent.currentImage.orderItems.services.lifecycle.lastUpdate.location.iataCode^IATA location code^Success^PAR^Success^""^Empty
DIM_LOCATION^LOCATION_ID^$.data.currentImage.payments[*].lifecycle.creation.location.iataCode^hashS($.data.currentImage.payments[*].lifecycle.creation.location.iataCode)^OrderOrderChangeEvent.currentImage.payments.lifecycle.creation.location.iataCode^IATA location code^Success^PAR^Success^""^Empty
DIM_LOCATION^LOCATION_ID^$.data.currentImage.payments[*].lifecycle.lastUpdate.location.iataCode^hashS($.data.currentImage.payments[*].lifecycle.lastUpdate.location.iataCode)^OrderOrderChangeEvent.currentImage.payments.lifecycle.lastUpdate.location.iataCode^IATA location code^Success^PAR^Success^""^Empty
DIM_LOCATION^LOCATION_ID^$.data.currentImage.stakeholders[*].pointOfBusiness.location.iataCode^hashS($.data.currentImage.stakeholders[*].pointOfBusiness.location.iataCode)^OrderStakeholderPointOfBusiness.location.iataCode^IATA location code.^Success^PAR^Success^""^Empty
DIM_LOCATION^LOCATION_ID^$.attachments[*].payload.offerData.offer.context.pointOfBusiness.location.iataCode^hashS($.attachments[*].payload.offerData.offer.context.pointOfBusiness.location.iataCode)^attachments.payload.offerData.offer.context.pointOfBusiness.location.iataCode^""^Missing^""^Missing^""^Missing
DIM_LOCATION^IATA_CODE^$.data.currentImage.lifecycle.creation.location.iataCode^""^OrderOrderChangeEvent.currentImage.lifecycle.creation.location.iataCode^IATA location code^Success^PAR^Success^""^Empty
DIM_LOCATION^IATA_CODE^$.data.currentImage.lifecycle.lastUpdate.location.iataCode^""^OrderOrderChangeEvent.currentImage.lifecycle.lastUpdate.location.iataCode^IATA location code^Success^PAR^Success^""^Empty
DIM_LOCATION^IATA_CODE^$.data.currentImage.agreements.lifecycle.creation.location.iataCode^""^OrderOrderChangeEvent.currentImage.agreements.lifecycle.creation.location.iataCode^IATA location code^Success^PAR^Success^""^Empty
DIM_LOCATION^IATA_CODE^$.data.currentImage.agreements.lifecycle.lastUpdate.location.iataCode^""^OrderOrderChangeEvent.currentImage.agreements.lifecycle.lastUpdate.location.iataCode^IATA location code^Success^PAR^Success^""^Empty
DIM_LOCATION^IATA_CODE^$.data.currentImage.orderItems[*].lifecycle.creation.location.iataCode^""^OrderOrderLifecycle.creation.location.iataCode^IATA location code^Success^PAR^Success^""^Empty
DIM_LOCATION^IATA_CODE^$.data.currentImage.orderItems[*].lifecycle.lastUpdate.location.iataCode^""^OrderOrderLifecycle.lastUpdate.location.iataCode^IATA location code^Success^PAR^Success^""^Empty
DIM_LOCATION^IATA_CODE^$.data.currentImage.orderItems[*].services[*].lifecycle.creation.location.iataCode^""^OrderOrderChangeEvent.currentImage.orderItems.services.lifecycle.creation.location.iataCode^IATA location code^Success^PAR^Success^""^Empty
DIM_LOCATION^IATA_CODE^$.data.currentImage.orderItems[*].services[*].lifecycle.lastUpdate.location.iataCode^""^OrderOrderChangeEvent.currentImage.orderItems.services.lifecycle.lastUpdate.location.iataCode^IATA location code^Success^PAR^Success^""^Empty
DIM_LOCATION^IATA_CODE^$.data.currentImage.payments[*].lifecycle.creation.location.iataCode^""^OrderOrderChangeEvent.currentImage.payments.lifecycle.creation.location.iataCode^IATA location code^Success^PAR^Success^""^Empty
DIM_LOCATION^IATA_CODE^$.data.currentImage.payments[*].lifecycle.lastUpdate.location.iataCode^""^OrderOrderChangeEvent.currentImage.payments.lifecycle.lastUpdate.location.iataCode^IATA location code^Success^PAR^Success^""^Empty
DIM_LOCATION^IATA_CODE^$.data.currentImage.stakeholders[*].pointOfBusiness.location.iataCode^""^OrderStakeholderPointOfBusiness.location.iataCode^IATA location code.^Success^PAR^Success^""^Empty
DIM_LOCATION^IATA_CODE^$.attachments[*].payload.offerData.offer.context.pointOfBusiness.location.iataCode^""^attachments.payload.offerData.offer.context.pointOfBusiness.location.iataCode^""^Missing^""^Missing^""^Missing
DIM_LOCATION^NAME^$.data.currentImage.lifecycle.creation.location.iataCode^""^OrderOrderChangeEvent.currentImage.lifecycle.creation.location.iataCode^IATA location code^Success^PAR^Success^""^Empty
DIM_LOCATION^NAME^$.data.currentImage.lifecycle.lastUpdate.location.iataCode^""^OrderOrderChangeEvent.currentImage.lifecycle.lastUpdate.location.iataCode^IATA location code^Success^PAR^Success^""^Empty
DIM_LOCATION^NAME^$.data.currentImage.agreements.lifecycle.creation.location.iataCode^""^OrderOrderChangeEvent.currentImage.agreements.lifecycle.creation.location.iataCode^IATA location code^Success^PAR^Success^""^Empty
DIM_LOCATION^NAME^$.data.currentImage.agreements.lifecycle.lastUpdate.location.iataCode^""^OrderOrderChangeEvent.currentImage.agreements.lifecycle.lastUpdate.location.iataCode^IATA location code^Success^PAR^Success^""^Empty
DIM_LOCATION^NAME^$.data.currentImage.orderItems[*].lifecycle.creation.location.iataCode^""^OrderOrderLifecycle.creation.location.iataCode^IATA location code^Success^PAR^Success^""^Empty
DIM_LOCATION^NAME^$.data.currentImage.orderItems[*].lifecycle.lastUpdate.location.iataCode^""^OrderOrderLifecycle.lastUpdate.location.iataCode^IATA location code^Success^PAR^Success^""^Empty
DIM_LOCATION^NAME^$.data.currentImage.orderItems[*].services[*].lifecycle.creation.location.iataCode^""^OrderOrderChangeEvent.currentImage.orderItems.services.lifecycle.creation.location.iataCode^IATA location code^Success^PAR^Success^""^Empty
DIM_LOCATION^NAME^$.data.currentImage.orderItems[*].services[*].lifecycle.lastUpdate.location.iataCode^""^OrderOrderChangeEvent.currentImage.orderItems.services.lifecycle.lastUpdate.location.iataCode^IATA location code^Success^PAR^Success^""^Empty
DIM_LOCATION^NAME^$.data.currentImage.payments[*].lifecycle.creation.location.iataCode^""^OrderOrderChangeEvent.currentImage.payments.lifecycle.creation.location.iataCode^IATA location code^Success^PAR^Success^""^Empty
DIM_LOCATION^NAME^$.data.currentImage.payments[*].lifecycle.lastUpdate.location.iataCode^""^OrderOrderChangeEvent.currentImage.payments.lifecycle.lastUpdate.location.iataCode^IATA location code^Success^PAR^Success^""^Empty
DIM_LOCATION^NAME^$.data.currentImage.stakeholders[*].pointOfBusiness.location.iataCode^""^OrderStakeholderPointOfBusiness.location.iataCode^IATA location code.^Success^PAR^Success^""^Empty
DIM_LOCATION^NAME^$.attachments[*].payload.offerData.offer.context.pointOfBusiness.location.iataCode^""^attachments.payload.offerData.offer.context.pointOfBusiness.location.iataCode^""^Missing^""^Missing^""^Missing
DIM_LOCATION^COUNTRY_ISO2_CODE^$.data.currentImage.lifecycle.creation.location.address.countryCode.value^""^OrderOrderChangeEvent.currentImage.lifecycle.creation.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_LOCATION^COUNTRY_ISO2_CODE^$.data.currentImage.lifecycle.lastUpdate.location.address.countryCode.value^""^OrderOrderChangeEvent.currentImage.lifecycle.lastUpdate.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_LOCATION^COUNTRY_ISO2_CODE^$.data.currentImage.agreements.lifecycle.creation.location.address.countryCode.value^""^OrderOrderChangeEvent.currentImage.agreements.lifecycle.creation.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_LOCATION^COUNTRY_ISO2_CODE^$.data.currentImage.agreements.lifecycle.lastUpdate.location.address.countryCode.value^""^OrderOrderChangeEvent.currentImage.agreements.lifecycle.lastUpdate.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_LOCATION^COUNTRY_ISO2_CODE^$.data.currentImage.orderItems[*].lifecycle.creation.location.address.countryCode.value^""^OrderOrderLifecycle.creation.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_LOCATION^COUNTRY_ISO2_CODE^$.data.currentImage.orderItems[*].lifecycle.lastUpdate.location.address.countryCode.value^""^OrderOrderLifecycle.lastUpdate.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_LOCATION^COUNTRY_ISO2_CODE^$.data.currentImage.orderItems[*].services[*].lifecycle.creation.location.address.countryCode.value^""^OrderOrderChangeEvent.currentImage.orderItems.services.lifecycle.creation.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_LOCATION^COUNTRY_ISO2_CODE^$.data.currentImage.orderItems[*].services[*].lifecycle.lastUpdate.location.address.countryCode.value^""^OrderOrderChangeEvent.currentImage.orderItems.services.lifecycle.lastUpdate.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_LOCATION^COUNTRY_ISO2_CODE^$.data.currentImage.payments[*].lifecycle.creation.location.address.countryCode.value^""^OrderOrderChangeEvent.currentImage.payments.lifecycle.creation.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_LOCATION^COUNTRY_ISO2_CODE^$.data.currentImage.payments[*].lifecycle.lastUpdate.location.address.countryCode.value^""^OrderOrderChangeEvent.currentImage.payments.lifecycle.lastUpdate.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_LOCATION^COUNTRY_ISO2_CODE^$.data.currentImage.stakeholders[*].pointOfBusiness.location.countries[*].code.value^""^OrderStakeholderPointOfBusiness.location.countries.code^"Representation of the country code based on the ISO 3166-1 alpha-2.
For the current list of codes, see https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2"^Success^FR (for France)^Success^""^Empty
DIM_LOCATION^COUNTRY_ISO2_CODE^$.attachments[*].payload.offerData.offer.context.pointOfBusiness.location.countries[*].code.value^""^attachments.payload.offerData.offer.context.pointOfBusiness.location.countries.code^""^Missing^""^Missing^""^Missing
DIM_LOCATION^COUNTRY_ID^$.data.currentImage.lifecycle.creation.location.address.countryCode.value^hashS($.data.currentImage.lifecycle.creation.location.address.countryCode.value)^OrderOrderChangeEvent.currentImage.lifecycle.creation.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_LOCATION^COUNTRY_ID^$.data.currentImage.lifecycle.lastUpdate.location.address.countryCode.value^hashS($.data.currentImage.lifecycle.lastUpdate.location.address.countryCode.value)^OrderOrderChangeEvent.currentImage.lifecycle.lastUpdate.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_LOCATION^COUNTRY_ID^$.data.currentImage.agreements.lifecycle.creation.location.address.countryCode.value^hashS($.data.currentImage.agreements.lifecycle.creation.location.address.countryCode.value)^OrderOrderChangeEvent.currentImage.agreements.lifecycle.creation.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_LOCATION^COUNTRY_ID^$.data.currentImage.agreements.lifecycle.lastUpdate.location.address.countryCode.value^hashS($.data.currentImage.agreements.lifecycle.lastUpdate.location.address.countryCode.value)^OrderOrderChangeEvent.currentImage.agreements.lifecycle.lastUpdate.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_LOCATION^COUNTRY_ID^$.data.currentImage.orderItems[*].lifecycle.creation.location.address.countryCode.value^hashS($.data.currentImage.orderItems[*].lifecycle.creation.location.address.countryCode.value)^OrderOrderLifecycle.creation.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_LOCATION^COUNTRY_ID^$.data.currentImage.orderItems[*].lifecycle.lastUpdate.location.address.countryCode.value^hashS($.data.currentImage.orderItems[*].lifecycle.lastUpdate.location.address.countryCode.value)^OrderOrderLifecycle.lastUpdate.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_LOCATION^COUNTRY_ID^$.data.currentImage.orderItems[*].services[*].lifecycle.creation.location.address.countryCode.value^hashS($.data.currentImage.orderItems[*].services[*].lifecycle.creation.location.address.countryCode.value)^OrderOrderChangeEvent.currentImage.orderItems.services.lifecycle.creation.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_LOCATION^COUNTRY_ID^$.data.currentImage.orderItems[*].services[*].lifecycle.lastUpdate.location.address.countryCode.value^hashS($.data.currentImage.orderItems[*].services[*].lifecycle.lastUpdate.location.address.countryCode.value)^OrderOrderChangeEvent.currentImage.orderItems.services.lifecycle.lastUpdate.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_LOCATION^COUNTRY_ID^$.data.currentImage.payments[*].lifecycle.creation.location.address.countryCode.value^hashS($.data.currentImage.payments[*].lifecycle.creation.location.address.countryCode.value)^OrderOrderChangeEvent.currentImage.payments.lifecycle.creation.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_LOCATION^COUNTRY_ID^$.data.currentImage.payments[*].lifecycle.lastUpdate.location.address.countryCode.value^hashS($.data.currentImage.payments[*].lifecycle.lastUpdate.location.address.countryCode.value)^OrderOrderChangeEvent.currentImage.payments.lifecycle.lastUpdate.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_LOCATION^COUNTRY_ID^$.data.currentImage.stakeholders[*].pointOfBusiness.location.countries[*].code.value^hashS($.data.currentImage.stakeholders[*].pointOfBusiness.location.countries[*].code.value)^OrderStakeholderPointOfBusiness.location.countries.code^"Representation of the country code based on the ISO 3166-1 alpha-2.
For the current list of codes, see https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2"^Success^FR (for France)^Success^""^Empty
DIM_LOCATION^COUNTRY_ID^$.attachments[*].payload.offerData.offer.context.pointOfBusiness.location.countries[*].code.value^hashS($.attachments[*].payload.offerData.offer.context.pointOfBusiness.location.countries[*].code.value)^attachments.payload.offerData.offer.context.pointOfBusiness.location.countries.code^""^Missing^""^Missing^""^Missing
DIM_COUNTRY^COUNTRY_ID^$.data.currentImage.lifecycle.creation.location.address.countryCode.value^hashS($.data.currentImage.lifecycle.creation.location.address.countryCode.value)^OrderOrderChangeEvent.currentImage.lifecycle.creation.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_COUNTRY^COUNTRY_ID^$.data.currentImage.lifecycle.lastUpdate.location.address.countryCode.value^hashS($.data.currentImage.lifecycle.lastUpdate.location.address.countryCode.value)^OrderOrderChangeEvent.currentImage.lifecycle.lastUpdate.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_COUNTRY^COUNTRY_ID^$.data.currentImage.agreements.lifecycle.creation.location.address.countryCode.value^hashS($.data.currentImage.agreements.lifecycle.creation.location.address.countryCode.value)^OrderOrderChangeEvent.currentImage.agreements.lifecycle.creation.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_COUNTRY^COUNTRY_ID^$.data.currentImage.agreements.lifecycle.lastUpdate.location.address.countryCode.value^hashS($.data.currentImage.agreements.lifecycle.lastUpdate.location.address.countryCode.value)^OrderOrderChangeEvent.currentImage.agreements.lifecycle.lastUpdate.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_COUNTRY^COUNTRY_ID^$.data.currentImage.orderItems[*].lifecycle.creation.location.address.countryCode.value^hashS($.data.currentImage.orderItems[*].lifecycle.creation.location.address.countryCode.value)^OrderOrderLifecycle.creation.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_COUNTRY^COUNTRY_ID^$.data.currentImage.orderItems[*].lifecycle.lastUpdate.location.address.countryCode.value^hashS($.data.currentImage.orderItems[*].lifecycle.lastUpdate.location.address.countryCode.value)^OrderOrderLifecycle.lastUpdate.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_COUNTRY^COUNTRY_ID^$.data.currentImage.orderItems[*].services[*].lifecycle.creation.location.address.countryCode.value^hashS($.data.currentImage.orderItems[*].services[*].lifecycle.creation.location.address.countryCode.value)^OrderOrderChangeEvent.currentImage.orderItems.services.lifecycle.creation.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_COUNTRY^COUNTRY_ID^$.data.currentImage.orderItems[*].services[*].lifecycle.lastUpdate.location.address.countryCode.value^hashS($.data.currentImage.orderItems[*].services[*].lifecycle.lastUpdate.location.address.countryCode.value)^OrderOrderChangeEvent.currentImage.orderItems.services.lifecycle.lastUpdate.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_COUNTRY^COUNTRY_ID^$.data.currentImage.payments[*].lifecycle.creation.location.address.countryCode.value^hashS($.data.currentImage.payments[*].lifecycle.creation.location.address.countryCode.value)^OrderOrderChangeEvent.currentImage.payments.lifecycle.creation.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_COUNTRY^COUNTRY_ID^$.data.currentImage.payments[*].lifecycle.lastUpdate.location.address.countryCode.value^hashS($.data.currentImage.payments[*].lifecycle.lastUpdate.location.address.countryCode.value)^OrderOrderChangeEvent.currentImage.payments.lifecycle.lastUpdate.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_COUNTRY^COUNTRY_ID^$.data.currentImage.stakeholders.pointOfBusiness.location.countries[*].code.value^hashS($.data.currentImage.stakeholders.pointOfBusiness.location.countries[*].code.value)^OrderStakeholderPointOfBusiness.location.countries.code^"Representation of the country code based on the ISO 3166-1 alpha-2.
For the current list of codes, see https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2"^Success^FR (for France)^Success^""^Empty
DIM_COUNTRY^COUNTRY_ID^$.attachments[*].payload.offerData.offer.context.pointOfBusiness.location.countries[*].code.value^hashS($.attachments[*].payload.offerData.offer.context.pointOfBusiness.location.countries[*].code.value)^attachments.payload.offerData.offer.context.pointOfBusiness.location.countries.code^""^Missing^""^Missing^""^Missing
DIM_COUNTRY^ISO2_CODE^$.data.currentImage.lifecycle.creation.location.address.countryCode.value^""^OrderOrderChangeEvent.currentImage.lifecycle.creation.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_COUNTRY^ISO2_CODE^$.data.currentImage.lifecycle.lastUpdate.location.address.countryCode.value^""^OrderOrderChangeEvent.currentImage.lifecycle.lastUpdate.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_COUNTRY^ISO2_CODE^$.data.currentImage.agreements.lifecycle.creation.location.address.countryCode.value^""^OrderOrderChangeEvent.currentImage.agreements.lifecycle.creation.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_COUNTRY^ISO2_CODE^$.data.currentImage.agreements.lifecycle.lastUpdate.location.address.countryCode.value^""^OrderOrderChangeEvent.currentImage.agreements.lifecycle.lastUpdate.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_COUNTRY^ISO2_CODE^$.data.currentImage.orderItems[*].lifecycle.creation.location.address.countryCode.value^""^OrderOrderLifecycle.creation.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_COUNTRY^ISO2_CODE^$.data.currentImage.orderItems[*].lifecycle.lastUpdate.location.address.countryCode.value^""^OrderOrderLifecycle.lastUpdate.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_COUNTRY^ISO2_CODE^$.data.currentImage.orderItems[*].services[*].lifecycle.creation.location.address.countryCode.value^""^OrderOrderChangeEvent.currentImage.orderItems.services.lifecycle.creation.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_COUNTRY^ISO2_CODE^$.data.currentImage.orderItems[*].services[*].lifecycle.lastUpdate.location.address.countryCode.value^""^OrderOrderChangeEvent.currentImage.orderItems.services.lifecycle.lastUpdate.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_COUNTRY^ISO2_CODE^$.data.currentImage.payments[*].lifecycle.creation.location.address.countryCode.value^""^OrderOrderChangeEvent.currentImage.payments.lifecycle.creation.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_COUNTRY^ISO2_CODE^$.data.currentImage.payments[*].lifecycle.lastUpdate.location.address.countryCode.value^""^OrderOrderChangeEvent.currentImage.payments.lifecycle.lastUpdate.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_COUNTRY^ISO2_CODE^$.data.currentImage.stakeholders.pointOfBusiness.location.countries[*].code.value^""^OrderStakeholderPointOfBusiness.location.countries.code^"Representation of the country code based on the ISO 3166-1 alpha-2.
For the current list of codes, see https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2"^Success^FR (for France)^Success^""^Empty
DIM_COUNTRY^ISO2_CODE^$.attachments[*].payload.offerData.offer.context.pointOfBusiness.location.countries[*].code.value^""^attachments.payload.offerData.offer.context.pointOfBusiness.location.countries.code^""^Missing^""^Missing^""^Missing
DIM_COUNTRY^NAME^$.data.currentImage.lifecycle.creation.location.address.countryCode.value^""^OrderOrderChangeEvent.currentImage.lifecycle.creation.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_COUNTRY^NAME^$.data.currentImage.lifecycle.lastUpdate.location.address.countryCode.value^""^OrderOrderChangeEvent.currentImage.lifecycle.lastUpdate.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_COUNTRY^NAME^$.data.currentImage.agreements.lifecycle.creation.location.address.countryCode.value^""^OrderOrderChangeEvent.currentImage.agreements.lifecycle.creation.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_COUNTRY^NAME^$.data.currentImage.agreements.lifecycle.lastUpdate.location.address.countryCode.value^""^OrderOrderChangeEvent.currentImage.agreements.lifecycle.lastUpdate.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_COUNTRY^NAME^$.data.currentImage.orderItems[*].lifecycle.creation.location.address.countryCode.value^""^OrderOrderLifecycle.creation.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_COUNTRY^NAME^$.data.currentImage.orderItems[*].lifecycle.lastUpdate.location.address.countryCode.value^""^OrderOrderLifecycle.lastUpdate.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_COUNTRY^NAME^$.data.currentImage.orderItems[*].services[*].lifecycle.creation.location.address.countryCode.value^""^OrderOrderChangeEvent.currentImage.orderItems.services.lifecycle.creation.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_COUNTRY^NAME^$.data.currentImage.orderItems[*].services[*].lifecycle.lastUpdate.location.address.countryCode.value^""^OrderOrderChangeEvent.currentImage.orderItems.services.lifecycle.lastUpdate.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_COUNTRY^NAME^$.data.currentImage.payments[*].lifecycle.creation.location.address.countryCode.value^""^OrderOrderChangeEvent.currentImage.payments.lifecycle.creation.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_COUNTRY^NAME^$.data.currentImage.payments[*].lifecycle.lastUpdate.location.address.countryCode.value^""^OrderOrderChangeEvent.currentImage.payments.lifecycle.lastUpdate.location.address.countryCode^ISO 3166-1 country code^Success^""^Empty^""^Empty
DIM_COUNTRY^NAME^$.data.currentImage.stakeholders.pointOfBusiness.location.countries[*].code.value^""^OrderStakeholderPointOfBusiness.location.countries.code^"Representation of the country code based on the ISO 3166-1 alpha-2.
For the current list of codes, see https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2"^Success^FR (for France)^Success^""^Empty
DIM_COUNTRY^NAME^$.attachments[*].payload.offerData.offer.context.pointOfBusiness.location.countries[*].code.value^""^attachments.payload.offerData.offer.context.pointOfBusiness.location.countries.code^""^Missing^""^Missing^""^Missing
DIM_COMPANY^COMPANY_ID^$.data.currentImage.lifecycle.creation.user.company.name^hashS($.data.currentImage.lifecycle.creation.user.company.name)^OrderOrderChangeEvent.currentImage.lifecycle.creation.user.company.name^""^Missing^""^Missing^""^Missing
DIM_COMPANY^COMPANY_ID^$.data.currentImage.lifecycle.lastUpdate.user.company.name^hashS($.data.currentImage.lifecycle.lastUpdate.user.company.name)^OrderOrderChangeEvent.currentImage.lifecycle.lastUpdate.user.company.name^""^Missing^""^Missing^""^Missing
DIM_COMPANY^COMPANY_ID^$.attachments[*].payload.offerData.offerSet.offerStakeholders[*].company.name^hashS($.attachments[*].payload.offerData.offerSet.offerStakeholders[*].company.name)^attachments.payload.offerData.offerSet.offerStakeholders.company.name^Common name of the organization.^Success^""^Empty^""^Empty
DIM_COMPANY^COMPANY_ID^$.attachments[*].payload.offerData.offerSet.loyaltyAccounts[*].airlineLevel.company.name^hashS($.attachments[*].payload.offerData.offerSet.loyaltyAccounts[*].airlineLevel.company.name)^attachments.payload.offerData.offerSet.loyaltyAccounts.airlineLevel.company.name^""^Missing^""^Missing^""^Missing
DIM_COMPANY^COMPANY_ID^$.attachments[*].payload.offerData.offerSet.loyaltyAccounts[*].allianceLevel.company.name^hashS($.attachments[*].payload.offerData.offerSet.loyaltyAccounts[*].allianceLevel.company.name)^attachments.payload.offerData.offerSet.loyaltyAccounts.allianceLevel.company.name^""^Missing^""^Missing^""^Missing
DIM_COMPANY^CODE^$.data.currentImage.lifecycle.creation.user.company.name^""^OrderOrderChangeEvent.currentImage.lifecycle.creation.user.company.name^""^Missing^""^Missing^""^Missing
DIM_COMPANY^CODE^$.data.currentImage.lifecycle.lastUpdate.user.company.name^""^OrderOrderChangeEvent.currentImage.lifecycle.lastUpdate.user.company.name^""^Missing^""^Missing^""^Missing
DIM_COMPANY^CODE^$.attachments[*].payload.offerData.offerSet.offerStakeholders[*].company.name^""^attachments.payload.offerData.offerSet.offerStakeholders.company.name^Common name of the organization.^Success^""^Empty^""^Empty
DIM_COMPANY^CODE^$.attachments[*].payload.offerData.offerSet.loyaltyAccounts[*].airlineLevel.company.name^""^attachments.payload.offerData.offerSet.loyaltyAccounts.airlineLevel.company.name^""^Missing^""^Missing^""^Missing
DIM_COMPANY^CODE^$.attachments[*].payload.offerData.offerSet.loyaltyAccounts[*].allianceLevel.company.name^""^attachments.payload.offerData.offerSet.loyaltyAccounts.allianceLevel.company.name^""^Missing^""^Missing^""^Missing
DIM_COMPANY^NAME^$.data.currentImage.lifecycle.creation.user.company.name^""^OrderOrderChangeEvent.currentImage.lifecycle.creation.user.company.name^""^Missing^""^Missing^""^Missing
DIM_COMPANY^NAME^$.data.currentImage.lifecycle.lastUpdate.user.company.name^""^OrderOrderChangeEvent.currentImage.lifecycle.lastUpdate.user.company.name^""^Missing^""^Missing^""^Missing
DIM_COMPANY^NAME^$.attachments[*].payload.offerData.offerSet.offerStakeholders[*].company.name^""^attachments.payload.offerData.offerSet.offerStakeholders.company.name^Common name of the organization.^Success^""^Empty^""^Empty
DIM_COMPANY^NAME^$.attachments[*].payload.offerData.offerSet.loyaltyAccounts[*].airlineLevel.company.name^""^attachments.payload.offerData.offerSet.loyaltyAccounts.airlineLevel.company.name^""^Missing^""^Missing^""^Missing
DIM_COMPANY^NAME^$.attachments[*].payload.offerData.offerSet.loyaltyAccounts[*].allianceLevel.company.name^""^attachments.payload.offerData.offerSet.loyaltyAccounts.allianceLevel.company.name^""^Missing^""^Missing^""^Missing
FACT_OFFER_HISTO^INTERNAL_ZORDER^$.attachments[*].payload.offerData.offer.lifecycle.creation.dateTime - $.attachments[*].payload.offerData.offer.id^""^attachments.payload.offerData.offer.lifecycle.creation.dateTime^time of the change (ISO 8601)^Success^2018-11-19T04:54Z^Success^""^Empty
FACT_OFFER_HISTO^OFFER_ID^$.attachments[*].payload.offerData.offer.id^hashM($.attachments[*].payload.offerData.offer.id)^attachments.payload.offerData.offer.id^Offer ID^Success^""^Empty^""^Empty
FACT_OFFER_HISTO^REFERENCE_KEY^$.attachments[*].payload.offerData.offer.id^""^attachments.payload.offerData.offer.id^Offer ID^Success^""^Empty^""^Empty
FACT_OFFER_HISTO^OFFER_SET_IDENTIFIER^$.attachments[*].payload.offerData.offerSet.id^""^attachments.payload.offerData.offerSet.id^""^Empty^""^Empty^""^Empty
FACT_OFFER_HISTO^STATUS^$.attachments[*].payload.offerData.offer.lifecycle.status^""^attachments.payload.offerData.offer.lifecycle.status^"String enumerate describing the current condition of the object.

Available values are
- MANAGED: the object is being handled by the orchestration platform in the context of the proposal API
- DETERMINED: the object has been determined in its execution plan construction steps by the orchestration platform in the context of the proposal API
- ORCHESTRATED: the object has been fully defined and populated by the orchestration platform in the context of the proposal API
- SELECTED: the object is being handled by the orchestration platform in the context of the selection API
- VALIDATED: the object has been determined in its execution plan construction steps by the orchestration platform in the context of the selection API
- PRICED: the object has been fully defined and populated by the orchestration platform in the context of the selection API
- DELETED: the object had been flagged as deleted by the orchestration platform"^Success^EXPIRED|PRESENTED|CONFIRMED|BUILDING|COHERENT|MANAGED|DETERMINED|ORCHESTRATED|SELECTED|VALIDATED|PRICED|DELETED^Success^""^Empty
FACT_OFFER_HISTO^CREATION_DATETIME_OFFER^$.attachments[*].payload.offerData.offer.lifecycle.creation.dateTime^""^attachments.payload.offerData.offer.lifecycle.creation.dateTime^time of the change (ISO 8601)^Success^2018-11-19T04:54Z^Success^""^Empty
FACT_OFFER_HISTO^VERSION_OFFER^$.attachments[*].payload.offerData.offer.lifecycle.version^""^attachments.payload.offerData.offer.lifecycle.version^""^Empty^""^Empty^""^Empty
FACT_OFFER_HISTO^VALID_FROM^$.attachments[*].payload.offerData.offer.lifecycle.lastUpdate.dateTime^""^attachments.payload.offerData.offer.lifecycle.lastUpdate.dateTime^time of the change (ISO 8601)^Success^2018-11-19T04:54Z^Success^""^Empty
FACT_OFFER_ITEM_HISTO^INTERNAL_ZORDER^$.attachments[*].payload.offerData.offer.lifecycle.creation.dateTime - $.attachments[*].payload.offerData.offer.id^""^attachments.payload.offerData.offer.lifecycle.creation.dateTime^time of the change (ISO 8601)^Success^2018-11-19T04:54Z^Success^""^Empty
FACT_OFFER_ITEM_HISTO^OFFER_ITEM_ID^$.attachments[*].payload.offerData.offer.offerItems[*].id^hashM($.attachments[*].payload.offerData.offer.offerItems[*].id)^attachments.payload.offerData.offer.offerItems.id^The offer item identifier is unique locally to this offer, not globally unique. To globally identify a particular offer item, a full reference is needed, in the form of `/airline-offers/offers/{id1}/offer-items/{id2}`.^Success^""^Empty^""^Empty
FACT_OFFER_ITEM_HISTO^REFERENCE_KEY^$.attachments[*].payload.offerData.offer.offerItems[*].id^""^attachments.payload.offerData.offer.offerItems.id^The offer item identifier is unique locally to this offer, not globally unique. To globally identify a particular offer item, a full reference is needed, in the form of `/airline-offers/offers/{id1}/offer-items/{id2}`.^Success^""^Empty^""^Empty
FACT_OFFER_ITEM_HISTO^OFFER_ID^$.attachments[*].payload.offerData.offer.id^hashM($.attachments[*].payload.offerData.offer.id)^attachments.payload.offerData.offer.id^Offer ID^Success^""^Empty^""^Empty
FACT_OFFER_ITEM_HISTO^CREATION_DATETIME_OFFER^$.attachments[*].payload.offerData.offer.lifecycle.creation.dateTime^""^attachments.payload.offerData.offer.lifecycle.creation.dateTime^time of the change (ISO 8601)^Success^2018-11-19T04:54Z^Success^""^Empty
FACT_OFFER_ITEM_HISTO^VERSION_OFFER^$.attachments[*].payload.offerData.offer.lifecycle.version^""^attachments.payload.offerData.offer.lifecycle.version^""^Empty^""^Empty^""^Empty
FACT_OFFER_ITEM_HISTO^VALID_FROM^$.attachments[*].payload.offerData.offer.lifecycle.lastUpdate.dateTime^""^attachments.payload.offerData.offer.lifecycle.lastUpdate.dateTime^time of the change (ISO 8601)^Success^2018-11-19T04:54Z^Success^""^Empty
FACT_OFFER_ITEM_PRICE_HISTO^INTERNAL_ZORDER^$.attachments[*].payload.offerData.offer.lifecycle.creation.dateTime - $.attachments[*].payload.offerData.offer.id^""^attachments.payload.offerData.offer.lifecycle.creation.dateTime^time of the change (ISO 8601)^Success^2018-11-19T04:54Z^Success^""^Empty
FACT_OFFER_ITEM_PRICE_HISTO^OFFER_ITEM_PRICE_ID^$.attachments[*].payload.offerData.offer.offerItems[*].id - $.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].type^hashM($.attachments[*].payload.offerData.offer.offerItems[*].id - $.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].type)^attachments.payload.offerData.offer.offerItems.id^The offer item identifier is unique locally to this offer, not globally unique. To globally identify a particular offer item, a full reference is needed, in the form of `/airline-offers/offers/{id1}/offer-items/{id2}`.^Success^""^Empty^""^Empty
FACT_OFFER_ITEM_PRICE_HISTO^REFERENCE_KEY^$.attachments[*].payload.offerData.offer.offerItems[*].id - $.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].type^""^attachments.payload.offerData.offer.offerItems.id^The offer item identifier is unique locally to this offer, not globally unique. To globally identify a particular offer item, a full reference is needed, in the form of `/airline-offers/offers/{id1}/offer-items/{id2}`.^Success^""^Empty^""^Empty
FACT_OFFER_ITEM_PRICE_HISTO^PRICE_TYPE^$.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].type^""^attachments.payload.offerData.offer.offerItems.priceInformation.amountsCollections.type^""^Missing^""^Missing^""^Missing
FACT_OFFER_ITEM_PRICE_HISTO^AMOUNT_TYPE^$.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].totalAmount.type^""^attachments.payload.offerData.offer.offerItems.priceInformation.amountsCollections.totalAmount.type^""^Missing^""^Missing^""^Missing
FACT_OFFER_ITEM_PRICE_HISTO^AMOUNT_VALUE_ORIGINAL^$.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].totalAmount.value - $.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].totalAmount.decimalPlaces^element_at(split($.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].totalAmount.value - $.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].totalAmount.decimalPlaces,'-'),1)/(10*element_at(split($.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].totalAmount.value - $.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].totalAmount.decimalPlaces,'-'),2))^attachments.payload.offerData.offer.offerItems.priceInformation.amountsCollections.totalAmount^""^Missing^""^Missing^""^Missing
FACT_OFFER_ITEM_PRICE_HISTO^AMOUNT_CURRENCY_ORIGINAL^$.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].totalAmount.currency^""^attachments.payload.offerData.offer.offerItems.priceInformation.amountsCollections.totalAmount.currency^""^Missing^""^Missing^""^Missing
FACT_OFFER_ITEM_PRICE_HISTO^AMOUNT_VALUE^$.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].totalAmount.value - $.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].totalAmount.decimalPlaces^element_at(split($.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].totalAmount.value - $.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].totalAmount.decimalPlaces,'-'),1)/(10*element_at(split($.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].totalAmount.value - $.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].totalAmount.decimalPlaces,'-'),2))^attachments.payload.offerData.offer.offerItems.priceInformation.amountsCollections.totalAmount^""^Missing^""^Missing^""^Missing
FACT_OFFER_ITEM_PRICE_HISTO^AMOUNT_CURRENCY^$.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].totalAmount.currency^""^attachments.payload.offerData.offer.offerItems.priceInformation.amountsCollections.totalAmount.currency^""^Missing^""^Missing^""^Missing
FACT_OFFER_ITEM_PRICE_HISTO^OFFER_ID^$.attachments[*].payload.offerData.offer.id^hashM($.attachments[*].payload.offerData.offer.id)^attachments.payload.offerData.offer.id^Offer ID^Success^""^Empty^""^Empty
FACT_OFFER_ITEM_PRICE_HISTO^CREATION_DATETIME_OFFER^$.attachments[*].payload.offerData.offer.lifecycle.creation.dateTime^""^attachments.payload.offerData.offer.lifecycle.creation.dateTime^time of the change (ISO 8601)^Success^2018-11-19T04:54Z^Success^""^Empty
FACT_OFFER_ITEM_PRICE_HISTO^VERSION_OFFER^$.attachments[*].payload.offerData.offer.lifecycle.version^""^attachments.payload.offerData.offer.lifecycle.version^""^Empty^""^Empty^""^Empty
FACT_OFFER_ITEM_PRICE_HISTO^VALID_FROM^$.attachments[*].payload.offerData.offer.lifecycle.lastUpdate.dateTime^""^attachments.payload.offerData.offer.lifecycle.lastUpdate.dateTime^time of the change (ISO 8601)^Success^2018-11-19T04:54Z^Success^""^Empty
FACT_OFFER_ITEM_PRICE_COMPOSITION_HISTO^INTERNAL_ZORDER^$.attachments[*].payload.offerData.offer.lifecycle.creation.dateTime - $.attachments[*].payload.offerData.offer.id^""^attachments.payload.offerData.offer.lifecycle.creation.dateTime^time of the change (ISO 8601)^Success^2018-11-19T04:54Z^Success^""^Empty
FACT_OFFER_ITEM_PRICE_COMPOSITION_HISTO^OFFER_ITEM_PRICE_COMPOSITION_ID^$.attachments[*].payload.offerData.offer.offerItems[*].id - $.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].type - $.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].totalAmount.composition[*].type^hashM($.attachments[*].payload.offerData.offer.offerItems[*].id - $.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].type - $.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].totalAmount.composition[*].type)^attachments.payload.offerData.offer.offerItems.id^The offer item identifier is unique locally to this offer, not globally unique. To globally identify a particular offer item, a full reference is needed, in the form of `/airline-offers/offers/{id1}/offer-items/{id2}`.^Success^""^Empty^""^Empty
FACT_OFFER_ITEM_PRICE_COMPOSITION_HISTO^REFERENCE_KEY^$.attachments[*].payload.offerData.offer.offerItems[*].id - $.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].type - $.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].totalAmount.composition[*].type^""^attachments.payload.offerData.offer.offerItems.id^The offer item identifier is unique locally to this offer, not globally unique. To globally identify a particular offer item, a full reference is needed, in the form of `/airline-offers/offers/{id1}/offer-items/{id2}`.^Success^""^Empty^""^Empty
FACT_OFFER_ITEM_PRICE_COMPOSITION_HISTO^AMOUNT_TYPE^$.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].totalAmount.composition[*].type^""^attachments.payload.offerData.offer.offerItems.priceInformation.amountsCollections.totalAmount.composition.type^""^Missing^""^Missing^""^Missing
FACT_OFFER_ITEM_PRICE_COMPOSITION_HISTO^AMOUNT_VALUE_ORIGINAL^$.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].totalAmount.composition[*].value - $.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].totalAmount.composition[*].decimalPlaces^element_at(split($.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].totalAmount.composition[*].value - $.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].totalAmount.composition[*].decimalPlaces,'-'),1)/(10*element_at(split($.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].totalAmount.composition[*].value - $.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].totalAmount.composition[*].decimalPlaces,'-'),2))^attachments.payload.offerData.offer.offerItems.priceInformation.amountsCollections.totalAmount.composition^""^Missing^""^Missing^""^Missing
FACT_OFFER_ITEM_PRICE_COMPOSITION_HISTO^AMOUNT_CURRENCY_ORIGINAL^$.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].totalAmount.composition[*].currency^""^attachments.payload.offerData.offer.offerItems.priceInformation.amountsCollections.totalAmount.composition.currency^""^Missing^""^Missing^""^Missing
FACT_OFFER_ITEM_PRICE_COMPOSITION_HISTO^AMOUNT_VALUE^$.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].totalAmount.composition[*].value - $.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].totalAmount.composition[*].decimalPlaces^element_at(split($.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].totalAmount.composition[*].value - $.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].totalAmount.composition[*].decimalPlaces,'-'),1)/(10*element_at(split($.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].totalAmount.composition[*].value - $.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].totalAmount.composition[*].decimalPlaces,'-'),2))^attachments.payload.offerData.offer.offerItems.priceInformation.amountsCollections.totalAmount.composition^""^Missing^""^Missing^""^Missing
FACT_OFFER_ITEM_PRICE_COMPOSITION_HISTO^AMOUNT_CURRENCY^$.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].totalAmount.composition[*].currency^""^attachments.payload.offerData.offer.offerItems.priceInformation.amountsCollections.totalAmount.composition.currency^""^Missing^""^Missing^""^Missing
FACT_OFFER_ITEM_PRICE_COMPOSITION_HISTO^OFFER_ITEM_PRICE_ID^$.attachments[*].payload.offerData.offer.offerItems[*].id - $.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].type^hashM($.attachments[*].payload.offerData.offer.offerItems[*].id - $.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].type)^attachments.payload.offerData.offer.offerItems.id^The offer item identifier is unique locally to this offer, not globally unique. To globally identify a particular offer item, a full reference is needed, in the form of `/airline-offers/offers/{id1}/offer-items/{id2}`.^Success^""^Empty^""^Empty
FACT_OFFER_ITEM_PRICE_COMPOSITION_HISTO^OFFER_ID^$.attachments[*].payload.offerData.offer.id^hashM($.attachments[*].payload.offerData.offer.id)^attachments.payload.offerData.offer.id^Offer ID^Success^""^Empty^""^Empty
FACT_OFFER_ITEM_PRICE_COMPOSITION_HISTO^CREATION_DATETIME_OFFER^$.attachments[*].payload.offerData.offer.lifecycle.creation.dateTime^""^attachments.payload.offerData.offer.lifecycle.creation.dateTime^time of the change (ISO 8601)^Success^2018-11-19T04:54Z^Success^""^Empty
FACT_OFFER_ITEM_PRICE_COMPOSITION_HISTO^VERSION_OFFER^$.attachments[*].payload.offerData.offer.lifecycle.version^""^attachments.payload.offerData.offer.lifecycle.version^""^Empty^""^Empty^""^Empty
FACT_OFFER_ITEM_PRICE_COMPOSITION_HISTO^VALID_FROM^$.attachments[*].payload.offerData.offer.lifecycle.lastUpdate.dateTime^""^attachments.payload.offerData.offer.lifecycle.lastUpdate.dateTime^time of the change (ISO 8601)^Success^2018-11-19T04:54Z^Success^""^Empty
FACT_OFFER_ITEM_RETAILING_HISTO^INTERNAL_ZORDER^$.attachments[*].payload.offerData.offer.lifecycle.creation.dateTime - $.attachments[*].payload.offerData.offer.id^""^attachments.payload.offerData.offer.lifecycle.creation.dateTime^time of the change (ISO 8601)^Success^2018-11-19T04:54Z^Success^""^Empty
FACT_OFFER_ITEM_RETAILING_HISTO^OFFER_ITEM_RETAILING_ID^$.attachments[*].payload.offerData.offer.offerItems[*].id - $.attachments[*].payload.offerData.offer.offerItems[*].retailing.branding.fareFamilies[*].id^hashM($.attachments[*].payload.offerData.offer.offerItems[*].id - $.attachments[*].payload.offerData.offer.offerItems[*].retailing.branding.fareFamilies[*].id)^attachments.payload.offerData.offer.offerItems.id^The offer item identifier is unique locally to this offer, not globally unique. To globally identify a particular offer item, a full reference is needed, in the form of `/airline-offers/offers/{id1}/offer-items/{id2}`.^Success^""^Empty^""^Empty
FACT_OFFER_ITEM_RETAILING_HISTO^REFERENCE_KEY^$.attachments[*].payload.offerData.offer.offerItems[*].id - $.attachments[*].payload.offerData.offer.offerItems[*].retailing.branding.fareFamilies[*].id^""^attachments.payload.offerData.offer.offerItems.id^The offer item identifier is unique locally to this offer, not globally unique. To globally identify a particular offer item, a full reference is needed, in the form of `/airline-offers/offers/{id1}/offer-items/{id2}`.^Success^""^Empty^""^Empty
FACT_OFFER_ITEM_RETAILING_HISTO^IDENTIFIER^$.attachments[*].payload.offerData.offer.offerItems[*].retailing.branding.fareFamilies[*].id^""^attachments.payload.offerData.offer.offerItems.retailing.branding.fareFamilies.id^Fare family item identifier.^Success^""^Empty^""^Empty
FACT_OFFER_ITEM_RETAILING_HISTO^CODE^$.attachments[*].payload.offerData.offer.offerItems[*].retailing.branding.fareFamilies[*].code^""^attachments.payload.offerData.offer.offerItems.retailing.branding.fareFamilies.code^Defines code of Fare Family^Success^""^Empty^""^Empty
FACT_OFFER_ITEM_RETAILING_HISTO^OWNER^$.attachments[*].payload.offerData.offer.offerItems[*].retailing.branding.fareFamilies[*].owner^""^attachments.payload.offerData.offer.offerItems.retailing.branding.fareFamilies.owner^Defines the Company owner of Fare Family^Success^""^Empty^""^Empty
FACT_OFFER_ITEM_RETAILING_HISTO^RANKING^$.attachments[*].payload.offerData.offer.offerItems[*].retailing.branding.fareFamilies[*].ranking^""^attachments.payload.offerData.offer.offerItems.retailing.branding.fareFamilies.ranking^Defines the Ranking of the fare family, it s used for extented pricing record^Success^""^Empty^""^Empty
FACT_OFFER_ITEM_RETAILING_HISTO^NAME^$.attachments[*].payload.offerData.offer.offerItems[*].retailing.branding.fareFamilies[*].name^""^attachments.payload.offerData.offer.offerItems.retailing.branding.fareFamilies.name^Defines the name of the fare family^Success^FB ECONOMY LIGHT^Success^""^Empty
FACT_OFFER_ITEM_RETAILING_HISTO^COMMERCIAL_FARE_FAMILY^$.attachments[*].payload.offerData.offer.offerItems[*].retailing.branding.fareFamilies[*].commercialFareFamily^""^attachments.payload.offerData.offer.offerItems.retailing.branding.fareFamilies.commercialFareFamily^A Commercial Fare Family code is used to target one or several Fare Families. The same Commercial Fare Family can target different sets of fare families depending on the geographical market and the point of sale of the request.^Success^DCSREVCHG has three set of fare familes (WGADCS, ANYDCS, BUSDCS)^Success^""^Empty
FACT_OFFER_ITEM_RETAILING_HISTO^FARE_FAMILY_DESCRIPTION^$.attachments[*].payload.offerData.offer.offerItems[*].retailing.branding.fareFamilies[*].fareFamilyDescription^""^attachments.payload.offerData.offer.offerItems.retailing.branding.fareFamilies.fareFamilyDescription^Description of the fare family.^Success^""^Empty^""^Empty
FACT_OFFER_ITEM_RETAILING_HISTO^OFFER_ID^$.attachments[*].payload.offerData.offer.id^hashM($.attachments[*].payload.offerData.offer.id)^attachments.payload.offerData.offer.id^Offer ID^Success^""^Empty^""^Empty
FACT_OFFER_ITEM_RETAILING_HISTO^CREATION_DATETIME_OFFER^$.attachments[*].payload.offerData.offer.lifecycle.creation.dateTime^""^attachments.payload.offerData.offer.lifecycle.creation.dateTime^time of the change (ISO 8601)^Success^2018-11-19T04:54Z^Success^""^Empty
FACT_OFFER_ITEM_RETAILING_HISTO^VERSION_OFFER^$.attachments[*].payload.offerData.offer.lifecycle.version^""^attachments.payload.offerData.offer.lifecycle.version^""^Empty^""^Empty^""^Empty
FACT_OFFER_ITEM_RETAILING_HISTO^VALID_FROM^$.attachments[*].payload.offerData.offer.lifecycle.lastUpdate.dateTime^""^attachments.payload.offerData.offer.lifecycle.lastUpdate.dateTime^time of the change (ISO 8601)^Success^2018-11-19T04:54Z^Success^""^Empty
FACT_OFFER_ITEM_OFFICE_HISTO^INTERNAL_ZORDER^$.attachments[*].payload.offerData.offer.lifecycle.creation.dateTime - $.attachments[*].payload.offerData.offer.id^""^attachments.payload.offerData.offer.lifecycle.creation.dateTime^time of the change (ISO 8601)^Success^2018-11-19T04:54Z^Success^""^Empty
FACT_OFFER_ITEM_OFFICE_HISTO^OFFER_ITEM_OFFICE_ID^$.attachments[*].payload.offerData.offer.offerItems[*].id - $.attachments[*].payload.offerData.offer.offerItems[*].offices[*].officeDetails.id^hashM($.attachments[*].payload.offerData.offer.offerItems[*].id - $.attachments[*].payload.offerData.offer.offerItems[*].offices[*].officeDetails.id)^attachments.payload.offerData.offer.offerItems.id^The offer item identifier is unique locally to this offer, not globally unique. To globally identify a particular offer item, a full reference is needed, in the form of `/airline-offers/offers/{id1}/offer-items/{id2}`.^Success^""^Empty^""^Empty
FACT_OFFER_ITEM_OFFICE_HISTO^REFERENCE_KEY^$.attachments[*].payload.offerData.offer.offerItems[*].id - $.attachments[*].payload.offerData.offer.offerItems[*].offices[*].officeDetails.id^""^attachments.payload.offerData.offer.offerItems.id^The offer item identifier is unique locally to this offer, not globally unique. To globally identify a particular offer item, a full reference is needed, in the form of `/airline-offers/offers/{id1}/offer-items/{id2}`.^Success^""^Empty^""^Empty
FACT_OFFER_ITEM_OFFICE_HISTO^IDENTIFIER^$.attachments[*].payload.offerData.offer.offerItems[*].offices[*].officeDetails.id^""^attachments.payload.offerData.offer.offerItems.offices.officeDetails.id^""^Missing^""^Missing^""^Missing
FACT_OFFER_ITEM_OFFICE_HISTO^IATA_NUMBER^$.attachments[*].payload.offerData.offer.offerItems[*].offices[*].officeDetails.IATANumber^""^attachments.payload.offerData.offer.offerItems.offices.officeDetails.IATANumber^""^Missing^""^Missing^""^Missing
FACT_OFFER_ITEM_OFFICE_HISTO^OFFICE_TYPE^$.attachments[*].payload.offerData.offer.offerItems[*].offices[*].officeType^""^attachments.payload.offerData.offer.offerItems.offices.officeType^""^Missing^""^Missing^""^Missing
FACT_OFFER_ITEM_OFFICE_HISTO^COUNTRY_CODE^$.attachments[*].payload.offerData.offer.offerItems[*].offices[*].countryCode^""^attachments.payload.offerData.offer.offerItems.offices.countryCode^""^Missing^""^Missing^""^Missing
FACT_OFFER_ITEM_OFFICE_HISTO^CITY_CODE^$.attachments[*].payload.offerData.offer.offerItems[*].offices[*].trueCityCode^""^attachments.payload.offerData.offer.offerItems.offices.trueCityCode^""^Missing^""^Missing^""^Missing
FACT_OFFER_ITEM_OFFICE_HISTO^IATA_LINE_NUMBER^$.attachments[*].payload.offerData.offer.offerItems[*].offices[*].iataLineNumber^""^attachments.payload.offerData.offer.offerItems.offices.iataLineNumber^""^Missing^""^Missing^""^Missing
FACT_OFFER_ITEM_OFFICE_HISTO^ERSP_NUMBER^$.attachments[*].payload.offerData.offer.offerItems[*].offices[*].erspNumber^""^attachments.payload.offerData.offer.offerItems.offices.erspNumber^""^Missing^""^Missing^""^Missing
FACT_OFFER_ITEM_OFFICE_HISTO^ARC_NUMBER^$.attachments[*].payload.offerData.offer.offerItems[*].offices[*].arcNumber^""^attachments.payload.offerData.offer.offerItems.offices.arcNumber^""^Missing^""^Missing^""^Missing
FACT_OFFER_ITEM_OFFICE_HISTO^OFFER_ID^$.attachments[*].payload.offerData.offer.id^hashM($.attachments[*].payload.offerData.offer.id)^attachments.payload.offerData.offer.id^Offer ID^Success^""^Empty^""^Empty
FACT_OFFER_ITEM_OFFICE_HISTO^CREATION_DATETIME_OFFER^$.attachments[*].payload.offerData.offer.lifecycle.creation.dateTime^""^attachments.payload.offerData.offer.lifecycle.creation.dateTime^time of the change (ISO 8601)^Success^2018-11-19T04:54Z^Success^""^Empty
FACT_OFFER_ITEM_OFFICE_HISTO^VERSION_OFFER^$.attachments[*].payload.offerData.offer.lifecycle.version^""^attachments.payload.offerData.offer.lifecycle.version^""^Empty^""^Empty^""^Empty
FACT_OFFER_ITEM_OFFICE_HISTO^VALID_FROM^$.attachments[*].payload.offerData.offer.lifecycle.lastUpdate.dateTime^""^attachments.payload.offerData.offer.lifecycle.lastUpdate.dateTime^time of the change (ISO 8601)^Success^2018-11-19T04:54Z^Success^""^Empty
FACT_OFFER_ITEM_AIR_BOUNDS_HISTO^INTERNAL_ZORDER^$.attachments[*].payload.offerData.offer.lifecycle.creation.dateTime - $.attachments[*].payload.offerData.offer.id^""^attachments.payload.offerData.offer.lifecycle.creation.dateTime^time of the change (ISO 8601)^Success^2018-11-19T04:54Z^Success^""^Empty
FACT_OFFER_ITEM_AIR_BOUNDS_HISTO^OFFER_ITEM_AIR_BOUNDS_ID^$.attachments[*].payload.offerData.offer.offerItems[*].id - $.attachments[*].payload.offerData.offer.offerItems[*].airBounds[*].id^hashM($.attachments[*].payload.offerData.offer.offerItems[*].id - $.attachments[*].payload.offerData.offer.offerItems[*].airBounds[*].id)^attachments.payload.offerData.offer.offerItems.id^The offer item identifier is unique locally to this offer, not globally unique. To globally identify a particular offer item, a full reference is needed, in the form of `/airline-offers/offers/{id1}/offer-items/{id2}`.^Success^""^Empty^""^Empty
FACT_OFFER_ITEM_AIR_BOUNDS_HISTO^REFERENCE_KEY^$.attachments[*].payload.offerData.offer.offerItems[*].id - $.attachments[*].payload.offerData.offer.offerItems[*].airBounds[*].id^""^attachments.payload.offerData.offer.offerItems.id^The offer item identifier is unique locally to this offer, not globally unique. To globally identify a particular offer item, a full reference is needed, in the form of `/airline-offers/offers/{id1}/offer-items/{id2}`.^Success^""^Empty^""^Empty
FACT_OFFER_ITEM_AIR_BOUNDS_HISTO^BOARD_POINT^$.attachments[*].payload.offerData.offer.offerItems[*].airBounds[*].boardPointIataCode^""^attachments.payload.offerData.offer.offerItems.airBounds.boardPointIataCode^IATA code of departure airport corresponding to the first leg of the bound^Success^""^Empty^""^Empty
FACT_OFFER_ITEM_AIR_BOUNDS_HISTO^OFF_POINT^$.attachments[*].payload.offerData.offer.offerItems[*].airBounds[*].offPointIataCode^""^attachments.payload.offerData.offer.offerItems.airBounds.offPointIataCode^IATA code of arrival airport corresponding to the last leg of the bound^Success^""^Empty^""^Empty
FACT_OFFER_ITEM_AIR_BOUNDS_HISTO^ELAPSED_TRAVELING_TIME^$.attachments[*].payload.offerData.offer.offerItems[*].airBounds[*].elapsedTravelingTime^""^attachments.payload.offerData.offer.offerItems.airBounds.elapsedTravelingTime^Traveling time the bound in minutes^Success^""^Empty^""^Empty
FACT_OFFER_ITEM_AIR_BOUNDS_HISTO^BOUND_POSITION^$.attachments[*].payload.offerData.offer.offerItems[*].airBounds[*].boundPosition^""^attachments.payload.offerData.offer.offerItems.airBounds.boundPosition^Rank of the bound within the itinerary^Success^""^Empty^""^Empty
FACT_OFFER_ITEM_AIR_BOUNDS_HISTO^OFFER_ID^$.attachments[*].payload.offerData.offer.id^hashM($.attachments[*].payload.offerData.offer.id)^attachments.payload.offerData.offer.id^Offer ID^Success^""^Empty^""^Empty
FACT_OFFER_ITEM_AIR_BOUNDS_HISTO^CREATION_DATETIME_OFFER^$.attachments[*].payload.offerData.offer.lifecycle.creation.dateTime^""^attachments.payload.offerData.offer.lifecycle.creation.dateTime^time of the change (ISO 8601)^Success^2018-11-19T04:54Z^Success^""^Empty
FACT_OFFER_ITEM_AIR_BOUNDS_HISTO^VERSION_OFFER^$.attachments[*].payload.offerData.offer.lifecycle.version^""^attachments.payload.offerData.offer.lifecycle.version^""^Empty^""^Empty^""^Empty
FACT_OFFER_ITEM_AIR_BOUNDS_HISTO^VALID_FROM^$.attachments[*].payload.offerData.offer.lifecycle.lastUpdate.dateTime^""^attachments.payload.offerData.offer.lifecycle.lastUpdate.dateTime^time of the change (ISO 8601)^Success^2018-11-19T04:54Z^Success^""^Empty
