package com.amadeus.airbi.json2star.common.views.generators

import com.amadeus.airbi.json2star.common._
import com.amadeus.airbi.json2star.common.views.generators.MarkdownDSL.{Converters, DocElem}
import com.amadeus.airbi.json2star.common.views.lookups.YamlLookup
import com.amadeus.airbi.rawvault.common.application.config.ModelConfigLoader
import com.amadeus.airbi.rawvault.common.config.GDPRZone
import com.typesafe.scalalogging.Logger
import org.slf4j.LoggerFactory

import scala.collection.immutable.ListSet

/** It generates the Markdown Content to create the Confluence Documentation from the mapping file
  * It supports the generation of Table Overview and Table Details with fields
  */
object DocGenerator {

  private def EmptyValue: String = ""
  private val HistoSuffix = "_HISTO"

  case class Config(
    modelConfFile: String,
    tablesSelectors: Set[String],
    disabledStackableAddons: Set[String],
    yamlFilePath: Option[String] = None,
    filterHistoColumns: Boolean,
    filterColumnsByRegex: Option[String],
    originOldPrefix: String,
    originNewPrefix: String
  )

  lazy val logger: Logger = Logger(LoggerFactory.getLogger(getClass.getName))

  /** Generate doc from the config
    *
    * @param config the configuration of the DocGenerator
    * @return the documentation string
    */
  def from(config: DocGenerator.Config): String = {
    val tablesConfig =
      ModelConfigLoader.defaultLoad(config.modelConfFile, config.tablesSelectors, config.disabledStackableAddons)
    val jsonToYamlPaths = tablesConfig.jsonToYamlPaths
    val tables = TablesDef.consolidate(tablesConfig)
    s"""
### Tables overview
#### Fact Tables
${renderTableOverview(tables.factTables, includeSubdomain = true)}
#### Dimension Tables
${renderTableOverview(tables.dimTables)}
#### Association Tables
${renderTableOverview(tables.assoTables)}
### Tables details with fields
#### Fact Tables Fields
${renderTableDetails(tables.factTables, tables, config, jsonToYamlPaths)}
#### Dimension Tables Fields
${renderTableDetails(tables.dimTables, tables, config, jsonToYamlPaths)}
#### Association Tables Fields
${renderTableDetails(tables.assoTables, tables, config, jsonToYamlPaths)}
"""
  }

  private def renderTableOverview(tables: List[TableDef], includeSubdomain: Boolean = false): String = {
    if (includeSubdomain) {
      // Auto-completes the subdomains on the provided tables (by inferring the subdomains of the latest tables from the histo tables)
      val tablesWithSubdomain = tables.map(t => {
        if (t.schema.subdomain.isEmpty && !t.schema.name.endsWith(HistoSuffix)) {
          val histo = tables.find(_.schema.name.equals(t.schema.name + HistoSuffix))
          (t, histo.flatMap(_.schema.subdomain))
        } else {
          (t, t.schema.subdomain)
        }
      })
      // Gets a list of subdomains in functional order (as they appear in the model)
      val sortedSubdomains = tables.map(_.schema.subdomain).flatten.to[ListSet].to[List]
      // Sorts the tables by subdomain first (in functional order) and then by name
      tablesWithSubdomain
        .sortBy { case (table, subdomain) => {
          val subdomainIndex = subdomain match {
            case Some(subdomain) => sortedSubdomains.indexOf(subdomain)
            case None => Integer.MAX_VALUE // Puts tables without a subdomain at the end
          }
          (subdomainIndex, table.schema.name)
        }}
        .map { case (table, subdomain) => overviewRowDocFrom(table, includeSubdomain = true, subdomain) }
        .toTableMd
    } else {
      tables.sortBy(_.schema.name).map(overviewRowDocFrom(_)).toTableMd
    }
  }

  private def renderTableDetails(
    tables: List[TableDef],
    allTables: TablesDef,
    config: Config,
    jsonToYamlPaths: Map[String, String]
  ): String = {
    tables.sortBy(_.table.name).flatMap(h => detailsTableDocFrom(h, allTables, config, jsonToYamlPaths)).toTableMd
  }

  /** Generate the content of Table Overview
    *
    * @param td a table definition
    * @param includeSubdomain a boolean indicating if the subdomain is to be displayed
    * @param subdomain the subdomain of the table (optional)
    * @return a list of DocElem
    */
  def overviewRowDocFrom(td: TableDef, includeSubdomain: Boolean = false, subdomain: Option[String] = None): List[DocElem] = {
    val docElems = List(
      DocElem("Table", td.schema.name),
      DocElem("Description", td.schema.description.flatMap(_.description).getOrElse(EmptyValue)),
      DocElem("GDPR Zone", td.schema.gdprZone.map(GDPRZone.toString).getOrElse(EmptyValue)),
      DocElem("Granularity", td.schema.description.flatMap(_.granularity).getOrElse(EmptyValue)),
      DocElem("Primary key", td.schema.keyColumns.map(_.name).mkString("-"))
    )
    val row = if (includeSubdomain) {
      docElems :+ DocElem("Subdomain", subdomain.getOrElse(""))
    } else {
      docElems
    }
    row
  }

  /** Generate the content of Table Details Section
    *
    * @param td a table definition
    * @param allTd all Tables in the domain
    * @param config the configuration of the DocGenerator
    * @param jsonToYamlPaths a Map to convert JSON path to Metadata YAML path
    * @return a list of list of DocElem
    */
  private def detailsTableDocFrom(
    td: TableDef,
    allTd: TablesDef,
    config: Config,
    jsonToYamlPaths: Map[String, String]
  ): List[List[DocElem]] = {
    // Load YAML Metadata config
    val yamlLookup = config.yamlFilePath.map(fp => YamlLookup(fp, jsonToYamlPaths))

    val schemaCols = if (config.filterHistoColumns) filterSchemaColumns(td, allTd) else td.schema.columns
    val docSchemaCols = config.filterColumnsByRegex match {
      case Some(colToExcludePattern) => schemaCols.filterNot(_.name.matches(colToExcludePattern))
      case None => schemaCols
    }
    val rows = docSchemaCols.map { col =>
      val isMandatoryValue = if (col.belongsToPK) {
        "PK"
      } else if (col.isMandatory) { "Y" }
      else {
        "N"
      }

      val colDetails = ColumnDetails.from(yamlLookup, col, td.schema.name)
      val gdprZone = col.consolidatedGdprZone(td.schema.name).map(GDPRZone.toString).getOrElse(EmptyValue)
      val gdprZoneValue = concatenateDoc(gdprZone +: colDetails.piiTypes)
      List(
        DocElem("Table", td.schema.name),
        DocElem("Field", col.name),
        DocElem("Description", concatenateDoc(colDetails.descriptions)),
        DocElem("Example", concatenateDoc(colDetails.examples)),
        DocElem("GDPR Zone", gdprZoneValue),
        DocElem("Format", DeltaSchemaGenerator.toTableType(col.columnType)),
        DocElem("Mandatory", isMandatoryValue),
        DocElem("In FK to", col.fk.getOrElse(Seq()).map(_.table).mkString(", ")),
        DocElem("Source Path", generateOrigin(col, config.originOldPrefix, config.originNewPrefix))
      )
    }
    rows
  }

  /** Filter out columns from the Historical table to avoid repeat columns present also in the Latest table
    *
    * If a table is Historical --> keep columns not present in its Latest Table
    *
    * @param td    a TableDef
    * @param allTd all Tables in the domain
    * @return list of columns
    */
  def filterSchemaColumns(td: TableDef, allTd: TablesDef): List[ColumnDef] = {
    val schemaName = td.schema.name
    if (td.schema.name.endsWith("_HISTO")) {
      val latestSchemaName = td.schema.name.stripSuffix(HistoSuffix)
      allTd.tables.map(_.schema).filter(s => s.name == latestSchemaName) match {
        case Nil =>
          logger.warn(s"No Latest Table present for ${schemaName} - keep all columns")
          td.schema.columns
        case latestTable :: Nil =>
          logger.info(s"Latest Table present for ${schemaName} - discard columns")
          val histoCols = td.schema.columns
          // Discard histo cols when they are not present in latest col (check on the name of column)
          histoCols.filterNot { hc =>
            val latestColNames = latestTable.columns.map(lc => lc.name)
            latestColNames.contains(hc.name)
          }
        case ls =>
          throw new Exception(
            s"ERROR: there are more latest tables for the same historical ${schemaName}: ${ls.mkString(",")}"
          )
      }
    } else {
      td.schema.columns
    }
  }

  private def concatenateDoc(elems: Seq[String]): String = {
    elems
      .filterNot(_.trim.isEmpty)
      .map(o => s"<p>$o</p>")
      .mkString("")
  }

  private def generateOrigin(col: ColumnDef, oldPrefix: String, newPrefix: String): String = {
    concatenateDoc(
      col.origins
        .map {
          case Origin(_, _, _, LiteralType) => EmptyValue
          case Origin(_, _, Some(transformed), _) => transformed
          case o => o.raw
        }
        .map(_.replace(oldPrefix, newPrefix))
    )
  }

}
