@startuml star-schema_CORR_DCSBAG_v1_0_0

' Header
hide circle
hide <<assotable>> stereotype
hide <<dimtable>> stereotype
hide <<maintable>> stereotype
hide <<mainsubdomain>> stereotype
hide methods
left to right direction

!define TABLE_GRADIENT_BACKGROUND #F2F2F2-fcffd6
!define MAIN_TABLE_GRADIENT_HEADER Orange-%lighten("Yellow", 60)

!function $pk($content)
!return "<color:#ff0000>" + $content + "</color>"
!endfunction

!function $fk($content)
!return "<color:#0000ff>" + $content + "</color>"
!endfunction

skinparam {
    DefaultFontName Monospaced
    ranksep 250
    linetype polyline
    tabSize 4
    HyperlinkUnderline false
    HyperlinkColor #0000ff
}

skinparam frame {
    FontSize 28
    FontSize<<mainsubdomain>> 34
    BorderThickness<<mainsubdomain>> 3
}

skinparam class {
    BackgroundColor TABLE_GRADIENT_BACKGROUND
    HeaderBackgroundColor %lighten("Crimson", 40)
    HeaderBackgroundColor<<maintable>> MAIN_TABLE_GRADIENT_HEADER
    HeaderBackgroundColor<<dimtable>> LightBlue
    HeaderBackgroundColor<<assotable>> %lighten("LimeGreen", 30)
    ColorArrowSeparationSpace Red
    BorderColor Black
    BorderColor<<maintable>> MediumBlue
    BorderThickness<<maintable>> 3
    ArrowColor Blue
    FontSize 16
    FontSize<<maintable>> 20
    FontStyle Bold
}

' Frames
frame "DCSBAG" #DDDDDD {

entity "FACT_BAG_GROUP_HISTO" {
$pk("BAG_GROUP_ID  Binary  NN  PK")
$pk("VERSION       Long    NN  PK")
}

entity "FACT_BAG_HISTO" {
$pk("BAG_ID   Binary  NN  PK")
$pk("VERSION  Long    NN  PK")
}

entity "FACT_BAG_LEG_DELIVERY_HISTO" {
$pk("BAG_LEG_DELIVERY_ID  Binary  NN  PK")
$pk("VERSION              Long    NN  PK")
}
}

frame "PNR" #DDDDDD {

entity "FACT_AIR_SEGMENT_PAX_HISTO" {
$pk("AIR_SEGMENT_PAX_ID  Binary  NN  PK")
$pk("VERSION             Long    NN  PK")
$pk("AIR_SEGMENT_ID      Binary  NN  PK")
}

entity "FACT_TRAVELER_HISTO" {
$pk("TRAVELER_ID  Binary  NN  PK")
$pk("VERSION      Long    NN  PK")
}

entity "FACT_RESERVATION_HISTO" {
$pk("RESERVATION_ID  Binary  NN  PK")
$pk("VERSION         Long    NN  PK")
}
}

frame "DCSPAX" #DDDDDD {

entity "FACT_PASSENGER_HISTO" {
$pk("PASSENGER_ID  Binary  NN  PK")
$pk("VERSION       Long    NN  PK")
}

entity "FACT_SEGMENT_DELIVERY_HISTO" {
$pk("SEGMENT_DELIVERY_ID  Binary  NN  PK")
$pk("VERSION              Long    NN  PK")
}

entity "FACT_LEG_DELIVERY_HISTO" {
$pk("LEG_DELIVERY_ID  Binary  NN  PK")
$pk("VERSION          Long    NN  PK")
}
}

' Free entities
entity "ASSO_DCS_PASSENGER_BAG_GROUP_HISTO"<<assotable>> {
$fk("[[#{DCSPAX.FACT_PASSENGER_HISTO} PASSENGER_ID]]             Binary         FK")
$fk("[[#{DCSBAG.FACT_BAG_GROUP_HISTO} BAG_GROUP_ID]]             Binary         FK")
$fk("[[#{DCSPAX.FACT_PASSENGER_HISTO} VERSION_PASSENGER]]        Long       NN  FK")
$fk("[[#{DCSBAG.FACT_BAG_GROUP_HISTO} VERSION_BAG_GROUP]]        Long       NN  FK")
DATE_BEGIN               Timestamp        
DATE_END                 Timestamp        
IS_LAST_VERSION          Boolean          
REFERENCE_KEY_BAG_GROUP  String           
REFERENCE_KEY_PASSENGER  String           
}

entity "ASSO_DCS_PASSENGER_BAG_HISTO"<<assotable>> {
$fk("[[#{DCSPAX.FACT_PASSENGER_HISTO} PASSENGER_ID]]                         Binary         FK")
$fk("[[#{DCSBAG.FACT_BAG_HISTO} BAG_ID]]                               Binary         FK")
$fk("[[#{DCSPAX.FACT_PASSENGER_HISTO} VERSION_PASSENGER]]                    Long       NN  FK")
$fk("[[#{DCSBAG.FACT_BAG_GROUP_HISTO} VERSION_BAG_GROUP]]                    Long       NN  FK")
DATE_BEGIN                           Timestamp        
DATE_END                             Timestamp        
IS_LAST_VERSION                      Boolean          
REFERENCE_KEY_RESPONSIBLE_PASSENGER  String           
REFERENCE_KEY_BAG_GROUP              String           
RESPONSIBLE_PASSENGER_ID             String           
REFERENCE_KEY_PASSENGER              String           
REFERENCE_KEY_BAG                    String           
}

entity "ASSO_SEGMENT_DELIVERY_BAG_LEG_DELIVERY_HISTO"<<assotable>> {
$fk("[[#{DCSPAX.FACT_SEGMENT_DELIVERY_HISTO} SEGMENT_DELIVERY_ID]]             Binary         FK")
$fk("[[#{DCSBAG.FACT_BAG_LEG_DELIVERY_HISTO} BAG_LEG_DELIVERY_ID]]             Binary         FK")
$fk("[[#{DCSPAX.FACT_SEGMENT_DELIVERY_HISTO} VERSION_PASSENGER]]               Long       NN  FK")
$fk("[[#{DCSBAG.FACT_BAG_LEG_DELIVERY_HISTO} VERSION_BAG_GROUP]]               Long       NN  FK")
DATE_BEGIN                      Timestamp        
DATE_END                        Timestamp        
IS_LAST_VERSION                 Boolean          
REFERENCE_KEY_PASSENGER         String           
REFERENCE_KEY_SEGMENT_DELIVERY  String           
REFERENCE_KEY_BAG_GROUP         String           
REFERENCE_KEY_BAG_LEG_DELIVERY  String           
}

entity "ASSO_LEG_DELIVERY_BAG_LEG_DELIVERY_HISTO"<<assotable>> {
$fk("[[#{DCSPAX.FACT_LEG_DELIVERY_HISTO} LEG_DELIVERY_ID]]                 Binary         FK")
$fk("[[#{DCSBAG.FACT_BAG_LEG_DELIVERY_HISTO} BAG_LEG_DELIVERY_ID]]             Binary         FK")
$fk("[[#{DCSPAX.FACT_LEG_DELIVERY_HISTO} VERSION_PASSENGER]]               Long       NN  FK")
$fk("[[#{DCSBAG.FACT_BAG_LEG_DELIVERY_HISTO} VERSION_BAG_GROUP]]               Long       NN  FK")
DATE_BEGIN                      Timestamp        
DATE_END                        Timestamp        
IS_LAST_VERSION                 Boolean          
REFERENCE_KEY_PASSENGER         String           
REFERENCE_KEY_LEG_DELIVERY      String           
REFERENCE_KEY_BAG_GROUP         String           
REFERENCE_KEY_BAG_LEG_DELIVERY  String           
}

entity "ASSO_RESERVATION_BAG_GROUP_HISTO"<<assotable>> {
$fk("[[#{PNR.FACT_RESERVATION_HISTO} RESERVATION_ID]]             Binary         FK")
$fk("[[#{DCSBAG.FACT_BAG_GROUP_HISTO} BAG_GROUP_ID]]               Binary         FK")
$fk("[[#{PNR.FACT_RESERVATION_HISTO} VERSION_RESERVATION]]        Long       NN  FK")
$fk("[[#{DCSBAG.FACT_BAG_GROUP_HISTO} VERSION_BAG_GROUP]]          Long       NN  FK")
DATE_BEGIN                 Timestamp        
DATE_END                   Timestamp        
IS_LAST_VERSION            Boolean          
REFERENCE_KEY_RESERVATION  String           
REFERENCE_KEY_BAG_GROUP    String           
}

entity "ASSO_TRAVELER_BAG_HISTO"<<assotable>> {
$fk("[[#{PNR.FACT_TRAVELER_HISTO} TRAVELER_ID]]                Binary         FK")
$fk("[[#{DCSBAG.FACT_BAG_HISTO} BAG_ID]]                     Binary         FK")
$fk("[[#{PNR.FACT_TRAVELER_HISTO} VERSION_RESERVATION]]        Long       NN  FK")
$fk("[[#{DCSBAG.FACT_BAG_HISTO} VERSION_BAG_GROUP]]          Long       NN  FK")
DATE_BEGIN                 Timestamp        
DATE_END                   Timestamp        
IS_LAST_VERSION            Boolean          
REFERENCE_KEY_RESERVATION  String           
REFERENCE_KEY_TRAVELER     String           
REFERENCE_KEY_BAG_GROUP    String           
REFERENCE_KEY_BAG          String           
}

entity "ASSO_AIR_SEGMENT_PAX_BAG_LEG_DELIVERY_HISTO"<<assotable>> {
$fk("[[#{PNR.FACT_AIR_SEGMENT_PAX_HISTO} AIR_SEGMENT_PAX_ID]]              Binary         FK")
$fk("[[#{DCSBAG.FACT_BAG_LEG_DELIVERY_HISTO} BAG_LEG_DELIVERY_ID]]             Binary         FK")
$fk("[[#{PNR.FACT_RESERVATION_HISTO} VERSION_RESERVATION]]             Long       NN  FK")
$fk("[[#{DCSBAG.FACT_BAG_GROUP_HISTO} VERSION_BAG_GROUP]]               Long       NN  FK")
DATE_BEGIN                      Timestamp        
DATE_END                        Timestamp        
IS_LAST_VERSION                 Boolean          
REFERENCE_KEY_RESERVATION       String           
REFERENCE_KEY_AIR_SEGMENT_PAX   String           
REFERENCE_KEY_BAG_GROUP         String           
REFERENCE_KEY_BAG_LEG_DELIVERY  String           
}

' Relationships
ASSO_DCS_PASSENGER_BAG_GROUP_HISTO --> DCSPAX.FACT_PASSENGER_HISTO
ASSO_DCS_PASSENGER_BAG_GROUP_HISTO -up-> DCSBAG.FACT_BAG_GROUP_HISTO
ASSO_DCS_PASSENGER_BAG_HISTO --> DCSPAX.FACT_PASSENGER_HISTO
ASSO_DCS_PASSENGER_BAG_HISTO -up-> DCSBAG.FACT_BAG_HISTO
ASSO_SEGMENT_DELIVERY_BAG_LEG_DELIVERY_HISTO --> DCSPAX.FACT_SEGMENT_DELIVERY_HISTO
ASSO_SEGMENT_DELIVERY_BAG_LEG_DELIVERY_HISTO -up-> DCSBAG.FACT_BAG_LEG_DELIVERY_HISTO
ASSO_LEG_DELIVERY_BAG_LEG_DELIVERY_HISTO --> DCSPAX.FACT_LEG_DELIVERY_HISTO
ASSO_LEG_DELIVERY_BAG_LEG_DELIVERY_HISTO -up-> DCSBAG.FACT_BAG_LEG_DELIVERY_HISTO
ASSO_RESERVATION_BAG_GROUP_HISTO --> PNR.FACT_RESERVATION_HISTO
ASSO_RESERVATION_BAG_GROUP_HISTO -up-> DCSBAG.FACT_BAG_GROUP_HISTO
ASSO_TRAVELER_BAG_HISTO --> PNR.FACT_TRAVELER_HISTO
ASSO_TRAVELER_BAG_HISTO -up-> DCSBAG.FACT_BAG_HISTO
ASSO_AIR_SEGMENT_PAX_BAG_LEG_DELIVERY_HISTO --> PNR.FACT_AIR_SEGMENT_PAX_HISTO
ASSO_AIR_SEGMENT_PAX_BAG_LEG_DELIVERY_HISTO -up-> DCSBAG.FACT_BAG_LEG_DELIVERY_HISTO

@enduml
