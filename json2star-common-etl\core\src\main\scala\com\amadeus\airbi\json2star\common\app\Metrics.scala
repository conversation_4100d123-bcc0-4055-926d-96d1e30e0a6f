package com.amadeus.airbi.json2star.common.app

import com.amadeus.airbi.rawvault.common.vault.spark.MapAccumulator
import org.apache.spark.sql.SparkSession
import org.apache.spark.util.LongAccumulator

case class Metrics(
  transformed: LongAccumulator,
  dropped: LongAccumulator,
  transformErrors: LongAccumulator,
  tableRowCounts: MapAccumulator
) {
  def totProcessed: Long = {
    transformed.value + transformErrors.value + dropped.value
  }
}

object Metrics {
  def register(spark: SparkSession): Metrics = {
    val tableRowCounts = new MapAccumulator
    val mtx = new Metrics(
      transformed = spark.sparkContext.longAccumulator("EventTransformed"),
      dropped = spark.sparkContext.longAccumulator("EventDropped"),
      transformErrors = spark.sparkContext.longAccumulator("EventTransformFailed"),
      tableRowCounts = tableRowCounts
    )
    spark.sparkContext.register(tableRowCounts, "TableRowsCounts")
    mtx
  }
}
