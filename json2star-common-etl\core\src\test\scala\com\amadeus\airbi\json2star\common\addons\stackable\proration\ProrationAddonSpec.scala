package com.amadeus.airbi.json2star.common.addons.stackable.proration

import com.amadeus.airbi.json2star.common.addons.base.mapping.Mapping
import com.amadeus.airbi.json2star.common.addons.base.mapping.Table.KeyValueRow
import com.amadeus.airbi.json2star.common.addons.stackable.StackableAddonValidationException
import com.amadeus.airbi.json2star.common.addons.stackable.proration.ProrationColumns._
import com.amadeus.airbi.json2star.common.addons.stackable.proration.ProrationSpecHelper._
import com.amadeus.airbi.json2star.common.app.Json2StarApp.readMappingConfig
import com.amadeus.airbi.json2star.common.extdata.{ExtData, PorsExtData, PorsExtDataType}
import com.amadeus.airbi.rawvault.common.RootConfig
import com.amadeus.airbi.rawvault.common.application.config.TablesConfig
import com.amadeus.airbi.rawvault.common.config.ColumnMetadataRule.Replace
import com.amadeus.airbi.rawvault.common.config.ColumnMetadataValue
import com.amadeus.airbi.rawvault.common.testfwk.Json2StarSpec

class ProrationAddonSpec extends Json2StarSpec {

  val optdPath = "src/test/resources/datasets/proration/extdata/input_optd_por_public_sample.csv"
  val jsonPath = "/datasets/proration/data/MH-PRD_load-date=01-10-2024.json"

  // selector to load failure use cases
  val failSelector: Set[String] = Set("fail")

  "ProrationAddon" should "only be compatible with Mapping" in {
    ProrationAddon.getCompatibleBaseAddons shouldBe List(Mapping)
  }

  def metadataValue(s: String): ColumnMetadataValue = ColumnMetadataValue(s, Replace)

  /* config validation */

  it should "validate its configuration: valid config should be ok" in {
    val mappingFile = "datasets/proration/simplified_tktemd.conf"
    val mapping: TablesConfig = readMappingConfig(mappingFile)
    val tables = mapping.copy(tables = mapping.tables.filter(_.name == "FACT_COUPON_HISTO"))
    val tableOk = tables.tables.head
    // it must not throw
    ProrationAddon.validate(tables, tableOk, tableOk.stackableAddons.head)
  }

  it should "validate its configuration: fail if base addon is not Mapping" in {
    val mappingFile = "datasets/proration/simplified_tktemd.conf"
    val mapping: TablesConfig = readMappingConfig(mappingFile, failSelector)
    val tables = mapping.copy(tables = mapping.tables.filter(_.name == "FACT_FAIL"))
    val tableKo = tables.tables.head
    // it must throw because not mapping
    assertThrows[StackableAddonValidationException](
      ProrationAddon.validate(tables, tableKo, tableKo.stackableAddons.head)
    )
  }

  it should "validate its configuration: fail if any of the json paths is not defined" in {
    val mappingFile = "datasets/proration/simplified_tktemd.conf"
    val mapping: TablesConfig = readMappingConfig(mappingFile, failSelector)
    val tables = mapping.copy(tables = mapping.tables.filter(_.name == "FACT_FAIL_PATHS_HISTO"))
    val tableKo = tables.tables.head
    // it must throw because path missing
    assertThrows[StackableAddonValidationException](
      ProrationAddon.validate(tables, tableKo, tableKo.stackableAddons.head)
    )
  }

  it should "validate its configuration: fail if any of the json paths does not start with $" in {
    val mappingFile = "datasets/proration/simplified_tktemd.conf"
    val mapping: TablesConfig = readMappingConfig(mappingFile, failSelector)
    val tables = mapping.copy(tables = mapping.tables.filter(_.name == "FACT_FAIL_PATHS_$_HISTO"))
    val tableKo = tables.tables.head
    // it must throw because path not starting with $
    assertThrows[StackableAddonValidationException](
      ProrationAddon.validate(tables, tableKo, tableKo.stackableAddons.head)
    )
  }

  it should "validate its configuration: fail if any of the mandatory columns is not defined" in {
    val mappingFile = "datasets/proration/simplified_tktemd.conf"
    val mapping: TablesConfig = readMappingConfig(mappingFile, failSelector)
    val tables = mapping.copy(tables = mapping.tables.filter(_.name == "FACT_FAIL_COLS_HISTO"))
    val tableKo = tables.tables.head
    // it must throw because column missing
    assertThrows[StackableAddonValidationException](
      ProrationAddon.validate(tables, tableKo, tableKo.stackableAddons.head)
    )
  }

  /* enrichment */

  it should "enrich FACT_COUPON_HISTO rows with proration" in {
    val mappingFile = "datasets/proration/simplified_tktemd.conf"
    val mapping: TablesConfig = readMappingConfig(mappingFile)

    // input
    val factCouponHisto = mapping.copy(tables = mapping.tables.filter(_.name == "FACT_COUPON_HISTO")).tables.head
    val rc = rootConfigWithOptd("", "some_db", optdPath)
    val extData: ExtData = ExtData(Map(PorsExtDataType -> PorsExtData.load(rc, spark)))
    val jsonRoot = getJsonDocument(jsonPath)
    val inputRows = fetchCouponRows(jsonRoot)

    // enrich with proration and project on proration output columns + sequence number (then sort by it)
    val rows = ProrationAddon.enrichTableRows(inputRows, factCouponHisto.stackableAddons.head, jsonRoot, rc, extData)
    val projectedRows = rows
      .map(
        _.filterKeys(
          (SEQUENCE_NUMBER +: ProrationColumns.outputCols
            .filter(_ != ProrationColumns.INTERNAL_PRORATION_METADATA)).contains
        )
      )
      .sortBy(m => m(SEQUENCE_NUMBER).toInt)

    // assert
    val FareCalcAlgo = "FARE CALC"
    val Curr = "USD"
    val ExchangeDate = "2023-01-25"
    val Meta = None

    projectedRows shouldBe List(
      enrichedRow("1", "64.19", Curr, FareCalcAlgo, ExchangeDate, Meta),
      enrichedRow("2", "147.81", Curr, FareCalcAlgo, ExchangeDate, Meta),
      enrichedRow("3", "157.12", Curr, FareCalcAlgo, ExchangeDate, Meta),
      enrichedRow("4", "228.68", Curr, FareCalcAlgo, ExchangeDate, Meta)
    )
  }

  it should "enrich FACT_COUPON_HISTO rows with proration when an unexpected failure happens" in {
    val mappingFile = "datasets/proration/simplified_tktemd.conf"
    val mapping: TablesConfig = readMappingConfig(mappingFile)

    // input
    val factCouponHisto = mapping.copy(tables = mapping.tables.filter(_.name == "FACT_COUPON_HISTO")).tables.head
    val rc = rootConfigWithOptd("", "some_db", optdPath)
    val rcMeta = rc.copy(
      etl = rc.etl.copy(common = rc.etl.common.copy(prorationEnableMetadataColumn = true))
    )

    // set a null in the ExtData to trigger an exception at runtime
    // the fallback step is called and the metadata is filled with the exception message when enableMeta is true
    val extData: ExtData = ExtData(null)
    val jsonRoot = getJsonDocument(jsonPath)
    val inputRows = fetchCouponRows(jsonRoot)

    // enrich with proration and project on proration output columns + sequence number (then sort by it)
    val rows =
      ProrationAddon.enrichTableRows(inputRows, factCouponHisto.stackableAddons.head, jsonRoot, rcMeta, extData)
    val projectedRows = rows
      .map(
        _.filterKeys((SEQUENCE_NUMBER +: ProrationColumns.outputCols).contains)
      )
      .sortBy(m => m(SEQUENCE_NUMBER).toInt)

    // Verify that columns with default empty values are built
    val NoneAlgo = "NONE"
    val Curr = null
    val ProratedFare = null
    val ExchangeDate = "2023-01-25"
    val meta = Some(
      """{"input":null,"tktStatus":"NONE-ALL","exceptionMsg":"java.lang.NullPointerException","inputStep":null,"fclStep":null,"dstStep":null}"""
    )
    projectedRows shouldBe List(
      enrichedRow("1", ProratedFare, Curr, NoneAlgo, ExchangeDate, meta),
      enrichedRow("2", ProratedFare, Curr, NoneAlgo, ExchangeDate, meta),
      enrichedRow("3", ProratedFare, Curr, NoneAlgo, ExchangeDate, meta),
      enrichedRow("4", ProratedFare, Curr, NoneAlgo, ExchangeDate, meta)
    )
  }

  /* utils */

  private def rootConfigWithOptd(datadir: String, database: String, optdPath: String): RootConfig = {
    val rc = rootConfig(datadir, database, isLight = DefaultIsLight)
    rc.copy(etl = rc.etl.copy(common = rc.etl.common.copy(refDataOptdLocation = Some(optdPath))))
  }

  private def enrichedRow(
    seq: String,
    proratedfare: String,
    curr: String,
    algo: String,
    exchangeDate: String,
    meta: Option[String]
  ): KeyValueRow = {
    Map(
      SEQUENCE_NUMBER -> seq,
      ESTIMATED_PRORATED_FARE_ORIGINAL -> proratedfare,
      ESTIMATED_PRORATED_FARE_CURRENCY_ORIGINAL -> curr,
      ESTIMATED_PRORATED_FARE_ALGORITHM -> algo
    ) ++ meta.map(m => INTERNAL_PRORATION_METADATA -> m)
  }

}
