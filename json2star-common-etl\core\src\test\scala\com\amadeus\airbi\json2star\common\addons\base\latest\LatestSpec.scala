package com.amadeus.airbi.json2star.common.addons.base.latest

import com.amadeus.airbi.common.utils.CommonSpec
import com.amadeus.airbi.json2star.common.app.Json2StarApp.readMappingConfig
import com.amadeus.airbi.json2star.common.{ColumnDef, Schema, TablesDef}
import com.amadeus.airbi.rawvault.common.application.config.TableDescription
import com.amadeus.airbi.rawvault.common.config.ColumnType._
import com.amadeus.airbi.rawvault.common.config.{ColumnType, _}
import com.amadeus.airbi.rawvault.common.testfwk.SpecHelper

class LatestSpec extends CommonSpec with SpecHelper {

  private def fk(schema: String, table: String, column: Option[String] = None) = {
    Some(List(FKeyRelationship(Option(schema), table, column)))
  }

  Latest.getClass.getName should "generate latest schema (correlation)" in {
    val mp = readMappingConfig("datasets/latest_correlation/correlation_pnr_tkt_latest_schema.conf")
    val schemasWithOrigin = TablesDef.consolidate(mp).tables.map(_.schema)
    // NOTE: we ignore the origins in this test, as they are tested elsewhere
    val schemas = schemasWithOrigin.map(s => s.copy(columns = s.columns.map(c => c.copy(origins = Seq()))))

    val assoAirSegmentPaxCouponCols = List(
      columnDef(
        "AIR_SEGMENT_PAX_ID",
        binaryStrColumn,
        isMandatory = false,
        belongsToPK = true,
        fk("PNR", "FACT_AIR_SEGMENT_PAX")
      ),
      columnDef("COUPON_ID", binaryStrColumn, isMandatory = false, belongsToPK = true, fk("TKT", "FACT_COUPON")),
      columnDef("VERSION_PNR", longColumn, isMandatory = true, belongsToPK = false),
      columnDef("VERSION_TRAVEL_DOCUMENT", longColumn, isMandatory = true, belongsToPK = false)
    )

    schemas should ===(
      List(
        Schema(
          "ASSO_AIR_SEGMENT_PAX_COUPON_HISTO",
          List(
            columnDef(
              "AIR_SEGMENT_PAX_ID",
              binaryStrColumn,
              isMandatory = false,
              belongsToPK = true,
              fk("PNR", "FACT_AIR_SEGMENT_PAX_HISTO")
            ),
            columnDef("COUPON_ID", binaryStrColumn, isMandatory = false, belongsToPK = true, fk("TKT", "FACT_COUPON_HISTO")),
            columnDef(
              "VERSION_PNR",
              longColumn,
              isMandatory = true,
              belongsToPK = true,
              fk("PNR", "FACT_AIR_SEGMENT_PAX_HISTO", Some("VERSION"))
            ),
            columnDef(
              "VERSION_TRAVEL_DOCUMENT",
              longColumn,
              isMandatory = true,
              belongsToPK = true,
              fk("TKT", "FACT_COUPON_HISTO", Some("VERSION"))
            ),
            columnDef(
              "DATE_BEGIN",
              timestampColumn,
              isMandatory = false,
              belongsToPK = false,
              meta = Some(
                ColumnMetadata.from(
                  description = Some(s"Validity start date of correlation"),
                  example = None,
                  piiType = None,
                  gdprZone = Some(GDPRZone.Green)
                )
              )
            ),
            columnDef(
              "DATE_END",
              timestampColumn,
              isMandatory = false,
              belongsToPK = false,
              meta = Some(
                ColumnMetadata.from(
                  description = Some(s"Validity end date of correlation"),
                  example = None,
                  piiType = None,
                  gdprZone = Some(GDPRZone.Green)
                )
              )
            ),
            columnDef(
              "IS_LAST_VERSION",
              booleanColumn,
              isMandatory = false,
              belongsToPK = false,
              meta = Some(
                ColumnMetadata.from(
                  description = Some(s"True if it is the last correlation otherwise False"),
                  example = None,
                  piiType = None,
                  gdprZone = Some(GDPRZone.Green)
                )
              )
            ),
            columnDef(
              "REFERENCE_KEY_PASSENGER",
              strColumn,
              isMandatory = false,
              belongsToPK = false
            )
          ),
          Some(
            TableDescription(
              description = Some("Correlation table between FACT_AIR_SEGMENT_PAX_HISTO and FACT_COUPON_HISTO"),
              granularity = Some(
                "1 row for each different tuple (AIR_SEGMENT_PAX_ID, COUPON_ID, VERSION_PNR, VERSION_TRAVEL_DOCUMENT)"
              )
            )
          ),
          partitionColumn = Some("IS_LAST_VERSION"),
          kind = Schema.Materialized
        ),
        Schema(
          "ASSO_AIR_SEGMENT_PAX_COUPON",
          assoAirSegmentPaxCouponCols,
          Some(
            TableDescription(
              description = Some("Latest view of ASSO_AIR_SEGMENT_PAX_COUPON_HISTO"),
              granularity = Some("Same as ASSO_AIR_SEGMENT_PAX_COUPON_HISTO, considering only the latest version")
            )
          ),
          partitionColumn = None,
          kind = Schema.View(
            s"SELECT ${assoAirSegmentPaxCouponCols.map(_.name).mkString(",")} FROM ASSO_AIR_SEGMENT_PAX_COUPON_HISTO WHERE IS_LAST_VERSION=true AND (IS_LAST_VERSION is not null)"
          )
        )
      )
    )
  }

  it should "generate latest schema (mapping)" in {
    val mp = readMappingConfig("datasets/latest/simple_mapping_no_vault_latest_schema.conf")
    val schemasWithOrigin = TablesDef.consolidate(mp).tables.map(_.schema)
    // we ignore the origins in this test, as they are tested elsewhere
    val schemas = schemasWithOrigin.map(s => s.copy(columns = s.columns.map(c => c.copy(origins = Seq()))))

    val factReservationCols = List(
      // RESERVATION_ID is part of primary key
      columnDef("RESERVATION_ID", binaryStrColumn, isMandatory = true, belongsToPK = true, preExpr = Some("hashM({0})")),
      columnDef("REFERENCE_KEY", strColumn, isMandatory = false, belongsToPK = false),
      columnDef("RECORD_LOCATOR", strColumn, isMandatory = false, belongsToPK = false),
      // VERSION is defined mandatory in the config of its source table (FACT_RESERVATION_HISTO)
      // but it is not part of primary key as FACT_RESERVATION is a Latest table
      columnDef("VERSION", intColumn, isMandatory = true, belongsToPK = false),
      // columnDef("NIP", intColumn, isMandatory = false, belongsToPK = false), // dropped
      columnDef("GROUP_SIZE", intColumn, isMandatory = false, belongsToPK = false),
      columnDef("GROUP_NAME", strColumn, isMandatory = false, belongsToPK = false),
      columnDef("GROUP_SIZE_TAKEN", intColumn, isMandatory = false, belongsToPK = false),
      columnDef("POINT_OF_SALE_OWNER_ID", binaryStrColumn, isMandatory = false, belongsToPK = false, preExpr = Some("hashM({0})")),
      columnDef("POINT_OF_SALE_CREATION_ID", binaryStrColumn, isMandatory = false, belongsToPK = false, preExpr = Some("hashM({0})")),
      columnDef("POINT_OF_SALE_LAST_UPDATE_ID", binaryStrColumn, isMandatory = false, belongsToPK = false, preExpr = Some("hashM({0})")),
      columnDef("PNR_CREATION_DATE", timestampColumn, isMandatory = false, belongsToPK = false),
      columnDef("LOAD_DATE", timestampColumn, isMandatory = false, belongsToPK = false)
    )

    val factAirSegmentPaxCols = List(
      // AIR_SEGMENT_PAX_ID is part of primary key
      columnDef("AIR_SEGMENT_PAX_ID", strColumn, isMandatory = true, belongsToPK = true, preExpr = Some("hashM({0})")),
      columnDef("REFERENCE_KEY", strColumn, isMandatory = false, belongsToPK = false),
      columnDef(
        "RESERVATION_ID",
        strColumn,
        isMandatory = false,
        belongsToPK = false,
        fk = Some(List(FKeyRelationship(None, "FACT_RESERVATION", None))),
        preExpr = Some("hashM({0})")
      ),
      columnDef("TRAVELER_ID", strColumn, isMandatory = false, belongsToPK = false, preExpr = Some("hashM({0})")),
      columnDef("SEGMENT_SCHEDULE_ID", strColumn, isMandatory = false, belongsToPK = false, preExpr = Some("hashM({0})")),
      columnDef("CABIN_CODE_ID", strColumn, isMandatory = false, belongsToPK = false, preExpr = Some("hashXS({0})")),
      columnDef("BOOKING_CLASS_CODE_ID", strColumn, isMandatory = false, belongsToPK = false, preExpr = Some("hashXS({0})")),
      columnDef("BOOKING_STATUS_ID", strColumn, isMandatory = false, belongsToPK = false, preExpr = Some("hashXS({0})")),
      columnDef("POINT_OF_SALE_ID", strColumn, isMandatory = false, belongsToPK = false, preExpr = Some("hashM({0})")),
      columnDef("FIRST_NAME", strColumn, isMandatory = false, belongsToPK = false),
      columnDef("CABIN_CODE", strColumn, isMandatory = false, belongsToPK = false),
      columnDef("BOOKING_CLASS_CODE", strColumn, isMandatory = false, belongsToPK = false),
      columnDef("BOOKING_STATUS", strColumn, isMandatory = false, belongsToPK = false),
      columnDef("IS_INFO", booleanColumn, isMandatory = false, belongsToPK = false),
      columnDef("PRODUCT_ID", strColumn, isMandatory = false, belongsToPK = false),
      columnDef("PDI_NOREC", strColumn, isMandatory = false, belongsToPK = false, preExpr = Some("substring_index({0}, '-', 1)")),
      columnDef("PDI_GOSHOW", strColumn, isMandatory = false, belongsToPK = false, preExpr = Some("substring_index({0}, '-', 1)")),
      columnDef("PDI_DISTRIBUTION_ID", strColumn, isMandatory = false, belongsToPK = false, preExpr = Some("substring_index({0}, '-', 3)")),
      columnDef("PDI_TRANSFER_FLAG", strColumn, isMandatory = false, belongsToPK = false, preExpr = Some("substring_index({0}, '-', 3)")),
      columnDef("DEPARTURE_DATE_TIME", timestampColumn, isMandatory = false, belongsToPK = false),
      columnDef("RECORD_LOCATOR", strColumn, isMandatory = false, belongsToPK = false),
      columnDef("PNR_CREATION_DATE", timestampColumn, isMandatory = false, belongsToPK = false),
      // VERSION is defined mandatory in the config of its source table (FACT_AIR_SEGMENT_PAX_HISTO)
      // but it is not part of primary key as FACT_AIR_SEGMENT_PAX is a Latest table
      columnDef("VERSION", intColumn, isMandatory = true, belongsToPK = false),
      columnDef("LOAD_DATE", timestampColumn, isMandatory = false, belongsToPK = false) // added one
    )

    schemas.filter(s => s.name == "FACT_RESERVATION" || s.name == "FACT_AIR_SEGMENT_PAX") should ===(
      List(
        Schema(
          "FACT_RESERVATION",
          factReservationCols,
          Some(
            TableDescription(
              description = Some("Latest view of FACT_RESERVATION_HISTO"),
              granularity = Some("Same as FACT_RESERVATION_HISTO, considering only the latest version")
            )
          ),
          partitionColumn = None,
          kind = Schema.View(
            s"SELECT ${factReservationCols.map(_.name).mkString(",")} FROM FACT_RESERVATION_HISTO WHERE IS_LAST_VERSION=true AND (VERSION is not null)"
          )
        ),
        Schema(
          "FACT_AIR_SEGMENT_PAX",
          factAirSegmentPaxCols,
          Some(
            TableDescription(
              description = Some("Latest view of FACT_AIR_SEGMENT_PAX_HISTO"),
              granularity = Some("Same as FACT_AIR_SEGMENT_PAX_HISTO, considering only the latest version")
            )
          ),
          partitionColumn = None,
          kind = Schema.View(
            s"SELECT ${factAirSegmentPaxCols.map(_.name).mkString(",")} FROM FACT_AIR_SEGMENT_PAX_HISTO WHERE IS_LAST_VERSION=true"
          )
        )
      )
    )
  }

  it should "generate latest schema (source-correlation)" in {
    val mp = readMappingConfig("datasets/latest_correlation/correlation_dcspax_pnr_latest_schema.conf")
    val schemasWithOrigin = TablesDef.consolidate(mp).tables.map(_.schema)
    // NOTE: we ignore the origins in this test, as they are tested elsewhere
    val schemas = schemasWithOrigin.map(s => s.copy(columns = s.columns.map(c => c.copy(origins = Seq()))))

    val assoCouponServiceDeliveryCols = List(
      columnDef("COUPON_ID", strColumn, isMandatory = true, belongsToPK = true),
      columnDef("SERVICE_DELIVERY_ID", strColumn, isMandatory = true, belongsToPK = true),
      columnDef("REFERENCE_KEY_TRAVEL_DOCUMENT", strColumn, isMandatory = false, belongsToPK = false),
      //columnDef("REFERENCE_KEY_COUPON", strColumn, isMandatory = false, belongsToPK = false), // droppped
      columnDef("REFERENCE_KEY_PASSENGER", strColumn, isMandatory = false, belongsToPK = false),
      columnDef("REFERENCE_KEY_SERVICE_DELIVERY", strColumn, isMandatory = false, belongsToPK = false),
      columnDef("VERSION_TRAVEL_DOCUMENT", strColumn, isMandatory = false, belongsToPK = false),
      columnDef("VERSION_PASSENGER", strColumn, isMandatory = false, belongsToPK = false)
    )

    schemas should ===(
      List(
        Schema(
          "ASSO_COUPON_SERVICE_DELIVERY_HISTO",
          List(
            columnDef("COUPON_ID", strColumn, isMandatory = true, belongsToPK = true),
            columnDef("SERVICE_DELIVERY_ID", strColumn, isMandatory = true, belongsToPK = true),
            columnDef("REFERENCE_KEY_TRAVEL_DOCUMENT", strColumn, isMandatory = false, belongsToPK = false),
            columnDef("REFERENCE_KEY_COUPON", strColumn, isMandatory = false, belongsToPK = false),
            columnDef("REFERENCE_KEY_PASSENGER", strColumn, isMandatory = false, belongsToPK = false),
            columnDef("REFERENCE_KEY_SERVICE_DELIVERY", strColumn, isMandatory = false, belongsToPK = false),
            columnDef("VERSION_TRAVEL_DOCUMENT", strColumn, isMandatory = false, belongsToPK = true),
            columnDef("VERSION_PASSENGER", strColumn, isMandatory = false, belongsToPK = true),
            columnDef("DATE_BEGIN", timestampColumn, isMandatory = false, belongsToPK = false),
            columnDef("DATE_END", timestampColumn, isMandatory = false, belongsToPK = false),
            columnDef("IS_LAST_VERSION", booleanColumn, isMandatory = false, belongsToPK = false)
          ),
          Some(
            TableDescription(
              description = Some("Source Correlation table between DCSPAX and PNR"),
              granularity = Some(
                "1 row for each different tuple (SERVICE_DELIVERY_ID, COUPON_ID, VERSION_PASSENGER, VERSION_TRAVEL_DOCUMENT)"
              )
            )
          ),
          partitionColumn = Some("IS_LAST_VERSION"),
          kind = Schema.Materialized
        ),
        Schema(
          "ASSO_COUPON_SERVICE_DELIVERY",
          assoCouponServiceDeliveryCols,
          Some(
            TableDescription(
              description = Some("Latest view of ASSO_COUPON_SERVICE_DELIVERY_HISTO"),
              granularity = Some("Same as ASSO_COUPON_SERVICE_DELIVERY_HISTO, considering only the latest version")
            )
          ),
          kind = Schema.View(
            s"SELECT ${assoCouponServiceDeliveryCols.map(_.name).mkString(",")} FROM ASSO_COUPON_SERVICE_DELIVERY_HISTO WHERE IS_LAST_VERSION=true AND (VERSION is not null)"
          )
        )
      )
    )
  }
}
