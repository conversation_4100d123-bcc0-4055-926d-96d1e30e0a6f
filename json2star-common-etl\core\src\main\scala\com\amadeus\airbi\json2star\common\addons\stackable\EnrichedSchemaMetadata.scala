package com.amadeus.airbi.json2star.common.addons.stackable

import com.amadeus.airbi.json2star.common.Origin
import com.amadeus.airbi.rawvault.common.application.config.TableDescription
import com.amadeus.airbi.rawvault.common.config.ColumnMetadata

/** Schema metadata enrichment done by a stackable addon
  * @param columnsMetadata it contains only elements for the column actually enriched
  * @param description it contains Some(description) if an enrichment is done
  */
case class EnrichedSchemaMetadata(
  columnsMetadata: Map[String, EnrichedColumnMetadata] = Map.empty,
  description: Option[TableDescription] = None
)

/** Enriched columns metadata as provided by a stackable addon
  * @param origins if set by the stackable addon, it will replace the existing sequence of origins (if any)
  * @param meta if set by the stackable addon, it will replace *entirely* the existing ColumnMetadata (if any)
  */
case class EnrichedColumnMetadata(
  origins: Option[Seq[Origin]] = None,
  meta: Option[ColumnMetadata] = None
)

object EnrichedSchemaMetadata {
  val NoEnrichment: EnrichedSchemaMetadata = EnrichedSchemaMetadata(Map.empty, None)
}
