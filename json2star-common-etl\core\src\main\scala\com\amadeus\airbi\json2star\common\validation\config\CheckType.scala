package com.amadeus.airbi.json2star.common.validation.config

import com.amadeus.airbi.json2star.common.validation.config.CheckType.Value


/** DUMMY => used in unit test to validate ValidationExecutor behaviour
  * NO_DUPES => Check if they are dupes in star schema tables
  * FULLY_PROCESSED => check if all received records were processed
  * FOREIGN_KEYS => check consistency of FK through database
  * CHECK_NULL_COLUMNS => check columns are not null,
  * TABLE_COUNT => Check table is not empty
  */
object CheckType extends Enumeration {
  val DUMMY, NO_DUPES, NO_DUPES_LATEST, FULLY_PROCESSED, NO_NULL_COLUMN, TABLE_COUNT, FOREIGN_KEYS = Value
}