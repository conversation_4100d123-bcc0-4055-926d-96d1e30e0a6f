package com.amadeus.airbi.json2star.common.prefiller

import com.amadeus.airbi.json2star.common.config.{AppConfig, DimPrefillerParams, ProcessingParams}
import com.typesafe.scalalogging.Logger
import org.slf4j.LoggerFactory

import java.sql.Timestamp

case class PrefillerConfig(
  common: AppConfig.Common,
  mappingConfFilePath: String,
  tablesSelectors: Set[String],
  prefillerParams: DimPrefillerParams,
  processingParams: Option[ProcessingParams],
  loadDate : Timestamp
)

object PrefillerConfig {
  lazy val logger: Logger = Logger(LoggerFactory.getLogger(getClass.getName))
  def apply(confFile: String, loadDate : Timestamp): PrefillerConfig = {
    val appConfig = AppConfig.apply(confFile)

    appConfig.dimPrefillerParams match {
      case Some(params) =>
        PrefillerConfig(
          common = appConfig.common,
          mappingConfFilePath = appConfig.modelConfFile,
          tablesSelectors = appConfig.tablesSelectors,
          prefillerParams = params,
          processingParams = appConfig.processingParams,
          loadDate = loadDate
        )
      case None =>
        throw new IllegalArgumentException("app config file must contain prefiller source files parameters !")
    }

  }

}
