{"version": "2.0.0", "tasks": [{"label": "sbt compile", "type": "shell", "command": "sbt", "args": ["compile"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": "$scala"}, {"label": "sbt test", "type": "shell", "command": "sbt", "args": ["test"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": "$scala"}, {"label": "sbt assembly", "type": "shell", "command": "sbt", "args": ["assembly"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": "$scala"}, {"label": "sbt clean", "type": "shell", "command": "sbt", "args": ["clean"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "sbt dependencyTree", "type": "shell", "command": "sbt", "args": ["dependencyTree"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "sbt scalafmt", "type": "shell", "command": "sbt", "args": ["scalafmt"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}]}