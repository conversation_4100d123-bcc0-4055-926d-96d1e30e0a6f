package com.amadeus.airbi.json2star.common.addons.base.mapping

import com.amadeus.airbi.json2star.common.config.ResizeParams
import com.amadeus.airbi.json2star.common.resize.ResizeLogic
import com.amadeus.airbi.json2star.common.resize.ResizeLogic.DoResize

class MappingResizeLogic(
  oldVersionsCount: Long,
  newRecords: Long
) extends ResizeLogic {

  def shouldResize(resizeParams: ResizeParams): ResizeLogic.Resize = {
    val oldVersions = oldVersionsCount > 0
    val trafficPeak = newRecords > resizeParams.trafficPeakThreshold

    (oldVersions, trafficPeak) match {
      // back to default
      case (false, false) => DoResize(resizeParams.numWorkersDefault, "Default cluster size needed")
      // traffic peak
      case (false, true) => DoResize(resizeParams.numWorkersTrafficPeak, "Traffic peak")
      // pit histo branch
      case (true, false) => DoResize(resizeParams.numWorkersPitHisto, "Revised PIT needs to read/write histo partition")
      // peak and pit histo => pick max
      case (true, true) =>
        DoResize(
          math.max(resizeParams.numWorkersTrafficPeak, resizeParams.numWorkersPitHisto),
          "Revised PIT histo + Traffic peak"
        )
    }
  }
}
