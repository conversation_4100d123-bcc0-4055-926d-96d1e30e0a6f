package com.amadeus.airbi.json2star.common.addons.stackable.dummy

import com.amadeus.airbi.json2star.common.Schema
import com.amadeus.airbi.json2star.common.addons.base.Addon
import com.amadeus.airbi.json2star.common.addons.base.mapping.Mapping
import com.amadeus.airbi.json2star.common.addons.stackable.EnrichedSchemaMetadata.NoEnrichment
import com.amadeus.airbi.json2star.common.addons.stackable._
import com.amadeus.airbi.json2star.common.extdata.{DummyExtData, DummyExtDataType, ExtData, ExtDataType}
import com.amadeus.airbi.rawvault.common.RootConfig
import com.amadeus.airbi.rawvault.common.application.config.{Merge, TableConfig, TableDescription, TablesConfig}
import com.amadeus.airbi.rawvault.common.config.ColumnMetadataRule.Replace
import com.amadeus.airbi.rawvault.common.config.{ColumnMetadata, ColumnMetadataValue}
import com.amadeus.airbi.json2star.common.addons.base.mapping.Table.KeyValueRow
import com.jayway.jsonpath.DocumentContext
import org.apache.spark.sql.DataFrame

/** This stackable addon is meant to be used *ONLY* in unit tests.
  */
object DummyAddon extends StackableAddon[Dummy] {

  val ExpectedErrorMessage = "expected_error"

  override def getCompatibleBaseAddons: List[Addon[_]] = List(Mapping)

  override def getRequiredExtData: List[ExtDataType] = List(DummyExtDataType)

  override def validate(c: TablesConfig, t: TableConfig, addonConfig: StackableAddonConfig): Unit = {
    val config = getConfig(addonConfig)
    config.action match {
      case "fail" => throw new StackableAddonValidationException(ExpectedErrorMessage)
      case _ => ()
    }
  }

  def enrichedTableDescription(action: String): TableDescription =
    TableDescription(Some(enrichedDescription(action)), Some(enrichedDescription(action)))

  def enrichedDescription(action: String): String = s"enriched_description_$action"

  override def enrichSchemaMetadata(
    c: TablesConfig,
    t: TableConfig,
    addonConfig: StackableAddonConfig,
    schema: Schema
  ): EnrichedSchemaMetadata = {
    val config = getConfig(addonConfig)

    config.action match {
      case "enrich_load_date" =>
        val enrichedValue = Some(ColumnMetadataValue(enrichedDescription(config.action), Replace))
        EnrichedSchemaMetadata(
          columnsMetadata =
            Map("LOAD_DATE" -> EnrichedColumnMetadata(meta = Some(ColumnMetadata(description = enrichedValue))))
        )
      case "enrich_table" =>
        EnrichedSchemaMetadata(
          description = Some(enrichedTableDescription(config.action))
        )
      case _ => NoEnrichment
    }
  }

  override def enrichTableRows(
    rows: List[KeyValueRow],
    addonConfig: StackableAddonConfig,
    jsonRoot: DocumentContext,
    rootConfig: RootConfig,
    extData: ExtData
  ): List[KeyValueRow] = {
    val config = getConfig(addonConfig)
    config.action match {
      case "fail_closure" => throw new RuntimeException(ExpectedErrorMessage)
      case "enrich_with_external_data" =>
        val extDataValue = extData.get[DummyExtData](DummyExtDataType).value
        val enrichedRows = (config.srcCol, config.dstCol) match {
          case (Some(srcCol), Some(dstCol)) =>
            rows.map { row =>
              val srcValue = row(srcCol)
              val dstValue = s"enrich(${srcValue}, ${extDataValue})"
              row.updated(dstCol, dstValue)
            }
          case _ => rows
        }
        enrichedRows
      case _ => rows
    }
  }

  /** Enrichment done before the merge
    *
    * @param b           dataframe generated by a base addons and possibly enriched by previous stackable addons
    * @param addonConfig stackable addon configuration for the table
    * @param rootConfig application root configuration
    * @return the enriched dataframe ready to be merged
    */
  override def enrichBatch(b: DataFrame, addonConfig: StackableAddonConfig, rootConfig: RootConfig): DataFrame = {
    val config = getConfig(addonConfig)
    config.action match {
      case "fail_batch" => throw new RuntimeException(ExpectedErrorMessage)
      case _ => b
    }
  }

}
