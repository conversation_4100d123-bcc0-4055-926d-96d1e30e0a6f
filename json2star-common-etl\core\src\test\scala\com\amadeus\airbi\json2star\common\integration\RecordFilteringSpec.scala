package com.amadeus.airbi.json2star.common.integration

import com.amadeus.airbi.json2star.common.app.Json2StarApp.readMappingConfig
import com.amadeus.airbi.json2star.common.app.{Json2StarApp, Metrics}
import com.amadeus.airbi.rawvault.common.application.config.{MappingRule, Ruleset}
import com.amadeus.airbi.rawvault.common.testfwk.{Json2StarSpec, TmpDir}

class RecordFilteringSpec extends Json2StarSpec {

  val testBaseDir = "src/test/resources/datasets/recordsfilter/"
  val mappingFile: String = testBaseDir + "mapping.conf"
  val pathExpectedResults: String = testBaseDir + "expected_results/"
  val dataDir = "datasets/recordsfilter/data/"

  override def beforeAll: Unit = {
    super.beforeAll
    createTables(mappingFile)
  }

  override def beforeEach: Unit = {
    cleanTables(mappingFile)
  }

  "the engine" should "filter no rows when filter is not specified" in withStrTmpDir { tmp =>
    val rc = rootConfig(datadir = dataDir, database = inputDatabase, checkpointPath = Some(tmp), isLight = DefaultIsLight)
    val model = readMappingConfig(mappingFile).copy(ruleset = None)
    val metrics = Metrics.register(spark)
    Json2StarApp.run(spark, rc, model, Some(metrics))
    checkTableContent("FACT_HISTO", pathExpectedResults + "/nofilter/" + _ + ".csv", model = Some(model))
    (metrics.transformed.value, metrics.dropped.value, metrics.transformErrors.value) shouldBe (3, 0, 0)
  }

  it should "filter rows when filter is on an array" in withStrTmpDir { tmp =>
    val rc = rootConfig(datadir = dataDir, database = inputDatabase, checkpointPath = Some(tmp), isLight = DefaultIsLight)
    val model = readMappingConfig(mappingFile).copy(ruleset =
      Some(Ruleset(mapping = Some(MappingRule(recordFilter = Some("$.mainResource.current.image.correlations")))))
    )
    val metrics = Metrics.register(spark)
    Json2StarApp.run(spark, rc, model, Some(metrics))
    checkTableContent("FACT_HISTO", pathExpectedResults + "/correlationfilter/" + _ + ".csv", model = Some(model))
    (metrics.transformed.value, metrics.dropped.value, metrics.transformErrors.value) shouldBe (1, 2, 0)
  }

  it should "filter rows when filter is on an object" in withStrTmpDir { tmp =>
    val rc = rootConfig(datadir = dataDir, database = inputDatabase, checkpointPath = Some(tmp), isLight = DefaultIsLight)
    val model =
      readMappingConfig(mappingFile).copy(ruleset =
        Some(Ruleset(mapping = Some(MappingRule(recordFilter = Some("$.mainResource.current.image.bagsGroup")))))
      )
    val metrics = Metrics.register(spark)
    Json2StarApp.run(spark, rc, model, Some(metrics))
    checkTableContent("FACT_HISTO", pathExpectedResults + "/objectfilter/" + _ + ".csv", model = Some(model))
    (metrics.transformed.value, metrics.dropped.value, metrics.transformErrors.value) shouldBe (2, 1, 0)
  }

  it should "filter rows when filter is on an object element" in withStrTmpDir { tmp =>
    val rc = rootConfig(datadir = dataDir, database = inputDatabase, checkpointPath = Some(tmp), isLight = DefaultIsLight)
    val model =
      readMappingConfig(mappingFile).copy(ruleset =
        Some(Ruleset(mapping = Some(MappingRule(recordFilter = Some("$.mainResource.current.image.bagsGroup.source")))))
      )
    val metrics = Metrics.register(spark)
    Json2StarApp.run(spark, rc, model, Some(metrics))
    checkTableContent("FACT_HISTO", pathExpectedResults + "/objectelementfilter/" + _ + ".csv", model = Some(model))
    (metrics.transformed.value, metrics.dropped.value, metrics.transformErrors.value) shouldBe (1, 2, 0)
  }

}
