package com.amadeus.airbi.json2star.common.prefiller

import com.amadeus.airbi.common.utils.CommonSpec
import com.amadeus.airbi.json2star.common.app.Json2StarApp.readMappingConfig
import com.amadeus.airbi.json2star.common.config.{DimPrefillerFileParams, DimPrefillerParams}
import com.amadeus.airbi.rawvault.common.application.config.{ColumnFiller, DimPrefiller}

import java.sql.Timestamp

class RefDataPrefillerSpec extends CommonSpec {

  val mappingFile = "src/test/resources/datasets/dim_prefiller/mapping.conf"
  val appConfigFile = "src/test/resources/datasets/dim_prefiller/application.conf"

  it should "create params from appConf file" in {

    val prefillerConfig = PrefillerConfig.apply(appConfigFile, Timestamp.valueOf("2024-01-10 02:02:02"))

    val expectedFileParams = DimPrefillerFileParams(
      path = "path10",
      format = "CSV",
      options = Map("delimiter" -> "^", "header" -> "true")
    )
    val expectedResults = DimPrefillerParams(
      folderPath = "toto",
      inputFiles = Map({ "KEY1" -> expectedFileParams }, { "KEY2" -> expectedFileParams })
    )
    prefillerConfig.prefillerParams should be equals expectedResults

  }

  it should "create model from mapping file" in {

    val prefillerConfig = PrefillerConfig.apply(appConfigFile, Timestamp.valueOf("2024-01-10 02:02:02"))

    val mapping = readMappingConfig(mappingFile, prefillerConfig.tablesSelectors)
    val expectedResults = Seq(
      DimPrefiller(
        dataSourceKey = "KEY1",
        columnFiller = Seq(
          ColumnFiller("PASSENGER_TYPE_ID", "TYPE_CODE"),
          ColumnFiller("PASSENGER_TYPE_CODE", "TYPE_CODE"),
          ColumnFiller("PASSENGER_TYPE_LABEL", "TYPE_LABEL")
        )
      ),
      DimPrefiller(
        dataSourceKey = "KEY2",
        columnFiller = Seq(
          ColumnFiller("PASSENGER_TYPE_ID", "TYPE_CODE"),
          ColumnFiller("PASSENGER_TYPE_CODE", "TYPE_CODE"),
          ColumnFiller("PASSENGER_TYPE_LABEL", "TYPE_LABEL")
        )
      )
    )

    mapping.tableByName("DIM_PASSENGER_TYPE").prefiller should be equals expectedResults

  }

}
