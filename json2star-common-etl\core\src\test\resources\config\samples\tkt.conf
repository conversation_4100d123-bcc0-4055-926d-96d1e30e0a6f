//Version Field cast to bigint
versionExprVal: "bigint({0})"
//Version Field type
versionTypeVal: "longColumn"

   "defaultComment" : "A coment here",
   "partition-spec" : {
          "key" : "DOC_CREATION_DATE",
          "column-name": "PART_DOC_CREATION_MONTH",
          "expr": "date_format(DOC_CREATION_DATE, \"yyyy-MM\")"
    },
  "tables": [
    {
        "name": "FACT_TRAVEL_DOCUMENT_HISTO",
        "mapping": {
          "merge": {
            "key-columns": ["TRAVEL_DOCUMENT_ID", "VERSION"],
            "if-dupe-take-higher": ["LOAD_DATE"]
          },
          "root-sources": [{ "blocks": [{"base": "$.mainResource.current.image"}]}],
          "columns": [
            {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})" },
            {"name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
            {"name": "VERSION", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.version"}]}},
            {"name": "DOC_CREATION_DATE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}]}},
            {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]}},
            {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
            {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
          ],
          "pit": {
            "type": "master-pit-table",
            "master" : {
              "pit-key": "TRAVEL_DOCUMENT_ID",
              "pit-version": "VERSION",
              "valid-from": "DATE_BEGIN",
              "valid-to": "DATE_END",
              "is-last": "IS_LAST_VERSION"
            }
          },
          "description": {
            "description": "",
            "granularity": ""
          }
        },
        "table-snowflake": {
          "cluster-by": ["date_trunc('WEEK', DOC_CREATION_DATE)"]
        }
    },
    {
        "name": "TKT_PNR_PARTIAL_CORR_PIT",
        "mapping": {
          "merge": {
            "key-columns": ["RESERVATION_ID", "TRAVEL_DOCUMENT_ID", "AIR_SEGMENT_PAX_ID", "COUPON_ID", "VERSION"],
            "if-dupe-take-higher": ["LOAD_DATE"]
          },
          "root-sources": [{ "blocks": [
            {"corr": "$.correlatedResourcesCurrent.TKT-PNR.correlations[*]"},
            {"coupon": "$.corrTktPnr.items[*]"}
          ]}],
          "columns": [
            {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
            {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"root": "$.mainResource.current.image.id"}]}, "expr": "hashM({0})"},
            {"name": "AIR_SEGMENT_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "false", "sources": {"blocks": [{"coupon": "$.pnrAirSegmentId"}, {"coupon": "$.pnrTravelerId"}]}, "expr": "hashM({0})"},
            {"name": "COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "false", "sources": {"blocks": [{"coupon": "$.ticketCouponId"}]}, "expr": "hashM({0})"},
            {"name": "VERSION", "column-type": "intColumn", "sources": {"blocks": [{"root": "$.mainResource.current.image.version"}]}},
            {"name": "REFERENCE_KEY_PNR", "column-type": "strColumn", "is-mandatory": "false", "sources": {"blocks": [{"corr": "$.toId"}]}},
            {"name": "REFERENCE_KEY_TRAVEL_DOC", "column-type": "strColumn", "is-mandatory": "false", "sources": {"blocks": [{"root": "$.mainResource.current.image.id"}]}},
            {"name": "REFERENCE_KEY_AIR_SEGMENT_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "false", "sources": {"blocks": [{"coupon": "$.pnrAirSegmentId"}, {"coupon": "$.pnrTravelerId"}]}},
            {"name": "REFERENCE_KEY_COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "false", "sources": {"blocks": [{"coupon": "$.ticketCouponId"}]}},
            {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"root": "$.mainResource.current.image.latestEvent.dateTime"}]}},
            {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
            {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
          ],
          "pit": {
            "type": "secondary-pit-table"
          },
          "description": {
            "description": "It contains PNR-TKT correlation information as seen by TKT.",
            "granularity": "1 PAX-SEG"
          }
        }
    },
    {
      "name": "INTERNAL_PARTIAL_CORR_TRAVEL_DOC_FLIGHT_DATE", // TKT->SKD #1/3
      "table-selectors": ["SKD_ACTIVE", "TKT_ACTIVE"],
      "mapping": {
        "merge": {
          "key-columns": ["TRAVEL_DOCUMENT_ID", "FLIGHT_DATE_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [ { "blocks": [
          {"base": "$.correlatedResourcesCurrent.TKT-SKD"},
          {"corr": "$.correlations[*]"},
          {"items": "$.corrTktSkd.items[*]" }] } ],
        "columns": [
          {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"items": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"items": "$.???"}]}, "expr": "hashM({0})"},
          {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"items": "$.id"}]}},
          {"name": "REFERENCE_KEY_FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"items": "$.???"}]}},
          {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.fromFullVersion"}]},"expr": ${versionExprVal}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"items": "$.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
        ],
        "pit": {
          "type": "secondary-pit-table"
        },
        "description": {
          "description": "It contains SKD-??? correlation information.",
          "granularity": "???",
          "links": ["???"]
        }
      }
    },
    {
      "name": "INTERNAL_PARTIAL_CORR_SEGMENT_COUPON_FLIGHT", // TKT->SKD #2/3
      "table-selectors": ["SKD_ACTIVE", "TKT_ACTIVE"],
      "mapping": {
        "merge": {
          "key-columns": ["COUPON_ID", "FLIGHT_SEGMENT_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [ { "blocks": [
          {"base": "$.correlatedResourcesCurrent.TKT-SKD"},
          {"corr": "$.correlations[*]"},
          {"items": "$.corrTktSkd.items[*]" }] } ],
        "columns": [
          {"name": "COUPON_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"items": "$.ticketCouponId"}]}, "expr": "hashM({0})"},
          {"name": "FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"items": "$.flightSegmentId"}]}, "expr": "hashM({0})"},
          {"name": "REFERENCE_KEY_COUPON_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"items": "$.ticketCouponId"}]}},
          {"name": "REFERENCE_KEY_FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"items": "$.flightSegmentId"}]}},
          {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.fromFullVersion"}]},"expr": ${versionExprVal}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"items": "$.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
        ],
        "pit": {
          "type": "secondary-pit-table"
        },
        "description": {
          "description": "It contains SKD-??? correlation information.",
          "granularity": "???",
          "links": ["???"]
        }
      }
    },
    {
      "name": "INTERNAL_PARTIAL_CORR_FLIGHT_COUPON_CODESHARE", // TKT->SKD #3/3
      "table-selectors": [["SKD_ACTIVE", "TKT_ACTIVE"]],
      "mapping": {
        "merge": {
          "key-columns": ["COUPON_ID", "CODESHARE_FLIGHT_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [ { "blocks": [
          {"base": "$.correlatedResourcesCurrent.TKT-SKD"},
          {"corr": "$.correlations[*]"},
          {"items": "$.corrTktSkd.items[*]" }] } ],
        "columns": [
          {"name": "COUPON_ID", "column-type": "binaryStrColumn",                          "sources": {"blocks": [{"items": "$.ticketCouponId"}]}, "expr": "hashM({0})"},
          {"name": "CODESHARE_FLIGHT_ID", "column-type": "binaryStrColumn",                "sources": {"blocks": [{"items": "$.partnershipFlightId"}]}, "expr": "hashM({0})"},
          {"name": "REFERENCE_KEY_COUPON_ID", "column-type": "binaryStrColumn",            "sources": {"blocks": [{"items": "$.ticketCouponId"}]}},
          {"name": "REFERENCE_KEY_CODESHARE_FLIGHT_ID", "column-type": "binaryStrColumn",  "sources": {"blocks": [{"items": "$.partnershipFlightId"}]}},
          {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.fromFullVersion"}]},"expr": ${versionExprVal}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"items": "$.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
        ],
        "pit": {
          "type": "secondary-pit-table"
        },
        "description": {
          "description": "It contains SKD-??? correlation information.",
          "granularity": "???",
          "links": ["???"]
        }
      }
    }
  ]
