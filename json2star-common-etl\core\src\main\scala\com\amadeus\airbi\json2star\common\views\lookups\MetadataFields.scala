package com.amadeus.airbi.json2star.common.views.lookups

import com.amadeus.airbi.json2star.common._

import scala.util.Try

/** MetadataFields is the result of the YAML Lookup
  *
  * @param description description of the column
  * @param examples a list of example of the column
  * @param piiType piiType of the column
  */
case class MetadataFields(
  description: Option[String],
  examples: Seq[String],
  piiType: Option[String]
)

object MetadataFields {

  /** Build the MetadataFields from the Yaml using the Column Def for the lookup
    *
    * @param col column definition
    * @param yamlLookup yaml lookup
    * @return MetadataFields
    */
  def from(col: ColumnDef, yamlLookup: YamlLookup): Seq[MetadataFields] = {
    // For a column there can be more origins (json paths)
    val yamlInfos = col.origins.flatMap { sourcePath =>
      val foundYamlInfo = sourcePath.sourceType match {
        // When the source is Literal, the information is already present in the col ColumnDef
        case LiteralType =>
          Some(
            MetadataFields(
              description = None,
              examples = Seq(sourcePath.raw),
              piiType = None
            )
          )
        case SourceCorrelationType => None
        case MappingType =>
          // Note: the lookup can end in unknown data structure of YAML file
          // Keep Try to prevent exceptions and fallback with empty value
          val foundYamlInfo = Try(yamlLookup.getByJsonPath(sourcePath)).toOption.flatMap { case (_yamlPath, yamlInfo) =>
            yamlInfo
          }
          val yamlInfo = foundYamlInfo.map(d => applyHashInfo(d, sourcePath))
          yamlInfo
      }
      foundYamlInfo
    }
    yamlInfos
  }

  /** If the json path has a hash transformation, the values found in YAML Lookup are transformed as
    * - description will have a prefix "Hash Of "
    * - example will become empty
    *
    * @param yamlInfo value of a yaml yaml lookup
    * @param jsonPath json path
    * @return enriched yaml info
    */
  private def applyHashInfo(yamlInfo: MetadataFields, jsonPath: Origin): MetadataFields = {
    if (jsonPath.transformation.exists(s => s.toLowerCase.contains("hash"))) {
      yamlInfo.copy(
        description = yamlInfo.description.map(d => s"Hash of ${d}"),
        examples = Seq()
      )
    } else {
      yamlInfo
    }
  }
}
