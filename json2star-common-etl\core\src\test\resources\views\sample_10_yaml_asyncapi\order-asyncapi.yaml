asyncapi: 3.0.0
id: 'urn:com.amadeus.events.dih.airline_order'
info:
  title: Amadeus Airline Order Management System
  version: 0.2.1
  description: This document describes the messages sent by Amadeus DIH on behalf of its Order Management System.
defaultContentType: application/json
channels:
  /events/v0/dih/AY/orders/order-data-changes:
    address: /events/v0/dih/AY/orders/order-data-changes
    messages:
      orderDataChanges.message:
        $ref: '#/components/messages/orderDataChangesMsg'
    description: Airline Order data changes are sent to this channel.
operations:
  orderDataChanges:
    action: send
    channel:
      $ref: '#/channels/~1events~1v0~1dih~1AY~1orders~1order-data-changes'
    messages:
      - $ref: >-
          #/channels/~1events~1v0~1dih~1AY~1orders~1order-data-changes/messages/orderDataChanges.message
components:
  messages:
    orderDataChangesMsg:
      name: orderDataChanges
      title: Order Data Changes
      summary: Message containing order data changes, its attachments, links, and associated business events.
      payload:
        properties:
          data:
            $ref: '#/components/schemas/OrderOrderChangeEvent'
          attachments:
            $ref: '#/components/schemas/attachments'
          links:
            $ref: '#/components/schemas/Relationship.v1'


  schemas:

    attachments:
      type: array
      description: List of attachments associated to the main payload.
      items:
        type: object
        properties:
          payload:
            type: string
            oneOf:
              - $ref: '#/components/schemas/OfferPayload'
              - $ref: '#/components/schemas/PersonalDataPayload'
          metaData:
            type: object
            description: 'Meta data associated to payload inside document envelope, helping to read/understand the DocumentEnvelope payload.'
            properties:
              dataVersion:
                type: string
                description: |-
                  It corresponds to the actual version of the data produced by the domain and respects the following properties:
                  - content of the data referring to a specific version to be immutable and unique;
                  - it is sequential and supports ordering / comparison; 
                  - this information can be replicated by the ETag when transported through HTTP (i.e. for REST APIs)
              schemaName:
                type: string
                description: |-
                  It conveys the data model name associated to the referenced payload
                  It corresponds the following pattern: `{domain}.{sub_domain}.{open_data_name}.v{grammarSemVer}`, where `sub_domain` is optional.
                  Some adaptations might be needed depending on the language code generation and to reach the actual name (i.e. offer_set would be OfferSet in the equivalent CamelCase OAS model)
                example: airline_offer.offer.offer_set
              schemaVersion:
                type: string
                description: |-
                  It refers to the grammar version used as baseline to produce the data.
                  It is based to the semver pattern (Major.minor.patch).
                  As reminder, Beta versions can be used for initial versions of the model (i.e. 0.y.z) and as such not apply the semantic versioning breaking change policy
                example: 2.1.3
              timestamp:
                type: string
                format: date-time
                description: |-
                  (optional) It refers to the time by when the data is produced. To be considered only for informative purposes 
                  (i.e. it shouldn't be used to determine older/newer relationships)
                example: '2021-01-30T08:30:00Z'
              schemaSources:
                type: array
                description: '(optional) To be returned by schema registry look-up (based on the pattern {domain}.{sub_domain}.{open_data_name}.v{grammarSemVer}). It provides the chain of open data used as source for building this Open Data'
                items:
                  type: string
                example:
                  - travel_offer.offer.v2.1.0
                  - airline_offer.offer.v2.0.0
              type:
                type: string
                description: '(optional) To be used for describing the format of data (JSON, proto, etc)'
              encoding:
                type: string
                description: '(optional) To be used for describing the encoding of data. none (nested object), text (escaped JSON string), base64.'
          uri:
            type: string
            format: url
            description: URL value
            example: /v1/airline-offer/AY/offers/1F4ZZZ_synsKdXliHsZ41U4JU5CbNrgBgf4

    links:
      title: Links
      type: object
      description: Links to retrieve the offer and order payload
      properties:
        href:
          type: string
          format: url
          description: |-
            URL value.
            When used in the context of Open Data URI string will be based on the pattern " / {schemaMajorVersion} / {data-domain} / {tenant} / {sub-domain} / {dataName} / {dataId}"
            `Tenant` and `sub-domain` are optional.
            The same URI pattern can be used for exposing the corresponding ReST API: in this case `dataName` corresponds to the API resource name.
        methods:
          type: array
          description: HTTP methods supported by the sibling URI
          items:
            type: string
            enum:
              - GET
              - POST
              - PUT
              - PATCH
              - DELETE
              - OPTIONS
        rel:
          type: string
          description: 'Expose the type of relation between the current entity and the describe entity : https://www.iana.org/assignments/link-relations/link-relations.xhtml'

    amount:
      title: amount
      type: object
      properties:
        applicabilities:
          type: array
          items:
            $ref: '#/components/schemas/applicability'
        type:
          type: string
          enum:
            - TOTAL
            - TOTAL_BASE
            - TOTAL_TAX
            - TOTAL_FEE
            - TOTAL_WITHOUT_FEE
            - TAX
            - FEE_COMPONENT
            - RESIDUAL_VALUE
            - ADDITIONAL_COLLECTION
            - MATCHED
            - BASE_SELLING_IN_FARE_FILING_CURRENCY
            - BASE_SELLING
            - BASE_NET_IN_FARE_FILING_CURRENCY
            - BASE_NET
            - COMMISSION
            - SUPER_COMMISSION
            - MAX_FEE_FOP
            - MIN_FEE_FOP
            - FEE
            - FEE_BASE
            - FEE_TAX
            - BASE_FARE
            - EQUIV_FARE
            - BASE_BEFORE_PRICE_ADJUSTMENT
            - TOTAL_BEFORE_PRICE_ADJUSTMENT
            - BASE_FILING_IN_CURRENCY_OF_SALE
            - TOTAL_FILING_IN_CURRENCY_OF_SALE
            - TOTAL_SURCHARGE
            - FUEL_SURCHARGE
            - BASE_LEVEL_FARE
            - BASE_LEVEL_TAX
            - TOTAL_BASE_LEVEL_FARE
            - BASE_MARKET_REFERENCE_PRICE
            - TOTAL_MARKET_REFERENCE_PRICE
          description: Type of Amount
        displayValue:
          type: string
        value:
          type: integer
          format: int64
          nullable: true
        decimalPlaces:
          type: integer
          nullable: true
        currency:
          type: string
        currencyType:
          type: string
          enum:
            - REDEMPTION
            - MONETARY
          description: 'currencyType defines if the currency code represents a redemption currency (ex: PTS) or a monetary currency (ex: EUR, USD).'
        amountDetails:
          $ref: '#/components/schemas/amountDetails'
        composition:
          type: array
          items:
            $ref: '#/components/schemas/amount'
        currencyContext:
          type: array
          description: 'currencyContext is used for reissue offers, to define if the currency is the Issuing Currency Of Origin (ICOO), the Issuing Currency Of Selling (ICOS) or the the Ressuing Currency Of Selling (RCOS)'
          items:
            type: string
        surcharge:
          type: array
          description: Provides details of the breakdown of surcharges
          items:
            $ref: '#/components/schemas/Surcharge'


    commissionAmountDetails:
      title: commissionAmountDetails
      allOf:
        - $ref: '#/components/schemas/amountDetails'
        - type: object
          properties:
            commissionType:
              type: string
              description: Type of commission
              enum:
                - AMOUNT
                - PERCENTAGE
                - NET_AMOUNT
    penaltyAmountDetails:
      title: penaltyAmountDetails
      allOf:
        - $ref: '#/components/schemas/amountDetails'
        - type: object
          properties:
            penaltyType:
              type: string
              description: Indicates if the penalty is an amount or a percentage.
              enum:
                - PENALTY_AMOUNT
                - PENALTY_PERCENTAGE
      description: Indicated the details of the penalty amount.
    fareData:
      title: fareData
      allOf:
        - $ref: '#/components/schemas/constructionData'
        - type: object
          properties:
            lastTicketingDate:
              type: string
              format: datetime
              description: 'the last ticketing date/time information coming from ATPCO filing (category 5 - Advance Purchase, Priceable unit/journey).'
            isValidatingCarrierFeesExist:
              type: boolean
              nullable: true
              description: Indicates that fees exist for this validating carrier
            isFOPChangeImpactFees:
              type: boolean
              nullable: true
              description: Indicates that a change of Form of Payment may have an impact on the fees
            itineraryType:
              type: string
              description: |-
                Indicates if the fare is Domestic or International. 
                This differentiation is based on the itinerary itself, as per ATPCO definition: 
                  - domestic fare: when the fare break points are in the same country code.
                  - international fare: when the fare break points are in different country codes.  
                However, this categorization could be also overriden by specific rules by airlines or market defined in Amadeus pricing engine.
              enum:
                - INTERNATIONAL
                - DOMESTIC
            fareComponentInformationList:
              type: array
              items:
                $ref: '#/components/schemas/fareComponentInformation'
            negociatedFareInformation:
              $ref: '#/components/schemas/negociatedFareInformation'
            corporateCode:
              type: string
              description: corporate code associated with the offer
            corporateName:
              type: string
              description: corporate name associated with the offer
            fareCategory:
              type: string
              description: Indicates the type of fare priced. The value "OTHER" can be used for all fare categories that are not covered currently
              enum:
                - ATAF
                - IATA
                - NEGOTIATED_CONSOLIDATOR
                - NEGOTIATED
                - DDF
                - DDF_CORPORATE
                - TOUR_CODE
                - DDF_INCLUSIVE_TOUR_CODE
                - DDF_BULK_TOUR_CODE
                - OVERRIDE
                - OTHER
                - PRIVATE_IN_TY
      x-examples:
        example-fareData:
          constructionProcess: FARE_INFORMATION_PER_TRAVELLER
          constructionData:
            constructionDataType: fareData
            isValidatingCarrierFeesExist: true
            isFOPChangeImpactFees: true
            lastTicketingDate: '2022-08-29T15:28'
          applicabilities:
            - travellersRef:
                - PAX1
                - PAX2
            - boundRef:
                - 0
                - 1
    fareComponentInformation:
      title: fareComponentInformation
      type: object
      description: Contains information at fare component level.
      x-examples:
        'example-fareComponentInformation associated to 1 airLegCabinProduct and 2 servicesItems, with 4 mini-rules references':
          fareBasis: Y6XCASE1
          publishingCarrier: 6X
          amounts:
            - type: TOTAL_BASE
              value: 4225
              decimalPlaces: 2
              currency: NUC
              currencType: MONETARY
          fareOrigin: LON
          fareDestination: NCE
          fareDirectionality: BOTH
          fareType: RP
          fareClass: Y6XCASE1
          ruleNumber: SL01
          tariffNumber: 21
          primeBookingcode: 'Y'
          validityDates:
            - notValidBefore: '2022-07-15'
            - notValidAfter: '2022-07-25'
            - productRef:
                - 13850901217
          affdFareFamilyRef: 1
          shoppingFareFamilyRef: 2
          applicabilities:
            - itemsRef:
                - serviceItemsRef:
                    - 1-PAX13850901217
                    - 1-PAX23850901217
                - productRef:
                    - 13850901217
                - termsAndConditionsRef:
                    - 0
                    - 1
                    - 2
                    - 4
      properties:
        fareBasis:
          type: string
          description: Alpha-numeric code used by airlines to identify a fare type and applicable rules to the fare
        ticketDesignator:
          type: string
          description: Indicates the ticket designator
        publishingCarrier:
          type: string
          description: Indicates the publishing carrier of the fare
        pricedPassengerTypeCode:
          type: string
          description: Indicates the passenger type priced
        corpoName:
          type: string
          description: Indicates the corporate name
        corpoCode:
          type: string
          description: Indicates the corporate code used to price
        amounts:
          type: array
          items:
            $ref: '#/components/schemas/amount'
        fareOrigin:
          type: string
          description: Indicates the start point of the fare
        fareDestination:
          type: string
          description: Indicates the end point of the fare
        fareDirectionality:
          type: string
          description: 'Indicates if the fare is one way, round trip or both one way and round trip'
          enum:
            - ONE_WAY
            - ROUNDTRIP
            - BOTH
        fareType:
          type: string
          description: Fare type code.
        fareClass:
          type: string
          description: Indicates the Fare class
        ruleNumber:
          type: string
          description: Indicates the rule of the fare
        tariffNumber:
          type: string
          description: Indicates the tariff number (standardized harmonized codes used to identify products internationally) of the fare
        primeBookingCode:
          type: string
          description: Indicates the prime booking code of the fare
        isFareMarginal:
          type: boolean
          nullable: true
          description: Specifies if fare is marginal or not
        validityDates:
          type: array
          items:
            type: object
            properties:
              notValidBefore:
                type: string
                format: date
                description: the earliest date the flight may be used at the fare paid (NVB) in ISO format.
              notValidAfter:
                type: string
                format: date
                description: the latest date the flight may be used at the fare paid (NVA) in ISO format.
              productRefs:
                type: array
                items:
                  type: string
        affdFareFamilyRef:
          type: string
          description: 'Reference to the pricing AFFD fare family item in retailing, applicable to this fare component.'
        shoppingFareFamilyRef:
          type: string
          description: 'Reference to the shopping fare family item in retailing, applicable to this fare component.'
        applicabilities:
          type: array
          items:
            $ref: '#/components/schemas/applicability'
          description: 'Indicates to which items this fare Component applies (serviceItemsRef and productsRef), and the applicable mini-rules (with termAndConditionsRef) if any.'


    amountDetails:
      title: amountDetails
      type: object
      discriminator: type
        # mapping:
        #   feeAmountDetails: '#/components/schemas/feeAmountDetails'
        #   taxAmountDetails: '#/components/schemas/taxAmountDetails'
        #   commissionAmountDetails: '#/components/schemas/commissionAmountDetails'
      #   penaltyAmountDetails: '#/components/schemas/penaltyAmountDetails'
      properties:
        id:
          type: string
        type:
          type: string
      required:
        - type

    Fee.v2:
      title: Fee
      description: Ticketing fees (OB fees) detail.
      allOf:
        - $ref: '#/components/schemas/Fee.v1'
        - type: object
          properties:
            subType:
              type: string
              description: |-
                Defines the type of the fees.
                For Amadeus pricing engine: ticketing fees, form of payment fees (including credit card fees) and service request fees'
            refundableIndicator:
              type: boolean
              description: Indicates if the fee is refundable or not
            commissionIndicator:
              type: boolean
              description: Indicates if the fee is commissionnable or not
            interlineSettlementIndicator:
              type: boolean
              description: Indicates if the fee has interline settlement


    AdjustmentInformation:
      title: AdjustmentInformation
      description: An internal resource to carry the information that are domain/product specific to AAM and dynamic pricing
      type: object
      properties:
        id:
          type: string
        applicability:
          $ref: '#/components/schemas/applicability'
        type:
          type: string
          enum:
            - AMADEUS_ANYTIME_MERCHANDISING
            - DYNAMIC_PRICING
          description: Indicates the merchandising adjustment applied
        ruleCoverage:
          type: string
          enum:
            - LOCATION
            - SEGMENT
            - BOUND
            - ITINERAY
            - NONE
          description: Indicates the coverage of the rule
        enrichedDiscountId:
          type: string
          description: Discount ID as returned by the process
        cappingStatus:
          type: string
          enum:
            - NO_CAPPING_APPLIED
            - MIN_TAX_EXCLUDED_REACHED
            - MAX_TAX_EXCLUDED_REACHED
            - MIN_TAX_INCLUDED_REACHED
            - MAX_TAX_INCLUDED_REACHED
          description: Indicate the capping criteria considered when applying the biasing rule.
        merchandisingRuleOwner:
          type: string
          description: The Carrier which is the owner of the rule in AAM product
        extendedRuleOwners:
          type: array
          description: The airlines that can also use the AAM rule part of this adjustment.
          items:
            type: string
        seatCharacteristics:
          type: array
          description: 'If the rule is applied on a seat service, list of seat characteristics for which the rule is applicable'
          items:
            $ref: '#/components/schemas/Seat.v1'
        bestRuleRef:
          type: boolean
          nullable: true
          description: Reference to indicate whether the adjustment applied is the best one per passenger
        bestItineraryRuleRef:
          type: boolean
          nullable: true
          description: Reference to indicate whether the adjustment applied is the best one for the itinerary
        biasingRule:
          type: object
          properties:
            biasingType:
              type: string
              enum:
                - DISCOUNT
                - SURCHARGE
                - WAIVER
            biasingRateOrFlat:
              type: string
              enum:
                - RATE
                - FLAT
            biasingAmount:
              $ref: '#/components/schemas/amount'
              description: Indicates the amount if the adjustment is a fixed amount
            biasingRate:
              type: integer
              description: Indicates the percentahe of adjustment with 2 decimals
            reason:
              type: string
              description: Indicates the reason for the merchandising adjustment
            hideOldPrice:
              type: boolean
              description: TRUE if biasing must be silent
              nullable: true
            aamMerActionId:
              type: string
              description: Rule ID as defined in the AAM database
            promotions:
              type: array
              description: Contains the details of the promotions included in the adjustment
              items:
                $ref: '#/components/schemas/promotionalCode'
            biasingRuleId:
              type: string
              description: Biasing rule reference in AAM message
            coverageItinerary:
              type: boolean
              description: Indicates whether the rule applied is applicable for the entire itinerary
              nullable: true
            partners:
              type: array
              description: Reference to a partner on which the biasing rules are applicable.
              items:
                type: string
            ruleWeight:
              type: string
              description: Weight of the rule as defined by the owner of the adjustment product. It allows the engine to prioritize the rules applied when the transaction is eligible for mutually exclusive adjustments.
            hasFOPCondition:
              type: boolean
              description: Set to true if the AAM rule applied has Form of Payment restrictions
              nullable: true
            dynamicPricingDetails:
              type: array
              description: |-
                Dynamic pricing amounts, in the airline currency. 
                Warning: all the amounts are the sum for all passengers listed in the applicability of the AdjustmentInformation.
                Please not that in case of boundary rules 0% (or 0 absolute value) applying, there is no Dynamic Pricing adjustment information.

                Values of elementaryPriceType:
                - TARGET_DYNAMIC_PRICE: optimized target price returned by Dynamic pricing (includes the price gap if applicable).
                - UPPER_BOUNDARY_RATE: upper limit of the reference price allowed, defined as a maximum markup in percentage of the reference price.
                - LOWER_BOUNDARY_RATE: lower limit of the reference price allowed, defined as a maximum discount in percentage of the reference price.
                - UPPER_BOUNDARY_PRICE: upper limit of the reference price allowed, defined as a maximum markup in absolute value from the reference price.
                - LOWER_BOUNDARY_PRICE: lower limit of the reference price allowed, defined as a maximum discount in absolute value from the reference price.
                - PRICE_GAP: fixed gap in absolute value compared to lowest fare family in the cabin. 
                - PRICE_BEFORE_BIASING: reference price. It is the price without any Dynamic pricing adjustment.
                - PRICE_AFTER_BIASING: dynamic price. It is the price after Dynamic pricing adjustment (including the application of the price gap and boundaries threshold).
              items:
                $ref: '#/components/schemas/ElementaryPrice.v1'
            boundaryStatus:
              type: string
              enum:
                - NONE
                - UPPER
                - LOWER
                - NOT_REACHED
                - UPPER_REACHED
                - LOWER_REACHED
              description: 'Indicates if the upper boundary limit (UPPER_REACHED), or the lower boundary limit (LOWER_REACHED), or none of them had been reached (NOT_REACHED).'

    feeAmountDetails:
      title: feeAmountDetails
      allOf:
        - $ref: '#/components/schemas/amountDetails'
        - $ref: '#/components/schemas/Fee.v2'
      description: Ticketing fees (OB fees) detail.

    feeComponentInformation:
      title: feeComponentInformation
      type: object
      properties:
        feeOrigin:
          type: string
          description: Indicates the fee component origin (for Flight related services)
        feeDestination:
          type: string
          description: Indicates the fee component destination (for Flight related services)
        feeLocationOverride:
          type: string
          description: In case of merchandising service this location replaces the fee component origin and destination
        amounts:
          type: array
          description: 'List of amounts for feeData (ex: matchedAmount)'
          items:
            $ref: '#/components/schemas/amount'
        taxAppliedStatus:
          type: string
          description: Indicates whether tax is applied or not
          enum:
            - APPLIED
            - NOT_APPLIED
        pricingInstances:
          type: integer
          description: Pricing Number of instances
        feeApplication:
          type: string
          description: |-
            ONE_WAY (OW) : fee application per One-Way
            ROUNDTRIP (RT) : fee application per Round-Trip
            PER_ITEM (PI) : fee application per Item
            PER_TRAVEL (PT) : fee application per Travel  
            PER_TICKET (TKT): fee application per Ticket
            ZERO_POINT_FIVE_PER_KILO (H) : 0.5% of the fare per kg
            ONE_PERCENT_PER_KILO (C) : 1% of the fare per kg
            ONE_POINT_FIVE_PERCENT_PER_KILO (P) : 1.5% of the fare per kg  
            PER_KILO_OVER_FREE_ALLOWANCE (K) : per 1kg over the free allowance
            PER_FIVE_KILOS_OVER_FREE_ALLOWANCE (F) : per 5kg over the free allowance
            ZERO: when the value of the amount to pay is null
          enum:
            - ONE_WAY
            - ROUNDTRIP
            - PER_ITEM
            - PER_TRAVEL
            - PER_TICKET
            - ZERO_POINT_FIVE_PER_KILO
            - ONE_PERCENT_PER_KILO
            - ONE_POINT_FIVE_PERCENT_PER_KILO
            - PER_KILO_OVER_FREE_ALLOWANCE
            - PER_FIVE_KILOS_OVER_FREE_ALLOWANCE
            - ZERO
        matchedSeqNumber:
          type: string
          description: Matching sequence S7 number
        discountID:
          type: string
          description: The unique identifier of the biasing rule applied
        reasonOfDiscount:
          type: string
          description: Explains the reason of the discount
        baggageRequest:
          $ref: '#/components/schemas/BaggagePolicyV1bis'
          description: The baggage information included in the request
        applicabilities:
          type: array
          items:
            $ref: '#/components/schemas/applicability'

    feeData:
      title: feeData
      allOf:
        - $ref: '#/components/schemas/constructionData'
        - type: object
          properties:
            baggageTravelUnitNumber:
              type: integer
              description: Indicates the number of baggage travel units
            feeComponentInformationList:
              type: array
              items:
                $ref: '#/components/schemas/feeComponentInformation'
            filingAmounts:
              type: array
              items:
                $ref: '#/components/schemas/amount'
              description: Indicates the filing amounts

    negociatedFareInformation:
      title: negociatedFareInformation
      type: object
      description: Contains all information related to the nego fares.
      properties:
        reportingMethodText:
          type: string
          description: 'Text identifying the reporting method (ie: Net Remit, Flexible commission ..etc)'
        publishingCarrier:
          type: string
          description: Indicates the negotiated fare publishing carrier.
        commercialAgreement:
          type: string
          description: Indicates the reference of the commercial agreement.
        tourCode:
          type: string
          description: Indicates the tour code identifier.
        valueCode:
          type: string
          description: Indicates the value code.
        remittanceIndicator:
          type: string
          description: Indicates the remittance level indicator.
        schemeIndicator:
          type: string
          description: Indicates type of scheme.
        formOfPaymentIndicator:
          type: string
          description: Indicates form of payment acceptance.
        amounts:
          type: array
          description: Amounts specific to nego fare.
          items:
            $ref: '#/components/schemas/amount'

    priceAdjustmentData:
      title: priceAdjustmentData
      description: Describe Price Adjustment Rules about discount and markups which has been applied on the price
      allOf:
        - $ref: '#/components/schemas/constructionData'
        - type: object
          properties:
            id:
              type: string
            type:
              type: string
              enum:
                - GENERIC
                - EXTERNAL
              description: String enumerate describing the current condition of the object.
            reasonCode:
              type: string
              description: 'Reason code for price adjustement, used for reporting and accounting'
            biasingType:
              type: string
              enum:
                - DISCOUNT
                - SURCHARGE
                - WAIVER
              description: |-
                String enumerate describing 3 types of biasing.

                Available values are
                - DISCOUNT: A deduction from the price
                - SURCHARGE: A markup on the price
                - WAIVER: The price is waived
            biasingProvider:
              type: string
              description: Identifier of the owner of the biasing to be applied.
      x-examples:
        example priceAdjustmentData:
          constructionProcess: PRICE_ADJUSTMENT
          constructionData:
            constructionDataType: priceAdjustmentData
            id: '1'
            type: GENERIC
            biasingType: DISCOUNT
            biasingProvider: FLX
            reasonCode: ELITE_DISCOUNT
    priceComputationData:
      title: priceComputationData
      allOf:
        - $ref: '#/components/schemas/constructionData'
        - type: object
          properties:
            resultingPrice:
              $ref: '#/components/schemas/amount'
            priceComputationApplication:
              type: string
              description: The application that is responsible for the price computation
            priceDescription:
              type: string
              description: Details about the nature of the price computation
            pricingApplicationDetails:
              type: array
              items:
                $ref: '#/components/schemas/AdjustmentInformation'
            applicability:
              $ref: '#/components/schemas/applicability'

    taxAmountDetails:
      title: taxAmountDetails
      allOf:
        - $ref: '#/components/schemas/amountDetails'
        - type: object
          properties:
            taxCode:
              type: string
              description: International Standards Organization (ISO) Tax code.It is a two-letter country code.
            taxSubcode:
              type: string
              description: Tax Subcode in Amadeus Pricing Engine.
            taxCountry:
              type: string
            taxName:
              type: string
              description: 'Defines the type of Taxes, PFC taxes, Airport Taxes etc'
            areaCode:
              type: string
              description: Defines the airport or city code for ZP or PFC taxes
            exempted:
              type: boolean
              description: Indicates if tax is exempted or not
              nullable: true
            taxRateIndication:
              type: string
              description: indicates that the tax is a percentage of a taxable unit charge
            taxSourceType:
              type: string
              enum:
                - PERCENTAGE_OF_FARE
                - PERCENTAGE_OF_TAX
                - FIXED_AMOUNT
              description: Indicates the type of amount on which tax is based on. A tax can be either a percentage or flat tax. A percentage tax is always based on either a Fare or another tax whereas a flat tax is a fixed amount. This is only used in tax breakdown cases.

    taxData:
      title: taxData
      allOf:
        - $ref: '#/components/schemas/constructionData'
        - type: object
          properties:
            id:
              type: string
            amount:
              $ref: '#/components/schemas/amount'
            countryCode:
              type: string
            natureType:
              type: string
            rate:
              type: number


    OfferPayload:
      title: Offer Payload
      description: 'A payload describing a single offer, associated with its metadata and the offerSet it belongs to. '
      allOf:
        - $ref: '#/components/schemas/commonMetadata'
        - type: object
          properties:
            offerData:
              type: object
              properties:
                offer:
                  $ref: '#/components/schemas/offer'
                offerSet:
                  $ref: '#/components/schemas/offerSet'

    commonMetadata:
      title: commonMetadata
      type: object
      description: Common metadata associated with any request or reply.
      discriminator: type
        # mapping:
        #   offerItemsPayload: '#/components/schemas/offerItemsPayload'
        #   offersPayload: '#/components/schemas/offersPayload'
      #   offerPayload: '#/components/schemas/offerPayload'
      properties:
        type:
          type: string
        meta:
          type: string
        links:
          type: string
        dictionaries:
          $ref: '#/components/schemas/dictionary'
        warnings:
          type: array
          description: Warnings related to the requested operation.
          items:
            type: object
            properties:
              code:
                type: integer
              desc:
                type: string
    dictionary:
      title: dictionary
      type: object
      properties:
        attachments:
          type: array
          description: List of attachments stored with the offer
          items:
            $ref: '#/components/schemas/DocumentEnvelope.v1'
        products:
          type: array
          items:
            $ref: '#/components/schemas/Product-catalogue.v1_Product'
    DocumentEnvelope.v1:
      title: DocumentEnvelope
      type: object
      x-tags:
        - DocumentEnvelope
        - blob
        - blb
        - binary
        - document
        - compression
        - zip
        - attachment
      description: 'Data model to convey any kind of document, any format with associated meta data to help the identification and the decoding.'
      x-examples:
        IATA Xml example:
          payload: ewogICAgIndhcm5pbmdzIjogW3sKICAgICAgICAgICAgImNvZGUiOiAiNjUyMjciLAo
          metaData:
            ianaContentType: xml
            name: Amadeus_Order_Change_example1
            documentType: XML
            grammarType: XSD
            grammarName: IATA_OrderChangeNotifRQ
            grammarVersion: '7.000'
            gammmarDomain: 'http://www.iata.org/IATA/2015/00/2018.2/IATA_OrderChangeNotifRQ'
            grammarRelease: IATA2018.2
            encoding: BASE_64
      properties:
        payload:
          type: string
          description: This contains the payload of the document. It can either be raw data or encoded data in b6se4. see details in metaData block.
        metaData:
          title: DocumentEnvelope_MetaData
          type: object
          description: 'Meta data associated to payload inside document envelope, helping to read/understand the DocumentEnvelope payload.'
          properties:
            documentType:
              type: string
              description: Type of the document in the payload defined by the "grammarOwner"
              example: 'pdf, xml, json, jpg'
            version:
              type: string
              description: |-
                Document Version: Define by the producer that generated the document (see payload), not linked with grammarOwner.


                Recommendation is to use semantic version. It is however not enforced as each client application can define its own versioning.
              example: 2.1.0
            grammarType:
              type: string
              description: |-
                Grammar to be used to validate and deserialize the payload.


                It is usually used if the API needs to perform business processed on the data, and therefore need to "read/understand" the data transmitted.


                If the API only "forwards" the data to another application and does not need to understand the content of the payload.
              example: 'XSD, OAS2, OAS3, PROTO2, PROTO3, EDIFACT'
            grammarName:
              type: string
              description: Name of the grammar i.e. name of the file defining the grammar ( MessageRQ.xsd)
              example: IATA_OrderChangeNotifRQ
            ianaContentType:
              type: string
              description: 'Content-type see official list to be used: https://www.iana.org/assignments/media-types/media-types.xhtml'
              example: 'EDIFACT, xml, wsdl+xml, text/xml'
            name:
              type: string
              description: 'Document name: name of the document in the payload (namespace, see domain)'
            grammarOwner:
              type: string
              description: 'Controlling agency: company/ organization name of this grammar.'
              example: 'IATA, OTA, AMA'
            grammarDomain:
              type: string
              description: Namespace defined by the GrammarOnwer
              example: com.amadeus.xml.AMA2009A
            grammarVersion:
              type: string
              description: |-
                Version of the grammar to be used to understand the payload after decoding and decompression


                Recommendation is to use semantic version. It is however not enforced as each "grammarOwner" can define its own versioning.
              example: 1.0.0
            grammarRelease:
              type: string
              description: A grammar version can be related to a specific grammarOwner Release.
              example: OTA2019A
            compressionType:
              type: string
              description: Type of compression applied to the payload (ZLIB method used (see RFC 1950) )
              enum:
                - ZLIB
                - NONE
            encoding:
              type: string
              description: 'Example: BASE_64'
              enum:
                - BASE_64
                - BASE_64_URL
    Product-catalogue.v1_Product:
      title: Product
      type: object
      description: |-
        A Product is the smallest atomic unit that can be delivered to a traveller. It contains all its intrinsic attributes and characteristics which do not depend on a particular instantiation in a Service Item.

        This model is the base for all products; each product type has its own model which is a composition of this one, plus specific attributes per product type.

        The lifecycle of products if independent from offers or offer sets; its identifier is supposed to remain stable in time.
      discriminator: productType
      properties:
        id:
          type: string
          description: identifier of the product
        name:
          type: string
          description: name of the product
        productType:
          type: string
          description: |-
            The Product Type is the general category of the product. Each Product Type has a dedicated datamodel.

            The Product type value acts as a discriminator to select the right concrete product model. Therefore, it is expected to match the name of one of the models present in this specification.
        taxonomy:
          description: 'The product categorization as per IATA Taxonomy https://airtechzone.iata.org/docs/AirlineTaxonomy.html'
          type: string
      required:
        - id
        - productType
    offer:
      title: Offer
      type: object
      description: 'Sellable entity (package of Offer Items) with price, terms and conditions corresponding to a customer''s need (construction context OfferSet). Customizable list of OfferItems identified by a unique OfferId'
      properties:
        id:
          type: string
          description: Offer ID
        offerSetRefId:
          type: string
        offerItems:
          type: array
          items:
            $ref: '#/components/schemas/offerItem'
        context:
          $ref: '#/components/schemas/context'
        construction:
          $ref: '#/components/schemas/construction'
        lifecycle:
          $ref: '#/components/schemas/lifecycle'
        priceBreakdown:
          $ref: '#/components/schemas/priceBreakdown'
        retailing:
          $ref: '#/components/schemas/retailing'
        offerSetId:
          type: string
    offerItem:
      title: Offer Item
      type: object
      description: |-
        The Offer Item is an atomic **sellable** (contractual) item with a price, the terms and conditions that should be presented to the traveller, as well as its applicabilities (ie. under which conditions the offer is considered valid).

        It is made from one or more Service Items. As such, it should be understood as a non-customizable list of ServiceItems identified by a unique OfferItemId.

        An Offer Item can be associated to any number of travelers and doesn't need to be homogenous.
      properties:
        priceBreakdown:
          $ref: '#/components/schemas/priceBreakdown'
        retailing:
          $ref: '#/components/schemas/retailing'
        construction:
          $ref: '#/components/schemas/construction'
        context:
          type: array
          items:
            $ref: '#/components/schemas/context'
        serviceItems:
          type: array
          items:
            $ref: '#/components/schemas/serviceItem'
        feeComponentDetails:
          type: array
          items:
            $ref: '#/components/schemas/priceComponentDetails'
        id:
          type: string
          description: 'The offer item identifier is unique locally to this offer, not globally unique. To globally identify a particular offer item, a full reference is needed, in the form of `/airline-offers/offers/{id1}/offer-items/{id2}`.'
        lifecycle:
          $ref: '#/components/schemas/lifecycle'
        informativeMessages:
          type: array
          items:
            $ref: '#/components/schemas/informativeMessage'
        airBounds:
          type: array
          items:
            $ref: '#/components/schemas/airBound'
    priceBreakdown:
      title: Price Breakdown
      type: object
      description: |-
        Mandatory in Offer Items, and optionally at Offer level.

        When used as a `priceBreakdown` it describes the amount in cash or points regarding the fare, the taxes and the fees.
      properties:
        id:
          type: string
        prices:
          type: array
          items:
            $ref: '#/components/schemas/Price.v2'
        calculations:
          type: array
          description: Fee or Fare calculation used to compute the price. Can be either in monetary or in redemption.
          items:
            type: object
            properties:
              calculationLine:
                type: string
                description: Calculation line
              priceType:
                type: string
                enum:
                  - FARE
                  - FEE
                description: Indicates if the calculation line is related to a Fare (air) or to a Fee (service)
              currency:
                type: string
                description: Defines the currency used in the fare calculation line.
              currencyType:
                type: string
                enum:
                  - REDEMPTION
                  - MONETARY
                description: 'Defines if the fare calculation line is on redemption currency (ex: PTS) or on monetary currency (ex: EUR, USD).'
              conversionRate:
                type: object
                properties:
                  exchangeRate:
                    type: string
                    description: 'Indicates the rate of exchange from the neutral unit of construction (NUC) to the currency of the fare ie. in case of international itineraries. The NUC is a type of private currency used for fare calculations. See here for more information https://www.iata.org/en/publications/store/iata-rates-of-exchange/'
                  bankSellingRate:
                    type: string
                    description: Indicates the rate of exchange from the fare currency to the selling currency
              applicabilities:
                type: array
                items:
                  $ref: '#/components/schemas/Relationship.v1'
      x-examples:
        example-Fare price calculation with 2 fare calculation for each pax in PTS and in NUC:
          priceCalculations:
            - calculationLine: NCE 6X YTO0.006X SAO0.00NUC0.00END ROE1.271017
              priceType: FARE
              currency: NUC
              currencyType: MONETARY
              conversionRates:
                exchangeRate: 1.271017
                bankSellingRate: 0.010274
              applicability:
                travellersRef:
                  - PAX1
                  - PAX2
            - calculationLine: NCE 6X YTO0.006X SAO0.00NUC0.00END ROE1.271017
              priceType: FARE
              currency: NUC
              currencyType: MONETARY
              conversionRates:
                exchangeRate: 1.271017
                bankSellingRate: 0.010274
              applicability:
                travellersRef:
                  - PAX3
            - calculationLine: POINTS NCE YTO30300SAO186200TOTAL216500
              priceType: FARE
              currency: PTS
              currencyType: REDEMTPTION
              applicability:
                travellersRef:
                  - PAX1
                  - PAX2
            - calculationLine: POINTS NCE YTO30300SAO86200TOTAL116500
              priceType: FARE
              currency: PTS
              currencyType: REDEMTPTION
              applicability:
                travellersRef:
                  - PAX3
    Price.v2:
      type: object
      description: Price valuation information
      title: Price.v2
      x-tags:
        - Price
        - Tax
        - Fee
        - Margin
        - Markup
        - Discount
        - Surcharge
        - Commission
        - ElementaryPriceType
      properties:
        currencyCode:
          type: string
          description: currency code of the represented amount
        currencyType:
          type: string
          description: 'type of currency (monetary, reward points or crypto-currency), as defined in Currency model'
          enum:
            - MONETARY
            - LOYALTY_REWARD
            - CRYPTO
        amount:
          type: string
          description: 'Monetary amount, formatted as it should be presented to an end user'
        value:
          type: integer
          nullable: true
          description: 'Integer representation of the amount, where amount is shifted by decimalPlaces to get an integer value'
        decimalPlaces:
          type: integer
          nullable: true
          description: Number of decimal places considered for amount display and rounding
        base:
          $ref: '#/components/schemas/Price.v2'
        totalTaxes:
          $ref: '#/components/schemas/Price.v2'
        totalSurcharges:
          $ref: '#/components/schemas/Price.v2'
        totalFees:
          $ref: '#/components/schemas/Price.v2'
        fees:
          type: array
          items:
            $ref: '#/components/schemas/Fee.v1'
        taxes:
          type: array
          items:
            $ref: '#/components/schemas/Tax.v1'
        surcharges:
          type: array
          items:
            $ref: '#/components/schemas/Surcharge'
        commissions:
          type: array
          items:
            $ref: '#/components/schemas/Commission.v1'
        detailedPrices:
          type: array
          items:
            $ref: '#/components/schemas/Price.v2'
        priceQualifier:
          type: string
          description: Enumeration used inside the Price structure describing what a specific Price instance functionally represents
          enum:
            - ADDITIONAL_FEES_IN_POINTS
            - AGENCY_TOTAL
            - AMOUNT_PAID_IN_CASH_TO_COVER_BASE_FARE
            - AMOUNT_PAID_IN_CASH_TO_COVER_TAXES
            - AMOUNT_PAID_IN_POINTS_TO_COVER_TAXES
            - AUTOMATIC_NET_FARE
            - BALANCE_OF_DISCOUNT
            - BALANCE_OF_NET_FARE
            - BALANCE_OF_PUBLISHED_FARE
            - BASE_FARE
            - BASE_SELLING_FARE
            - COUPON_TOTAL_FARE
            - COUPON_VALUE
            - DISCOUNT_FARE
            - EQUIV_AUTOMATIC_NET_FARE
            - EQUIV_EXCHANGE_VALUE
            - EQUIV_FARE
            - EQUIV_OTHER_CHARGES
            - EQUIV_PUBLISHED_FARE
            - EQUIV_SELLING_FARE
            - EXCHANGE_VALUE
            - FARE_BALANCE
            - FB_SELL_AMOUNT
            - FE_SELL_AMOUNT
            - GRAND_TOTAL
            - MANUAL_NET_FARE
            - MARGIN
            - MARKUP
            - MILEAGE
            - MILEAGE_BALANCE
            - NET_TOTAL
            - NEW_BASE_FARE
            - NEW_DISCOUNT_FARE
            - NEW_NET_FARE
            - NEW_PUBLISHED_FARE
            - NON_TAXABLE_FARE
            - NO_SHOW_PENALTY
            - OLD_BASE_FARE
            - OLD_DISCOUNT_FARE
            - OLD_NET_FARE
            - OLD_PUBLISHED_FARE
            - OTHER_CHARGES
            - PENALTY
            - PENALTY_AFTER_NETTING
            - PENALTY_BASE_FARE
            - PENALTY_BEFORE_NETTING
            - PENALTY_TAXES
            - PUBLISHED_FARE
            - PUBLISHED_FARE_IN_FILLING_CURRENCY
            - RESIDUAL_VALUE
            - SELLING_GRAND_TOTAL
            - STAFF_GV_FARE
            - STAFF_PF_FARE
            - SVC_CHARGES
            - TAX_BALANCE
            - TICKET_DIFF
            - TOTAL_ADDITIONAL_COLLECTION
            - TOTAL_AMOUNT_PAID_IN_CASH
            - TOTAL_AMOUNT_PAID_IN_POINTS
            - TOTAL_CARRIER_FEES
            - TOTAL_FARE
            - TOTAL_NEW_TAX
            - TOTAL_NON_REFUND_OLD_FARE
            - TOTAL_NON_REFUND_OLD_TAX
            - TOTAL_PAID_IN_CASH
            - UPSELL_DIFFERENCE
            - REVENUE_ACCOUNTING_PAYMENT
            - REVENUE_ACCOUNTING_LISTING
            - REVENUE_ACCOUNTING_BILLED
            - REVENUE_ACCOUNTING_ACCOUNTED
            - REVENUE_ACCOUNTING_REFERENCE_FARE
            - REVENUE_ACCOUNTING_FILLING
            - REVENUE_ACCOUNTING_REPORTING
            - REVENUE_ACCOUNTING_REFUNDED
            - REVENUE_ACCOUNTING_PRORATION
            - REVENUE_ACCOUNTING_ORIGIN
            - REVENUE_ACCOUNTING_UNDEFINED
            - REVENUE_ACCOUNTING_RECONCILIATION
            - REWARD_FARE
            - CHEAPEST_ECONOMY_REWARD_FARE
            - REFERENCE_FARE
            - TOTAL_TAX_IN_POINTS
            - TOTAL_ADDITIONAL_SSFEE
            - TOTAL_ADDITIONAL_SSFEE_IN_POINTS
            - PUBLISHED_RETAIL_FARE
            - TOTAL_TAX_EXCLUDING_YQ_YR
            - BASE_FARE_IN_POINTS
            - TOTAL_MARKET_REFERENCE_PRICE
            - BASE_MARKET_REFERENCE_PRICE
            - SUB_TOTAL
            - TOTAL_TAX
        priceBreakdownType:
          type: string
          description: 'Specifies the dimension used for the split, for instance PER_BOUND or PER_PASSENGER. Only used for Prices used within a priceBreakdown.'
          x-ama-enum:
            name: priceBreakdownType
            values:
              - value: PER_BOUND
                description: 'When doing a price breakdown per itinerary bound (outbound, inbound trip)'
              - value: PER_PASSENGER
                description: When doing a price breakdown per passenger or passenger type (each referencing the passenger(s) concerned)
              - value: PER_PRODUCT
                description: When doing a price breakdown per product being purchased
              - value: PER_PAYMENT_DUE_TIME
                description: 'When doing a price breakdown for a split payment (partial prepayment at booking time, remainder at delivery time)'
        priceBreakdowns:
          type: array
          description: 'One or more splits of the current price per a given dimension (per passenger, per bound, ...). Breakdowns (i.e. split) and detailedPrices (i.e. details about some specific elements) can be provided recursively.'
          items:
            $ref: '#/components/schemas/Price.v2'
        applicability:
          type: array
          items:
            $ref: '#/components/schemas/Relationship.v1'
    Fee.v1:
      type: object
      description: |-
        Fee applied to provide a service or a product to the client. Each  type of  fees  is  assigned  a  code,  a 3-digit  subcode  and  a commercial name. Example of code:
        OA, OB and OC are all Carrier (imposed) Service Fees.
        OA: IATA-defined code used for booking fees (optional, validating carrier only, not interlineable).
        OC: IATA-defined code used for fare related optional service or rule-buster service fees (optional, validating carrier only, not interlineable).
        OB Fee definition: also known as Carrier Ticketing Fees, are defined by IATA as the industry standard for ticketing and for the application of credit card fees."O" indicates fees are "Off ticket" fees and are therefore not actually shown on the ticket. Ticket fees are used by airlines to instruct pricing systems how to collect ticketing (OB T type fees) and/or credit card fees (OB F type fees) on an airline s behalf when they are the validating carrier.
        Ticket fees can only be imposed and/or collected by the validating/plating carrier. These fees are used by the airline to cover the costs of accepting payment by credit card. The ticket fee is reported as tax code OB by IATA assignment.
      title: Fee
      properties:
        code:
          type: string
          description: Indicates the type of fee. Ex- "OB"
        subCode:
          type: string
          description: Identify uniquely the OB fee associated to the TST ( Amadeus Transitional Storage ticket ) - 1.Automated ticketing subcode (Txx) 2. Non-automated ticketing subcodes (Rxx)
        description:
          type: string
          description: Indicates the commercial name. Describes the fee
    Tax.v1:
      type: object
      description: 'IATA Tax definition: An impost for raising revenue for the general treasury and which will be used for general public purposes.'
      title: Tax
      x-examples:
        'New Tax (IZ : Air Passenger Solidarity Tax ; EB : embarkment)':
          value:
            amount: '18'
            currency: DKK
            code: IZ
      properties:
        value:
          type: integer
          description: 'Defines amount without decimal separator, it is an integer.'
        decimalPlaces:
          type: integer
          description: Defines the number of decimal position for the value
        amount:
          type: string
          description: Defines amount with decimal separator.
        currency:
          type: string
          description: 'Defines a monetary unit. It is a three alpha code (iata code). Example: EUR for Euros, USD for US dollar, etc.'
        code:
          type: string
          description: International Standards Organization (ISO) Tax code.It is a two-letter country code.
        nation:
          type: string
          description: 'Tax nation or designator. Example: US, GB'
    Surcharge:
      title: Surcharge
      type: object
      x-examples:
        example-basic:
          code: YDS
          description: Young driver surcharge
          value: 13525
          decimalPlaces: 2
          currencyCode: EUR
      description: Surcharge amounts such as the airport fee or the roadside assistance
      properties:
        description:
          type: string
          description: Example - Additional driver surcharge
        amount:
          type: string
          description: Monetary value formatted as a string
          example: '5000.50'
        currency:
          type: string
          description: Defines a specific monetary unit using ISO4217 currency code
          example: USD
        decimalPlaces:
          type: number
          description: Defines the number of decimal position of the monetary value.
          example: 2
        value:
          type: number
          description: Defines the monetary value without decimal position
          example: 500050
        code:
          description: Surcharge code
          type: string
          pattern: '[A-Z0-9]{1,4}'
    Commission.v1:
      type: object
      title: Commissions_Commission
      properties:
        commissionType:
          type: string
          description: Nature of the commission.
          enum:
            - NEW
            - NEW_RATE_ON_NET_FARE
            - CAP_LIMIT_AMOUNT
            - SECONDARY
            - SUPPLEMENTARY_AMOUNT
            - VAT_ON_NEW
            - VAT_RATE_ON_OLD
            - CANCELLATION_PENALTY
            - OLD
        amount:
          $ref: '#/components/schemas/ElementaryPrice.v1'
        percentage:
          type: number
          description: Percentage in case of a rate commission - can be decimal.
    ElementaryPrice.v1:
      title: ElementaryPrice
      type: object
      description: Structure representing any kind of monetary value.
      properties:
        value:
          type: integer
          description: Defines the monetary value without decimal position.
          example: 500050
        decimalPlaces:
          type: integer
          description: Defines the number of decimal position of the monetary value.
          example: 2
        currency:
          type: string
          description: Defines a specific monetary unit using ISO4217 currency code.
          example: CND
        elementaryPriceType:
          type: string
          description: 'Defines the type of price, eg. for base fare, total, grand total.'
          example: BASE_FARE
    Relationship.v1:
      description: Relationship allows to cross reference 2 entities via Link and/or id .
      title: Relationship
      x-tags:
        - Relationship
        - Relation
        - Link
      allOf:
        - type: object
          properties:
            id:
              type: string
              description: id of the related resource
            type:
              type: string
              description: Type of the related resource
              example: processed-dcs-passenger
            ref:
              type: string
              format: uri-reference
              description: Local reference of the related resource
            targetSchema:
              type: string
              description: The targetSchema would be an instance of definition to be used for building the associated API Request operation
            targetMediaType:
              type: string
              description: 'Related to "targetSchema", to be specified in case the media type is different for the referenced API request'
            hrefSchema:
              type: string
              description: 'type ( format, type, patterns, enum) definition for each URI parameters.'
            targetMetaForOpenData:
              $ref: '#/components/schemas/MetaForOpenData.v1'
        - $ref: '#/components/schemas/Links.v2'
    MetaForOpenData.v1:
      title: MetaForOpenData
      type: object
      description: It describes information with respect to the reusage of outer domains data (and give access to associated data models).
      x-tags:
        - MetaForOpenData
      properties:
        dataVersion:
          type: string
          description: |-
            It corresponds to the actual version of the data produced by the domain and respects the following properties:
            - content of the data referring to a specific version to be immutable and unique;
            - it is sequential and supports ordering / comparison; 
            - this information can be replicated by the ETag when transported through HTTP (i.e. for REST APIs)
        schemaName:
          type: string
          description: |-
            It conveys the data model name associated to the referenced payload
            It corresponds the following pattern: `{domain}.{sub_domain}.{open_data_name}.v{grammarSemVer}`, where `sub_domain` is optional.
            Some adaptations might be needed depending on the language code generation and to reach the actual name (i.e. offer_set would be OfferSet in the equivalent CamelCase OAS model)
          example: airline_offer.offer.offer_set
        schemaVersion:
          type: string
          description: |-
            It refers to the grammar version used as baseline to produce the data.
            It is based to the semver pattern (Major.minor.patch).
            As reminder, Beta versions can be used for initial versions of the model (i.e. 0.y.z) and as such not apply the semantic versioning breaking change policy
          example: 2.1.3
        timestamp:
          type: string
          format: date-time
          description: |-
            (optional) It refers to the time by when the data is produced. To be considered only for informative purposes 
            (i.e. it shouldn't be used to determine older/newer relationships)
          example: '2021-01-30T08:30:00Z'
        schemaSources:
          type: array
          description: '(optional) To be returned by schema registry look-up (based on the pattern {domain}.{sub_domain}.{open_data_name}.v{grammarSemVer}). It provides the chain of open data used as source for building this Open Data'
          items:
            type: string
          example:
            - travel_offer.offer.v2.1.0
            - airline_offer.offer.v2.0.0
        type:
          type: string
          description: '(optional) To be used for describing the format of data (JSON, proto, etc)'
        encoding:
          type: string
          description: '(optional) To be used for describing the encoding of data. none (nested object), text (escaped JSON string), base64.'
    Links.v2:
      title: Links
      type: object
      description: 'Web link , see `https://tools.ietf.org/html/rfc8288`'
      properties:
        href:
          type: string
          format: url
          description: |-
            URL value.
            When used in the context of Open Data URI string will be based on the pattern " / {schemaMajorVersion} / {data-domain} / {tenant} / {sub-domain} / {dataName} / {dataId}"
            `Tenant` and `sub-domain` are optional.
            The same URI pattern can be used for exposing the corresponding ReST API: in this case `dataName` corresponds to the API resource name.
        methods:
          type: array
          description: HTTP methods supported by the sibling URI
          items:
            type: string
            enum:
              - GET
              - POST
              - PUT
              - PATCH
              - DELETE
              - OPTIONS
        rel:
          type: string
          description: 'Expose the type of relation between the current entity and the describe entity : https://www.iana.org/assignments/link-relations/link-relations.xhtml'
    retailing:
      title: retailing
      type: object
      description: retailing data
      properties:
        id:
          type: string
        branding:
          type: object
          properties:
            fareFamilies:
              type: array
              description: 'List of fare families applicable at different level (itinerary, bounds, fare Components), from different sources (AFFD, shopping) to the offerItem.'
              items:
                $ref: '#/components/schemas/fareFamily'
        baggageDisclosure:
          title: baggageDisclosure
          type: object
          properties:
            baggageTravelUnits:
              type: array
              description: Baggage details of a baggage travel unit
              items:
                type: object
                properties:
                  freeBaggageAllowances:
                    type: array
                    description: Free baggage details associated to a baggage travel unit
                    items:
                      type: object
                      properties:
                        productRef:
                          type: string
                          description: Reference to the baggage product in the catalogue of product
                        atpcoReasonForIssuanceCode:
                          type: string
                          description: The Reason for Issuance Code (RFIC) is a single character code that defines which group of services an Electronic Miscellaneous Document (EMD) belongs to.
                        atpcoReasonForIssuanceSubCode:
                          type: string
                          description: 'A Reason For Issuance Sub-Code (RFISC) is a three-letter code in the coupon of an Electronic Miscellaneous Document (EMD) used to inform about the nature of the service purchased. The RFISC can either be defined by the industry or by the carrier. '
                        freeBaggageDetails:
                          description: Indicates the details of the allowed free baggage
                          $ref: '#/components/schemas/BaggagePolicyV1bis'
                        applicabilities:
                          type: array
                          items:
                            $ref: '#/components/schemas/applicability'
                  mostSignificantCarrierCode:
                    type: string
                    description: Two letter IATA standard carrier code
                    example: 6X
          x-examples:
            example-baggageDisclosure:
              baggageTravelUnit:
                freeBaggageAllowance:
                  productRef: BG1
                  atpcoReasonForIssuanceCode: C
                  atpcoReasonForIssuanceSubCode: 0DF
                  baggageDetails:
                    baggageCharacteristics:
                      policyDetails:
                        value: 3
                  applicabilities:
                    travellersRef:
                      - PAX1
                      - PAX2
                    productRef: 2943294629
                mostSignificantCarrierCode: 6X
        informativeMessage:
          type: array
          items:
            $ref: '#/components/schemas/informativeMessage'
        itemRanking:
          type: integer
        richContent:
          type: string
        presentation:
          type: string
        reasonForPriceAdjustment:
          type: string
          description: Provide an aggregated reason of all the price adjustments
          x-examples:
            Simple discount: Your loyalty is awarded of 15% discount.
            Cumulative discounts: 'You have a special discount of 20% for Black Friday, with an additional promotional discount of 10% giving you a total discount of 28%.'
        reasonCodeForPriceAdjustment:
          type: string
          description: Provide an aggregated reason of all the price adjustments
          x-examples:
            Simple discount: Your loyalty is awarded of 15% discount.
            Cumulative discounts: 'You have a special discount of 20% for Black Friday, with an additional promotional discount of 10% giving you a total discount of 28%.'
        boundMapping:
          type: array
          description: |-
            Used to indicate the mapping between the requested bounds and the products. 
            For effective bounds please use offerSet/construction/constructionSteps with constructionProcess=BOUND_INFORMATION.
          items:
            type: object
            properties:
              itemRef:
                type: string
              requestedboundRef:
                type: string
        remainingSupply:
          type: string
          description: |-
            Remaining supply available for the given item. 
            For the case of serviceItem containing airLegCabin information, remainingSupply indicated the remaining seat availability of the segment. Possible values from 0 to 9: If 0: no seat available, from 1 to 8: indicates the actual number of seats available, if 9: indicates that 9 seats or more are available.
        medias:
          type: array
          description: |-
            This is used to store the medias associated with the service. 
            The source/owner of the media will be indicated using the category field in the model.
          items:
            $ref: '#/components/schemas/Media'
        marketReferencePrices:
          type: array
          description: Indicates the price before the discounts
          items:
            $ref: '#/components/schemas/Price.v2'
    fareFamily:
      title: Fare Family
      type: object
      properties:
        associatedServices:
          type: array
          items:
            type: object
            x-examples:
              PRE-PAID BAGGAGE:
                associatedServices:
                  productRef: BG1
                  atpcoClassification: F
                  reasonForIssuanceCode: C
                  reasonForIssuanceSubCode: 0AA
                  fareFamilyServiceStatus: CHARGEABLE
            properties:
              id:
                type: string
              productRef:
                type: string
                description: Reference to the product linked with the service
              atpcoClassification:
                type: string
                description: Type of the service according to ATPCO
              atpcoReasonForIssuanceCode:
                type: string
                description: The Reason for Issuance Code (RFIC) is a single character code that defines which group of services an Electronic Miscellaneous Document (EMD) belongs to.
                minLength: 1
                maxLength: 1
              atpcoReasonForIssuanceSubCode:
                type: string
                description: 'A Reason For Issuance Sub-Code (RFISC) is a three-letter code in the coupon of an Electronic Miscellaneous Document (EMD) used to inform about the nature of the service purchased. The RFISC can either be defined by the industry or by the carrier. '
                minLength: 3
                maxLength: 3
              fareFamilyServiceStatus:
                type: string
                description: Indicates the applicability/status of the service linked to the fare family
                enum:
                  - NOT_OFFERED
                  - CHARGEABLE
                  - FREE
                  - DISPLAYED_BUT_NOT_OFFERED
              applicabilities:
                $ref: '#/components/schemas/applicability'
                description: Association with the leg and passengers
        code:
          type: string
          description: Defines code of Fare Family
        owner:
          type: string
          description: Defines the Company owner of Fare Family
        ranking:
          type: string
          description: 'Defines the Ranking of the fare family, it s used for extented pricing record'
        name:
          type: string
          description: Defines the name of the fare family
          example: FB ECONOMY LIGHT
        advertisedCabin:
          type: string
          description: 'Defines the cabin of the fare family (M:economy, W:Premium economy, C:Business, F:First)'
          example: M
        commercialFareFamily:
          type: string
          description: A Commercial Fare Family code is used to target one or several Fare Families. The same Commercial Fare Family can target different sets of fare families depending on the geographical market and the point of sale of the request.
          example: 'DCSREVCHG has three set of fare familes (WGADCS, ANYDCS, BUSDCS)'
        fareFamilyDescription:
          type: string
          description: Description of the fare family.
        id:
          type: string
          description: Fare family item identifier.
        applicabilities:
          type: array
          items:
            $ref: '#/components/schemas/applicability'
      description: |-
        Contains fare family information.
        The applicability within Fare Family structure indicates to which item this fare family applies: 
          offerItemsRef when the fare family applies for the whole itinerary (if the offerItem contains air) or the ancillary service (if the offerItem contains only service)
          boundRef when the fare family applies to specific bound(s)
          serviceItemsRef when the fare family applies to specific service item(s)
      x-examples:
        'example-fare family BCLASSIC: applicable to the itinerary and bound 2':
          applicabilities:
            - itemsRef:
                - offerItemsRef:
                    - 1
            - boundRef:
                - 2
          id: 1
          code: BCLASSIC
          owner: 6X
          ranking: 9991
          name: BUSINESS CLASSIC
          commercialFareFamily: CFF6X
          fareFamilyDescription: BUSINESS CLASSIC
    applicability:
      title: Applicability
      type: object
      properties:
        travelerIds:
          type: array
          description: Reference to the id of an OfferStakeholder of type 'CUSTOMER' in offerset/offerInstructions
          items:
            type: string
        itemsRef:
          type: object
          properties:
            offerIds:
              type: array
              description: Reference(s) to the id of the offer(s) as defined by the offer management system
              items:
                type: string
            offerItemIds:
              type: array
              description: Reference(s) to the id of the offerItem(s) from the offer Referenced under applicability/offerIds. We expect to have only one offer referenced since an offerItemId is unique in a context of an offer
              items:
                type: string
            serviceItemIds:
              type: array
              description: Reference(s) to the id of the serviceItems(s) from the offerItem Referenced under applicability/offerItemsIds. We expect to have only one offerItem referenced since an serviceItemsId is unique in a context of an offerItem
              items:
                type: string
            productIds:
              type: array
              description: Reference(s) to the id of the Product(s). Could be the product Id as defined by the product catalog or the product attached under the dictionnary section of the current payload
              items:
                type: string
            otherElementsRef:
              type: array
              items:
                type: string
            termsAndConditionIds:
              type: array
              description: Reference(s) to the id of the contractRules/termsAndConditions from either the offerItem reference in this applicability or the contextual offerItem
              items:
                type: string
            orderIds:
              type: array
              description: Reference(s) to the id of the order(s) as defined by the order management system
              items:
                type: string
            orderItemRef:
              type: string
            orderItemIds:
              type: array
              description: Reference(s) to the id of the orderItems(s) from the order referenced in applicability/orderIds
              items:
                type: string
            orderFulfillmentRef:
              type: string
            orderFulfillmentIds:
              type: array
              description: Reference(s) to the id of the orderFulfillment(s) from the orderItem referenced in applicability/orderItemIds
              items:
                type: string
        merchantsRef:
          type: array
          items:
            type: string
        customersRef:
          type: array
          items:
            type: string
        responsiblesRef:
          type: array
          items:
            type: string
        deliveryProvidersRef:
          type: array
          items:
            type: string
        providersRef:
          type: array
          items:
            type: string
        externalProductRef:
          type: string
          description: It contains the product reference of the legacy offer. It could be either the service_reference or the flight_ref depending on the type of the serviceItem. ex. SEG5
        boundIds:
          type: array
          description: Reference to the id of the offerItem/airBound
          items:
            type: string
        itemState:
          type: string
          enum:
            - TRANSFERRED
            - CANCELLED
          description: |-
            - TRANSFERRED: State of an item which is transferred. Usage in exchange: Specific situation on offer reconstruction when a ticket is partialy exchanged. Some of the remaining open fulfillments should be transferred into a new orderItem. This means only if this particular order fulfillment is transferred into a new serviceItem(fulfillment), then this new offerItem is valid.
            - CANCELLED: State of an item which is cancelled. Usage in exchange: only if this particular order fulfillment is cancelled, then this new offerItem is valid.
      x-examples:
        OrderStateDuringExchangeExample:
          applicabilities:
            - itemRef:
                orderRef: orderID1
                orderItemRef: orderID1-1
                orderFulfillmentRef: orderID1-1-1
              itemState: CANCELLED
            - itemRef:
                orderRef: orderID1
                orderItemRef: orderID1-1
                orderFulfillmentRef: orderID1-1-2
                serviceItemRef: serviceItemID2-2-1
              itemState: TRANSFERRED
      description: |-
        Link an item to other records:
        - set of travellers or other stakeholders
        - other offer resources (offers, offer items, ...)
        - order actions (order item is cancelled, ...)
    BaggagePolicyV1bis:
      title: BaggagePolicy
      type: object
      description: 'Gives information on a particular policy for a Carry-On or Checkin Baggage. '
      properties:
        type:
          type: string
          enum:
            - WEIGHT
            - PIECE
          description: Baggage allowance by weight (expressed in kilograms or pounds) or by pieces
        weightUnit:
          type: string
          enum:
            - LB
            - KG
          description: Weight unit used in baggage allowance
        quantity:
          type: integer
          nullable: true
          description: List of applicable restrictions
        baggageCharacteristics:
          title: BaggagePolicyDescription
          type: object
          description: Description of one baggage Policy
          properties:
            policyDetails:
              title: BaggagePolicyRestriction
              type: array
              items:
                type: object
                description: 'Baggage Policies restriction in weight Size for Carry on and Checkin luggage. '
                properties:
                  type:
                    type: string
                    enum:
                      - WEIGHT
                      - SIZE
                  subType:
                    type: string
                    enum:
                      - RANGE_OCCURRENCE
                      - RANGE_WEIGHT
                      - RANGE_PIECE
                      - UNDEFINED
                    description: Determine the restriction in size or weight
                  qualifier:
                    type: string
                    enum:
                      - MAX
                      - MIN
                      - UPTO
                      - OVER
                  value:
                    type: integer
                    nullable: true
                    description: Value of the policy restriction
                  unit:
                    type: string
                    enum:
                      - KG
                      - LB
                      - INCH
                      - CM
                    description: ' Unit used in the policy description'
    informativeMessage:
      title: Informative Message
      type: object
      description: 'Contains the list of informative messages, applicable to a list of traveller(s) for a given type of message.'
      properties:
        travellersRef:
          type: array
          items:
            type: string
        messageType:
          type: string
          enum:
            - ENDORSEMENT
            - APPENDED_MESSAGE
          description: |-
            Type of the message. Possible values are:
            - ENDORSEMENT: corresponds to the endorsement fare element (FE line)
            - APPENDED_MESSAGE: corresponds to the informative pricing message(s) returned by pricing or repricing step.
        messages:
          type: array
          description: List of messages applicable for the traveller(s) referenced in travellersRef and corresponding to the message type.
          items:
            type: object
            properties:
              text:
                type: string
                description: Message free text.
              code:
                type: string
                description: code to identify the message
                example: BaggageUnbalanced
              rank:
                type: integer
                description: Rank of the message among messages for given messageType and traveller(s).
              isHighlighted:
                type: boolean
                description: 'In case messageType ="APPENDED_MESSAGE", identifies the message with highest priority among the messages requiring a hierarchy.'
              applicabilities:
                type: array
                description: To define if the message applies to a particular bound or passenger or segment...
                items:
                  $ref: '#/components/schemas/applicability'
      x-examples:
        example-informativeMessage APPENDED_MESSAGE:
          travellersRef:
            - PAX1
            - PAX2
          messageType: APPENDED_MESSAGE
          messages:
            - text: NOT FARED AT PASSENGER TYPE REQUESTED *5*
              rank: 1
              isHighlighted: true
            - text: CARD PAYMENT FEES UP TO EUR 47.04 MAY APPLY
              rank: 2
            - text: PRICED WITH VALIDATING CARRIER 6X - REPRICE IF DIFFERENT VC
              rank: 3
        example-informativeMessage ENDORSEMENT:
          travellersRef:
            - PAX3
            - PAX4
          messageType: ENDORSEMENT
          messages:
            - text: CHNG FEE APPLY /REFUND FEE APPLY /NO SHOW FEE APPLY
    Media:
      type: object
      title: Media
      description: 'Media is a digital content like image, video with associated text and description, several scales and some metadata can be provided also.'
      properties:
        id:
          type: string
          description: Image Id
          example: 69810B23CB8644A18AF760DC66BE41A6
        type:
          type: string
          default: file
          description: data type file
          enum:
            - file
            - Image
            - Icon
          x-enum-varnames:
            - file
            - Image
            - Icon
          example: file
        name:
          type: string
          description: name of the file
          example: guest_room
        title:
          type: string
          description: media title
          example: My image title
        caption:
          type: string
          description: Media caption
          example: This is a media caption
        hint:
          type: string
          description: Hint text
        alt:
          type: string
          description: 'Media description for visually impaired people. See https://www.w3.org/WAI/tutorials/images/'
          example: This text is meant to be read by screen reader softwares
        href:
          type: string
          format: uri
          example: 'http://pdt.multimediarepository.testing.amadeus.com/cmr/retrieve/hotel/69810B23CB8644A18AF760DC66BE41A6'
          description: |
            href to display the original media.
            href for scaled versions of that media are provided at MediaScale level
        description:
          type: object
          description: 'Specific type to convey a list of string for specific information type ( via qualifier) in specific character set, or language'
          title: QualifiedFreeText
          properties:
            text:
              type: string
              description: Free Text
              example: Do you need and example ?
            lang:
              type: string
              description: Langage as per IANA.org RFC 5646
              example: fr-FR
            status:
              type: string
              example: ACTIVE
            charSet:
              type: string
              description: Character Set
              enum:
                - ASCII_7
                - UTF_8
              example: UTF_8
            encoding:
              type: string
              description: encoding charset
              enum:
                - BINARY
                - BASE_64
              example: BASE_64
            ianaContentType:
              type: string
              description: 'Follow the RFC define by http://www.iana.org/assignments/media-types/media-types.xhtml'
              example: text/plain
        category:
          type: string
          description: media category
          example: EXTERIOR
        tags:
          type: array
          description: tags associated to the media
          items:
            type: string
        mediaScales:
          type: array
          items:
            type: object
            title: Media Scale
            description: Media Scale is a version in the media with different size and dimension.
            properties:
              href:
                type: string
                format: uri
                example: 'http://pdt.multimediarepository.testing.amadeus.com/cmr/retrieve/hotel/69810B23CB8644A18AF760DC66BE41A6'
                description: href to display the scaled versions of the media
              size:
                type: object
                properties:
                  value:
                    type: integer
                    example: 200
                  unit:
                    $ref: '#/components/schemas/unit'
              dimensions:
                $ref: '#/components/schemas/dimensions'
              duration:
                type: string
                description: 'Defines the duration as per ISO 8601 (https://en.wikipedia.org/wiki/ISO_8601#Durations). Example pattern : ''^P(?=.*[0-9])([YMDTHMS0-9.]+).*[YMDTHMS]$'''
                example: P1Y2M3DT4H5M6S
                title: Duration
        mediaMetaData:
          type: object
          title: Media MetaData
          description: ''
          properties:
            mediaType:
              type: string
              description: 'media type as per IANA (https://www.iana.org/assignments/media-types/media-types.xhtml)'
              enum:
                - application
                - audio
                - font
                - example
                - image
                - message
                - model
                - multipart
                - text
                - video
              example: image
            subType:
              type: string
              description: media subtype / file format
              example: 'PNG, MKV'
            encoding:
              type: string
              description: specifies the media encoding
              example: 'PNG, H265'
            etag:
              type: string
              format: date-time
              description: |
                The date and time of the last update.
              example: '2010-08-14T13:00:00'
            size:
              type: object
              properties:
                value:
                  type: integer
                  example: 200
                unit:
                  type: string
                  title: Unit
                  description: Unit type.
                  enum:
                    - NIGHT
                    - PIXELS
                    - KILOGRAMS
                    - POUNDS
                    - CENTIMETERS
                    - INCHES
                    - BITS_PER_PIXEL
                    - KILOMETERS
                    - MILES
                    - BYTES
                    - KILOBYTES
                    - KILOGRAMS_PER_LITRE
                    - POUNDS_PER_GALLON
                    - POUNDS_PER_US_GALLON
            dimensions:
              description: dimensions
              type: object
              title: Dimensions
              properties:
                height:
                  type: integer
                  description: height of an object
                width:
                  type: integer
                  description: width of an object
                length:
                  type: integer
                  description: length of an object
                decimalPlaces:
                  type: integer
                  description: number of decimal places
                unit:
                  $ref: '#/components/schemas/unit'
            duration:
              type: string
              description: 'Defines the duration as per ISO 8601 (https://en.wikipedia.org/wiki/ISO_8601#Durations). Example pattern : ''^P(?=.*[0-9])([YMDTHMS0-9.]+).*[YMDTHMS]$'''
              example: P1Y2M3DT4H5M6S
              title: Duration
            application:
              type: string
              description: Application name for viewing or editing the Media
            mediaSource:
              type: object
              title: Media Source
              description: Source and copyright of the media owner
              properties:
                code:
                  type: string
                  description: Owner code of the media
                version:
                  type: string
                  description: Version of the file
                filename:
                  type: string
                  description: File name of the media
                symbology:
                  type: string
                  description: 'Logo, icon'
                copyright:
                  type: string
                  description: Copyright text related to the media Owner
            clickToAction:
              type: object
              description: <a href = '$href'> plainText </a>
              properties:
                plainText:
                  type: string
                  description: 'hyperlink text:  <a> $plainText </a>'
                href:
                  type: string
                  description: url associated actionText <a href = '$href'> </a>
        mediaType:
          type: string
          description: Mimetype
          example: IMAGE
    unit:
      type: string
      title: Unit
      description: Unit type.
      enum:
        - NIGHT
        - PIXELS
        - KILOGRAMS
        - POUNDS
        - CENTIMETERS
        - INCHES
        - BITS_PER_PIXEL
        - KILOMETERS
        - MILES
        - BYTES
        - KILOBYTES
        - KILOGRAMS_PER_LITRE
        - POUNDS_PER_GALLON
        - POUNDS_PER_US_GALLON
    dimensions:
      description: dimensions
      type: object
      title: Dimensions
      properties:
        height:
          type: integer
          description: height of an object
        width:
          type: integer
          description: width of an object
        length:
          type: integer
          description: length of an object
        decimalPlaces:
          type: integer
          description: number of decimal places
        unit:
          $ref: '#/components/schemas/unit'
    construction:
      title: Construction Data
      type: object
      description: |-
        Data that needs to be persisted and/or exchanged to build the offers within the strict boundaries of the offer management processes. 
        Consequently, this must not be used to exchange data with the sales channels.
        Typical example are the data that were used to build an initial price, that are necessary to perform a re-pricing later on.

        Ownership of construction data entries: 
        - Any offer processes can create an entry.
        - Any offer processes can read an entry.
        - Only the process creating the entry can update it.
      properties:
        id:
          type: string
        constructionSteps:
          type: array
          description: Log of construction steps (append only)
          items:
            $ref: '#/components/schemas/constructionStep'
    constructionStep:
      title: Construction Step
      type: object
      properties:
        id:
          type: string
        constructionProcess:
          type: string
          enum:
            - TAX
            - CONVERSION
            - FEE_COMPONENT
            - SUM
            - FARE_COMPONENT
            - OFFER_CREATION
            - AVAILABILITY_INFORMATION
            - BOUND_INFORMATION
            - FEE_INFORMATION_PER_TRAVELLER
            - FARE_INFORMATION_PER_TRAVELLER
            - SERVICEITEM_INFORMATION
            - PRICE_ADJUSTMENT
            - FARE_BY_RULE
            - OFFER_ITEM_INFORMATION
            - PRICE_COMPUTATION_DETAILS
            - FLIGHT_REWARD_INFORMATION
        constructionData:
          $ref: '#/components/schemas/constructionData'
        parentConstructionStepsRef:
          type: array
          items:
            type: string
        applicabilities:
          type: array
          items:
            $ref: '#/components/schemas/applicability'
    constructionData:
      title: Construction Step Data
      type: object
      discriminator: constructionDataType
        # mapping:
        #   fareData: '#/components/schemas/fareData'
        #   offerCreationData: '#/components/schemas/offerCreationData'
        #   offerSetCreationData: '#/components/schemas/offerSetCreationData'
        #   taxData: '#/components/schemas/taxData'
        #   feeData: '#/components/schemas/feeData'
        #   priceAdjustmentData: '#/components/schemas/priceAdjustmentData'
      #   priceComputationData: '#/components/schemas/priceComputationData'
      properties:
        constructionDataType:
          type: string
        value:
          type: string
    context:
      title: Context
      type: object
      description: 'The context provided by a channel in which an offer set is evaluated: link to existing records such as orders or offers, the stakeholders to consider with their roles, etc ...'
      properties:
        travellersRef:
          type: array
          items:
            type: string
        responsibleRef:
          type: string
        customersRef:
          type: array
          items:
            type: string
        providersRef:
          type: string
        merchantRef:
          type: string
        deliveryProviderRef:
          type: string
        correlationId:
          type: string
        keywords:
          type: array
          description: Used to convey SSR or SK information provided by the agent to influence the pricing
          items:
            type: string
        channelName:
          type: string
    serviceItem:
      title: Service Item
      type: object
      x-examples:
        Flight NCE-CDG:
          id: SI-1234-456-1
          products:
            - id: 6X12-12AUG2-NCECDG
      description: |-
        The Service Item is the smallest unit **deliverable to a traveller** by a delivery system (Operations/DCS). It represents the instantiation of exactly one Product in the context of a particular offer; it contains the set of attributes and characteristics which are not an intrinsic characteristic of a product, but depend on a particular context, yet are still needed to deliver the service to the traveller.

        A Service Item is generally associated to zero or one traveller, but it can be also associated to additional infant travellers.

        When an Order is created, each of its fullfillment item is associated to exactly one Service Item.
      properties:
        id:
          type: string
          description: 'The service item identifier is unique locally to this offer, not globally unique. To globally identify a particular service item, a full reference is needed, in the form of `/airline-offers/offers/{id1}/offer-items/{id2}/service-items/{id3}`.'
        inventoryStatus:
          type: string
          enum:
            - INVENTORY_NOT_SECURED
            - INVENTORY_SECURED
            - INVENTORY_EXPIRED
          description: |-
            String enumerate describing the current status of service item.

            Available values are
            - INVENTORY_NOT_SECURED: Inventory does not lock any quota for this service item.
            - INVENTORY_SECURED: Service Item deliverable and secured with inventory guarantee.
            - INVENTORY_EXPIRED: Service Item delivery time has already passed. It's no more deliverable.
        retailing:
          $ref: '#/components/schemas/retailing'
        context:
          $ref: '#/components/schemas/context'
        construction:
          $ref: '#/components/schemas/construction'
        productRefId:
          type: string
          description: identifier of the corresponding product or product bundle
        productType:
          type: string
          description: |-
            The Product Type is the general category of the product. Each Product Type has a dedicated datamodel.

            This field must have the same value than the `product.productType` the product reference points to.
        serviceItemExecution:
          $ref: '#/components/schemas/serviceItemExecution'
    serviceItemExecution:
      title: Service Item Execution
      oneOf:
        - $ref: '#/components/schemas/serviceItemExecutionAirLegCabin'
        - $ref: '#/components/schemas/serviceItemExecutionAirAncillaryService'
        - $ref: '#/components/schemas/serviceItemExecutionServiceSeat'
        - $ref: '#/components/schemas/serviceItemExecutionServiceBaggage'
        - type: string
          description: 'Unstructured freeform string, only to be used in case there is no specific serviceItemExecution model available for a given productType'
      description: |-
        Information necessary for the delivery of a Service Item, which is not a intrinsic characteristic of the product, but depends on the particular instantiation of said product in an offer.

        For instance:
        - the booking class in which a LegCabin product is offered goes there
        - RFISC/RFIC codes for a service

        The specific model of a Service Item Execution object depends on the Product Type code.
    serviceItemExecutionAirLegCabin:
      type: object
      x-examples:
        ex_ExecutionAirLegCabin:
          serviceItemExecution:
            bookingClass: K
            flightStatus: CONFIRMED
            secureFlight: 'true'
            flightConnectionQualifier: X
            itineraryOrder: '1'
            bookingClassRebookStatus: NO_REBOOK_NEEDED
            dualInventory:
              bookingClass: 'N'
              cabinCode: W
              segmentElapsedFlyingTime: 110
      description: 'Attributes related to the delivery of an air service item, which are not an instrinsic characteristic of a LegCabin product, but depend on its particular instantiation in the scope of an offer.'
      properties:
        bookingClass:
          type: string
          description: 'Booking class: Primary RBD (ATPCo standard notation), Booked Class, BKC (Amadeus standard notation) '
        flightStatus:
          type: string
          enum:
            - CONFIRMED
            - OPEN
            - WAITING_LIST
            - STAND_BY
            - FLOWN
          description: Flight status flag
        governmentApproval:
          type: boolean
          nullable: true
          description: |-
            Government approval information is returned (for flights subject to that condition) in the availability response sent by the Availability Server.
            This flag indicates flight is subjected to government approval.
        itineraryOrder:
          type: integer
          description: The order/rank of the leg at itinerary level
        secureFlight:
          type: boolean
          nullable: true
          description: Secure flight information is an indicator to specify whether the flight is secure.
        exchangeStatus:
          type: string
          description: 'In case of repricing offer (type R), indicates if the flight is: OLD, when flight already exists in the old itinerary. NEW, when the flight does not exist in the old itinerary'
          enum:
            - OLD
            - NEW
        flightConnectionQualifier:
          type: string
          enum:
            - TRANSFER
            - STOPOVER
          description: Indicates the type of the connection. The flightConnectionQualifier will be only set in the serviceExecutionLegCabin of the last leg of the flight.
        dualInventory:
          type: object
          description: 'Dual Inventory Fare (DIF): Secondary RBD (ATPCo standard notation), Ticketed Class - TKC (Amadeus standard notation).'
          properties:
            bookingClass:
              type: string
              description: This attribute refers to the bookingClass code value corresponding to the dual inventory.
            cabinCode:
              type: string
              description: This attribute refers to the Cabin code value corresponding to the dual inventory.
        bookingClassRebookStatus:
          type: string
          description: Indicator whether the booking class needs to be rebooked in case of the best price
          enum:
            - TO_BE_REBOOKED
            - NO_REBOOK_NEEDED
        segmentElapsedFlyingTime:
          type: integer
          description: 'Duration of the segment in minutes. Note that in case of several legs for the same segment, this duration represention the flying time of the whole segment is duplicated for each leg.'
          nullable: true
        validatingCarrierRef:
          type: string
          description: 'In the airline industry, the validating carrier (sometimes abbreviated VC) is an airline which issues a ticket or miscellaneous charges order for transportation over the routes of another airlines to this Agreement. This is represented by a 2-letter code.'
    lifecycle:
      title: lifecycle
      type: object
      properties:
        version:
          type: integer
        lastUpdate:
          $ref: '#/components/schemas/EventLog.v2'
        creation:
          $ref: '#/components/schemas/EventLog.v2'
        lastUpdaterRef:
          type: string
        associatedResources:
          type: array
          items:
            $ref: '#/components/schemas/Relationship.v1'
        status:
          type: string
          enum:
            - EXPIRED
            - PRESENTED
            - CONFIRMED
            - BUILDING
            - COHERENT
            - MANAGED
            - DETERMINED
            - ORCHESTRATED
            - SELECTED
            - VALIDATED
            - PRICED
            - DELETED
          description: |-
            String enumerate describing the current condition of the object.

            Available values are
            - MANAGED: the object is being handled by the orchestration platform in the context of the proposal API
            - DETERMINED: the object has been determined in its execution plan construction steps by the orchestration platform in the context of the proposal API
            - ORCHESTRATED: the object has been fully defined and populated by the orchestration platform in the context of the proposal API
            - SELECTED: the object is being handled by the orchestration platform in the context of the selection API
            - VALIDATED: the object has been determined in its execution plan construction steps by the orchestration platform in the context of the selection API
            - PRICED: the object has been fully defined and populated by the orchestration platform in the context of the selection API
            - DELETED: the object had been flagged as deleted by the orchestration platform
        durabilityHint:
          type: string
          enum:
            - EPHEMERAL
            - PERSISTENT
        versionDetails:
          type: string
          description: Freetext details about offer version
        expirationDateTime:
          type: string
          description: |-
            Indicates the expiration date and time of the offer/offerSet as defined by the client.
            Default values are the following:
            - EPHEMERAL OFFER/OFFERSET: 30 minutes
            - PERSISTED OFFER/OFFERSET: 1 year
          format: date-time
    EventLog.v2:
      title: EventLog
      type: object
      description: The object representing one event and its details
      properties:
        id:
          type: string
          description: Identifier of the change
          example: 64GqpZfbcAW61PtL
          x-protobuf-index: 1
        dateTime:
          type: string
          description: time of the change (ISO 8601)
          format: date-time
          example: '2018-11-19T04:54Z'
          x-protobuf-index: 2
    airBound:
      title: airBound
      type: object
      description: 'A bound indicates a part of a itinerary, from traveller point of view. A bound is defined by its Origin and Destination, its position in the itinerary, its elapsed traveling time, and it is composed of consecutive leg(s).'
      properties:
        id:
          type: string
        elapsedTravelingTime:
          type: integer
          description: Traveling time the bound in minutes
          nullable: true
        boundPosition:
          type: integer
          description: Rank of the bound within the itinerary
          nullable: true
        exchangeBoundStatus:
          type: string
          enum:
            - NA
            - CHANGE
            - KEEP_FLIGHT
            - KEEP_FLIGHT_AND_FARES
            - ADD
            - REMOVE
          description: 'The exchange status can take one of the following values - Change, Keep flights, Keep flights and Fares, Add, Remove. This status will be filled only in ATC flow'
        offPointIataCode:
          type: string
          description: IATA code of arrival airport corresponding to the last leg of the bound
        boardPointIataCode:
          type: string
          description: IATA code of departure airport corresponding to the first leg of the bound
        airLegs:
          type: array
          items:
            type: object
            description: Array of productRef (product IDs) of airLegCabinProduct and the position of the leg in the bound
            properties:
              productRefId:
                type: string
                description: ID of airLegCabinProduct in the bound
              legPosition:
                type: integer
                nullable: true
                description: rank of the leg (airLegCabinProduct) in the bound
    priceComponentDetails:
      title: priceComponentDetails
      type: object
      description: fee and fare component details
      properties:
        id:
          type: string
        serviceItemsRef:
          type: array
          items:
            type: string
        priceBreakdown:
          $ref: '#/components/schemas/priceBreakdown'
    serviceItemExecutionAirAncillaryService:
      title: serviceItemExecutionAirAncillaryService
      type: object
      x-examples:
        GOLF EQUIPMENT:
          serviceItemExecution:
            atpcoCommercialName: GOLF EQUIPMENT
            atpcoClassification: F
            electronicMiscellaneousDocumentType: FLIGHT_ASSOCIATED
            atpcoGroup: BAGGAGE
            industryCarrierIndicator: INDUSTRY
            reasonForIssuanceCode: OC
            reasonForIssuanceSubCode: 0EW
            bookingInstructions:
              format: '%NBPIECE%%STX%'
              formatDetail:
                - key: NBPIECE
                  value: '[0-9]{1,2}'
                  description: NB OF PIECE
                  formatMandatory: 'true'
                - key: STX
                  value: '[0-9]{1,2}'
                  description: FreeText
                  formatMandatory: 'false'
      properties:
        atpcoCommercialName:
          type: string
          description: Indicates the commercial name associated to the service
        atpcoClassification:
          type: string
          description: |-
            Type of the service according to ATPCO. Current applicable values are:
            FLIGHT: Flight-related: services associated to one or several flights e.g. in-flight services (e.g. seat, meal)
            RULE_BUSTER: services modifying the rules associated to fares or ticket (e.g. advance purchase, refund/reissue)
            TICKET: services associated to a ticket (e.g. insurance)
            MERCHANDISE: standalone services, not associated to any flight or ticket (e.g. tee-shirt)
            BAGGAGE_ALLOWANCE: free baggage allowance
            BAGGAGE_CHARGES: excess baggage fees (including carry-on)
            EMBARGOES: operational bagagge restrictions
            PRE_PAID_BAGGAGE: optional and chargeable additional baggage allowance
        electronicMiscellaneousDocumentType:
          type: string
          description: |-
            Indicates the type of electronic miscellaneous Document:
            STANDALONE: 
            FLIGHT_ASSOCIATED: Associated to a flight coupon of a ticket
            STANDALONE_TKT_REF: Standalone referenced to a ticket number but not associated to a flight coupon of a ticket
            OTHER: Other than EMD
          enum:
            - STANDALONE
            - FLIGHT_ASSOCIATED
            - STANDALONE_TKT_REF
            - OTHER
        freeText:
          type: string
        atpcoGroup:
          type: string
          description: |-
            Industry service fees group list
            - BRANDED_FARES (BF)
            - BAGGAGE (BG)
            - CARBON_OFFSET (CO)
            - FREQUENT_FLYER (FF)
            - NON_AIR_SERVICES_GROUND_TRANSPORT (GT)
            - IN_FLIGHT_ENTERTAINMENT (IE)
            - LOUNGE (LG)
            - MEDICAL (MD)
            - MEAL_BEVERAGE (ML)
            - PETS (PT)
            - RULE_OVERRIDE (RO)
            - PRE_RESERVED_SEAT_ASSIGN (SA)
            - STANDBY (SB)
            - STORE (ST)
            - TRAVEL_SERVICES (TS)
            - UNACCOMPANIED_TRAVEL_ESCORT (UN)
            - UPGRADES (UP)
            - UNACCOMPANIED_TRAVEL_UNESCORTED (UU)
            See the following link for more information: 
            https://www.atpco.net/sites/atpco-public/files/all_pdfs/Opt_Scvs_Industry_Sub_Codes_Online_C.pdf
          enum:
            - BRANDED_FARES
            - BAGGAGE
            - CARBON_OFFSET
            - FREQUENT_FLYER
            - NON_AIR_SERVICES_GROUND_TRANSPORT
            - IN_FLIGHT_ENTERTAINMENT
            - LOUNGE
            - MEDICAL
            - MEAL_BEVERAGE
            - PETS
            - RULE_OVERRIDE
            - PRE_RESERVED_SEAT_ASSIGN
            - STANDBY
            - STORE
            - TRAVEL_SERVICES
            - UNACCOMPANIED_TRAVEL_ESCORT
            - UPGRADES
            - UNACCOMPANIED_TRAVEL_UNESCORTED
        atpcoSubGroup:
          type: string
          description: |-
            Industry service fees sub group list (RFISC). For the full list:
            https://www.atpco.net/sites/atpco-public/files/all_pdfs/Opt_Scvs_Industry_Sub_Codes_Online_C.pdf
        industryCarrierIndicator:
          type: string
          enum:
            - CARRIER
            - INDUSTRY
          description: The industryCarrierIndicator value reflects whether the RFISC is an industry-defined code or carrier-specific code.
        atpcoReasonForIssuanceCode:
          type: string
          description: The Reason for Issuance Code (RFIC) is a single character code that defines which group of services an Electronic Miscellaneous Document (EMD) belongs to.
          minLength: 1
          maxLength: 1
        atpcoReasonForIssuanceSubCode:
          type: string
          description: 'A Reason For Issuance Sub-Code (RFISC) is a three-letter code in the coupon of an Electronic Miscellaneous Document (EMD) used to inform about the nature of the service purchased. The RFISC can either be defined by the industry or by the carrier. '
          minLength: 3
          maxLength: 3
        serviceCode:
          type: string
          description: 4 characters code corresponding to the marketing SSR or SVC code
          minLength: 4
          maxLength: 4
        applicabilities:
          type: array
          items:
            $ref: '#/components/schemas/applicability'
          description: Indicates the applicability of the service linked to the fare family
        atpcoFirstServiceDescription:
          type: string
          description: Indicates the ATCPCo first description code to further define the oc group/subgroup
        atpcoSecondServiceDescription:
          type: string
          description: Indicates the ATCPCo second description code to further define the oc group/subgroup
        serviceCodeSSIM:
          type: string
          description: Indicates the SSIM (Standard Schedules Information Manual) code
        bookingMethodType:
          type: string
          description: |-
            Indicates the booking method type
            SPECIAL_SERVICE_REQUEST: Special Service Request (SSR)
            AUXILIARY_SERVICE: Auxiliary service (SVC)
            CONTACT_CARRIER_BOOKING: Available for Display/Pricing. Contact Carrier for Booking
            NO_BOOKING_REQ: No Booking Required
            BLANK: If the field is not present, it means no application (no specific booking requirement)
          enum:
            - SPECIAL_SERVICE_REQUEST
            - AUXILIARY_SERVICE
            - CONTACT_CARRIER_BOOKING
            - NO_BOOKING_REQ
        bookingInstructions:
          type: object
          description: 'Service booking instructions to include SSR, OSI and upgrade Method.'
          properties:
            format:
              type: string
              description: PNR booking method
            formatDetail:
              type: array
              description: Value bilaterally agreed between trading partners to enter when booking a service.
              items:
                type: object
                properties:
                  key:
                    type: string
                  value:
                    type: string
                    description: Valid regex
                  description:
                    type: string
                  formatMandatory:
                    type: boolean
                    nullable: true
                    description: indicator to specify if the format detail is mandatory in the format

    serviceItemExecutionServiceSeat:
      title: serviceItemExecutionServiceSeat
      allOf:
        - $ref: '#/components/schemas/serviceItemExecutionAirAncillaryService'
        - type: object
          properties:
            seatDetails:
              $ref: '#/components/schemas/Seat.v1'
    Seat.v1:
      type: object
      description: Seat information
      title: Seat
      x-examples:
        Handicapped Aisle Seat:
          value:
            number: 12C
            characteristicsCodes:
              - A
              - H
            cabin: 'Y'
            zone: M
      properties:
        number:
          description: 'Seat number corresponding to the concatenation of the seatmap row and the column information, for example 12B'
          type: string
        characteristicsCodes:
          description: Examples of possible characteristics  A -> aisle AA -> all available aisle AJ -> adjacent aisle seat AV -> only available aisle seats AW -> all available window B -> bassinet BK -> blocked for prefered pax in adjacent seat CC -> center section RU -> unusable reclined seat RM -> suitable for pax with reduced mobility K -> bulkhead H -> handicapped I -> infant MA -> medically ok M -> seat without a movie view N -> no smoking S -> smoking U -> unaccompanied minor W -> window E -> emergency exit row C -> crew CH -> chargeable F -> added seat LH -> restricted seat offered long haul segment J -> rear facing seat KA -> bulkhead seat with movie screen OW -> overwing FB -> first bed seat ES -> economy seat BC -> business bed seat BS -> business seat EP -> economy plus seat PE -> premium economy seat EK -> economy comfort seat NB -> next to bassinet HS -> hard to stretch leg due to door 9 -> centre WA -> window aisle 1W -> no window 99 -> unavailable for sale RS -> right side of aircraft LS -> left side of aircraft EC -> electronic connection AS -> individual airphone 2 -> leg rest available L -> leg space seat 1B -> not allowed for medical 1A -> not allowed for infant IE -> not suitable for child DE -> deportee PC -> pet in cabin 1M -> seat with movie view O -> preferential seat V -> vacant or offered last Q -> quiet zone X -> no facility 1D -> restricted recline seat 1C -> not allowed for unaccompanied minor 3 -> individual video screen choice movies 3B -> individual video screen choice movies games info 3A -> individual video screen no choice movie IA -> inside aisle G -> forward end of cabin Z -> buffer zone EA -> not on exit FC -> front of cabin class compartment GR -> offered to travelers belonging to a group 1 -> restricted general 70 -> individual video screen services unspecified 700 -> individual video screen services unspecified 1NS -> unsuitable SC -> stretcher TF -> forward zone TR -> rear zone NA -> narrow leg space LI -> limited leg room IFE device LE -> limited leg room emergency exit NM -> non movable armrest I2 -> suitable for adult with 2 infants TC -> tail row X0 -> non-iata X0 X1 -> non-iata X1 X2 -> non-iata X2 X3 -> non-iata X3 X4 -> non-iata X4 X5 -> non-iata X5 X6 -> non-iata X6 X7 -> non-iata X7 X8 -> non-iata X8 X9 -> non-iata X9 Y0 -> non-iata Y0 Y1 -> non-iata Y1 Y2 -> non-iata Y2 Y3 -> non-iata Y3 Y4 -> non-iata Y4 Y5 -> non-iata Y5 Y6 -> non-iata Y6 Y7 -> non-iata Y7 Y8 -> non-iata Y8 Y9 -> non-iata Y9 R0 -> non-iata R0 R1 -> non-iata R1 R2 -> non-iata R2 R3 -> non-iata R3 R4 -> non-iata R4 R5 -> non-iata R5 R6 -> non-iata R6 R7 -> non-iata R7 R8 -> non-iata R8 R9 -> non-iata R9 72 -> undesirable CS -> conditional LF -> lie flat LB -> left facing lie flat LL -> left facing lie flat LR -> right facing lie flat LT -> right facing lie flat right angle MS -> middle
          type: array
          items:
            type: string
        cabin:
          description: Cabin code associated to the seat
          type: string
        zone:
          description: 'The name of the zone that the seat is resident, used for boarding priority'
          type: string
    serviceItemExecutionServiceBaggage:
      type: object
      allOf:
        - $ref: '#/components/schemas/serviceItemExecutionAirAncillaryService'
        - type: object
          properties:
            baggageDetails:
              $ref: '#/components/schemas/BaggagePolicyV1bis'
    offerSet:
      title: Offer Set
      allOf:
        - $ref: '#/components/schemas/offerInstructions'
        - type: object
          properties:
            id:
              type: string
            construction:
              $ref: '#/components/schemas/construction'
            lifecycle:
              $ref: '#/components/schemas/lifecycle'
      description: Common construction context of a set of Offer(s). Identified by a unique OfferSetId.
    offerInstructions:
      title: Offer Instructions
      type: object
      description: |-
        All the information controlled and provided, to the Offer Management System, by external systems & touchpoints.

        It is integrated into the Offer Set, and complemented by OfMS-controlled data to constitute the Offer Set context as a whole.
      properties:
        requestInformation:
          type: array
          items:
            $ref: '#/components/schemas/request'
        context:
          $ref: '#/components/schemas/context'
        offerStakeholders:
          type: array
          description: 'Full set of stakeholders participating in a set of offers, including (but not limited to) travellers, agents, sellers, aggregators, ...'
          items:
            $ref: '#/components/schemas/OfferStakeholder.v1'
        channelConfigurations:
          type: array
          description: 'Raw data to be passed to proposal process (example: set up different fare classes, specify routing rules, create customized pricing rules, and manage other features like loyalty programs and ancillary services.)'
          items:
            $ref: '#/components/schemas/DocumentEnvelope.v1'
    request:
      title: Request
      type: object
      x-examples:
        Checked bags with sport equipments of more than 20kg:
          parties:
            - stakeholderRef: PAX1
          requestCriteria:
            servicesMandatory:
              - services:
                  productType: bag
                  taxonomy: 17D4
                  policies:
                    - quantity: 20
                legcabin:
                  marketingFlightDesignator:
                    carrierCode: 6X
                    flightNumber: '1234'
                  cabin:
                    code: 'Y'
                  leg:
                    boardPointIataCode: NCE
                    offPointIataCode: JFK
        Two Bounds:
          parties:
            - stakeholderRef: PAX1
          requestCriteria:
            requestedBounds:
              - bound:
                  origin: NCE
                  destination: JFK
                  departureDate: '2022-01-01'
              - bound:
                  origin: JFK
                  destination: NCE
                  departureDate: '2022-01-15'
        Requested Seat:
          requestCriteria:
            requestedSeats:
              - seats:
                  - number: 12C
                offerItemRefID: '14'
                serviceItemRefID: 14-LEG2-PAX1
      description: Contains a verbatim (untouched by OfMS) of everything that was requested by the channel in the scope of an Offer Set.
      properties:
        id:
          type: string
        requestDate:
          type: string
        parties:
          type: array
          description: Restricts the request to a subset of travellers.
          items:
            $ref: '#/components/schemas/party'
        requestCriteria:
          type: object
          properties:
            requestedBounds:
              type: array
              description: Itinerary
              items:
                type: object
                properties:
                  airBound:
                    $ref: '#/components/schemas/airBound'
                  rank:
                    type: integer
            requestedSeats:
              type: array
              description: Seats to select
              items:
                $ref: '#/components/schemas/requestedSeat'
            corporateCodes:
              type: array
              items:
                $ref: '#/components/schemas/promotionalCode'
            corporateIdentifiers:
              type: array
              items:
                $ref: '#/components/schemas/corporateIdentifier'
            currencyParameter:
              type: string
            discountCodes:
              type: array
              items:
                $ref: '#/components/schemas/promotionalCode'
            productsMandatory:
              type: array
              description: Mandatory products to propose through this request
              items:
                $ref: '#/components/schemas/requestedProductFilter'
            productsExclude:
              type: array
              description: Products to exclude through this request
              items:
                $ref: '#/components/schemas/requestedProductFilter'
            commercialFareFamilies:
              type: array
              description: The Commercial Fare Families you want to request.
              x-examples:
                cffBoundLevel:
                  code: CFFECO
                  applicabilities:
                    - boundRef:
                        - 0
                        - 2
                cffItineraryLevel:
                  code: CFFECO
              items:
                $ref: '#/components/schemas/commercialFareFamily'
        keywords:
          type: array
          description: 'Used to convey SSR or SK information provided by the agent to influence the pricing, to be applied to this request specifically'
          items:
            type: string
    party:
      title: party
      type: object
      properties:
        stakeholderRef:
          type: string
        rank:
          type: integer
    requestedSeat:
      title: requestedSeat
      type: object
      description: Assign a set of requested seat numbers to serviceItems
      properties:
        seats:
          type: array
          description: List of requested seats by customer
          items:
            $ref: '#/components/schemas/Seat.v1'
        offerItemRefID:
          type: string
          description: Offer Item ID matching the requested seat
        serviceItemRefID:
          type: string
          description: Service Item ID matching the requested seat
      x-examples:
        simple requestedSeat:
          seats:
            - number: 14A
          offerItemRefID: '12'
          serviceItemRefID: 12-LEG1-PAX1
        multiple seats same service item:
          seats:
            - number: 14A
            - number: 14B
          offerItemRefID: '12'
          serviceItemRefID: 12-LEG1-PAX2
    promotionalCode:
      title: promotionalCode
      type: object
      description: common object for corporate codes and discount codes
      properties:
        airlineRef:
          type: string
        code:
          type: string
          description: Indicates the corporate contract code
        type:
          type: string
        corporateContractNumber:
          type: string
          description: Indicates the corporate contract number
    corporateIdentifier:
      title: corporateIdentifier
      type: object
      description: common object for Corporate Identification
      properties:
        code:
          type: string
        type:
          type: string
    requestedProductFilter:
      title: Requested Products Filter
      type: object
      description: |-
        Request for particular products:
        - Include some legcabins 
        - Include some services
        - Include some services on particular legcabins
      properties:
        legcabin:
          $ref: '#/components/schemas/airLegCabinProduct'
        service:
          $ref: '#/components/schemas/serviceProduct'
        applicabilities:
          type: array
          items:
            $ref: '#/components/schemas/applicability'
      x-examples:
        Seats requested:
          service:
            id: 1
            productType: serviceProduct
            taxonomy: 1838
          applicabilities:
            - travellersRef:
                - PAX1
              itemsRef:
                productsRef:
                  - airLegCabinRef
    OfferStakeholder.v1:
      type: object
      title: OfferStakeholder
      properties:
        id:
          type: string
          description: unique identifier of the stakeholder (unity within the scope of the commercial transaction starting from offer)
        role:
          type: string
          enum:
            - SELLER
            - PROVIDER
            - CUSTOMER
          description: |-
            Role of the physical person and/or legal entity in the commercial transaction context.
            Valid values are:
            -SELLER: entity selling products and/or services.
            -PROVIDER: entity providing products and/or services.
            -CUSTOMER: end customer intending to and actually purchasing goods and services to be consumed by him/her or other consumer without commercial reselling purpose.
        individual:
          type: object
          properties:
            relationships:
              type: array
              items:
                $ref: '#/components/schemas/OfferRelationship.v1'
            loyaltyAccounts:
              type: array
              items:
                $ref: '#/components/schemas/FrequentFlyer.v1'
        company:
          allOf:
            - $ref: '#/components/schemas/Company.v1'
        stakeholderDetails:
          $ref: '#/components/schemas/OfferCustomer.v1'
    commercialFareFamily:
      title: commercialFareFamily
      type: object
      description: A requested Commercial Fare Family. This can be mapped with requested bounds
      properties:
        code:
          type: string
        applicabilities:
          $ref: '#/components/schemas/applicability'
    airLegCabinProduct:
      title: Leg Cabin Product
      allOf:
        - $ref: '#/components/schemas/Product-catalogue.v1_Product'
        - type: object
          properties:
            marketingFlightDesignator:
              $ref: '#/components/schemas/FlightDesignator.v2'
            operatingFlightDesignator:
              $ref: '#/components/schemas/FlightDesignator.v2'
            leg:
              $ref: '#/components/schemas/Leg.v1'
            cabin:
              $ref: '#/components/schemas/Cabin.v1'
            flightPoints:
              type: array
              items:
                $ref: '#/components/schemas/FlightPoint.v1'
      x-examples:
        6X*/8X HKG-SFO leg in cabin M:
          id: '1346602422'
          productType: airLegCabinProduct
          leg:
            boardPointIataCode: HKG
            offPointIataCode: SFO
            scheduledArrivalDateTime: '2022-02-23T19:40:00'
            scheduledDepartureDateTime: '2022-03-23T23:40:00'
            aircraftEquipment:
              equipmentType: '359'
          cabin:
            code: M
          operatingFlightDesignator:
            carrierCode: 8X
            flightNumber: '2'
          marketingFlightDesignator:
            carrierCode: 6X
            flightNumber: '3'
      description: The right to fly on a Leg Cabin Date is the atomic unit of service delivery for air travellers.
    serviceProduct:
      title: serviceProduct
      allOf:
        - $ref: '#/components/schemas/Product-catalogue.v1_Product'
    OfferRelationship.v1:
      description: Relationship allows to cross reference 2 entities via Link and/or id .
      title: Relationship
      x-tags:
        - Relationship
        - Relation
        - Link
      type: object
      properties:
        id:
          type: string
          description: id of the related resource
        type:
          type: string
          description: Type of the related resource
          example: processed-dcs-passenger
        ref:
          type: string
          format: uri-reference
          description: Local reference of the related resource
        targetSchema:
          type: string
          description: The targetSchema would be an instance of definition to be used for building the associated API Request operation
        targetMediaType:
          type: string
          description: 'Related to "targetSchema", to be specified in case the media type is different for the referenced API request'
        hrefSchema:
          type: string
          description: 'type ( format, type, patterns, enum) definition for each URI parameters.'
        href:
          type: string
          format: url
          description: URL value
        methods:
          type: array
          description: HTTP methods supported by the sibling URI
          items:
            $ref: '#/components/schemas/HttpMethods.v1'
        rel:
          type: string
          description: 'Expose the type of relation between the current entity and the describe entity : https://www.iana.org/assignments/link-relations/link-relations.xhtml'
    FrequentFlyer.v1:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for frequent flyer
          example: FF0001
        frequentFlyerNumber:
          type: string
          description: Loyalty card number
          example: 6X090807061234
        serviceCode:
          type: string
          description: 'Service code (FQTV = Mileage program accrual, FQTU = Upgrade and accrual, FQTR = Mileage program redemption, FQTS = Service request)'
          example: FQTU
        applicableAirlineCode:
          type: string
          description: Code of the airline for which frequent traveller data has been added
          example: 6X
        sequenceNumber:
          type: string
          description: Sequence number
          example: 1
        confirmationStatus:
          type: string
          description: Current status
          example: HK
        airlineLevel:
          $ref: '#/components/schemas/Tier.v1'
        allianceLevel:
          $ref: '#/components/schemas/Tier.v1'
        certificateNumber:
          type: string
          description: Certificate number
          example: 123456
        stockControlNumber:
          type: string
          description: Stock control number
          example: SCN001
        customerValue:
          type: integer
          description: Customer value (ACV) coming from loyalty system
          example: 2
        isValidationOverriden:
          type: boolean
          description: Indicator set when the validation status of the fqtv number has been overriden by an agent
          example: false
      title: FrequentFlyer
    Company.v1:
      title: Company
      type: object
      x-tags:
        - Company
        - Stakeholder
        - Organization
        - Corporation
        - Partnership
        - Alliance
        - Airline
      description: |
        A Company is association or collection of individuals, whether natural persons, juridic persons, or a mixture of both, with a specific objective.
      x-examples:
        Amadeus company:
          name: Amadeus
          legalName: 'Amadeus IT Group, S.A.'
          businessName: Amadeus IT Group
          code: 1A
          ownerCode: IATA
      properties:
        name:
          type: string
          description: Common name of the organization.
    OfferCustomer.v1:
      title: OfferCustomer
      type: object
      properties:
        accounts:
          $ref: '#/components/schemas/CustomerAccount.v1'
        customerTypesMap:
          type: array
          items:
            $ref: '#/components/schemas/OfferCustomerTypesMapItem.v1'
    CustomerAccount.v1:
      title: CustomerAccount
      type: object
      properties:
        id:
          type: string
          description: |
            unique identifier of the account within a commercial transaction scope (Offer/Order/Delivery)
        accountType:
          type: string
          description: nature of the account
          enum:
            - BANK
            - LOYALTY
            - WEB_PROFILE
            - VOUCHER
        name:
          type: string
          description: plain text name of the account
        code:
          type: string
          description: code of the account
        number:
          type: string
    OfferCustomerTypesMapItem.v1:
      title: CustomerTypesMapItem
      type: object
      properties:
        customerTypeListName:
          type: string
          description: Name of the category of customer type
          example: PTC
        customerTypeCode:
          type: string
          description: Code of the customer type
          example: MIL
        customerTypeLabel:
          type: string
          description: Label of the cutomer type
          example: Military
        customerTypeOrganizationName:
          type: string
          description: Organization associated to this customer type item
          example: Airline
    FlightDesignator.v2:
      type: object
      properties:
        carrierCode:
          type: string
          description: Two characters IATA standard carrier code
          example: 6X
        flightNumber:
          type: string
          description: 1-4 digit number
          example: '555'
        operationalSuffix:
          type: string
          description: the operational suffix
          example: A
        carrierName:
          type: string
          description: Full name of the carrier. This field can be used to either complement the carrier code if available or give the carrier name when IATA standard carrier code does not exist
          example: Amadeus SIX
      title: FlightDesignator
    Leg.v1:
      type: object
      title: Leg
      x-examples:
        ex_Leg:
          leg:
            boardPointIataCode: SFO
            offPointIataCode: SIN
            aircraftEquipment:
              aircraftType: '359'
            scheduledDepartureDateTime: '2022-08-04T11:30:00'
            scheduledArrivalDateTime: '2021-08-05T19:10:00'
      properties:
        id:
          type: string
          description: identifier of the leg
          example: GVA-YUL
        boardFlightPointId:
          type: string
        offFlightPointId:
          type: string
        boardPointIataCode:
          type: string
          description: IATA code of the leg's departure airport
          example: GVA
        offPointIataCode:
          type: string
          description: IATA code of the leg's arrival airport
          example: YUL
        scheduledDepartureDateTime:
          type: string
          format: dateTime
          description: 'scheduled departure date and time of the Leg in local time, ISO format yyyy-mm-ddThh:mm:ss'
          example: '2018-09-17T12:40:00'
        scheduledArrivalDateTime:
          type: string
          format: dateTime
          description: 'scheduled arrival date and time of the Leg in local time, ISO format yyyy-mm-ddThh:mm:ss.'
          example: '2018-09-17T14:45:00'
        routingOrder:
          type: number
          description: Splitting a segment into several legs this field must be used to differentiate the order/rank of legs within a flight. Must be used to keep track of leg order.
        serviceType:
          type: string
          description: service type defined for this leg
          enum:
            - UNKNOWN_SERVICE_TYPE
            - J
            - S
            - U
            - F
            - V
            - M
            - Q
            - G
            - B
            - A
            - R
            - C
            - O
            - H
            - L
            - P
            - T
            - K
            - D
            - E
            - W
            - X
            - I
            - 'N'
            - 'Y'
            - Z
          example: J
        bookingClassList:
          type: array
          description: lnformation on the list of booking class defined on this leg.
          items:
            $ref: '#/components/schemas/BookingClass.v1'
        aircraftEquipment:
          $ref: '#/components/schemas/AircraftEquipment.v1'
        deis:
          type: array
          items:
            $ref: '#/components/schemas/DEI.v2'
    Cabin.v1:
      title: Cabin
      type: object
      properties:
        code:
          type: string
          description: cabin code filed in inventory for the sold seat
    FlightPoint.v1:
      type: object
      title: FlightPoint
      description: 'Flight Point is description of an airport physical position of an aircraft during a flight between legs, segments of an itinerary with all timings related to arrival and departure'
      x-tags:
        - flightPoint
        - pointOfCommencement
      properties:
        departure:
          type: object
          title: FlightPoint_Departure
          description: FlightPoint_Departure allows to define a specific airport depature location (position & timing) of an aircraft.
          properties:
            aircraftParkingStands:
              type: array
              items:
                $ref: '#/components/schemas/items'
            gates:
              type: array
              items:
                $ref: '#/components/schemas/items_2'
            terminals:
              type: array
              items:
                $ref: '#/components/schemas/items_3'
                type: object
                title: Terminal
                properties:
                  code:
                    type: string
                    description: terminal code
            timings:
              type: array
              items:
                $ref: '#/components/schemas/items_4'
        id:
          type: string
          description: identifier of the flight point
          example: GVA_1
        iataCode:
          type: string
          description: iata code of the airport
          example: GVA
        city:
          type: string
          description: city code as provided by referential data. only used for data staging platform/AAH
          example: GVA
        arrival:
          type: object
          title: FlightPoint_Arrival
          description: FlightPoint_Arrival allows to define a specific airport arrival location (position & timing) of an aircraft.
          properties:
            timings:
              type: array
              items:
                type: object
                title: FlightPoint_Timing
                description: 'Information for flight timings which are related to type of timing information and values, delays associated to a departure or an arrival at specific endpoint for a flight associated to a location.'
                x-examples:
                  Structured:
                    estimatedOnBlockTime: '2016-07-17T22:30+10:00'
                    actualOffBlockTime: '2016-07-17T12:20+08:00'
                    actualTakeOffTime: '2016-07-17T12:32+08:00'
                    actualTouchDownTime: '2016-07-17T22:24+10:00'
                    actualOnBlockTime: '2016-07-17T22:32+10:00'
                  With delay:
                    qualifier: STD
                    value: '2018-06-12T02:15:20'
                    delays:
                      - reason: reason code of the delay
                        duration: '30'
                  System source updating the timings:
                    qualifier: STD
                    value: '2018-06-12T02:15:20'
                    source: SKD
                    timestamp: '2018-06-11T04:25:11'
                properties:
                  timeInterval:
                    type: number
                    format: double
                    description: 'This conveys the interval of time in minutes. If the value is positive then it means there is time left for a flight point (departure, arrival, connection,...). On the other hand, if the value is negative then it means flight "past" this point (for example is departed already).'
                    example: -30
                  timestamp:
                    type: string
                    description: 'timestamp of the data yyyy-mm-ddThh:mm:ss.mmmmmm'
                    format: datetime
                  source:
                    type: string
                    description: source of the timing information
                    enum:
                      - UNKNOW_ORIGIN
                      - SKD
                      - FLIX
                      - MVT
                      - DIV
                      - DCS
                      - OAG
                      - AIRLINE_GROUND_OPERATIONAL_DATA
                  delays:
                    type: array
                    items:
                      type: object
                      properties:
                        reason:
                          type: string
                          description: reason code of the delay
                        duration:
                          type: integer
                          description: duration of the delay (number of minutes)
                  value:
                    type: string
                    format: local-date-time
                    description: 'value of the timing is (local) and not ISO format (yyyy-mm-ddThh:mm:ss) or ISO compliant with ZULU format (yyyy-mm-ddThh:mm:ssZ) '
                    example: '2018-06-12T02:15:00'
                  qualifier:
                    type: string
                    description: type of timing information
                    enum:
                      - STD
                      - STA
                      - ETD_OFF_BLOCK
                      - ETD_TAKEOFF
                      - ETD_FLIX_FD
                      - ETA_TOUCHDOWN
                      - ETA_ON_BLOCK
                      - ETA_FLIX_FD
                      - ATD_OFF_BLOCK
                      - ATD_TAKEOFF
                      - ATA_TOUCHDOWN
                      - ATA_ON_BLOCK
                      - RR_ON_BLOCK
                      - FR_TOUCHDOWN
                      - FR_ON_BLOCK
                      - ETA_ON_BLOCK_FORCED_RETURN
                      - ETA_TOUCHDOWN_FORCED_RETURN
                      - ETD_DIV
                      - ETA_TOUCHDOWN_DIV
                      - ETA_ON_BLOCK_DIV
                      - ATA_TOUCHDONW_DIV
                      - ATA_ON_BLOCK_DIV
                      - ESTIMATED_FIRST_PAX_THROUGH_GATE
                      - ETB
                      - ETB_FIRST_PAX
                      - TOB
                      - MVT_AFTER_PUSHBACK
                      - CTO
                      - NEXT_INFO
                      - ADVICE_TIME_OF_DEPARTURE_LOCAL
                      - STANDARD_TIME_OF_DEPARTURE_ZULU
                      - STANDARD_TIME_OF_ARRIVAL_ZULU
                      - ESTIMATED_TIME_OF_DEPARTURE_ZULU
                      - ESTIMATED_TIME_OF_ARRIVAL_ZULU
                      - ACTUAL_TIME_OF_DEPARTURE_ZULU
                      - ACTUAL_TIME_OF_ARRIVAL_ZULU
                    example: STD
                  scheduledArrivalDateTime:
                    type: string
                    format: date-time
                    description: ISO datetime format
                  actualOnBlockDateTime:
                    type: string
                    format: date-time
                    description: ISO datetime format
                  estimatedOnBlockDateTime:
                    type: string
                    format: date-time
                    description: ISO datetime format
                  estimatedTouchDownDateTime:
                    type: string
                    format: date-time
                    description: ISO datetime format
                  actualTouchDownDateTime:
                    type: string
                    format: date-time
                    description: ISO datetime format
                  scheduledDepartureDateTime:
                    type: string
                    format: date-time
                    description: ISO datetime format
                  actualOffBlockDateTime:
                    type: string
                    format: date-time
                    description: ISO datetime format
                  estimatedOffBlockDateTime:
                    type: string
                    format: date-time
                    description: ISO datetime format
                  estimatedTakeOffDateTime:
                    type: string
                    format: date-time
                    description: ISO datetime format
                  actualTakeOffDateTime:
                    type: string
                    format: date-time
                    description: ISO datetime format
            terminals:
              type: array
              items:
                type: object
                title: Terminal
                properties:
                  code:
                    type: string
                    description: terminal code
                  source:
                    type: string
                    description: source of the information
                    enum:
                      - UNKNOW_ORIGIN
                      - SKD
                      - FLIX
                      - MVT
                      - DIV
                      - DCS
                      - OAG
                      - AIRLINE_GROUND_OPERATIONAL_DATA
                  timestamp:
                    type: string
                    format: datetime
                    description: 'timestamp of the information yyyy-mm-ddThh:mm:ss.mmmmmm'
            gates:
              type: array
              items:
                type: object
                title: Gate
                properties:
                  mainGate:
                    type: string
                    description: main gate name
                  alternateGate:
                    type: string
                    description: alternate gate name
                  source:
                    type: string
                    description: source of the information
                    enum:
                      - UNKNOW_ORIGIN
                      - SKD
                      - FLIX
                      - MVT
                      - DIV
                      - DCS
                      - OAG
                      - AIRLINE_GROUND_OPERATIONAL_DATA
                  timestamp:
                    type: string
                    format: datetime
                    description: 'timestamp of the information yyyy-mm-ddThh:mm:ss.mmmmmm'
            aircraftParkingStands:
              type: array
              items:
                type: object
                title: FlightPoint_AircraftParkingStand
                description: |-
                  Aircraft stand markings shall be provided for designated parking positions on a paved apron.

                  An Identification of parking position on lead-in marking

                  Extract from 
                  https://www.icao.int/SAM/Documents/GREPECAS/2011/GRPAGAAOP8/AGAAOPSG08WP14REv.pdf
                properties:
                  location:
                    type: string
                    example: LOC 004
    items:
      type: object
      title: FlightPoint_AircraftParkingStand
      description: |-
        Aircraft stand markings shall be provided for designated parking positions on a paved apron.

        An Identification of parking position on lead-in marking

        Extract from 
        https://www.icao.int/SAM/Documents/GREPECAS/2011/GRPAGAAOP8/AGAAOPSG08WP14REv.pdf
      properties:
        location:
          type: string
          example: LOC 004
    items_2:
      type: object
      title: Gate
      properties:
        mainGate:
          type: string
          description: main gate name
        alternateGate:
          type: string
          description: alternate gate name
        source:
          type: string
          description: source of the information
          enum:
            - UNKNOW_ORIGIN
            - SKD
            - FLIX
            - MVT
            - DIV
            - DCS
            - OAG
            - AIRLINE_GROUND_OPERATIONAL_DATA
        timestamp:
          type: string
          format: datetime
          description: 'timestamp of the information yyyy-mm-ddThh:mm:ss.mmmmmm'
    items_3:
      type: object
      title: Terminal
      properties:
        code:
          type: string
          description: terminal code
        source:
          type: string
          description: source of the information
          enum:
            - UNKNOW_ORIGIN
            - SKD
            - FLIX
            - MVT
            - DIV
            - DCS
            - OAG
            - AIRLINE_GROUND_OPERATIONAL_DATA
        timestamp:
          type: string
          format: datetime
          description: 'timestamp of the information yyyy-mm-ddThh:mm:ss.mmmmmm'
    items_4:
      type: object
      title: FlightPoint_Timing
      description: 'Information for flight timings which are related to type of timing information and values, delays associated to a departure or an arrival at specific endpoint for a flight associated to a location.'
      x-examples:
        Structured:
          estimatedOnBlockTime: '2016-07-17T22:30+10:00'
          actualOffBlockTime: '2016-07-17T12:20+08:00'
          actualTakeOffTime: '2016-07-17T12:32+08:00'
          actualTouchDownTime: '2016-07-17T22:24+10:00'
          actualOnBlockTime: '2016-07-17T22:32+10:00'
        With delay:
          qualifier: STD
          value: '2018-06-12T02:15:20'
          delays:
            - reason: reason code of the delay
              duration: '30'
        System source updating the timings:
          qualifier: STD
          value: '2018-06-12T02:15:20'
          source: SKD
          timestamp: '2018-06-11T04:25:11'
      properties:
        timeInterval:
          type: number
          format: double
          description: 'This conveys the interval of time in minutes. If the value is positive then it means there is time left for a flight point (departure, arrival, connection,...). On the other hand, if the value is negative then it means flight "past" this point (for example is departed already).'
          example: -30
        timestamp:
          type: string
          description: 'timestamp of the data yyyy-mm-ddThh:mm:ss.mmmmmm'
          format: datetime
        source:
          type: string
          description: source of the timing information
          enum:
            - UNKNOW_ORIGIN
            - SKD
            - FLIX
            - MVT
            - DIV
            - DCS
            - OAG
            - AIRLINE_GROUND_OPERATIONAL_DATA
        delays:
          type: array
          items:
            type: object
            properties:
              reason:
                type: string
                description: reason code of the delay
              duration:
                type: integer
                description: duration of the delay (number of minutes)
        value:
          type: string
          format: local-date-time
          description: 'value of the timing is (local) and not ISO format (yyyy-mm-ddThh:mm:ss) or ISO compliant with ZULU format (yyyy-mm-ddThh:mm:ssZ) '
          example: '2018-06-12T02:15:00'
        qualifier:
          type: string
          description: type of timing information
          enum:
            - STD
            - STA
            - ETD_OFF_BLOCK
            - ETD_TAKEOFF
            - ETD_FLIX_FD
            - ETA_TOUCHDOWN
            - ETA_ON_BLOCK
            - ETA_FLIX_FD
            - ATD_OFF_BLOCK
            - ATD_TAKEOFF
            - ATA_TOUCHDOWN
            - ATA_ON_BLOCK
            - RR_ON_BLOCK
            - FR_TOUCHDOWN
            - FR_ON_BLOCK
            - ETA_ON_BLOCK_FORCED_RETURN
            - ETA_TOUCHDOWN_FORCED_RETURN
            - ETD_DIV
            - ETA_TOUCHDOWN_DIV
            - ETA_ON_BLOCK_DIV
            - ATA_TOUCHDONW_DIV
            - ATA_ON_BLOCK_DIV
            - ESTIMATED_FIRST_PAX_THROUGH_GATE
            - ETB
            - ETB_FIRST_PAX
            - TOB
            - MVT_AFTER_PUSHBACK
            - CTO
            - NEXT_INFO
            - ADVICE_TIME_OF_DEPARTURE_LOCAL
            - STANDARD_TIME_OF_DEPARTURE_ZULU
            - STANDARD_TIME_OF_ARRIVAL_ZULU
            - ESTIMATED_TIME_OF_DEPARTURE_ZULU
            - ESTIMATED_TIME_OF_ARRIVAL_ZULU
            - ACTUAL_TIME_OF_DEPARTURE_ZULU
            - ACTUAL_TIME_OF_ARRIVAL_ZULU
          example: STD
        scheduledArrivalDateTime:
          type: string
          format: date-time
          description: ISO datetime format
        actualOnBlockDateTime:
          type: string
          format: date-time
          description: ISO datetime format
        estimatedOnBlockDateTime:
          type: string
          format: date-time
          description: ISO datetime format
        estimatedTouchDownDateTime:
          type: string
          format: date-time
          description: ISO datetime format
        actualTouchDownDateTime:
          type: string
          format: date-time
          description: ISO datetime format
        scheduledDepartureDateTime:
          type: string
          format: date-time
          description: ISO datetime format
        actualOffBlockDateTime:
          type: string
          format: date-time
          description: ISO datetime format
        estimatedOffBlockDateTime:
          type: string
          format: date-time
          description: ISO datetime format
        estimatedTakeOffDateTime:
          type: string
          format: date-time
          description: ISO datetime format
        actualTakeOffDateTime:
          type: string
          format: date-time
          description: ISO datetime format
    HttpMethods.v1:
      title: HttpMethods
      type: string
      definition: List of existing HTTP methods
      enum:
        - GET
        - POST
        - PUT
        - PATCH
        - DELETE
        - OPTIONS
      x-enum-descriptions:
        - Http method GET
        - Http method POST
        - Http method PUT
        - Http method PATCH
        - Http method DELETE
        - Http method OPTIONS
      x-enum-varnames:
        - HTTP_METHODS_ENUM_GET
        - HTTP_METHODS_ENUM_POST
        - HTTP_METHODS_ENUM_PUT
        - HTTP_METHODS_ENUM_PATCH
        - HTTP_METHODS_ENUM_DELETE
        - HTTP_METHODS_ENUM_OPTIONS
      x-enum-protobuf-indexes:
        - 1
        - 2
        - 3
        - 4
        - 5
        - 6
    Tier.v1:
      type: object
      description: 'Description of a Tier in the context of the loyalty program <br> [ Read-only information ]'
      title: Tier
      x-examples:
        example-1:
          Tier:
            code: EMER
            name: EMERALD
            priorityCode: '1'
            companyCode: '*O'
            company:
              name: ONEWORLD
      x-tags:
        - Tier
        - Company
        - Individual
        - Membership
        - Loyalty
      properties:
        name:
          type: string
          description: Name of the given tier (e.g. Honorary Gold) - See the user manual for the list of acceptable values
          example: HONORARY GOLD
        code:
          type: string
          description: Code associated to the tier (e.g. HGOLD)
          example: HGOLD
        startDate:
          type: string
          description: Date at which this tier was attributed to the membership
          format: date
        endDate:
          type: string
          description: Date at which this tier was attributed to the membership
          format: date
        priorityCode:
          type: string
          nullable: true
          description: |
            Priority code used to synchronize with external systems. Should have a clear correspondence to the code.
        companyCode:
          type: string
          nullable: true
          description: Alliance code or airline code of the loyalty program
          example: 6X
        customerValue:
          type: integer
          nullable: true
          description: |
            This is a specific customer value provided by the airline to be used for downstream processes such as PCV. It reflects the value of the corporation for the airline depending on its travel history, potential and other internal factors. It can be a numeric value between 0 and 9999.
        company:
          $ref: '#/components/schemas/Tier_Company.v1'
        tierType:
          type: string
          description: 'Represents the tier type, defined in program management associated to the soft tier types'
          example: MAIN
        level:
          type: string
          description: 'Tier scale values within the Loyalty Program hierarchy. Ex: Blue, Silver, Gold, Platinum. Tiers can be renewed to keep a same Tier Level, upgraded to the next Tier Level or downgraded to the previous Tier Level.'
          example: BLUE
    BookingClass.v1:
      title: BookingClass
      type: object
      # definitions: null
      description: booking class description
      properties:
        levelOfService:
          type: string
          description: reservation level of service
        cabin:
          $ref: '#/components/schemas/Cabin.v1'
        code:
          type: string
          description: code identifying the booking class
    AircraftEquipment.v1:
      type: object
      description: description of the aircraft equipment
      title: AircraftEquipment
      properties:
        aircraftType:
          type: string
          description: 'aircraft type (ex 320, 777, ...)'
        aircraftOwner:
          type: string
          description: aircraft owner
        aircraftOwnerCode:
          type: string
          description: iata airline code of the aircraft owner
          example: '6X, 8X, ....'
    DEI.v2:
      type: object
      x-examples:
        DEI 5 - Cabin Crew Employer:
          code: 5
          value: DL
        DEI 7 - Meal Service Note:
          code: 7
          value: PDB/JDB/YD
      description: Data Element Identifier. Data elements identifiers are used in exchange of schedule information and are identified as a sequence of alpha/numeric characters used to encode flight characteristics. Depending on their context and location in IATA SSIM/SSM
      title: DEI
      properties:
        code:
          type: integer
          description: DEI code
          example: 505
        value:
          type: string
          description: 'DEI value from a existing Reference to IATA SSIM definition, in case multi values related to same code: ''/'' is a separator between several DEI Value.'
          example: ET
    Tier_Company.v1:
      description: Company details associated to the CompanyCode of this Tier
      title: Tier_Company
      type: object
      x-tags:
        - Company
        - Stakeholder
        - Organization
        - Corporation
        - Partnership
        - Alliance
        - Airline
      x-examples:
        Amadeus company:
          name: Amadeus
          legalName: 'Amadeus IT Group, S.A.'
          businessName: Amadeus IT Group
          code: 1A
          ownerCode: IATA
      properties:
        name:
          type: string
          description: Common name of the organization.

    #
    #   Order
    #
    OrderOrderChangeEvent:
      type: object
      title: OrderOrderChangeEvent
      properties:
        currentImage:
          $ref: '#/components/schemas/Order'
      allOf:
        - $ref: '#/components/schemas/GenericOpenDataChangeEvent'

    Order:
      type: object
      title: Order
      description: |-
        Order is a record created when an offer is selected for purchase by a customer. It allows to track the fulfillment of the liabilities that are thus created.
        Example for liabilities can be Payment liabilities from traveler towards service retailer/provider. It can also be service liabilities provided by the service retailer/provider to the traveler.
        The fulfillment of the liabilities created by the contractualization of several Offers can be tracked in one single Order.
        Similarly to an Offer which is composed of Offer Items, an Order is composed of Order Items.
      properties:
        id:
          type: string
          description: Unique id of an Order.
        lifecycle:
          $ref: '#/components/schemas/OrderOrderLifecycle'
        stakeholders:
          type: array
          description: Physical person or moral entity involved in the Order.
          items:
            $ref: '#/components/schemas/OrderStakeholder'
        agreements:
          type: array
          description: 'Legally enforceable contract formed when one party makes an offer that is accepted by the relevant stakeholders, contingent upon the fulfillment of specific conditions.'
          items:
            $ref: '#/components/schemas/OrderAgreement'
        orderItems:
          type: array
          description: List of items that constitute the Order.
          items:
            $ref: '#/components/schemas/OrderOrderItem'
        payments:
          type: array
          description: 'Order Payment Fulfillment details containing payment information, changelog and Order item references.'
          items:
            $ref: '#/components/schemas/OrderPaymentFulfillment'
      required:
        - id
        - lifecycle
      x-examples: {}
    OrderOrderLifecycle:
      type: object
      title: OrderOrderLifecycle
      description: Description of the evolution of the entity from the creation to the end which is tracked by the version.
      properties:
        dataVersion:
          type: string
          description: 'Version of the element. It starts at 1, each update/change increments the version by 1. Dotted notation is to be used when there is a rewrite of history.'
        creation:
          $ref: '#/components/schemas/OrderLifecycleEventLog'
        lastUpdate:
          $ref: '#/components/schemas/OrderLifecycleEventLog'
      required:
        - dataVersion
    OrderLifecycleEventLog:
      type: object
      title: OrderLifecycleEventLog
      description: Relevant details of a creation/update event in the Lifecycle of an Order related resource.
      properties:
        dateTime:
          type: string
          description: Date and time of the change occurence based on ISO-8601.
          format: date-time
        user:
          $ref: '#/components/schemas/OrderLifecycleEventLogUser'
        location:
          $ref: '#/components/schemas/OrderLifecycleEventLogLocation'
    OrderLifecycleEventLogUser:
      type: object
      title: OrderLifecycleEventLogUser
      description: The user that initiated/performed the change
      properties:
        organizationCode:
          type: string
          description: Organization currently used
          examples:
            - AC
        iataNumber:
          type: string
          description: 8-digit IATA number
    OrderLifecycleEventLogLocation:
      type: object
      title: OrderLifecycleEventLogLocation
      description: Description of a particular point or place in physical space
      properties:
        iataCode:
          type: string
          description: IATA location code
          examples:
            - PAR
        address:
          $ref: '#/components/schemas/OrderLifecycleEventLogLocationAddress'
    OrderLifecycleEventLogLocationAddress:
      type: object
      title: OrderLifecycleEventLogLocationAddress
      description: Address information
      properties:
        countryCode:
          type: string
          description: ISO 3166-1 country code
          pattern: '[a-zA-Z]{2}'
          x-risk-personal-data-field: ADDRESS
    OrderStakeholder:
      type: object
      title: OrderStakeholder
      description: 'Person, group, company or organization involved in the Order'
      properties:
        id:
          type: string
          description: Id of the Stakeholder in the related Order.
        role:
          $ref: "#/components/schemas/OrderStakeholderRole"
        party:
          $ref: "#/components/schemas/OrderStakeholderParty"
        seller:
          $ref: "#/components/schemas/OrderStakeholderSeller"
      required:
        - id
        - role
        - party
      examples:
        - id: d74d4561-f285-4a88-bd81-0fc0882475b5
          role: TRAVELER
          party:
            subType: individual
            individual:
              personalDataReference:
                href: /v1/privacy/6X/personal-data/************************************?dataVersion=1
        - id: 13a2c3bb-13ba-4a80-91e8-743f73522389
          role: SELLER
          party:
            subType: company
            company:
              name: Seller
              contacts:
                - email:
                    address: <EMAIL>
                - phone:
                    deviceType: MOBILE
                    number: "+33611223344"
                - address:
                    text: www.website.com
              companySubType: travelAgency
              travelAgency:
                iataNumber: "83241765"
                pseudoCityIdentifier: AD641
          seller:
            pointOfSale:
              location:
                iataCode: NCE
                countries:
                  - code: FR
            loginArea:
              office:
                systemCode: 1A
                id: NCE6X1234
        - id: 20543d93-6294-470a-85ab-fb1d96d67bc7
          role: SELLER
          party:
            subType: company
            company:
              name: Amadeus Six
              code: 6X
              codeOwner: IATA
              contacts:
                - email:
                    address: <EMAIL>
                - phone:
                    deviceType: MOBILE
                    number: "+33611223344"
                - address:
                    text: www.amadeus-six.com/us-en/
              companySubType: airline
              airline:
                numericalAirlineCode: "172"
          seller:
            pointOfSale:
              location:
                iataCode: NYC
                countries:
                  - code: US
            loginArea:
              office:
                systemCode: 1A
                id: NCE6X9876
        - id: 13a2c3bb-13ba-4a80-91e8-743f73522389
          role: RETAILER
          party:
            subType: company
            company:
              name: Amadeus Six
              code: 6X
              codeOwner: IATA
              companySubType: airline
              airline:
                numericalAirlineCode: "172"
        - id: 13a2c3bb-13ba-4a80-91e8-743f73522389
          role: AGGREGATOR
          party:
            subType: company
            company:
              name: Amadeus
              code: 1A
              codeOwner: IATA
        - id: cb5ded7e-a0c0-4b0e-99eb-dede0952c8d4
          role: PAYER
          party:
            subType: organization
            organization:
              name: Elementary School of My City
              headquartersLocation:
                address:
                  lines:
                    - 111 School Street
                  postalCode: "06530"
                  countryCode: FR
                  cityName: My City
              contacts:
                - email:
                    address: <EMAIL>
                  phone:
                    number: "+33499887766"
    OrderExternalResourceReference:
      type: object
      title: OrderExternalResourceReference
      description: Link for the referenced resource which is expressed as URI (absolute or relative).
      properties:
        id:
          type: string
          description: Id of the related resource.
        type:
          type: string
          description: Type of the related resource.
          examples:
            - processed-dcs-passenger
        href:
          type: string
          description: URI reference to the related source.
          format: uri-reference
        rel:
          type: string
          description: 'Expose the type of relation between the current entity and the describe entity : https://www.iana.org/assignments/link-relations/link-relations.xhtml'
    OrderStakeholderPointOfBusiness:
      type: object
      title: OrderStakeholderPointOfBusiness
      description: 'Characteristics and environmental information of a conceptually and individually identifiable point of business interaction leading to some busines operations (such as *Promotion*, *Search*, *Presentation*, *Purchase*, *Sales*, *Delivery*, *Support*, *Repair*...).'
      properties:
        location:
          $ref: '#/components/schemas/OrderStakeholderPointOfBusinessLocation'
        office:
          $ref: '#/components/schemas/OrderStakeholderPointOfBusinessOffice'
    OrderStakeholderPointOfBusinessLocation:
      type: object
      title: OrderStakeholderPointOfBusinessLocation
      description: Description of a particular point or place in physical space.
      properties:
        iataCode:
          type: string
          description: IATA location code.
          examples:
            - PAR
        countries:
          type: array
          items:
            $ref: '#/components/schemas/OrderStakeholderPointOfBusinessLocationCountry'
    OrderStakeholderPointOfBusinessLocationCountry:
      type: object
      title: OrderStakeholderPointOfBusinessLocationCountry
      description: Description of a particular geographical country.
      properties:
        code:
          type: string
          description: |-
            Representation of the country code based on the ISO 3166-1 alpha-2.
            For the current list of codes, see https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2
          examples:
            - FR (for France)
    OrderStakeholderPointOfBusinessOffice:
      type: object
      title: OrderStakeholderPointOfBusinessOffice
      description: Amadeus office.
      properties:
        id:
          type: string
          description: 'Id for a corporate user of a computer reservation system or global distribution system, typically a travel agency, also known as office ID.'
        iataNumber:
          type: string
          description: IATA assigned agency number.
        systemCode:
          type: string
          description: 2-3 character airline/CRS code of the system that the current transaction or the issuance is originated from.
    OrderStakeholderRole:
      enum:
        - SELLER
        - TRAVELER
        - BUYER
        - PAYER
        - RETAILER
        - AGGREGATOR
      title: OrderStakeholderRole
      description: Stakeholder role for the related Order
    OrderStakeholderParty:
      type: object
      title: OrderStakeholderParty
      description: A physical person or a legal entity
      properties:
        subType:
          $ref: "#/components/schemas/OrderStakeholderPartySubType"
        individual:
          $ref: "#/components/schemas/OrderStakeholderPartyIndividual"
        organization:
          $ref: "#/components/schemas/OrderStakeholderPartyOrganization"
        company:
          $ref: "#/components/schemas/OrderStakeholderPartyCompany"
    OrderStakeholderPartySubType:
      enum:
        - individual
        - organization
        - company
      title: OrderStakeholderPartySubType
      description: Sub type of the party representing the stakeholder
    OrderStakeholderPartyIndividual:
      type: object
      title: OrderStakeholderPartyIndividual
      description: An individual party (physical person) in an order stakeholder
      properties:
        personalDataRef:
          $ref: "#/components/schemas/OrderExternalResourceReference"
    OrderStakeholderPartyOrganization:
      type: object
      title: OrderStakeholderPartyOrganization
      description: An organization party (non-profit legal person) in an order stakeholder
      properties:
        name:
          type: string
          description: Common name of the organization
        headquartersLocation:
          $ref: "#/components/schemas/OrderStakeholderPartyOrganizationLocation"
        contacts:
          type: array
          description: List of contact information
          items:
            $ref: "#/components/schemas/OrderStakeholderContact"
    OrderStakeholderPartyOrganizationLocation:
      type: object
      title: OrderStakeholderPartyOrganizationLocation
      description: Organization headquarters location
      properties:
        address:
          $ref: "#/components/schemas/OrderStakeholderContactAddress"
    OrderStakeholderContactAddress:
      type: object
      title: OrderStakeholderContactAddress
      description: Address information
      properties:
        lines:
          type: array
          description: "Line 1 = Street address, Line 2 = Apartment, suite, unit, building, floor, etc"
          items:
            type: string
        postalCode:
          type: string
          examples:
            - "74130"
        countryCode:
          type: string
          description: ISO 3166-1 country code
          pattern: "[a-zA-Z]{2}"
        cityName:
          type: string
          description: Full city name.
          pattern: "[a-zA-Z -]{1,35}"
          examples:
            - Dublin
        stateCode:
          type: string
          description: State code (two character standard IATA state code)
          pattern: "[a-zA-Z0-9]{1,2}"
        postalBox:
          type: string
          examples:
            - BP 220
        state:
          type: string
          examples:
            - Florida
    OrderStakeholderContact:
      type: object
      title: OrderStakeholderContact
      description: Contact of the Organization
      properties:
        phone:
          $ref: "#/components/schemas/OrderStakeholderContactPhone"
        address:
          $ref: "#/components/schemas/OrderStakeholderContactAddress"
        email:
          $ref: "#/components/schemas/OrderStakeholderContactEmail"
    OrderStakeholderContactPhone:
      type: object
      title: OrderStakeholderContactPhone
      description: Phone information
      properties:
        deviceType:
          enum:
            - LANDLINE
            - MOBILE
            - FAX
          description: "Type of the device (Landline, Mobile or Fax)"
        countryCallingCode:
          type: string
          description: 'Country calling code of the phone number, as defined by the International Communication Union. Examples - "1" for US, "371" for Latvia.'
          pattern: "[0-9+]{2,5}"
          examples:
            - "+33"
        number:
          type: string
          description: Phone number. Composed of digits only. The number of digits depends on the country.
          pattern: "[0-9]{1,15}"
          examples:
            - "0336123123"
    OrderStakeholderContactEmail:
      type: object
      title: OrderStakeholderContactEmail
      description: Email information
      properties:
        address:
          type: string
          description: Email address
          examples:
            - <EMAIL>
    OrderStakeholderPartyCompany:
      type: object
      title: OrderStakeholderPartyCompany
      description: A company party (for-profit legal person) in an order stakeholder
      properties:
        name:
          type: string
          description: Common name of the organization
        contacts:
          type: array
          description: List of contact information
          items:
            $ref: "#/components/schemas/OrderStakeholderContact"
        code:
          type: string
          description: 'Free flow code, see field "codeOwner" to know the registry authority owner of this code.'
          examples:
            - "AMA, 1A"
        codeOwner:
          type: string
          description: "Owner (register authority) of the list of Company (Example: IATA is owner of IATA Airline Company codes)."
          examples:
            - IATA
        companySubType:
          $ref: "#/components/schemas/OrderStakeholderPartyCompanySubType"
        airline:
          $ref: "#/components/schemas/OrderStakeholderPartyCompanyAirline"
        travelAgency:
          $ref: "#/components/schemas/OrderStakeholderPartyCompanyTravelAgency"
    OrderStakeholderPartyCompanySubType:
      enum:
        - airline
        - travelAgency
      title: OrderStakeholderPartyCompanySubType
      description: Sub type of the company party representing the stakeholder in the order
    OrderStakeholderPartyCompanyAirline:
      type: object
      title: OrderStakeholderPartyCompanyAirline
      description: Company extension to become an airline
      properties:
        numericalAirlineCode:
          type: string
          description: 3-digits IATA numerical airline code
    OrderStakeholderPartyCompanyTravelAgency:
      type: object
      title: OrderStakeholderPartyCompanyTravelAgency
      description: Company extension to become a travel agency
      properties:
        agencyidentifier:
          type: string
          description: Unique agency seller ID
        iatanumber:
          type: string
          description: IATA-assigned numerical agency number
        pseudoCityIdentifier:
          type: string
          description: "An identifier for a corporate user of a computer reservation system (CRS) or global distribution system (GDS), typically a travel agency"
    OrderStakeholderSeller:
      type: object
      title: OrderStakeholderSeller
      description: Stakeholder extension to become a seller
      properties:
        pointOfSale:
          $ref: "#/components/schemas/OrderStakeholderSellerPointOfBusiness"
        loginArea:
          $ref: "#/components/schemas/OrderStakeholderSellerLoginArea"
    OrderStakeholderSellerPointOfBusiness:
      type: object
      title: OrderStakeholderSellerPointOfBusiness
      description: "Characteristics and environmental information of a conceptually and individually identifiable point of business interaction leading to some busines operations (such as *Promotion*, *Search*, *Presentation*, *Purchase*, *Sales*, *Delivery*, *Support*, *Repair*...)."
      properties:
        location:
          $ref: "#/components/schemas/OrderStakeholderPointOfBusinessLocation"
    OrderStakeholderSellerLoginArea:
      type: object
      title: OrderStakeholderSellerLoginArea
      description: It defines the right of a SecurityUser (LSS user) to access an office
      properties:
        office:
          $ref: "#/components/schemas/OrderStakeholderSellerLoginAreaOffice"
    OrderStakeholderSellerLoginAreaOffice:
      type: object
      title: OrderStakeholderSellerLoginAreaOffice
      description: Amadeus office
      properties:
        systemCode:
          type: string
          description: 2-3 character airline/CRS code of the system that originates the current transaction or the issuance
        id:
          type: string
          description: "Amadeus office ID, composed of 9 characters (same ad ID)"
    OrderAgreement:
      type: object
      title: OrderAgreement
      description: 'Legally enforceable contract formed when one party makes an offer that is accepted by the relevant stakeholders, contingent upon the fulfillment of specific conditions.'
      properties:
        id:
          type: string
          description: 'Id of the Agreement, unique within an Order.'
        lifecycle:
          $ref: '#/components/schemas/OrderOrderLifecycle'
        status:
          $ref: '#/components/schemas/OrderAgreementStatus'
        origin:
          $ref: '#/components/schemas/OrderAgreementOrigin'
        items:
          type: array
          description: Elements constituting the Agreement.
          items:
            $ref: '#/components/schemas/OrderAgreementItem'
        sellerPtr:
          $ref: '#/components/schemas/OrderInternalReference'
      required:
        - id
        - lifecycle
        - origin
        - items
    OrderAgreementStatus:
      enum:
        - ACTIVE
        - REVOKED
      title: OrderAgreementStatus
      description: |-
        Agreement status
        ACTIVE -> Agreement is applicable.
        REVOKED -> Agreement is no more applicable due to terms and conditions no longer valid.
    OrderAgreementOrigin:
      enum:
        - ORDER
        - RECONSTRUCTION
      title: OrderAgreementOrigin
      description: |-
        Agreement origin
        ORDER -> Agreement has been created from an Offer/Order native touchpoint.
        RECONSTRUCTION -> Agreement has been created from a PSS message going through smartbridge.
    OrderAgreementItem:
      type: object
      title: OrderAgreementItem
      description: Details for the Agreement item.
      properties:
        id:
          type: string
          description: Id of the Agreement item.
        offerItemRef:
          $ref: '#/components/schemas/OrderOfferItemReference'
        stakeholderPtrByOfferStakeholderId:
          type: object
          description: JSON pointer reference to the stakeholder id of the offer model. This will be used to match stakeholders between offer and order.
          additionalProperties:
            $ref: '#/components/schemas/OrderInternalReference'
      required:
        - id
    OrderOfferItemReference:
      type: object
      title: OrderOfferItemReference
      description: Reference details for the related Offer Item.
      properties:
        offerRef:
          $ref: '#/components/schemas/OrderExternalResourceReference'
        offerItemPtr:
          $ref: '#/components/schemas/OrderInternalReference'
    OrderInternalReference:
      type: string
      title: OrderInternalReference
      description: 'Link to another part of the resource, expressed as a JSON pointer starting from resource root. See https://datatracker.ietf.org/doc/html/rfc6901#section-5'
      format: json-pointer
    OrderOrderItem:
      type: object
      title: OrderOrderItem
      description: |-
        Order Item is the entity tracking the fulfillment of the service liabilities corresponding to the Service Items contained in one Offer Item inside the contractualized Offer that triggered the creation/update of the Order. It is a list of Service Fulfillments, each one uniquely identified.
        Any number of travelers / services / service types (flight, air ancillary, car, etc...) can be involved in the same Order Item, it doesn't need to be homogeneous.
      properties:
        id:
          type: string
          description: Unique id of the item within an Order.
        status:
          $ref: '#/components/schemas/OrderOrderItemStatus'
        createdByAgreementItemPtr:
          $ref: '#/components/schemas/OrderInternalReference'
        cancelledByAgreementItemPtr:
          $ref: '#/components/schemas/OrderInternalReference'
        services:
          type: array
          description: List of Order Service Fulfillments to be delivered part of this Order Item.
          items:
            $ref: '#/components/schemas/OrderServiceFulfillment'
      required:
        - id
    OrderOrderItemStatus:
      enum:
        - CONFIRMED
        - CANCELLED
      title: OrderOrderItemStatus
      description: Order Item status
      x-enum-descriptions:
        - Order Item status when the order item is CONFIRMED
        - Order Item status when the order item is CANCELLED
    OrderServiceFulfillment:
      type: object
      title: OrderServiceFulfillment
      description: |-
        Order Service Fulfillment tracks the fulfillment of the supplier's liability to deliver the ordered service, it is the smallest unit tracking the delivery of one service for one traveler in an Order.
        In that regard it corresponds to the Offer Service Item: when an Order is created one service fulfillment is created for each service 
        item present in the Offer.
      properties:
        context:
          $ref: '#/components/schemas/OrderServiceFulfillmentContext'
        expectation:
          $ref: '#/components/schemas/OrderServiceFulfillmentDetails'
        measure:
          $ref: '#/components/schemas/OrderServiceFulfillmentMeasure'
        convergence:
          $ref: '#/components/schemas/OrderFulfillmentConvergence'
      allOf:
        - $ref: '#/components/schemas/OrderFulfillment'
      Fulfillment items are categorized into four groups:
        - 'context, linking the item with the offer it originates from.'
        - 'expectation, indicating fulfillment details targeted by Order native touchpoint.'
        - 'measure, mirroring the fulfillment details as it appears in an external system synchronized through smartbridge (e.g. PSS information).'
        - 'convergence status, highlighting the Order Management system achievement on aligning measurement to expectation.'
    OrderServiceFulfillmentContext:
      type: object
      title: OrderServiceFulfillmentContext
      description: Background details regarding the responsibility associated with the service.
      properties:
        offerServiceItemRef:
          $ref: '#/components/schemas/OrderOfferServiceItemReference'
        stakeholders:
          type: array
          description: 'Person, group or organization involved in the Order.'
          items:
            $ref: '#/components/schemas/OrderServiceFulfillmentStakeholder'
        associatedServiceFulfillments:
          type: array
          description: |-
            Reference information for the associated Offer Service Item. It allows to explicit the link between two order items.
            e.g. Ancillary service associated to a flight item.
          items:
            $ref: '#/components/schemas/OrderServiceFulfillmentAssociation'
        transferredFromOrderItemPtrs:
          type: array
          description: |-
            'Array of pointers to order items (JSON pointer starting from resource root of the order therefore in the format of "/orderItems/<ordre_item_id>").
            This pointer will be present for fulfillment that has been transferred from one order item to another during an exchange.'
          items:
            $ref: '#/components/schemas/OrderInternalReference'
        soldProduct:
          $ref: '#/components/schemas/OrderServiceFulfillmentSoldProduct'
    OrderOfferServiceItemReference:
      type: object
      title: OrderOfferServiceItemReference
      description: Link to offer service item related. Referred resource is expressed as URI.
      properties:
        offerRef:
          $ref: '#/components/schemas/OrderExternalResourceReference'
        offerServiceItemPtr:
          $ref: '#/components/schemas/OrderInternalReference'
    OrderServiceFulfillmentStakeholder:
      type: object
      title: OrderServiceFulfillmentStakeholder
      description: 'Person, group or organization involved in the Order.'
      properties:
        stakeholderType:
          $ref: '#/components/schemas/OrderServiceFulfillmentStakeholderType'
        stakeholderPtr:
          $ref: '#/components/schemas/OrderInternalReference'
        personalData:
          $ref: '#/components/schemas/OrderServiceFulfillmentStakeholderPersonalData'
    OrderServiceFulfillmentStakeholderType:
      enum:
        - ADULT
        - INFANT
        - CHILD
      title: OrderServiceFulfillmentStakeholderType
      description: 'Stakeholder type, in the context of a specific fulfillment.'
      x-enum-descriptions:
        - Adult
        - Child
        - Infant
    OrderServiceFulfillmentStakeholderPersonalData:
      type: object
      title: OrderServiceFulfillmentStakeholderPersonalData
      description: Personal data details.
      properties:
        personalDataRef:
          $ref: '#/components/schemas/OrderExternalResourceReference'
        contactPtrs:
          type: array
          description: JSON pointers to contact information of the stakeholder.
          items:
            $ref: '#/components/schemas/OrderInternalReference'
        identityDocumentPtrs:
          type: array
          description: JSON pointers to identity document of the stakeholder.
          items:
            $ref: '#/components/schemas/OrderInternalReference'
        membershipPtrs:
          type: array
          description: JSON pointers to membership information of the stakeholder (e.g. frequent flyer)
          items:
            $ref: '#/components/schemas/OrderInternalReference'
    OrderServiceFulfillmentAssociation:
      type: object
      title: OrderServiceFulfillmentAssociation
      description: Reference information for the associated service item.
      properties:
        servicePtr:
          $ref: '#/components/schemas/OrderInternalReference'
        associationType:
          $ref: '#/components/schemas/OrderServiceFulfillmentAssociationType'
    OrderServiceFulfillmentAssociationType:
      enum:
        - ACCOMPANYING_PERSON
        - ACCOMPANIED_PERSON
        - RELATED_FLIGHT
        - ANCILLARY
      title: OrderServiceFulfillmentAssociationType
      description: Type of association from current service fulfillment to another one.
    OrderServiceFulfillmentSoldProduct:
      type: object
      title: OrderServiceFulfillmentSoldProduct
      description: Details for the sold product.
      properties:
        productRef:
          $ref: '#/components/schemas/OrderExternalResourceReference'
      allOf:
        - $ref: '#/components/schemas/OrderServiceFulfillmentPoductDetails'
    OrderServiceFulfillmentPoductDetails:
      type: object
      title: OrderServiceFulfillmentPoductDetails
      description: Details for the service item product.
      properties:
        productInstance:
          $ref: '#/components/schemas/OrderServiceFulfillmentPoductInstance'
        serviceItemExecution:
          $ref: '#/components/schemas/Service%20Item%20Execution'
    OrderServiceFulfillmentPoductInstance:
      type: object
      title: OrderServiceFulfillmentPoductInstance
      description: Concrete product model with multiple level of instances differentiated by product type. Each type matches one of the product instances.
      oneOf:
        - $ref: '#/components/schemas/Product'
        - $ref: '#/components/schemas/Leg%20Cabin%20Product'
        - $ref: '#/components/schemas/Leg%20Cabin%20Product_2'
        - $ref: '#/components/schemas/airSeatProduct'
    Product:
      type: object
      title: Product
      properties:
        id:
          type: string
          description: identifier of the product
        name:
          type: string
          description: name of the product
        productType:
          type: string
          description: |-
            The Product Type is the general category of the product. Each Product Type has a dedicated datamodel.

            The Product type value acts as a discriminator to select the right concrete product model. Therefore, it is expected to match the name of one of the models present in this specification.
        taxonomy:
          type: string
          description: 'The product categorization as per IATA Taxonomy https://airtechzone.iata.org/docs/AirlineTaxonomy.html'
    Leg Cabin Product:
      type: object
      title: Leg Cabin Product
      description: |-
        The right to fly on a Leg Cabin Date is the atomic unit of service delivery for air travellers.

        For more information about why the product is a leg cabin and not a segment, see https://rndwww.nce.amadeus.net/confluence/pages/viewpage.action?pageId=1490780068
      properties:
        id:
          type: string
          description: identifier of the product
        name:
          type: string
          description: name of the product
        productType:
          type: string
          description: |-
            The Product Type is the general category of the product. Each Product Type has a dedicated datamodel.

            The Product type value acts as a discriminator to select the right concrete product model. Therefore, it is expected to match the name of one of the models present in this specification.
        taxonomy:
          type: string
          description: 'The product categorization as per IATA Taxonomy https://airtechzone.iata.org/docs/AirlineTaxonomy.html'
        marketingFlightDesignator:
          $ref: '#/components/schemas/FlightDesignator'
        operatingFlightDesignator:
          $ref: '#/components/schemas/FlightDesignator'
        leg:
          $ref: '#/components/schemas/Leg'
        cabin:
          $ref: '#/components/schemas/Cabin'
      x-examples:
        AY*/SQ HKG-SFO leg in cabin M:
          id: '1346602422'
          productType: airLegCabinProduct
          leg:
            boardPointIataCode: HKG
            offPointIataCode: SFO
            scheduledArrivalDateTime: '2022-02-23T19:40:00'
            scheduledDepartureDateTime: '2022-03-23T23:40:00'
            aircraftEquipment:
              equipmentType: '359'
          cabin:
            code: M
          operatingFlightDesignator:
            carrierCode: SQ
            flightNumber: '2'
          marketingFlightDesignator:
            carrierCode: AY
            flightNumber: '3'
    FlightDesignator:
      type: object
      title: FlightDesignator
      properties:
        carrierCode:
          type: string
          description: Two letter IATA standard carrier code
          examples:
            - 6X
        flightNumber:
          type: string
          description: 1-4 digit number
          examples:
            - '555'
        operationalSuffix:
          type: string
          description: the operational suffix
          examples:
            - A
        carrierName:
          type: string
          description: Full name of the carrier. This field can be used to either complement the carrier code if available or give the carrier name when IATA standard carrier code does not exist
          examples:
            - Amadeus SIX
    Leg:
      type: object
      title: Leg
      description: Identifier of specified flight leg in the overall flight plan. It is composed of boardPoint and offPoint of the leg with date and time of aircraft departure.
      properties:
        id:
          type: string
          description: identifier of the leg
          examples:
            - GVA-YUL
        boardFlightPointId:
          type: string
        offFlightPointId:
          type: string
        boardPointIataCode:
          type: string
          description: IATA code of the leg's departure airport
          examples:
            - GVA
        offPointIataCode:
          type: string
          description: IATA code of the leg's arrival airport
          examples:
            - YUL
        scheduledDepartureDateTime:
          type: string
          description: 'scheduled departure date and time of the Leg in local time, ISO 8601 format yyyy-mm-ddThh:mm:ss'
          format: dateTime
          examples:
            - '2018-09-17T12:40:00'
        scheduledArrivalDateTime:
          type: string
          description: 'scheduled arrival date and time of the Leg in local time, ISO 8601 format yyyy-mm-ddThh:mm:ss.'
          format: dateTime
          examples:
            - '2018-09-17T14:45:00'
      x-examples:
        example-Leg:
          leg:
            id: GVA-YUL
            version: '2'
            boardPointIataCode: GVA
            offPointIataCode: YUL
            scheduledDepartureDateTime: '2001-12-31T12:00:00'
            routingOrder: '2'
            serviceType: J
            departureAircraftLocation: LHR Terminal 1 Gate 25
            aircraftEquipment:
              aircraftType: '332'
              aircraftSubType: A330-223B
              operationalFittedConfiguration: 27J263Y
              aircraftRegistration:
                - number: DATUZ
              scheduledFittedConfiguration:
                configurationContents:
                  - capacity: '12'
              scheduledSaleableConfiguration:
                configurationContents:
                  - capacity: '12'
              seating:
                - seatingConfigurationType: SEAT_ROW
            legDCSStatuses:
              acceptanceStatus: NOT_OPEN
              generalStatus: OPEN
              loadControlStatus: OPEN
              boardingStatus: OPEN
              rampStatus: OPEN
            legWeightBalance:
              loadController:
                name:
                  firstName: Scooby
                  lastName: Dooby
              passengerWeightType: ACI
              weightAndIndex: Standard
              includeTareWeightConfigurationCode: ACI
              unitLoadDeviceConfiguration:
                configurationContent: STANDARD
                capacityCategory: 6X
                capacityType: 6X
              weightBalanceConfiguration:
                - loadSerialNumber: '50432872'
                  aquariusSerialNumber: '50432672'
                  loadType: BULK
                  piggyBackedUnitLoadDeviceId:
                    - '90876'
                    - '10876'
                  unitLoadDevice:
                    equipmentCode: AKE1MH
                    heightCode: '5'
                    volumeLeftInEquipment: NO_VOLUME_AVAILABLE
                    percentageVolumeOfFullnessInEquipment: NO_VOLUME_AVAILABLE
                    grossWeight:
                      value: '559'
                      unit: kilograms
                      decimalPlaces: '2'
                    tareWeight:
                      value: '76'
                      unit: kilograms
                      decimalPlaces: '2'
                    changeOfGaugeDescription: Demo Arrival Terminal Information
                  boardPointIataCode: LON
                  offPointIataCode: KUL
                  loadVolume:
                    value: '694'
                  parentNumber: '50432872'
                  loadPosition: '51'
                  positionType: BAY
                  holdOfLoadPosition: FORWARD
                  holdNumber: '1'
                  barrowId: '11144'
                  loadDescription: Initial Load Output YAML
                  loadEvent: CREATE
                  loadControls:
                    isLoadSeenAtRamp: true
                    isDeadloadItemOffloaded: true
                    isLoadControllerEnteredVolume: true
                    isLoadDipLocker: true
                    isLoadRemainingOnBoard: true
                    isLoadAutoLoaded: true
                    isLoadExcluded: true
                    isTransitIndicatorSelected: true
                    isFloatingPallet: true
                    isRampCleared: true
                    createdBy: AGENT
                  deadLoad:
                    - deadLoadType: BAGGAGE
                      cargoSubType: Economy Baggage
                      containerType: BACK DOOR
                      volumeLeftInEquipment: THREE_QUARTERS_VOLUME_AVAILABLE
                      equipmentType: DQ
                      weight:
                        value: '100'
                        unit: kilograms
                      volume:
                        value: '100'
                        unit: CUBIC_FEET
                      numberOfPieces: '0'
                      priorityNumber: '1'
                      deadLoadReferenceNumber: '52100421'
                      boardPointIataCode: CGN
                      offPointIataCode: MUC
                      description: Demo DeadLoad description
                      deadLoadStatus: PROVISIONAL
                      deadLoadControls:
                        isRestLocationSelected: true
                        isTransitIndicatorSelected: true
                        isPrimaryCommodity: true
                        isLoadAutoLoaded: true
                        isDeadLoadWeightIncludedInDryOperatingWeight: true
                        isPiecesFirmedByLoadController: true
                        createdBy: AGENT
                      dangerousGoodsAndSpecialLoad:
                        - bagTagInformation: dgslDescriptionBTN
                          transportIndexInformation: dgslDescriptionQTI
                          iataCode: DIP
                          emergencyResponseCode: Demo emergencyResponseCode
                          uniqueIdentificationNumber: '1133'
                          packingGroup: MINOR_DANGER
                          hazardCategory: '9'
                          radioactiveCategory: '4'
                          subsidiaryRiskGroup: 2.1 2.2 2.3
                          aquariusSerialNumber: '50432'
                          parentNumber: AQID1004
                          deadLoadConfiguration:
                            airwayBillNumber: 081-44444444
                            description: This contains proper shipping name or description
                            uniqueIdentifier: '10567'
                            weight:
                              value: '2'
                              unit: kilograms
                            numberOfPieces: '8'
                            supplementaryInformation: dgslDescriptionSUP
                          dangerousGoodsAndSpecialLoadControls:
                            isMissingItemMandatory: true
                            isRigidity: true
                            isRigidityUnknown: true
                            isDangerousGood: true
                            isSpecialLoad: false
                            isAllowedOnCargoAircraft: true
                            isIataCodeAvailable: true
                            isParentNumberContent: true
                            isEmergencyResponseCodeAvailable: true
                            isRadioactive: true
                            isNumberOfPackageSpecified: false
                            isClassOrDivisionSpecified: false
                            isNetWeightAvailable: true
                            isUniqueIdentificationNumberAvailable: true
                            isInPackingGroup: true
                            isAirwayBillNumberAvailable: true
                            isInSubsidiaryRiskGroup: true
                            isBagTagInformationAvailable: true
                            isTransportIndexAvailable: true
                            isForNonRadioactiveItems: false
                            createdBy: AGENT
                      allPackedInOneForDeadLoad:
                        - deadLoadConfiguration:
                            airwayBillNumber: 081-55555555
                            description: This contains proper shipping name or description
                            uniqueIdentifier: '9388'
                            externalCargoUniqueId: AQID2211
                            weight:
                              value: '100'
                            height: '234'
                            numberOfPieces: '1'
                            supplementaryInformation: APO Supplementary Information
                          dangerousGoodsAndSpecialLoad:
                            - bagTagInformation: dgslDescriptionBTN
                              transportIndexInformation: dgslDescriptionQTI
                              iataCode: DIP
                              emergencyResponseCode: Demo emergencyResponseCode
                              uniqueIdentificationNumber: '1133'
                              packingGroup: MINOR_DANGER
                              hazardCategory: '9'
                              radioactiveCategory: '4'
                              subsidiaryRiskGroup: 2.1 2.2 2.3
                              aquariusSerialNumber: '50432'
                              parentNumber: AQID1004
                              deadLoadConfiguration:
                                airwayBillNumber: 081-44444444
                                description: This contains proper shipping name or description
                                uniqueIdentifier: '10567'
                                weight:
                                  value: '2'
                                  unit: kilograms
                                numberOfPieces: '8'
                                supplementaryInformation: dgslDescriptionSUP
                              dangerousGoodsAndSpecialLoadControls:
                                isMissingItemMandatory: true
                                isRigidity: true
                                isRigidityUnknown: true
                                isDangerousGood: true
                                isSpecialLoad: false
                                isAllowedOnCargoAircraft: true
                                isIATACodeAvailable: true
                                isParentNumberContent: true
                                isEmergencyResponseCodeAvailable: true
                                isRadioactive: true
                                isNumberOfPackageSpecified: false
                                isClassOrDivisionSpecified: false
                                isNetWeightAvailable: true
                                isUniqueIdentificationNumberAvailable: true
                                isInPackingGroup: true
                                isAirwayBillNumberAvailable: true
                                isInSubRiskGroup: true
                                isBagTagNumberAvailable: true
                                isTransportIndexAvailable: true
                                isForNonRadioactiveItems: false
                                createdBy: AGENT
                      overPackForDeadLoad:
                        - deadLoadConfiguration:
                            airwayBillNumber: 081-55555555
                            description: This contains proper shipping name or description
                            uniqueIdentifier: '9388'
                            externalCargoUniqueId: AQID2211
                            weight:
                              value: '100'
                            height: '234'
                            numberOfPieces: '1'
                            supplementaryInformation: OVP Supplementary Information
                          dangerousGoodsAndSpecialLoad:
                            - bagTagInformation: dgslDescriptionBTN
                              transportIndexInformation: dgslDescriptionQTI
                              iataCode: DIP
                              emergencyResponseCode: Demo emergencyResponseCode
                              uniqueIdentificationNumber: '1133'
                              packingGroup: MINOR_DANGER
                              hazardCategory: '9'
                              radioactiveCategory: '4'
                              subsidiaryRiskGroup: 2.1 2.2 2.3
                              aquariusSerialNumber: '50432'
                              parentNumber: AQID1004
                              deadLoadConfiguration:
                                airwayBillNumber: 081-44444444
                                description: This contains proper shipping name or description
                                uniqueIdentifier: '10567'
                                weight:
                                  value: '2'
                                  unit: kilograms
                                numberOfPieces: '8'
                                supplementaryInformation: dgslDescriptionSUP
                              dangerousGoodsAndSpecialLoadControls:
                                isMissingItemMandatory: true
                                isRigidity: true
                                isRigidityUnknown: true
                                isDangerousGood: true
                                isSpecialLoad: false
                                isAllowedOnCargoAircraft: true
                                isIATACodeAvailable: true
                                isParentNumberContent: true
                                isEmergencyResponseCodeAvailable: true
                                isRadioactive: true
                                isNumberOfPackageSpecified: false
                                isClassOrDivisionSpecified: false
                                isNetWeightAvailable: true
                                isUniqueIdentificationNumberAvailable: true
                                isInPackingGroup: true
                                isAirwayBillNumberAvailable: true
                                isInSubRiskGroup: true
                                isBagTagInformationAvailable: true
                                isTransportIndexAvailable: true
                                isForNonRadioactiveItems: false
                                createdBy: AGENT
                          overPackAllPackedInOneForDeadLoad:
                            - deadLoadConfiguration:
                                airwayBillNumber: 081-55555555
                                description: This contains proper shipping name or description
                                uniqueIdentifier: '9388'
                                externalCargoUniqueId: AQID2211
                                weight:
                                  value: '100'
                                height: '234'
                                numberOfPieces: '1'
                                supplementaryInformation: APO Supplementary Information
                              dangerousGoodsAndSpecialLoad:
                                - bagTagInformation: dgslDescriptionBTN
                                  transportIndexInformation: dgslDescriptionQTI
                                  iataCode: DIP
                                  emergencyResponseCode: Demo emergencyResponseCode
                                  uniqueIdentificationNumber: '1133'
                                  packingGroup: MINOR_DANGER
                                  hazardCategory: '9'
                                  radioactiveCategory: '4'
                                  subsidiaryRiskGroup: 2.1 2.2 2.3
                                  aquariusSerialNumber: '50432'
                                  parentNumber: AQID1004
                                  deadLoadConfiguration:
                                    airwayBillNumber: 081-44444444
                                    description: This contains proper shipping name or description
                                    uniqueIdentifier: '10567'
                                    weight:
                                      value: '2'
                                      unit: kilograms
                                    numberOfPieces: '8'
                                    supplementaryInformation: dgslDescriptionSUP
                                  dangerousGoodsAndSpecialLoadControls:
                                    isMissingItemMandatory: true
                                    isRigidity: true
                                    isRigidityUnknown: true
                                    isDangerousGood: true
                                    isSpecialLoad: false
                                    isAllowedOnCargoAircraft: true
                                    isIATACodeAvailable: true
                                    isParentNumberContent: true
                                    isEmergencyResponseCodeAvailable: true
                                    isRadioactive: true
                                    isNumberOfPackageSpecified: false
                                    isClassOrDivisionSpecified: false
                                    isNetWeightAvailable: true
                                    isUniqueIdentificationNumberAvailable: true
                                    isInPackingGroup: true
                                    isAirwayBillNumberAvailable: true
                                    isInSubRiskGroup: true
                                    isBagTagNumberAvailable: true
                                    isTransportIndexAvailable: true
                                    isForNonRadioactiveItems: false
                                    createdBy: AGENT
              passengerInformation:
                passengerOnCrewSeatInFlightDeck: '0'
                passengerOnCrewSeatInCabin: '4'
                passengerCountBySeatRow:
                  - row: '2'
                    maxCapacity: '15'
                    seatsOccupied: '6'
                passengerCountByCabin:
                  - cabin: B
                    maxCapacity: '15'
                    seatsOccupied: '6'
                passengerCountByClass:
                  - levelOfService: ECONOMY
                    passengerCounters:
                      - passengersAcceptanceType: BOOKED
                        seatsOccupied: '25'
                        numberOfAdults: '20'
                        numberOfMales: '12'
                        numberOfFemales: '6'
                        numberOfChildren: '2'
                        numberOfInfants: '0'
                        totalPassengersWeight:
                          value: '2064'
                          unit: Kilograms
                baggageByCommodity:
                  - commodityType: TOTAL_AMOUNT_COLLECTED
                    baggageCounters:
                      - numberOfBaggage: '86'
                        baggageWeight:
                          value: '1249'
                        passengerAcceptanceType: ACCEPTED
            legCounters:
              weightBalanceCounters:
                baggagePiecesAccepted: '90'
                totalBaggageWeight:
                  value: '1597'
                aircraftWeights:
                  - type: BASIC_WEIGHT
                    index: '0.6436'
                    weight:
                      unit: KG
                      value: '125.57'
                  - type: BALLAST_TRAPPED_FUEL
                    index: '0'
                    weight:
                      unit: KG
                      value: '0'
                  - type: TRIP_FUEL_WEIGHT
                    weight:
                      unit: KG
                      value: '52.09'
              weightBalanceControls:
                isAircraftContainerised: 'true'
                isNilTransitPassenger: 'true'
                isNoAllotment: 'true'
                isTareWeightIncludedByLoadController: 'true'
                isPartialAllocation: 'true'
                isRampClearanceNecessary: 'true'
                isFlightReleased: 'true'
                isWeightBalanceForLeg: 'true'
                isWeightPerBaggageIncluded: 'true'
                isWeightCoupledWithIndex: 'true'
                isLoadSeenAtRamp: true
                isDeadloadItemOffloaded: true
                isLoadControllerEnteredVolume: true
                isLoadDipLocker: true
                isLoadRemainingOnBoard: true
                isLoadAutoLoaded: true
                isLoadExcluded: true
                isTransitIndicatorSelected: true
                isFloatingPallet: true
                isRampCleared: true
                createdBy: AGENT
                isRestLocationSelected: true
                isPrimaryCommodity: true
                isDeadLoadWeightIncludedInDryOperatingWeight: true
                isPiecesFirmedByLoadController: true
                isMissingItemMandatory: true
                isRigidity: true
                isRigidityUnknown: true
                isDangerousGood: true
                isSpecialLoad: false
                isAllowedOnCargoAircraft: true
                isIATACodeAvailable: true
                isParentNumberContent: true
                isEmergencyResponseCodeAvailable: true
                isRadioactive: true
                isNumberOfPackageSpecified: false
                isClassOrDivisionSpecified: false
                isNetWeightAvailable: true
                isUniqueIdentificationNumberAvailable: true
                isInPackingGroup: true
                isAirwayBillNumberAvailable: true
                isInSubRiskGroup: true
                isBagTagNumberAvailable: true
                isTransportIndexAvailable: true
                isForNonRadioactiveItems: false
    Cabin:
      type: object
      title: Cabin
      properties:
        code:
          type: string
          description: cabin code filed in inventory for the sold seat
    Leg Cabin Product_2:
      type: object
      title: Leg Cabin Product
      properties:
        id:
          type: string
          description: identifier of the product
        name:
          type: string
          description: name of the product
        productType:
          type: string
          description: |-
            The Product Type is the general category of the product. Each Product Type has a dedicated datamodel.

            The Product type value acts as a discriminator to select the right concrete product model. Therefore, it is expected to match the name of one of the models present in this specification.
        taxonomy:
          type: string
          description: 'The product categorization as per IATA Taxonomy https://airtechzone.iata.org/docs/AirlineTaxonomy.html'
        segment:
          $ref: '#/components/schemas/FlightSegment'
        bookingClass:
          $ref: '#/components/schemas/BookingClass'
    FlightSegment:
      type: object
      title: FlightSegment
      description: 'Defining a flight segment, including both Operating and Marketing details when applicable - seats refer to SEATING product(s) & services refer to AIR_SERVICE product(s)'
      properties:
        duration:
          type: string
          examples:
            - PT12H30M
        aircraft:
          type: object
          title: AircraftEquipment
          description: description of the aircraft equipment
          properties:
            aircraftType:
              type: string
              description: 'aircraft type (ex 320, 777, ...)'
            aircraftDescription:
              type: string
              description: aircraft description (ex BOEING 737 ALL SERIES PASSENGER)
            realAircraftOwner:
              type: string
              description: real aircraft owner
            aircraftOwner:
              type: string
              description: aircraft owner
        marketing:
          type: object
          properties:
            flightDesignator:
              $ref: '#/components/schemas/FlightDesignator'
            bookingClass:
              $ref: '#/components/schemas/BookingClass'
            overBookingReason:
              enum:
                - FORCE_BOOKING
                - INVENTORY_SYNCHRONIZATION
              description: Indicates the Inventory origin of overbooking
              examples:
                - FORCE_BOOKING
            isOpenNumber:
              type: boolean
              description: 'Indicates an open flight segment, for which there is no specific flight number.'
              examples:
                - false
            fareBookingClass:
              $ref: '#/components/schemas/BookingClass'
        suffix:
          type: string
          description: The flight number suffix as assigned by the carrier
          minLength: 1
          maxLength: 4
          examples:
            - A
        number:
          type: string
          description: The flight number as assigned by the carrier
          minLength: 1
          maxLength: 4
          examples:
            - '123'
        carrierCode:
          type: string
          description: Providing the airline / carrier code
          minLength: 1
          maxLength: 2
          examples:
            - AC
        departure:
          type: object
          description: 'Departure or arrival information '
          properties:
            iataCode:
              type: string
              description: IATA Airport code
              examples:
                - JFK
            terminal:
              type: string
              description: Terminal name / number
              examples:
                - T2
            gate:
              type: string
              description: Gate information
              examples:
                - A21
            localDateTime:
              type: string
              description: Local dateTime of the departure or arrival
              format: dateTime
              examples:
                - '2020-01-13T17:09:00 at the "IATA airport code"'
            checkInEndTime:
              type: string
            localEstimatedDateTime:
              type: string
              description: 'ETD or ETA (Departure, depending whether the FlightEndPoint is a board; or Arrival if an off point)'
          x-examples: {}
        arrival:
          type: object
          description: 'Departure or arrival information '
          properties:
            iataCode:
              type: string
              description: IATA Airport code
              examples:
                - JFK
            terminal:
              type: string
              description: Terminal name / number
              examples:
                - T2
            gate:
              type: string
              description: Gate information
              examples:
                - A21
            localDateTime:
              type: string
              description: Local dateTime of the departure or arrival
              format: dateTime
              examples:
                - '2020-01-13T17:09:00 at the "IATA airport code"'
            checkInEndTime:
              type: string
            localEstimatedDateTime:
              type: string
              description: 'ETD or ETA (Departure, depending whether the FlightEndPoint is a board; or Arrival if an off point)'
          x-examples: {}
        stops:
          type: array
          description: 'Information regarding the different stops composing the flight segment. E.g. technical stop, change of gauge...'
          items:
            type: object
            description: Details of stops for direct or change of gauge flights
            properties:
              iataCode:
                type: string
                description: IATA Airport code
                examples:
                  - JFK
              duration:
                type: string
                description: Stop duration in ISO8601 (e.g. PT2H10M)
                examples:
                  - PT12H30M
              arrivalAt:
                type: string
                description: 'Arrival at the stop with the following format "yyyy-MM-dd''T''HH:mm:ssZ" (for example 2017-02-10T20:40:00+02:00)'
                format: date-time
                examples:
                  - '2017-10-23T20:00:00+02:00'
              departureAt:
                type: string
                description: 'Departure from the stop with the following format "yyyy-MM-dd''T''HH:mm:ssZ" (for example 2017-02-10T20:40:00+02:00)'
                format: date-time
                examples:
                  - '2017-10-23T20:00:00+02:00'
        operating:
          type: object
          properties:
            flightDesignator:
              $ref: '#/components/schemas/FlightDesignator'
    BookingClass:
      type: object
      title: BookingClass
      description: booking class description
      properties:
        levelOfService:
          type: string
          description: reservation level of service
        cabin:
          $ref: '#/components/schemas/Cabin'
        code:
          type: string
          description: code identifying the booking class
    airSeatProduct:
      type: object
      title: airSeatProduct
      properties:
        id:
          type: string
          description: identifier of the product
        name:
          type: string
          description: name of the product
        productType:
          type: string
          description: |-
            The Product Type is the general category of the product. Each Product Type has a dedicated datamodel.

            The Product type value acts as a discriminator to select the right concrete product model. Therefore, it is expected to match the name of one of the models present in this specification.
        taxonomy:
          type: string
          description: 'The product categorization as per IATA Taxonomy https://airtechzone.iata.org/docs/AirlineTaxonomy.html'
        seat:
          $ref: '#/components/schemas/Seat'
    Seat:
      type: object
      title: Seat
      description: Seat information
      properties:
        number:
          type: string
          description: 'Seat number corresponding to the concatenation of the seatmap row and the column information, for example 12B'
        characteristicsCodes:
          type: array
          description: Examples of possible characteristics (see SeatCharacteristicsCodes document)  A -> aisle AA -> all available aisle AJ -> adjacent aisle seat AV -> only available aisle seats AW -> all available window B -> bassinet BK -> blocked for prefered pax in adjacent seat CC -> center section RU -> unusable reclined seat RM -> suitable for pax with reduced mobility K -> bulkhead H -> handicapped I -> infant MA -> medically ok M -> seat without a movie view N -> no smoking S -> smoking U -> unaccompanied minor W -> window E -> emergency exit row C -> crew CH -> chargeable F -> added seat LH -> restricted seat offered long haul segment J -> rear facing seat KA -> bulkhead seat with movie screen OW -> overwing FB -> first bed seat ES -> economy seat BC -> business bed seat BS -> business seat EP -> economy plus seat PE -> premium economy seat EK -> economy comfort seat NB -> next to bassinet HS -> hard to stretch leg due to door 9 -> centre WA -> window aisle 1W -> no window 99 -> unavailable for sale RS -> right side of aircraft LS -> left side of aircraft EC -> electronic connection AS -> individual airphone 2 -> leg rest available L -> leg space seat 1B -> not allowed for medical 1A -> not allowed for infant IE -> not suitable for child DE -> deportee PC -> pet in cabin 1M -> seat with movie view O -> preferential seat V -> vacant or offered last Q -> quiet zone X -> no facility 1D -> restricted recline seat 1C -> not allowed for unaccompanied minor 3 -> individual video screen choice movies 3B -> individual video screen choice movies games info 3A -> individual video screen no choice movie IA -> inside aisle G -> forward end of cabin Z -> buffer zone EA -> not on exit FC -> front of cabin class compartment GR -> offered to travelers belonging to a group 1 -> restricted general 70 -> individual video screen services unspecified 700 -> individual video screen services unspecified 1NS -> unsuitable SC -> stretcher TF -> forward zone TR -> rear zone NA -> narrow leg space LI -> limited leg room IFE device LE -> limited leg room emergency exit NM -> non movable armrest I2 -> suitable for adult with 2 infants TC -> tail row X0 -> non-iata X0 X1 -> non-iata X1 X2 -> non-iata X2 X3 -> non-iata X3 X4 -> non-iata X4 X5 -> non-iata X5 X6 -> non-iata X6 X7 -> non-iata X7 X8 -> non-iata X8 X9 -> non-iata X9 Y0 -> non-iata Y0 Y1 -> non-iata Y1 Y2 -> non-iata Y2 Y3 -> non-iata Y3 Y4 -> non-iata Y4 Y5 -> non-iata Y5 Y6 -> non-iata Y6 Y7 -> non-iata Y7 Y8 -> non-iata Y8 Y9 -> non-iata Y9 R0 -> non-iata R0 R1 -> non-iata R1 R2 -> non-iata R2 R3 -> non-iata R3 R4 -> non-iata R4 R5 -> non-iata R5 R6 -> non-iata R6 R7 -> non-iata R7 R8 -> non-iata R8 R9 -> non-iata R9 72 -> undesirable CS -> conditional LF -> lie flat LB -> left facing lie flat LL -> left facing lie flat LR -> right facing lie flat LT -> right facing lie flat right angle MS -> middle
          items:
            type: string
        cabin:
          type: string
          description: Cabin code associated to the seat
        zone:
          type: string
          description: 'The name of the zone that the seat is resident, used for boarding priority'
        isGuaranteed:
          type: boolean
          description: Indicates whether the seat is guaranteed to a passenger or not
      x-examples:
        Handicapped Aisle Seat:
          value:
            number: 12C
            characteristicsCodes:
              - A
              - H
            cabin: 'Y'
            zone: M
    Service Item Execution:
      title: Service Item Execution
      description: |-
        Information necessary for the delivery of a Service Item, which is not a intrinsic characteristic of the product, but depends on the particular instantiation of said product in an offer.

        For instance:
        - the booking class in which a LegCabin product is offered goes there
        - RFISC/RFIC codes for a service

        The specific model of a Service Item Execution object depends on the Product Type code.
      oneOf:
        - $ref: '#/components/schemas/Order_ServiceItemExecutionAirLegCabin.v1'
        - $ref: '#/components/schemas/serviceItemExecutionAirAncillaryService'
        - $ref: '#/components/schemas/serviceItemExecutionServiceSeat'
    Order_ServiceItemExecutionAirLegCabin.v1:
      type: object
      description: 'This class contains attributes related to the delivery of an air service item, which are not an instrinsic characteristic of a LegCabin product, but depend on its particular instantiation in the scope of an offer.'
      properties:
        itineraryOrder:
          type: integer
          description: The order/rank of the leg at itinerary level
        bookingClass:
          type: string
          description: 'Booking classe is a marketing partition of the airline and is designated by a single letter code: F, J, C, Y, R. Price/fare rules are associated to this class, which leads to price variation over the time. Primary RBD (ATPCo standard notation), Booked Class, BKC (Amadeus standard notation) '
      x-examples:
        ex_ExecutionAirLegCabin:
          serviceItemExecution:
            bookingClass: K
    OrderServiceFulfillmentDetails:
      type: object
      title: OrderServiceFulfillmentDetails
      description: 'Expected information involving fulfilment status, stakeholders and delivered products.'
      properties:
        status:
          $ref: '#/components/schemas/Order_ServiceFulfillment_ServiceStatus'
        stakeholders:
          type: array
          description: 'Person, group or organization involved in the Order.'
          items:
            $ref: '#/components/schemas/OrderServiceFulfillmentStakeholder'
        product:
          $ref: '#/components/schemas/OrderServiceFulfillmentPoductDetails'
    Order_ServiceFulfillment_ServiceStatus:
      enum:
        - NOT_STARTED
        - BOOK_SUBMITTED
        - BOOK_ACCEPTED
        - BOOK_REJECTED
        - READY_TO_DELIVER
        - DELIVERED
        - CANCELLED
        - BOOK_WAITLISTED
        - CONTROLLED_BY_DELIVERY
        - IN_PROGRESS
        - INFORMED
      title: Order_ServiceFulfillment_ServiceStatus
      description: |-
        Fulfillment status for service item.
        - NOT_STARTED : When Fulfillment is created from Order based flow  
        - BOOK_SUBMITTED :  When booking request is sent to airline inventory system
        - BOOK_ACCEPTED : When booking request has been accepted by airline inventory system
        - BOOK_REJECTED : When booking request has been rejected by airline inventory system
        - READY_TO_DELIVER : When the fulfillment is in a situation allowing the provider of this service liability to start the delivery
        - DELIVERED : When the fulfillment has been delivered - Status received from airline delivery system
        - CANCELLED : When the cancellation request has been accepted by the airline inventory system
        - BOOK_WAITLISTED : When the booking is waitlisted
        - CONTROLLED_BY_DELIVERY : When the fulfillment is being managed by the delivery system
        - IN_PROGRESS : When the fulfillment is being delivered
        - INFORMED : When the fulfillment is being managed by the service retailer
    OrderServiceFulfillmentMeasure:
      type: object
      title: OrderServiceFulfillmentMeasure
      description: Current status and specifics regarding the service delivery.
      properties:
        consumptionDateTime:
          type: string
          description: 'The UTC date-time by when that particular service-related Fulfillment was moved to a final status (delivered, cancelled, rejected...), if any.'
          format: date-time
        externalRecordReferences:
          type: array
          items:
            $ref: '#/components/schemas/OrderExternalRecordReference'
          definition: Reference information to the related external records.
      allOf:
        - $ref: '#/components/schemas/OrderServiceFulfillmentDetails'
    OrderExternalRecordReference:
      type: object
      title: OrderExternalRecordReference
      description: Reference information to the related external records.
      properties:
        parent:
          $ref: '#/components/schemas/OrderExternalResourceReference'
        ref:
          type: string
          description: |-
            JSON pointer reference for the external record data that is local to the resource linked in parent.
            e.g. OfferItem within an Offer
          format: json-pointer
        referenceType:
          type: string
          description: Reference type for external records.
      discriminator: referenceType
    OrderFulfillmentConvergence:
      type: object
      title: OrderFulfillmentConvergence
      description: Information specifying the convergence status and a set of error details.
      properties:
        status:
          $ref: '#/components/schemas/OrderFulfillmentConvergenceStatus'
        errorDetails:
          $ref: '#/components/schemas/Errors'
    OrderFulfillmentConvergenceStatus:
      enum:
        - NOT_STARTED
        - ONGOING
        - CONVERGED
        - NOT_CONVERGED
      title: OrderFulfillmentConvergenceStatus
      description: Status of the effort made to align Order content with external systems (e.g. PSS).
    Errors:
      type: object
      title: Errors
      description: A set of errors
      properties:
        errors:
          type: array
          items:
            $ref: '#/components/schemas/Error'
    Error:
      type: object
      title: Error
      description: The Error Definition
      properties:
        status:
          type: integer
          description: 'The [HTTP status code](https://www.iana.org/assignments/http-status-codes/http-status-codes.xhtml) of this response. This is present only in terminal errors which cause an unsuccessful response. In the case of multiple errors, they must all have the same status.'
        code:
          type: integer
          description: 'A machine-readable error code, that will enable the API Consumers code to handle this type of error'
        title:
          type: string
          description: 'An error title from the Canned Messages table with a 1:1 correspondence to the error code. This may be localized'
        detail:
          type: string
          description: 'An easy-to-read explanation specific to this occurrence of the problem. It should give the API consumer an idea of what went wrong and how to recover from it. Like the title, this field’s value can be localized.'
        source:
          $ref: '#/components/schemas/Error_Source'
        documentation:
          type: string
          description: A link to a web page or file with further documentation to help the API consumer resolve this error
          format: url
    Error_Source:
      type: object
      title: Error_Source
      properties:
        parameter:
          type: string
          description: The key of the URI path or query parameter that caused the error
        pointer:
          type: string
          description: 'A JSON Pointer [RFC6901] to the associated entity in the request body that caused this error'
        example:
          type: string
          description: A sample input to guide the user when resolving this issue
    OrderFulfillment:
      type: object
      title: OrderFulfillment
      properties:
        id:
          type: string
          description: Id of the Fulfillment.
        lifecycle:
          $ref: '#/components/schemas/OrderOrderLifecycle'
        type:
          type: string
          description: Type for the Fulfillment (Service/Payment).
      required:
        - id
        - lifecycle
      discriminator: type
    OrderPaymentFulfillment:
      type: object
      title: OrderPaymentFulfillment
      description: |-
        Payment fulfillment items are categorized into four groups: 
          - context, linking the item with the fulfillments it originates from 
          - expectation, indicating payment fulfillment details targeted by Order native touchpoint
          - measure, mirroring the payment  fulfillment details as it appears in an external system synchronized through smartbridge (e.g. PSS   information)
          - convergence status, highlighting the Order Management system achievement on aligning measurement to expectation.
      properties:
        context:
          $ref: '#/components/schemas/OrderPaymentFulfillmentContext'
        expectation:
          $ref: '#/components/schemas/OrderPaymentFulfillmentExpectation'
        measure:
          $ref: '#/components/schemas/OrderPaymentFulfillmentMeasure'
        convergence:
          $ref: '#/components/schemas/OrderFulfillmentConvergence'
      allOf:
        - $ref: '#/components/schemas/OrderFulfillment'
    OrderPaymentFulfillmentContext:
      type: object
      title: OrderPaymentFulfillmentContext
      description: Reference information for the related Payment fulfillment item.
      properties:
        orderItemReferences:
          type: array
          description: Reference information for the related Order item.
          items:
            $ref: '#/components/schemas/OrderPaymentFulfillmentOrderItemReference'
    OrderPaymentFulfillmentOrderItemReference:
      type: object
      title: OrderPaymentFulfillmentOrderItemReference
      description: Reference information for the related Order item.
      properties:
        orderItemPtr:
          $ref: '#/components/schemas/OrderInternalReference'
    OrderPaymentFulfillmentExpectation:
      type: object
      title: OrderPaymentFulfillmentExpectation
      description: Expected payment information.
      properties:
        totalAmount:
          $ref: '#/components/schemas/OrderElementaryPrice'
    OrderElementaryPrice:
      type: object
      title: OrderElementaryPrice
      description: Structure representing any kind of monetary value.
      properties:
        value:
          type: number
          description: Defines the monetary value without decimal position.
          examples:
            - 500050
        decimalPlaces:
          type: number
          description: Defines the number of decimal position of the monetary value.
          examples:
            - 2
        currency:
          type: string
          description: Defines a specific monetary unit using ISO4217 currency code.
          examples:
            - CND
        elementaryPriceType:
          type: string
          description: 'Defines the type of price, eg. for base fare, total, grand total.'
          examples:
            - BASE_FARE
        currencyType:
          $ref: '#/components/schemas/OrderElementaryPriceCurrencyType'
    OrderElementaryPriceCurrencyType:
      enum:
        - MONETARY
        - LOYALTY
      title: OrderElementaryPriceCurrencyType
      description: 'Type of currency , either MONETARY or LOYALTY'
      x-enum-descriptions:
        - Currency MONETARY
        - Currency LOYALTY
    OrderPaymentFulfillmentMeasure:
      type: object
      title: OrderPaymentFulfillmentMeasure
      description: Payment details for the related Order retrieved from the payment orchestrator.
      properties:
        paymentDetails:
          type: array
          description: Attributes of the payment transaction.
          items:
            $ref: '#/components/schemas/OrderPaymentFulfillmentDetailsState'
    OrderPaymentFulfillmentDetailsState:
      type: object
      title: OrderPaymentFulfillmentDetailsState
      description: Details of an actual Payment done.
      properties:
        paymentRecordRef:
          $ref: '#/components/schemas/OrderExternalResourceReference'
        paymentType:
          $ref: '#/components/schemas/OrderPaymentFulfillmentPaymentType'
        amount:
          $ref: '#/components/schemas/OrderElementaryPrice'
        creditCardDetails:
          $ref: '#/components/schemas/OrderPaymentCard'
        paymentStatus:
          $ref: '#/components/schemas/OrderPaymentFulfillmentPaymentServerStatus'
    OrderPaymentFulfillmentPaymentType:
      enum:
        - CASH
        - CREDIT_CARD
        - MISCELLANEOUS
        - CHECK
        - GOVERNMENT_REQUEST
      title: OrderPaymentFulfillmentPaymentType
      description: Type of the payment method.
      x-enum-descriptions:
        - Fulfillment Payment Type CASH
        - Fulfillment Payment Type CREDIT_CARD
        - Fulfillment Payment Type MISCELLANEOUS
        - Fulfillment Payment Type CHECK
        - Fulfillment Payment Type GOVERNMENT_REQUEST
    OrderPaymentCard:
      type: object
      title: OrderPaymentCard
      description: Credit or Debits cards.
      properties:
        vendorCode:
          type: string
          description: '2-letter code designated for the card type. E.g. VI for VISA, MA for MasterCard.'
          maxLength: 30
          examples:
            - VI
        maskedCardNumber:
          type: string
          description: Masked card number.
        expiryDate:
          type: string
          description: Expiration date of the card formatted as "MMYY".
        tokenizedCardNumber:
          type: string
          description: Tokenized card number.
      x-examples:
        PaymentCard-VI-etvtst:
          paymentType: PaymentCard
          vendorCode: VI
          maskCardNumber: XXXXXXXXXXXX9999
          endValidityDateTime: '1221'
          holderName: etvtst
    OrderPaymentFulfillmentPaymentServerStatus:
      enum:
        - PAYMENT_PENDING
        - PAYMENT_AUTHORIZED
        - PAYMENT_NOT_AUTHORIZED
      title: OrderPaymentFulfillmentPaymentServerStatus
      description: Status of payment transaction.
      x-enum-descriptions:
        - Payment status from PaymentServer PAYMENT_PENDING
        - Payment status from PaymentServer PAYMENT_AUTHORIZED
        - Payment status from PaymentServer PAYMENT_NOT_AUTHORIZED
    GenericOpenDataChangeEvent:
      type: object
      title: GenericOpenDataChangeEvent
      description: |-
        Model to stream all data changes (creation/update/deletion) to the Global Event Mesh.
        The new version of the open data instance is inserted, along with the deltas with previous version.
        On top, we could add the associated business events generated at the time of this update.
      properties:
        currentImage:
          type: object
          description: Open data content of the event.
        changeLogs:
          type: array
          description: Log of all the changes occurred by the event.
          items:
            $ref: '#/components/schemas/GenericOpenDataChangeEventChangeLog'
        businessEvents:
          type: array
          description: Details for business event which can be sent in standalone or along with an Open Data change.
          items:
            $ref: '#/components/schemas/Generic%20business%20event'
    GenericOpenDataChangeEventChangeLog:
      type: object
      title: GenericOpenDataChangeEventChangeLog
      description: Logs of the open data changes that raised the event.
      oneOf:
        - $ref: '#/components/schemas/Event_HistoryChangeLog'
        - $ref: '#/components/schemas/EventLog_PatchOperation'
        - $ref: '#/components/schemas/EventLog_EntityChangeLog'
    Event_HistoryChangeLog:
      type: object
      title: Event_HistoryChangeLog
      description: Change log details
      allOf:
        - $ref: '#/components/schemas/EventLog_ChangeLog'
        - type: object
          description: History Changes
          properties:
            logType:
              type: string
              description: defines the type of log
              examples:
                - HistoryChangeLog
            id:
              type: string
              description: id of change log
              examples:
                - 64GqpZfbcAW61PtL-ACCC
            operation:
              enum:
                - add
                - remove
                - replace
                - move
                - copy
                - test
              description: type of change done
              examples:
                - add
            elementType:
              type: string
              description: type of element within which change has occurred
              examples:
                - LegDelivery
            elementId:
              type: string
              description: id of element within which change has occurred
              examples:
                - 2006EB8D0033D80E-FRA
            path:
              type: string
              description: 'A path indicating field in the original Entity, starting from id Type'
              examples:
                - /acceptanceDelivery/travelCabinCode
            description:
              type: string
              description: the description of the change
              examples:
                - ''
            value:
              type: object
              description: The new value that was set in the field
              examples:
                - J
            previousValue:
              type: object
              description: The previous value of the field
              examples:
                - O
    EventLog_ChangeLog:
      type: object
      title: EventLog_ChangeLog
      properties:
        logType:
          type: string
    EventLog_PatchOperation:
      type: object
      title: EventLog_PatchOperation
      description: 'A single JSON Patch operation as defined by RFC 6902 (see https://tools.ietf.org/html/rfc6902)'
      allOf:
        - $ref: '#/components/schemas/EventLog_ChangeLog'
        - type: object
          properties:
            op:
              enum:
                - add
                - remove
                - replace
                - move
                - copy
                - test
              description: The operation to be performed
            path:
              type: string
              description: 'A JSON Pointer as defined by RFC 6901 (see https://tools.ietf.org/html/rfc6901)'
            value:
              type: object
              description: The value to be used within the operations
            from:
              type: string
              description: 'A JSON Pointer as defined by RFC 6901 (see https://tools.ietf.org/html/rfc6901)'
          required:
            - op
            - path
    EventLog_EntityChangeLog:
      type: object
      title: EventLog_EntityChangeLog
      description: Before and After images
      properties:
        newValue:
          type: string
          description: Placeholder for a new object (full image)
        oldValue:
          type: string
          description: Placeholder for an old object (full image)
    Generic business event:
      type: object
      title: Generic business event
      description: 'Generic business event. It can be sent in standalone or along with an Open Data update. This is an alpha stage version, backward compatibility is not guaranteed.'
      properties:
        id:
          type: string
          description: Id of the business event.
        type:
          type: string
          description: Type of business event.
        area:
          type: string
          description: Area of the event
        domain:
          type: string
          description: Domain of the event.
        eventAttributes:
          type: object
          description: Optional attributes describing the event.
    PersonalDataPayload:
      title: Personal-Data Payload
      description: A payload describing the personal data belonging to a person.
      type: object
      properties:
        id:
          type: string
        lifecycle:
          $ref: '#/components/schemas/Lifecycle'
        loyaltyMemberships:
          type: array
          description: List of loyalty memberships
          items:
            $ref: '#/components/schemas/Membership'
        identityDocuments:
          type: array
          description: 'Identity documents'
          items:
            $ref: '#/components/schemas/IdentityDocument'
        placeOfBirth:
          type: string
          description: The individual's place of birth.
          example: London
        citizenships:
          type: array
          description: Citizenships of the individual, for instance European citizen, or a EU citizen.
          items:
            type: string
        nationalities:
          type: array
          description: Nationalities of the individual, for instance a Franco-British person who has dual nationality.
          items:
            type: string
        gender:
          type: string
          enum:
            - MALE
            - FEMALE
            - UNSPECIFIED
            - UNDISCLOSED
          title: Gender
          description: Gender for individual
        dateOfBirth:
          type: string
          description: The individual's date of birth.
          format: date
          example: '1978-10-05'
        contacts:
          type: array
          description: List of contact information.
          items:
            $ref: '#/components/schemas/Contact'
        names:
          type: array
          description: Names of the individual.
          minItems: 1
          maxItems: 5
          items:
            $ref: '#/components/schemas/Name'
        onlineIdentifiers:
          type: array
          items:
            $ref: '#/components/schemas/OnlineIdentifierItem'
      x-examples:
        id: '3b305744-8ecc-4863-b5f1-6831b3fcc7bb'
        lifecycle:
          dataVersion: '2'
        loyaltyMemberships:
          - membershipId: '*********'
            program:
              owner: 6X
        identityDocuments:
          - documentType: PASSPORT
            number: 'passportnumber'
            issuanceDate: '2019-08-24'
            expiryDate: '2019-08-24'
            effectiveDate: '2019-08-24'
            issuanceCountry: 'EN'
            issuanceLocation: 'London'
            nationality: 'EN'
            gender: FEMALE
            name:
              firstName: 'GINA'
              lastName: 'BLUE'
            birthDate: '1998-08-24'
            birthPlace: 'London'
        placeOfBirth: 'London'
        citizenships:
          - 'EN'
        nationalities:
          - 'EN'
        gender: FEMALE
        dateOfBirth: '1998-08-24'
        contacts:
          - phone:
              category: BUSINESS
              deviceType: MOBILE
              countryCode: FR
              countryCallingCode: '+33'
              areaCode: ' area code for Montreal is 514 or 438'
              number: '0336123123'
              text: (+33)6123123
            address:
              category: BUSINESS
              lines:
                - 132 RUE DU FOUR
              postalCode: 75006
              countryCode: 'FR'
              cityName: 'Paris'
              stateCode: string
              postalBox: string
              state: Florida
            email:
              category: BUSINESS
              address: <EMAIL>
              emailAddressType: EMAIL_ID
              purpose:
                - STANDARD
              locationType: HOME
        names:
          - firstName: 'GINA'
            lastName: 'BLUE'
            title: Mrs
            maidenName: string
            middleName: string
            fullName: 'GINA BLUE'

    Lifecycle:
      type: object
      title: Lifecycle
      description: Life Cycle attributes of the Personal Data
      properties:
        dataVersion:
          description: Version of the data in the payload
          type: string
    Membership:
      type: object
      title: Membership
      description: A membership represents a contract between an entity (individual, charity) and a loyalty program, which allows the entity to benefit from this program.
      properties:
        id:
          type: string
        membershipId:
          type: string
          description: Identifier of the membership within the program.
          example: '*********'
        program:
          type: object
          description: Program to which the membership is affiliated.
          properties:
            owner:
              type: string
              description: Owner of the program
      x-tags:
        - Membership
        - Loyalty
    IdentityDocument:
      type: object
      title: IdentityDocument
      description: The information that are found on an ID document.
      properties:
        id:
          type: string
        documentType:
          type: string
          enum:
            - VISA
            - PASSPORT
            - DRIVER_LICENSE
            - IDENTITY_CARD
            - RESIDENCE_PERMIT
            - VOTER_ID_CARD
            - WORK_PERMIT
            - MILITARY_ID_CARD
            - AIR_ATTENDANCE_LICENCE
            - FLIGHT_MECHANICAL_LICENCE
            - OPERATIONAL_DISPATCHER_LICENCE
            - LOYAL_ATTORNEY_IDENTIFICATION
            - FOREIGN_NATIONAL_REGISTRATION
            - KNOWN_TRAVELER
            - PILOT_LICENCE
            - NEXUS_CARD
            - APPROVED_NON_STANDARD_ID
            - TWN_RESIDENTS_TRAVEL_PERMIT_TO_FROM_CHN
            - CHN_RESIDENTS_TRAVEL_PERMIT_TO_FROM_TWN_AND_CHN
            - EXIT_AND_ENTRY_PERMIT_TWN
            - TRAVEL_PERMIT_TO_FROM_HKG_MAC_PUBLIC_AFFAIRS
            - HOME_RE_ENTRY_PERMIT_HUI_XIANG_ZHENG
            - BORDER_CROSSING_CARD
            - BIRTH_CERTIFICATE
            - ALIEN_PERMIT
            - CHN_RESIDENT_TRAVEL_PERMIT_TO_FROM_HKG_AND_MAC
            - RE_ENTRY_PERMIT
            - SEAFARERS_PASSPORT_SEAMEN
            - REDRESS
            - CHN_RESIDENT_TRAVEL_PERMIT_TO_HKG_AND_MAC
            - CHN_TRAVEL_PERMIT
            - CHN_EXIT_AND_ENTRY_PERMIT
            - OTHER_DOCUMENT
            - US_NATURALISED
            - PASSPORT_CARD
            - APPROVED_DOCUMENT
            - CREW_MEMBER_CERTIFICATE
            - SOCIAL_SECURITY_CARD
            - REFUGEE_REENTRY_PERMIT
            - TAXPAYER_IDENTIFICATION_NUMBER
          title: IdentityDocument_DocumentType
          description: The nature/type of the document.
          example: PASSPORT
        number:
          type: string
          description: The document number (shown on the document) . E.g. QFU514563221J
        issuanceDate:
          type: string
          description: Date at which the document has been issued (in ISO 8601 format).
          format: date
        expiryDate:
          type: string
          description: Date after which the document is not valid anymore (in ISO 8601 format).
          format: date
        effectiveDate:
          type: string
          description: Date at which the document starts to be valid (in ISO 8601 format). It may be different from the issuance date ("issuanceDate")
          format: date
        issuanceCountry:
          type: string
          description: ISO code (2-letters) of the country that issued the document.
          pattern: '[a-zA-Z]{2}'
        issuanceLocation:
          type: string
          description: 'A more precise information concerning the place where the document has been issued, when available. It may be a country, a state, a city or any other type of location. e.g. New-York'
        nationality:
          type: string
          description: ISO code (2-letters) of the nationality appearing on the document.
          pattern: '[a-zA-Z]{2}'
        gender:
          type: string
          enum:
            - MALE
            - FEMALE
            - UNKNOWN
          description: Gender as it appears on the document
        name:
          type: object
          title: IdentityDocument_Name
          description: Description of the name of a physical person
          properties:
            firstName:
              type: string
              description: First name.
              x-risk-personal-data-field: PASSENGER_NAME
            lastName:
              type: string
              description: Last name.
              x-risk-personal-data-field: PASSENGER_NAME
          x-tags:
            - Name
            - PII
        birthDate:
          type: string
          description: 'Birth date in ISO 8601 format. Example "2000-10-20".\ '
          format: date
        birthPlace:
          type: string
          description: Birth place as indicated on the document
    Contact:
      type: object
      title: Contact
      description: 'A contact refers to the information that can be used to reach a person, a company or an organization.'
      properties:
        id:
          type: string
        phone:
          type: object
          title: Phone
          description: Phone information.
          properties:
            category:
              type: string
              enum:
                - BUSINESS
                - PERSONAL
                - OTHER
              description: Category of the contact element
            addresseeName:
              type: string
              description: Adressee name (e.g. in case of emergency purpose it corresponds to name of the person to be contacted).
              pattern: '[a-zA-Z -]'
              x-risk-personal-data-field: PASSENGER_NAME
            deviceType:
              type: string
              enum:
                - MOBILE
                - LANDLINE
                - FAX
              description: 'Type of the device (Landline, Mobile or Fax)'
            countryCode:
              type: string
              description: Country code of the country (ISO3166-1). E.g. "US" for the United States
              pattern: '[A-Z]{2}'
              x-risk-personal-data-field: PHONE_NUMBER
              example: FR
            countryCallingCode:
              type: string
              description: 'Country calling code of the phone number, as defined by the International Communication Union. Examples - "1" for US, "371" for Latvia.'
              pattern: '[0-9+]{2,5}'
              x-risk-personal-data-field: PHONE_NUMBER
              example: '+33'
            areaCode:
              type: string
              description: Corresponds to a regional code or a city code. The length of the field varies depending on the area.
              pattern: '[0-9]{1,4}'
              x-risk-personal-data-field: PHONE_NUMBER
              example: ' area code for Montreal is 514 or 438'
            number:
              type: string
              description: Phone number. Composed of digits only. The number of digits depends on the country.
              pattern: '[0-9]{1,15}'
              x-risk-personal-data-field: PHONE_NUMBER
              example: '0336123123'
            text:
              type: string
              description: String containing the full phone number - applicable only when a structured phone (i.e. countryCallingCode + number) is not provided
              x-risk-personal-data-field: PHONE_NUMBER
              example: (+33)6123123
          x-tags:
            - phone
            - contact
            - address
            - PII
        address:
          type: object
          title: Address
          description: Address information
          properties:
            category:
              type: string
              enum:
                - BUSINESS
                - PERSONAL
                - OTHER
              description: Category of the contact element
            lines:
              type: array
              description: 'Line 1 = Street address, Line 2 = Apartment, suite, unit, building, floor, etc'
              items:
                type: string
              x-risk-personal-data-field: ADDRESS
            postalCode:
              type: string
              description: 'Example: 74130'
              x-risk-personal-data-field: ADDRESS
            countryCode:
              type: string
              description: ISO 3166-1 country code
              pattern: '[a-zA-Z]{2}'
              x-risk-personal-data-field: ADDRESS
            cityName:
              type: string
              description: 'Full city name. Example: Dublin'
              pattern: '[a-zA-Z -]{1,35}'
              x-risk-personal-data-field: ADDRESS
            stateCode:
              type: string
              description: State code (two character standard IATA state code)
              pattern: '[a-zA-Z0-9]{1,2}'
              x-risk-personal-data-field: ADDRESS
            postalBox:
              type: string
              description: E.g. BP 220
              x-risk-personal-data-field: ADDRESS
            text:
              type: string
              description: 'Field containing a full unformatted address. Only applicable when the fields lines, postalCode, countryCode, cityName are not filled.'
              x-risk-personal-data-field: ADDRESS
            state:
              type: string
              description: State, province or country name
              x-risk-personal-data-field: ADDRESS
              example: Florida
          x-tags:
            - address
            - postal
            - PII
        email:
          type: object
          title: Email
          description: Email information.
          properties:
            category:
              type: string
              enum:
                - BUSINESS
                - PERSONAL
                - OTHER
              description: Category of the contact element
            address:
              type: string
              description: Email address (e.g. <EMAIL>)
              x-risk-personal-data-field: 'EMAIL_ADDRESS,PASSENGER_NAME'
            emailAddressType:
              type: string
              enum:
                - EMAIL_ID
                - DISTRIBUTION_LIST
                - ALIAS
                - GROUP
              description: |-
                EmailAddressingType defines the format of Email Address. 
                EMAIL_ID (default) - Single Email Address.
                ex: <EMAIL>
                DISTRIBUTION_LIST - Similar to Email Mailing List - refers to a list of email addresses.
                Alias - A Nickname to Email Address or list of Emails. Do not follow Email Format. (Refer DLIST ABR Rule). RFC 5321  
                ex: SUPPAX
                Group - A group has an email address and whenever an email is sent to that address everyone in the group receives the email. 
                ex:   "<EMAIL>"
              default: EMAIL_ID
              example: EMAIL_ID
          x-examples:
            Email for a team:
              category: BUSINESS
              address: <EMAIL>
              emailAddresstype: GROUP
            Alias:
              category: BUSINESS
              address: suppax
              emailAddresstype: ALIAS
          x-tags:
            - phone
            - contact
            - address
            - PII
        language:
          type: string
          description: the preferred language of communication with this Contact
        purpose:
          type: array
          description: the purpose for which this contact is to be used
          items:
            type: string
            enum:
              - STANDARD
              - NOTIFICATION
              - EMERGENCY
              - MAILING
              - REGULATORY
              - BILLING
              - INFORMATION
              - SECURITY_IDENTIFY_CHALLENGE
              - REFERENCE_FOR_VERIFICATION
        isThirdParty:
          type: boolean
          description: 'If set, this flag indicates that the contact belongs to an other person than the passenger (e.g. friend or family member not part of the trip). This option is only available for mobile phone and email and for a notification purpose.'
        locationType:
          type: string
          enum:
            - HOME
            - DESTINATION
          description: Location "Contact function"
      x-examples:
        Several contacts for different Purpose:
          contacts:
            - email:
                address: <EMAIL>
                category: BUSINESS
              purpose: NOTIFICATION
            - email:
                address: <EMAIL>
                category: PERSONAL
              purpose: STANDARD
            - phone:
                number: '231366119911'
              purpose: STANDARD
      x-tags:
        - contact
        - phone
        - email
        - address
        - message
        - PII
    Name:
      type: object
      title: Name
      description: Description of the name of a physical person
      properties:
        id:
          type: string
        firstName:
          type: string
          description: First name.
          x-risk-personal-data-field: PASSENGER_NAME
        lastName:
          type: string
          description: Last name.
          x-risk-personal-data-field: PASSENGER_NAME
        title:
          type: string
          description: 'Contains all the suffixes and prefixes that can be appended to a name - Mr, Miss, Pr. - E.g. " Mr".'
          x-risk-personal-data-field: PASSENGER_TITLE
        maidenName:
          type: string
          description: The name given at birth time and that may have changed after a marriage.
          x-risk-personal-data-field: PASSENGER_NAME
        middleName:
          type: string
          description: 'Middle name(s), for example "Lee" in "John Lee Smith".'
          x-risk-personal-data-field: PASSENGER_NAME
        nameType:
          enum:
            - UNIVERSAL
            - NATIVE
            - ROMANIZATION
            - NATIVE_ROMANIZATION_ASCII
            - NATIVE_ROMANIZATION_ASCII_EXTENDED
            - NATIVE_ROMANIZABLE
            - NATIVE_NON_ROMANIZABLE
          description: 'the type of the reference name - When several name entities exist for a given Name element(e.g. Universal name, both Native names, Romanized name), the notion of reference name (i.e. active or main name) exists. It can be either the Universal name or the Native name/Phonetic name.'
        fullName:
          type: string
          description: 'free flow ,  Concatenation of first/mid/last. No order, no restriction, no pattern, blank separator ....'
          x-risk-personal-data-field: PASSENGER_NAME
        isPreferred:
          type: boolean
          description: Indicating this "Name item" should be used for communication purpose.
      x-tags:
        - Name
        - PII
    OnlineIdentifierItem:
      type: object
      description: 'Online identifiers are Personally Identifiable Information such as devices, applications, tools and protocols  (internet protocol addresses), cookie identifiers or radio frequency identification tags.'
      properties:
        id:
          type: string
        ipAddress:
          type: string
          description: 'Value of the IP (Internet Protocol) address, versions 4 or 6.'
          example: '***********'
