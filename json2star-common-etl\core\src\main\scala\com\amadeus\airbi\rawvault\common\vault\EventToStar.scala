package com.amadeus.airbi.rawvault.common.vault

import com.amadeus.airbi.json2star.common.extdata.ExtData
import com.amadeus.airbi.rawvault.common.processors.JsonProcessor
import com.amadeus.airbi.rawvault.common.vault.generators.TableGenerator
import com.amadeus.airbi.json2star.common.addons.base.mapping.MappingPipeline.TableMapping
import com.amadeus.airbi.json2star.common.addons.base.mapping.Table
import com.jayway.jsonpath.DocumentContext
import org.apache.spark.sql.Row

import java.sql.Timestamp
import java.time.Instant
import java.time.format.DateTimeFormatter
import scala.language.postfixOps
import scala.util.Try

object EventToStar {

  val StandardDateTimeFormat =
    DateTimeFormatter.ISO_INSTANT

  /** Explode the record into multiple tables
    *
    * @param recordId the id of the record (for debugging purposes)
    * @param record the row containing the json payload to process
    * @param tableConfs the model used to extract data from the json payload
    * @param filter the filter defining if this record is eligible or has to be discarded
    * @return an attempt containing all the tables generated from the record, or no value if the record is not eligible as per the filter
    */
  def transform(
    recordId: String,
    record: Row,
    tableConfs: List[TableMapping],
    extData: ExtData,
    filter: Option[String]
  ): Try[Option[Seq[Table]]] = Try {
    val (ld, jsonDocument) = extractMessage(record)
    val loadDateAsString = StandardDateTimeFormat.format(ld)
    val mustProcessRecord = filter.map(JsonProcessor.isEligible(jsonDocument, _)).getOrElse(true)
    if (mustProcessRecord) {
      Some(tableConfs.map(TableGenerator.createTable(recordId, loadDateAsString, _, jsonDocument, extData)))
    } else {
      None
    }
  }

  def extractMessage(
    record: Row
  ): (Instant, DocumentContext) = {

    val jsonDocument =
      JsonProcessor.parseJsonString(
        record.getAs[String]("BODY"),
        JsonProcessor.SINGLE_VALUE_CONFIGURATION
      )
    (record.getAs[Timestamp]("LOAD_DATE").toInstant, jsonDocument)
  }
}
