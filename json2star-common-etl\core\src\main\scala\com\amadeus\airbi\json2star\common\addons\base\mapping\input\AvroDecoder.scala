package com.amadeus.airbi.json2star.common.addons.base.mapping.input

import InputFormat._
import org.apache.spark.sql.functions.{col, concat_ws, udf}
import org.apache.spark.sql.types._
import org.apache.spark.sql.{<PERSON><PERSON><PERSON>e, Row}

import java.io._
import java.sql.Timestamp
import java.time.Instant
import java.time.format.DateTimeFormatter
import java.util.zip.GZIPInputStream

/** From https://docs.microsoft.com/en-us/azure/event-hubs/event-hubs-capture-overview
  * {
  * "type":"record",
  * "name":"EventData",
  * "namespace":"Microsoft.ServiceBus.Messaging",
  * "fields":[
  * {"name":"SequenceNumber","type":"long"},
  * {"name":"Offset","type":"string"},
  * {"name":"EnqueuedTimeUtc","type":"string"},
  * {"name":"SystemProperties","type":{"type":"map","values":["long","double","string","bytes"]}},
  * {"name":"Properties","type":{"type":"map","values":["long","double","string","bytes"]}},
  * {"name":"Body","type":["null","bytes"]}
  * ]
  * }
  */

object AvroDecoder {

  val SystemEnqueuedTimePropertyName = "x-opt-enqueued-time"
  val LONG_MEMBER = "member0"

  val AZURE_EVENTHUB_SEQUENCE_NUMBER = "sequenceNumber"
  val AZURE_EVENTHUB_OFFSET = "offset"
  val AZURE_EVENTHUB_BODY = "body"
  val AZURE_EVENTHUB_PROPERTIES = "properties"
  val AZURE_EVENTHUB_SYSTEM_PROPERTIES = "systemProperties"
  val AZURE_EVENTHUB_ENQUEUED_TIME_UTC = "enqueuedTimeUtc"

  object FieldsName extends Enumeration {
    type FieldsName = Value
    val SequenceNumber, Offset, EnqueuedTimeUtc, SystemProperties, Properties, Body = Value
  }

  val AzureCaptureDateTimeFormat =
    DateTimeFormatter
      .ofPattern("M/d/yyyy h:mm:ss a") // 4/19/2021 9:43:03 AM
      .withZone(java.time.ZoneOffset.UTC)

  val udfEnqueuedTimeParser = udf(enqueuedTimeParser(_, _))

  def enqueuedTimeParser(enqueuedTimeUtc: String, sysProperties: Map[String, Row]): Timestamp = {
    Timestamp.from(
      sysProperties
        .get(SystemEnqueuedTimePropertyName)
        .map(_.getAs[Long](LONG_MEMBER))
        .map(ms => Instant.ofEpochMilli(ms))
        .getOrElse(Instant.from(AzureCaptureDateTimeFormat.parse(enqueuedTimeUtc)))
    )
  }

  val udfUncompress = udf(decodeBody _)

  def decodeBody(body: Array[Byte]): String = {
    if (isGzipCompressed(body)) {
      gzipUncompress(body)
    } else {
      new String(body, "UTF-8")
    }
  }

  def readBody(record: Row): String = {
    decodeBody( record.getAs[Array[Byte]]("Body"))
  }

  def isGzipCompressed(bytes: Array[Byte]): Boolean =
    bytes != null && bytes.length >= 2 && // NOSONAR GZIP magic bytes count
      {
        val header: Int = (bytes(1) & 0xff) << 8 | (bytes(0) & 0xff); // NOSONAR GZIP magic bytes operations
        header == GZIPInputStream.GZIP_MAGIC;
      }

  def gzipUncompress(compressedData: Array[Byte]): String = {
    val bis = new ByteArrayInputStream(compressedData)
    try {
      //scala.io.Source.fromInputStream(new GZIPInputStream(bis)).mkString
      val bufreader = new BufferedReader(new InputStreamReader(new GZIPInputStream(bis), "UTF-8"))
      import scala.language.postfixOps
      Iterator continually bufreader.readLine takeWhile (_ != null) mkString
    } catch {
      case e: IOException =>
        throw new UncheckedIOException("Failed to uncompress input message", e)
    } finally {
      bis.close()
    }
  }

}

trait AvroDecoder extends InputFormat {
  import AvroDecoder._

  val avroSchema: StructType = {
    val unionType = StructType(
      Array(
        StructField("member0", LongType),
        StructField("member1", DoubleType),
        StructField("member2", StringType),
        StructField("member3", BinaryType)
      )
    )

    val fields =
      Array(
        StructField(AZURE_EVENTHUB_SEQUENCE_NUMBER, LongType, nullable = true),
        StructField(AZURE_EVENTHUB_OFFSET, StringType, nullable = true),
        StructField(AZURE_EVENTHUB_ENQUEUED_TIME_UTC, StringType, nullable = true),
        StructField(
          AZURE_EVENTHUB_SYSTEM_PROPERTIES,
          MapType(StringType, unionType, valueContainsNull = false),
          nullable = true
        ),
        StructField(
          AZURE_EVENTHUB_PROPERTIES,
          MapType(StringType, unionType, valueContainsNull = false),
          nullable = true
        ),
        StructField(AZURE_EVENTHUB_BODY, BinaryType, nullable = true)
      )

    StructType(fields)
  }

  implicit class _Adapter_( //NOSONAR
    df: DataFrame
  ) {
    val PAYLOAD_NAME = "bodyAsBinary"

    def withMetadata(): DataFrame = {
      df.withColumnRenamed(AZURE_EVENTHUB_BODY, PAYLOAD_NAME)
        .withColumn(
          LOAD_DATE,
          udfEnqueuedTimeParser(col(AZURE_EVENTHUB_ENQUEUED_TIME_UTC), col(AZURE_EVENTHUB_SYSTEM_PROPERTIES))
        )
        .withColumn(RECORD_ID, concat_ws("&", col(AZURE_EVENTHUB_SEQUENCE_NUMBER), col(AZURE_EVENTHUB_OFFSET)))
        .withColumn(BODY, udfUncompress(col(PAYLOAD_NAME)))
    }
  }
}
