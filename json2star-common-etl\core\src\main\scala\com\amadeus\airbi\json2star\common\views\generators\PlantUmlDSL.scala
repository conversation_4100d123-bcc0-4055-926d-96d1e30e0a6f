package com.amadeus.airbi.json2star.common.views.generators

/**
  * Utility object containing case classes representing various Plant UML document elements allowing to
  * programmatically build a Plant UML input document and render it into a string
  */
object PlantUmlDSL {
  trait Element {
    def render(): String
  }
  case class Document(title: String, header: String, frames: List[Frame], freeEntities: List[Entity],
                      relationships: List[Relationship]) extends Element {
    def render(): String = {
      s"""@startuml $title
         |
         |' Header
         |$header
         |
         |' Frames
         |${frames.map(_.render()).mkString("\n\n")}
         |
         |' Free entities
         |${freeEntities.map(_.render()).mkString("\n\n")}
         |
         |' Relationships
         |${relationships.map(_.render()).mkString("\n")}
         |
         |@enduml""".stripMargin
    }
  }

  case class Frame(name: String, stereotype: Option[String], color: String, entities: List[Entity]) extends Element {
    def render(): String = {
      s"""frame "$name"${renderStereotype(stereotype)} $color {
         |
         |${entities.map(_.render()).mkString("\n\n")}
         |}""".stripMargin
    }
    /**
      * Returns a new Frame with the same attributes as this one except that the entities are filtered with the specified predicate
      *
      * @param predicate an entity predicate
      * @return an optional new Frame or None if all the entities were filtered out
      */
    def filterEntities(predicate: Entity => Boolean): Option[Frame] = {
      val filteredEntities = entities.filter(predicate)
      filteredEntities match {
        case l if l.length > 0 => Some(Frame(name, stereotype, color, filteredEntities))
        case _ => None
      }
    }
  }

  case class Entity(schema: String, name: String, stereotype: Option[String], columns: List[Column]) extends Element {
    def render(): String = {
      s"""entity "$name"${renderStereotype(stereotype)} {
         |${columns.map(_.render()).mkString("\n")}
         |}""".stripMargin
    }
  }

  trait Key {
  }
  object PrimaryKey extends Key {
    override def toString(): String = { "PK" }
  }
  case class ForeignKey(targetSchema: Option[String], targetSubdomain: Option[String],
                        targetTable: String, targetColumn: String) extends Key {
    override def toString(): String = { "FK" }
  }

  case class Column(name: String, colType: String, isMandatory: Boolean, key: Option[Key],
                    maxNameLength: Int, maxTypeLength: Int) extends Element {
    def render(): String = {
      def prefix(prefix: Option[String]): String = prefix.map(_ + ".").getOrElse("")
      val renderedName = key match {
        case Some(ForeignKey(targetSchema, targetSubdomain, targetTable, _)) =>
          s"[[#{${prefix(targetSchema)}${prefix(targetSubdomain)}$targetTable} $name]]"
        case _ => name
      }
      // Local case class to store rendered column attributes
      case class Attribute(value: String, maxLength: Int, displayedLength: Option[Int] = None)
      val nameAttr = Attribute(renderedName, maxNameLength, Some(name.length))
      val typeAttr = Attribute(colType, maxTypeLength)
      val notNullAttr = Attribute(if (isMandatory) "NN" else "", 2)
      val keyAttr = Attribute(key.map(_.toString).getOrElse(""), 2)

      val line = List(nameAttr, typeAttr, notNullAttr, keyAttr)
        .map(a => {
          val paddedLength = a.displayedLength match {
            // Adjusts the padding if there is a difference between the displayed length and the actual length of the attribute
            // (it is the case for column names with links)
            case Some(l) => a.maxLength + (a.value.length - l)
            case _ => a.maxLength
          }
          a.value padTo (paddedLength, ' ')
        })
        .mkString("  ")
      key match {
        case Some(PrimaryKey) => "$pk(\"" + line + "\")"
        case Some(ForeignKey(_, _, _, _)) =>  "$fk(\"" + line + "\")"
        case _ => line
      }
    }
  }

  case class Relationship(fromTable: String, fromColumn: Option[String],
                          toSchema: Option[String], toTable: String, toColumn: Option[String],
                          direction: Option[String] = None) extends Element {
    def render(): String = {
      val from = s"$fromTable${fromColumn.map("::" + _).getOrElse("")}"
      val arrow = s"-${direction.getOrElse("")}->"
      val to = s"${toSchema.map(_ + ".").getOrElse("")}$toTable${toColumn.map("::" + _).getOrElse("")}"
      s"$from $arrow $to"
    }
    def removeColumns(): Relationship = {
      Relationship(fromTable, None, toSchema, toTable, None, direction)
    }
    def setDirection(direction: String): Relationship = {
      Relationship(fromTable, fromColumn, toSchema, toTable, toColumn, Some(direction))
    }
    def setDirectionIf(predicate: Relationship => Boolean, direction: String): Relationship = {
      predicate(this) match {
        case true => Relationship(fromTable, fromColumn, toSchema, toTable, toColumn, Some(direction))
        case false => this
      }
    }
  }

  private def renderStereotype(stereotype: Option[String]): String = {
    stereotype.map("<<" + _ + ">>").getOrElse("")
  }
}
