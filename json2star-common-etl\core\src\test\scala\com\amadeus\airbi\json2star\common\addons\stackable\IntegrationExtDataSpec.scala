package com.amadeus.airbi.json2star.common.addons.stackable

import com.amadeus.airbi.json2star.common.app.Json2StarApp
import com.amadeus.airbi.json2star.common.app.Json2StarApp.readMappingConfig
import com.amadeus.airbi.rawvault.common.testfwk.Json2StarSpec

import java.io.File
import scala.reflect.io.Directory

class IntegrationExtDataSpec extends Json2StarSpec {

  val mappingFile = "datasets/external_data/simplified_dcsbag_with_external_data.conf"

  val TableNames: Seq[String] = getTableNames(mappingFile)

  override def beforeAll: Unit = {
    super.beforeAll
    createTables(mappingFile)
  }

  override def beforeEach: Unit = {
    //cleanTables(TableNames) no need, only one test
  }

  "IntegrationExtDataSpec" should "use external data in stackable addon closure" taggedAs (SlowTestTag) in {
    val dataDir = "datasets/external_data/data/"
    val pathExpectedResults = "src/test/resources/" + dataDir + "expected_results/"
    val rc = rootConfig(dataDir, inputDatabase, isLight = DefaultIsLight)

    val directory = new Directory(new File(rc.etl.stream.sink.checkpointLocation))
    directory.deleteRecursively()
    val mapping = readMappingConfig(mappingFile)
    Json2StarApp.run(spark, rc, mapping)
    checkTablesContent(TableNames, pathExpectedResults, model = Some(mapping))
    directory.deleteRecursively()
  }

}
