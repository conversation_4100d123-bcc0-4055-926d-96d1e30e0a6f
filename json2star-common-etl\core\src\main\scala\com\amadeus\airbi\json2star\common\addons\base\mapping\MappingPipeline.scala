package com.amadeus.airbi.json2star.common.addons.base.mapping

import com.amadeus.airbi.datalake.common.spark.MergeHelper.dedupBatchRecordsRowNumber
import com.amadeus.airbi.json2star.common.addons.base.AddonTable
import com.amadeus.airbi.json2star.common.addons.base.mapping.MappingMerge.{mergeWithStackableAddons, simpleMergeInsertOnly, smartMerge}
import com.amadeus.airbi.json2star.common.addons.base.mapping.PitLogic._
import com.amadeus.airbi.json2star.common.addons.base.mapping.TablesToDf.tablesToDFWithExpressions
import com.amadeus.airbi.json2star.common.addons.base.mapping.input.InputFormat._
import com.amadeus.airbi.json2star.common.addons.stackable.StackableAddonConfig
import com.amadeus.airbi.json2star.common.app.DeltaMergeStatus.{<PERSON><PERSON><PERSON><PERSON>, MergeStatus, logMergeStatuses}
import com.amadeus.airbi.json2star.common.app.Display.{display, displayDuration}
import com.amadeus.airbi.json2star.common.app.TemporaryTable.{dropDefaultDeltaTable, temporaryTableName, toDefaultDeltaTable}
import com.amadeus.airbi.json2star.common.app.{FatalJson2StarAppException, Metrics, PipelineContext, ProcessingContext}
import com.amadeus.airbi.json2star.common.extdata.ExtData
import com.amadeus.airbi.rawvault.common.RootConfig
import com.amadeus.airbi.rawvault.common.application.config._
import com.amadeus.airbi.rawvault.common.vault.EventToStar
import com.amadeus.airbi.rawvault.common.vault.spark.SchemaManager
import com.typesafe.scalalogging.Logger
import org.apache.spark.sql._
import org.apache.spark.sql.functions._
import org.apache.spark.sql.streaming.{OutputMode, StreamingQuery}
import org.apache.spark.sql.types.StructType
import org.json4s.DefaultFormats
import org.json4s.jackson.Serialization.{write => asJson}
import org.slf4j.LoggerFactory
import pureconfig.ConfigReader
import pureconfig.error.CannotConvert
import pureconfig.generic.auto._

import java.time.LocalDate
import java.time.format.DateTimeParseException
import scala.concurrent.duration.Duration
import scala.concurrent.{Await, Future}
import scala.language.{implicitConversions, postfixOps}
import scala.util.{Failure, Success}

class MappingPipeline private (
  pipelineContext: PipelineContext,
  recordsFilter: Option[String],
  val metrics: Metrics
) extends Serializable {

  import MappingPipeline._
  import pipelineContext.sparkSession.implicits._
  import pipelineContext.xc

  lazy val logger: Logger = Logger(LoggerFactory.getLogger(getClass.getName))

  // shortcuts from pipeline context
  val rootConfig: RootConfig = pipelineContext.rootConfig
  implicit val sparkSession: SparkSession = pipelineContext.sparkSession
  val processingContext: ProcessingContext = pipelineContext.processingContext
  val mc: MappingAddonParams = processingContext.processingParams.addonParamAs[MappingAddonParams]("mapping")
  logger.info(s"### MAPPING CONFIG: ${asJson(mc)(DefaultFormats)}")

  /** Mapping Pipeline Entry Point
    *
    * - It calls the mapping engine to extract new tables rows from raw data. (1)
    * - For each new batch, it merges the new input into delta tables, applying PIT and the stackable addons. (2)
    */
  def run(mappingConf: List[AddonTable[MappingConfig]]): Unit = {
    val s = mappingConf.zipWithIndex.map { case (t, i) =>
      TableMapping(t.table.name, t.addonConfig, t.stackableAddons, rootConfig, i + 1, mappingConf.size)
    }
    mc.asInputFormat(pipelineContext.databricksUtils, sparkSession).processBatches(
      streamReader = sparkSession.readStream.options(mc.streamOptions),
      paths = mc.inputPaths,
      checkpointLocation = rootConfig.etl.stream.sink.checkpointLocation,
      trigger = pipelineContext.trigger(),
      generateTables = getTables(_, s),
      processBatchFunction = (ds: Dataset[Table], id: String) => processBatch(rootConfig, mappingConf)(ds, id)
    )
  }

  /** Mapping engine entry point. (1)
    *
    * It reads the raw data and, based on a configuration, transforms it in a dataset of tables.
    */
  private def getTables(rawRecords: InputDataFrame, mappingConf: List[TableMapping]): Dataset[Table] = {
    val mapConfBroadcast = sparkSession.sparkContext.broadcast(mappingConf)
    // Load external data and broadcast it to be available in the mpping processing step (EventToStar.transform)
    val extDataBroadcast = sparkSession.sparkContext.broadcast(
      ExtData.load(mappingConf, mappingConf.head.rootConfig, sparkSession: SparkSession)
    )

    val rawRecordsRep =
      mc.positiveRawInputRepartition.map(rawRecords.repartition).getOrElse(rawRecords)
    val tables: Dataset[Table] = rawRecordsRep
      .flatMap { record =>
        val rId = record.getAs[String](RECORD_ID)
        val recordId = s"record:$rId"
        EventToStar.transform(recordId, record, mapConfBroadcast.value, extDataBroadcast.value, recordsFilter) match {
          case Success(Some(tables)) =>
            metrics.transformed.add(1L)
            if (mc.enableTableMetrics) {
              tables.foreach { t =>
                //Per table, add metrics key->value:
                //  TABLE_NAME:OK -> N      (number of rows generated correctly)
                metrics.tableRowCounts.add(s"${t.name}:OK" -> t.rows.size)
                // TABLE_NAME:KO:PKN -> N  (number of rows discarded because of Primary Key Null)
                metrics.tableRowCounts.add(s"${t.name}:KO:PKN" -> t.stats.rowDiscardedCosPrimaryKeyNull)
                // TABLE_NAME:KO:MCN -> N  (number of rows discarded because of Mandatory Column Null)
                metrics.tableRowCounts.add(s"${t.name}:KO:MCN" -> t.stats.rowDiscardedCosMandatoryCols)
              }
            }
            tables
          case Success(None) =>
            logger.trace(s"Record $recordId dropped because of eligibility")
            metrics.dropped.add(1L)
            Seq()
          case Failure(exception) =>
            logger.trace(s"Record $recordId dropped because of failure: ${exception.getMessage}")
            // TODO ideally, create a dataframe of errors to be saved somewhere else
            // Change log level to error as this log will be very helpful to investigate live feeds
            metrics.transformErrors.add(1L)
            exception match {
              case fatal: FatalJson2StarAppException => throw fatal
              case _ => Seq()
            }
        }
      }
    tables
  }

  /** Batch processing entry point. (2)
    *
    * It processes the dataset of tables created by the mapping engine
    * and applies PIT logic and the stackable addon enrichment.
    *
    * Orchestration logic runs the following three groups:
    * - PIT table
    * - NO PIT tables
    * - SECONDARY PIT tables
    * For each group, the actual merge method is called in parallel (inside a Future) for each table of the group
    */
  private[mapping] def processBatch(
    conf: RootConfig,
    mappingConf: List[AddonTable[MappingConfig]]
  )(dsRaw: Dataset[Table], batchId: String): Unit = {
    val displayFlag = rootConfig.processingParams.displayMainEvents
    display(s"\n### BATCH ${batchId}\n", conf, logger)

    displayDuration(s"\nBATCH ${batchId}" + _, logger, displayFlag) {
      // Generate the temporary raw table name here to be able to cleanup
      val tmpTableName = temporaryTableName("RAW")
      // Retrieve the master pit table config and instantiate the MasterPitLogic instance here to be able to cleanup
      val (pitMas, pitMasCf) = mappingConf
        .map(i => (i, i.addonConfig.pit))
        .collect { case (j, i: MasterPitTable) => (j, i) }
        .head // we assume only one pit master for now
      val masterTableMapping = TableMapping(
        pitMas.table.name,
        pitMas.addonConfig,
        pitMas.stackableAddons,
        conf,
        tableCount = 1,
        tableIndex = 0
      )
      val masterPitLogic =
        new MasterPitLogic(masterTableMapping, pitMasCf.master, pitMas.table.name, pitMas.addonConfig.merge)

      try {
        val (ds, dsCount, newRecordsEstimation) = displayDuration(s"CACHE and COUNT" + _, logger, displayFlag) {
          val dsTable = if (mc.enableRawDeltaPersist) {
            toDefaultDeltaTable(dsRaw.toDF(), tmpTableName).as[Table]
          } else {
            dsRaw.cache()
          }
          sparkSession.sparkContext.setJobGroup(
            s"BATCH=$batchId",
            s"TYPE=RECORD-COUNT"
          )
          val dsCount = dsTable.count()
          val newRecordsEstimation = dsCount / math.max(mappingConf.size, 1)
          display(s"Cumulative number of tables touched by all the records: ${dsCount}", conf, logger)
          display(s"Estimated number of new records: ${newRecordsEstimation}", conf, logger)
          (dsTable, dsCount, newRecordsEstimation)
        }

        if (dsCount > 0) {
          // MASTER PIT TABLE
          val (distinctPitKeysInBatch, pittedMasterRows) =
            displayDuration("MASTER TABLE" + _, logger, displayFlag) {
              val masterMerge = mergeTablePitMaster(
                cfg = TableMapping(pitMas.table.name, pitMas.addonConfig, pitMas.stackableAddons, conf, 0, 1),
                batchId = batchId,
                microBatchDS = ds,
                pitLogic = masterPitLogic,
                newRecordsEstimation
              )
              val (masterStatus, distinctPitKeysInBatch, pittedMasterRows) = Await.result(masterMerge, Duration.Inf)
              val mergeFailure = logMergeStatuses(Seq(masterStatus), logger)
              mergeFailure.foreach { exception =>
                logger.warn(s"Master merge failure")
                throw exception
              }
              display(s"MASTER TABLE | Num: 1", conf, logger)
              (distinctPitKeysInBatch, pittedMasterRows)
            }

          // NO PIT TABLES
          displayDuration("NO-PIT TABLES" + _, logger, displayFlag) {
            val noPit = mappingConf.filter(_.addonConfig.noPit.isDefined)
            val noPitMerges = noPit.zipWithIndex.map { case (t, i) =>
              mergeTableNoPit(
                cfg = TableMapping(t.table.name, t.addonConfig, t.stackableAddons, conf, i, noPit.size),
                batchId = batchId,
                microBatchDS = ds
              )
            }
            val noPitStatuses = Await.result(Future.sequence(noPitMerges), Duration.Inf)
            val noPitFailures = logMergeStatuses(noPitStatuses, logger)
            logger.info(s"Merge failures count: ${noPitFailures.size}")
            noPitFailures.foreach { exception => throw exception }

            display(s"NO-PIT TABLES | Num: ${noPitStatuses.size}", conf, logger)
          }

          // SECONDARY PIT TABLES
          displayDuration("SEC TABLES" + _, logger, displayFlag) {
            val brDistinctPitKeysInBatch = if (mc.enableBroadcastDistinctPitKeys) {
              // Broadcast distinct pit keys - assumption: small enough to be in memory
              broadcast(distinctPitKeysInBatch)
            } else {
              // Cache distinct pit keys to avoid re-evaluation for each secondary - assumption: too big to be in memory
              distinctPitKeysInBatch.cache()
            }
            val secondary = mappingConf.filter(_.addonConfig.secondaryPit.isDefined)
            val secondaryMerges = secondary.zipWithIndex.map { case (t, i) =>
              mergeTablePitSecondary(ds)(
                cfg = TableMapping(t.table.name, t.addonConfig, t.stackableAddons, conf, i, secondary.size),
                batchId = batchId,
                pit = pitMasCf.master,
                pittedMasterRows = pittedMasterRows,
                batchDistinctPitKeys = brDistinctPitKeysInBatch
              )
            }
            val otherStatuses = Await.result(Future.sequence(secondaryMerges), Duration.Inf)
            val otherFailures = logMergeStatuses(otherStatuses, logger)
            logger.info(s"Merge failures count: ${otherFailures.size}")
            otherFailures.foreach { exception => throw exception }

            display(s"SEC TABLES | Num: ${otherStatuses.size}", conf, logger)
          }

          if (!mc.enableRawDeltaPersist) {
            ds.unpersist()
          }
        }
        else {
          display(s"Skipping batch $batchId processing as there is no new records", conf, logger)
        }
      } finally {
        if (mc.enableRawDeltaPersist) {
          dropDefaultDeltaTable(tmpTableName)
        }
        masterPitLogic.cleanupTemporaryTables
      }
    }
  }

  /* ***************
   * Merge methods
   * **************/

  private def mergeTableNoPit(
    cfg: TableMapping,
    batchId: String,
    microBatchDS: Dataset[Table]
  ): Future[MergeStatus] = Future {
    sparkSession.sparkContext.setJobGroup(
      s"BATCH=$batchId",
      s"TYPE=NO-PIT TABLE=${cfg.tableName} (${cfg.tableIndex + 1}/${cfg.tableCount})"
    )
    logger.info(s"SUBMITTED NO-PIT (batch $batchId): ${cfg.tableName}")
    val batch: DataFrame = tablesToDFWithExpressions(cfg, microBatchDS.filter($"name" === cfg.tableName))
    val deduplicatedBatch = dedupBatchRecordsRowNumber(
      batch,
      cfg.conf.merge.keyColumns,
      cfg.conf.merge.ifDupeTakeHigher.map(e => expr(e).desc_nulls_last)
    )

    mergeWithStackableAddons(
      rootConfig,
      cfg.tableName,
      deduplicatedBatch,
      cfg.conf.merge,
      cfg.stackableAddons,
      simpleMergeInsertOnly(mc.enablePreMergeDeltaPersist),
      TableAccess(cfg)
    )
    logger.info(s"PROCESSED NO-PIT (batch $batchId): ${cfg.tableName}")
    MergeOk(cfg.tableName)
  }

  private def mergeTablePitMaster(
    cfg: TableMapping,
    batchId: String,
    microBatchDS: Dataset[Table],
    pitLogic: MasterPitLogic,
    newRecordsEstimation: Long
  ): Future[(MergeStatus, DataFrame, PittedMasterRows)] = Future {
    sparkSession.sparkContext.setJobGroup(
      s"BATCH=$batchId",
      s"TYPE=MASTER TABLE=${cfg.tableName}"
    )
    logger.info(s"SUBMITTED MASTER (batch $batchId): ${cfg.tableName}")
    val batch: DataFrame = tablesToDFWithExpressions(cfg, microBatchDS.filter($"name" === cfg.tableName))

    /** The distinct keys computed here are potentially a super-set of the ones actually present for each secondary pit
      * table, but:
      * - we compute the dataframe here to do the distinct only once and then broadcast it
      * - the final results are still correct (idempotent merge of existing values, potentially writing more than needed)
      */
    val batchDistinctPitKeys = batch.select(pitLogic.pit.pitKey).distinct()

    // PIT general config consistency assertions
    pitLogic.validatePitConfigMaster()

    /* LOAD and SPLIT - Load batch with the latest partition and split the union in NEW_VERSIONS and OLD_VERSIONS */

    val masterSplit = pitLogic.splitNewAndOldVersions(batch, batchDistinctPitKeys)
    val oldVersionsCount = masterSplit.splitOldVersions.count() // Log count for OLD_VERSIONS
    display(s"MASTER PIT - SPLIT - Count of OLD_VERSIONS: $oldVersionsCount", rootConfig, logger)

    val resizeLogic = new MappingResizeLogic(oldVersionsCount, newRecordsEstimation)
    resizeLogic.handleClusterResize(sparkSession, pipelineContext.resizeActuator)

    /* MERGE 1 - Master Merge 1 uses NEW_VERSIONS */

    // Apply master pit logic
    val patchNewVersions = pitLogic.applyMasterPitLogic(masterSplit.splitNewVersions)
    // this contains all (and only) the versions in NEW_VERSIONS (pitted)
    val pittedNew = pitLogic.persistMergeAndProject(patchNewVersions, cfg.stackableAddons, MergeFlavorNew, rootConfig)

    /* MERGE 2 - Master Merge 2 uses OLD_VERSIONS */

    // Load delta table rows for distinct keys in OLD_VERSIONS
    val tableAccess = TableAccess(cfg)
    val deltaSchema = tableAccess.columns
    // brand new keys will not be present here (they are part of the toUpdateAndAddNewVersions, already handled)
    val batchDistinctOldKeys = masterSplit.splitOldVersions.select(col(pitLogic.pit.pitKey)).distinct
    // here we read both histo and latest partitions for all old keys, because we need the current latest to get the begin date
    val toUpdateOld = tableAccess.toDF.join(batchDistinctOldKeys, pitLogic.pit.pitKey).select(deltaSchema: _*)

    // Union those rows with OLD_VERSIONS and deduplicate.
    val toUpdateAndAddOldVersions = PitLogic.unionAndDedup(toUpdateOld, masterSplit.splitOldVersions, cfg.conf.merge)
    // Apply master pit logic
    val patchOldVersions = pitLogic.applyMasterPitLogic(toUpdateAndAddOldVersions)
    // this contains all the versions for the keys that are present in OLD_VERSIONS
    val pittedOld = pitLogic.persistMergeAndProject(patchOldVersions, cfg.stackableAddons, MergeFlavorOld, rootConfig)

    logger.info(s"PROCESSED MASTER (batch $batchId): ${cfg.tableName}")
    (MergeOk(cfg.tableName), batchDistinctPitKeys, new PittedMasterRows(pittedNew, pittedOld))
  }

  private def mergeTablePitSecondary(microBatchDS: Dataset[Table])(
    cfg: TableMapping,
    batchId: String,
    pit: PitTransformation,
    pittedMasterRows: PittedMasterRows,
    batchDistinctPitKeys: DataFrame
  ): Future[MergeStatus] = Future {
    sparkSession.sparkContext.setJobGroup(
      s"BATCH=$batchId",
      s"TYPE=PITSEC TABLE=${cfg.tableName} (${cfg.tableIndex + 1}/${cfg.tableCount})"
    )
    logger.info(s"SUBMITTED SECONDARY (batch $batchId): ${cfg.tableName}")
    val batch: DataFrame = tablesToDFWithExpressions(cfg, microBatchDS.filter($"name" === cfg.tableName))

    val tableAccess = TableAccess(cfg)
    val deltaSchema = tableAccess.columns
    val pitLogic = new SecondaryPitLogic(pit, tableAccess.columnNames, cfg.tableName)

    // PIT general config consistency assertions
    pitLogic.validatePitConfigSecondary(cfg.conf.merge)

    /* MERGE 1 - Secondary Merge 1 uses PITTED_NEW_VERSIONS projected on pit schema */

    // Load latest partition rows for distinct keys in batch

    // Note: no need to filter only keys in NEW_VERSIONS, because pit logic will join with pittedMasterRows.pittedNewVersions
    val latestPart = tableAccess.toDF.filter(col(pit.isLast))
    // rows that exist in latest part, matching the keys in the batch
    // there is only one version in the latest partition for a key, that's why the join is only on pitKey
    val toUpdate = latestPart.toDF.join(batchDistinctPitKeys, pit.pitKey).select(deltaSchema: _*)

    // Union those rows with the input batch and dedup
    val toUpdateAndAdd = PitLogic.unionAndDedup(toUpdate, batch, cfg.conf.merge)
    // Apply secondary pit logic
    val patchNewVersions = pitLogic.applySecondaryPitLogic(toUpdateAndAdd, pittedMasterRows.pittedNewVersions)

    mergeWithStackableAddons(
      rootConfig,
      cfg.tableName,
      patchNewVersions,
      cfg.conf.merge,
      cfg.stackableAddons,
      smartMerge(pit.isLast, isLastVal = Some(true)),
      tableAccess
    )

    /* MERGE 2 - Secondary Merge 2 uses PITTED_ALL_VERSIONS_OLD_KEYS projected on pit schema */

    // Load delta table rows for distinct keys in PITTED_ALL_VERSIONS_OLD_KEYS (same keys as OLD_VERSIONS)

    // Distinct keys (from master PIT) coming from the old part of the batch
    // Note: in pittedMasterRows.pittedAllVersionsOldKeys there are all the versions for keys in OLD_VERSIONS
    val batchDistinctKeysInOldMaster = pittedMasterRows.pittedAllVersionsOldKeys.select(pit.pitKey).distinct
    // Retrieve from the secondary all rows matching the above pit-keys
    // (now at secondary granularity, meaning 0, 1 or more lines per pitkey+version)
    val toUpdateSecondaryOld = tableAccess.toDF.join(batchDistinctKeysInOldMaster, pit.pitKey).select(deltaSchema: _*)
    // Filter the batch to keep only keys in pittedMasterRows.pittedAllVersionsOldKeys (to have the "old" input batch)
    val batchSecondaryOldVersions = batch.join(batchDistinctKeysInOldMaster, pit.pitKey).select(deltaSchema: _*)

    // Union the loaded table rows with the filtered batch, then deduplicate
    val toUpdateAndAddSecondaryOld =
      PitLogic.unionAndDedup(toUpdateSecondaryOld, batchSecondaryOldVersions, cfg.conf.merge)
    // Apply secondary pit logic
    val patchOldVersions =
      pitLogic.applySecondaryPitLogic(toUpdateAndAddSecondaryOld, pittedMasterRows.pittedAllVersionsOldKeys)

    mergeWithStackableAddons(
      rootConfig,
      cfg.tableName,
      patchOldVersions,
      cfg.conf.merge,
      cfg.stackableAddons,
      smartMerge(pit.isLast, isLastVal = None),
      tableAccess
    )
    logger.info(s"PROCESSED SECONDARY (batch $batchId): ${cfg.tableName}")
    MergeOk(cfg.tableName)
  }

}

object MappingPipeline {

  def apply(
    pipelineContext: PipelineContext,
    recordsFilter: Option[String] = None,
    metrics: Metrics
  ): MappingPipeline = {

    // Set current database
    val database = pipelineContext.rootConfig.etl.common.outputDatabase
    if (pipelineContext.sparkSession.catalog.databaseExists(database)) {
      pipelineContext.sparkSession.catalog.setCurrentDatabase(database)
    } else {
      throw new IllegalStateException(
        s"Database '$database' does not exist: please create database and tables first"
      )
    }
    new MappingPipeline(
      pipelineContext,
      recordsFilter,
      metrics
    )
  }

  /** This is the minimum mapping needed for a Spark executor to extract rows from messages
    */
  case class TableMapping(
    tableName: String,
    conf: MappingConfig,
    stackableAddons: List[StackableAddonConfig],
    rootConfig: RootConfig,
    tableCount: Int,
    tableIndex: Int
  ) {
    lazy val sparkSchema: StructType = SchemaManager.computeTableSchema(conf)
  }

  implicit val localDateConfigReader: ConfigReader[LocalDate] = ConfigReader.fromString[LocalDate] { str =>
    try {
      Right(LocalDate.parse(str))
    } catch {
      case e: DateTimeParseException =>
        Left(CannotConvert(str, "LocalDate", e.getMessage))
    }
  }
}
