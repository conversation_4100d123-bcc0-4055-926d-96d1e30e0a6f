{"partition-spec": {"key": "PNR_CREATION_DATE", "column-name": "PART_PNR_CREATION_MONTH", "expr": "date_format(PNR_CREATION_DATE, \"yyyy-MM\")"}, "tables": [{"name": "FACT_RESERVATION_HISTO", "table-selectors": ["FACT"], "mapping": {"merge": {"key-columns": ["RESERVATION_ID", "VERSION"], "if-dupe-take-higher": ["LOAD_DATE"]}, "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}]}], "columns": [{"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"}, {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}}, {"name": "RECORD_LOCATOR", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.reference"}]}}, {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}}, {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}]}}, {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}}, {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}}, {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}], "pit": {"type": "master-pit-table", "master": {"pit-key": "RESERVATION_ID", "pit-version": "VERSION", "valid-from": "DATE_BEGIN", "valid-to": "DATE_END", "is-last": "IS_LAST_VERSION"}}}}, {"name": "FACT_AIR_SEGMENT_PAX_HISTO", "mapping": {"merge": {"key-columns": ["AIR_SEGMENT_PAX_ID", "VERSION"], "if-dupe-take-higher": ["LOAD_DATE"]}, "root-sources": [{"blocks": [{"prod": "$.mainResource.current.image.products[?(@.subType == 'AIR')]"}, {"tr": "$.travelers[*]"}]}], "columns": [{"name": "AIR_SEGMENT_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"prod": "$.id"}, {"tr": "$.id"}]}, "expr": "hashM({0})"}, {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"prod": "$.id"}, {"tr": "$.id"}]}}, {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"root": "$.mainResource.current.image.id"}]}, "expr": "hashM({0})"}, {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "sources": {"blocks": [{"root": "$.mainResource.current.image.creation.dateTime"}]}}, {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"root": "$.mainResource.current.image.version"}]}}, {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"root": "$.mainResource.current.image.lastModification.dateTime"}]}}, {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}}, {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}], "pit": {"type": "secondary-pit-table"}}}, {"name": "PNR_TKT_PARTIAL_CORR_PIT", "table-selectors": ["PARTIAL", "CORR"], "mapping": {"merge": {"key-columns": ["RESERVATION_ID", "TRAVEL_DOCUMENT_ID", "AIR_SEGMENT_PAX_ID", "COUPON_ID", "VERSION"], "if-dupe-take-higher": ["LOAD_DATE"]}, "root-sources": [{"blocks": [{"corr": "$.correlatedResourcesCurrent.PNR-TKT.correlations[*]"}, {"coupon": "$.corrTktPnr.items[*]"}]}], "columns": [{"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"root": "$.mainResource.current.image.id"}]}, "expr": "hashM({0})"}, {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"}, {"name": "AIR_SEGMENT_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"coupon": "$.pnrAirSegmentId"}, {"coupon": "$.pnrTravelerId"}]}, "expr": "hashM({0})"}, {"name": "COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"coupon": "$.ticketCouponId"}]}, "expr": "hashM({0})"}, {"name": "VERSION", "column-type": "intColumn", "sources": {"blocks": [{"root": "$.mainResource.current.image.version"}]}}, {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"root": "$.mainResource.current.image.lastModification.dateTime"}]}}, {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}}, {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}], "pit": {"type": "secondary-pit-table"}}}]}