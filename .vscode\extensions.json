{"recommendations": ["scalameta.metals", "scala-lang.scala", "lightbend.vscode-sbt-scala", "vscjava.vscode-java-pack", "eamodio.gitlens", "usernamehw.errorlens", "coenraads.bracket-pair-colorizer-2", "christian-kohler.path-intellisense", "wayou.vscode-todo-highlight", "ms-vscode.vscode-json", "redhat.vscode-yaml", "ms-vscode.test-adapter-converter", "hbenl.vscode-test-explorer"], "unwantedRecommendations": ["ms-vscode.vscode-typescript-next", "ms-python.python"]}