{"version": "0.2.0", "configurations": [{"type": "scala", "request": "launch", "name": "Debug Scala Application", "mainClass": "${workspaceFolder}/core/src/main/scala/", "args": [], "jvmOptions": ["-Dspark.driver.bindAddress=127.0.0.1", "-Duser.country.format=US", "-Duser.language.format=en", "-Duser.timezone=UTC", "-Xms2000M", "-Xmx4000M", "-XX:+CMSClassUnloadingEnabled", "-XX:+UseCompressedOops", "-XX:+UseG1GC"], "env": {"HADOOP_HOME": "${env:HADOOP_HOME}"}}, {"type": "scala", "request": "launch", "name": "Debug Scala Test", "testClass": "*", "jvmOptions": ["-Dspark.driver.bindAddress=127.0.0.1", "-Duser.country.format=US", "-Duser.language.format=en", "-Duser.timezone=UTC", "-Dj2s.test.local=true", "-Xms2000M", "-Xmx4000M"]}]}