package com.amadeus.airbi.json2star.common.snowflake

case class SnowflakeSettings(
  sparkConf: Option[Map[String, String]],
  streamOptions: Option[Map[String, String]],
  mirror: Mirror,
  snowflakeConf: Map[String, SnowflakeVal],
  queryTag: Option[Map[String, String]],
  azureConf: AzureConfig,
  sink: SfSink
)

sealed trait SnowflakeVal
case class Clear(value: String) extends SnowflakeVal
case class Secret(secretName: String) extends SnowflakeVal

case class ServicePrincipal(
  clientId: String,
  clientSecret: String
)

case class AzureConfig(
  secretScope: String
)

case class SfSink(checkpointLocation: String, trigger: String)

case class Mirror(database: String)
