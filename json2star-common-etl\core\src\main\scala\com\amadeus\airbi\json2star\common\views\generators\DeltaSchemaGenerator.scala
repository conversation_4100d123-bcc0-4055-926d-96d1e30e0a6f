package com.amadeus.airbi.json2star.common.views.generators

import com.amadeus.airbi.json2star.common.views.generators.SchemaGenerator.Statements
import com.amadeus.airbi.json2star.common.{ColumnDef, Schema, TableDef}
import com.amadeus.airbi.rawvault.common.config.ColumnType.ColumnType
import com.amadeus.airbi.rawvault.common.vault.spark.SchemaManager

/** It generates the SQL DDL statements to create Delta Databricks Tables from the mapping file
  */
object DeltaSchemaGenerator extends SchemaGenerator {
  private[views] def toTableType(columnType: ColumnType): String = {
    SchemaManager.toDataType(columnType).sql
  }

  private[views] def toColumnDDL(col: ColumnDef): Seq[String] = {
    Seq(
      Some(col.name),
      Some(toTableType(col.columnType)),
      if (col.isMandatory) Some("NOT NULL") else None
    ).flatten
  }
  private[views] def createInternalTable: Boolean = true

  /** Generate the SQL statement to initialize a Table
    *
    * @param conf     a Table Config - ongoing HubConfigBean
    * @param database database name
    * @param options  configurable parameters for delta table
    * @return a init SQL
    */
  def toCreateTableSql(
    conf: TableDef,
    database: String,
    options: Map[String, String]
  ): Option[Statements] = {
    val s = conf.schema
    s.kind match {
      case Schema.Materialized => Some(toCreateTableSql(tableDef = s, database = database, options = options))
      case Schema.View(q) => Some(toCreateViewSql(tableDef = s, database = database, q))
    }
  }

  // Format Delta table properties using TBLPROPERTIES notation
  def buildTableProperties(options: Map[String, String]): String = {
    if (options.nonEmpty) {
      s"TBLPROPERTIES(${options
        .map { case (k, v) =>
          s"${k} = ${v}"
        }
        .mkString(", ")})"
    } else {
      ""
    }
  }

  private[views] def toCreateTableSql(
    tableDef: Schema,
    database: String,
    options: Map[String, String]
  ): Statements = {
    val tblOptionsStr = buildTableProperties(options)
    val partitionByStr = tableDef.partitionColumn.map(c => s" PARTITIONED BY($c)").getOrElse("")
    val statement = s"""|CREATE TABLE IF NOT EXISTS ${database}.${tableDef.name} (
        |${formatDDL(tableDef.columns)}
        |)
        |USING DELTA${partitionByStr}
        |${tblOptionsStr};""".stripMargin
    Statements(statement)
  }

  private[views] def toCreateViewSql(
    tableDef: Schema,
    database: String,
    query: String
  ): Statements = {
    val statement = s"""|CREATE VIEW IF NOT EXISTS ${database}.${tableDef.name}
        |AS ${query}
        |;""".stripMargin
    Statements(statement)
  }

}
