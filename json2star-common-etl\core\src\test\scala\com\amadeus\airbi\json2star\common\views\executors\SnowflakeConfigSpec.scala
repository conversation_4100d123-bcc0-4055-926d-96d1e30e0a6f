package com.amadeus.airbi.json2star.common.views.executors

import com.amadeus.airbi.common.utils.CommonSpec
import com.amadeus.airbi.json2star.common.config.AppConfig

class SnowflakeConfigSpec extends CommonSpec {

  def args(cmd: String, inputConfig: String, outputSQL: String): Array[String] = Array(
    "--app-config-file",
    inputConfig,
    "--command",
    cmd,
    "--dry-mode",
    outputSQL
  )

  SnowflakeConfig.getClass.getName should "generate the config from the args - create mode" in {
    val s = SnowflakeConfig.apply(args("CREATE", "views/sample_1/app.conf", "outSQLFile"))

    s should ===(
      SnowflakeConfig(
        AppConfig("views/sample_1/app.conf"),
        "CREATE",
        options = Map(
          "sfUrl" -> "snowflakecomputing.com",
          "sfUser" -> "ROBOTIC_DEV",
          "pem_private_key" -> "local-value",
          "sfRole" -> "APP_OWNER",
          "sfDatabase" -> "my_db",
          "sfSchema" -> "my_schema",
          "sfWarehouse" -> "DIHDLK_WH_XS",
          "query_tag" -> "{}",
          "internal_execute_query_in_sync_mode" -> "true"
        ),
        Some("outSQLFile")
      )
    )
  }

  it should "generate the config from the args - drop mode" in {
    val s = SnowflakeConfig.apply(args("DROP", "views/sample_1/app.conf", "outSQLFile"))

    s should ===(
      SnowflakeConfig(
        AppConfig("views/sample_1/app.conf"),
        "DROP",
        options = Map(
          "sfUrl" -> "snowflakecomputing.com",
          "sfUser" -> "ROBOTIC_DEV",
          "pem_private_key" -> "local-value",
          "sfRole" -> "APP_OWNER",
          "sfDatabase" -> "my_db",
          "sfSchema" -> "my_schema",
          "sfWarehouse" -> "DIHDLK_WH_XS",
          "query_tag" -> "{}",
          "internal_execute_query_in_sync_mode" -> "true"
        ),
        Some("outSQLFile")
      )
    )
  }

}
