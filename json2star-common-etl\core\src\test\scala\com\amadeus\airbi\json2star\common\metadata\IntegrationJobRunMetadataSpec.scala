package com.amadeus.airbi.json2star.common.metadata

import com.amadeus.airbi.json2star.common.app.Json2StarApp
import com.amadeus.airbi.json2star.common.app.Json2StarApp.readMappingConfig
import com.amadeus.airbi.json2star.common.metadata.JobRunMetadataTable.Status
import com.amadeus.airbi.rawvault.common.testfwk.Json2StarSpec
import io.delta.tables.DeltaTable

import java.io.File
import scala.reflect.io.Directory

class IntegrationJobRunMetadataSpec extends Json2StarSpec {

  val mappingFile = "datasets/single_table/single_table_model.conf"

  override def beforeAll: Unit = {
    super.beforeAll
    createTables(mappingFile)
  }

  private def simulateSettingDefaultDB(): Unit = {
    // Let's be sure we do not depend on setting the "right" current database (inputDatabase) before running the
    // metadata part.
    // We simulate a "fresh cluster start" (e.g. db is not inputDatabase), setting the outputDatabase as current.
    spark.catalog.setCurrentDatabase(outputDatabase)
  }

  override def beforeEach: Unit = {
    simulateSettingDefaultDB
    cleanTables(mappingFile)
  }

  override def afterEach: Unit = {
    spark.sql("DELETE FROM " + inputDatabase + "." + JobRunMetadataTable.Name + ";")
  }

  "IntegrationMetadataSpec" should "log metadata if enabled" taggedAs (SlowTestTag) in {
    val dataDir = "datasets/single_table/data/"
    val rcDefault = rootConfig(dataDir, inputDatabase, isLight = DefaultIsLight)
    val rc = rcDefault.copy(processingParams = rcDefault.processingParams.copy(enableMetadata = true))

    val directory = new Directory(new File(rc.etl.stream.sink.checkpointLocation))
    directory.deleteRecursively()
    val mapping = readMappingConfig(mappingFile)
    Json2StarApp.run(spark, rc, mapping)

    val table = DeltaTable.forName(JobRunMetadataTable.Name)
    table.toDF.count() shouldBe 1
    table.toDF.select("DOMAIN").collect().head.getAs[String]("DOMAIN") shouldBe "DOMAIN"
    table.toDF.select("STATUS").collect().head.getAs[String]("STATUS") shouldBe Status.FINISHED

    val expectedDetails = Map(
      MappingQualityMetrics.name -> Map(
        "totProcessed" -> 1,
        "transformed" -> 1,
        "dropped" -> 0,
        "transformErrors" -> 0,
        "tableRowCounts" -> Map(
          "FACT_BAG_GROUP_HISTO:KO:MCN" -> 0,
          "FACT_BAG_GROUP_HISTO:KO:PKN" -> 0,
          "FACT_BAG_GROUP_HISTO:OK" -> 1
        )
      )
    )
    val actual = MockQualityMetrics.detailsAsMap(table.toDF)
    actual shouldBe expectedDetails

    directory.deleteRecursively()
  }

  "IntegrationMetadataSpec" should "not log metadata if not enabled" taggedAs (SlowTestTag) in {
    val dataDir = "datasets/single_table/data/"
    val rcDefault = rootConfig(dataDir, inputDatabase, isLight = DefaultIsLight)
    val rc = rcDefault.copy(processingParams = rcDefault.processingParams.copy(enableMetadata = false))

    val directory = new Directory(new File(rc.etl.stream.sink.checkpointLocation))
    directory.deleteRecursively()
    val mapping = readMappingConfig(mappingFile)
    Json2StarApp.run(spark, rc, mapping)

    val table = DeltaTable.forName(JobRunMetadataTable.Name)
    table.toDF.count() shouldBe 0

    directory.deleteRecursively()
  }

}
