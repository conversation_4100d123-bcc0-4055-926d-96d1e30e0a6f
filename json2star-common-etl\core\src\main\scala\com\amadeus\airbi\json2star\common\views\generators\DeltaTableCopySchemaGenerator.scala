package com.amadeus.airbi.json2star.common.views.generators

import com.amadeus.airbi.json2star.common.views.executors.DeltaTableCopyConfig
import com.amadeus.airbi.json2star.common.views.generators.SchemaGenerator.Statements
import com.amadeus.airbi.json2star.common.{ColumnDef, Schema, TableDef}
import com.amadeus.airbi.rawvault.common.config.ColumnType.ColumnType

/** It generates the SQL DDL statements to copy Delta Databricks Tables from the previous version's database,
  * in case of partial reprocessing
  */
class DeltaTableCopySchemaGenerator(c: DeltaTableCopyConfig) extends SchemaGenerator {

  private[views] def toTableType(columnType: ColumnType): String = {
    throw new UnsupportedOperationException(s"No support for column-level methods in ${this.getClass.getName}")
  }

  private[views] def toColumnDDL(col: ColumnDef): Seq[String] = {
    throw new UnsupportedOperationException(s"No support for column-level methods in ${this.getClass.getName}")
  }

  private[views] def createInternalTable: Boolean = true

  /** Generate the SQL statement to copy a table from the previous version's database
    *
    * @param conf     a Table Config - ongoing HubConfigBean
    * @param database database name
    * @param options  configurable parameters for delta table
    * @return a init SQL
    */
  def toCreateTableSql(
    conf: TableDef,
    database: String,
    options: Map[String, String]
  ): Option[Statements] = {
    val s = conf.schema
    s.kind match {
      case Schema.Materialized => toCreateTableSql(tableDef = s, database = database, options = options)
      case Schema.View(_) => None // No need to copy views
    }
  }

  private[views] def toCreateTableSql(
    tableDef: Schema,
    database: String,
    options: Map[String, String]
  ): Option[Statements] = {
    val tablesToReprocess = c.tablesToReprocess.map(_.trim.toUpperCase())
    tablesToReprocess.contains(tableDef.name) match {
      case true => None // No copy needed for tables to reprocess
      case false =>
        val previousDb = c.previousDatabase
        val tableProps = DeltaSchemaGenerator.buildTableProperties(options)
        val create = s"CREATE OR REPLACE TABLE ${database}.${tableDef.name} CLONE ${previousDb}.${tableDef.name} ${tableProps}"
        val analyze = s"ANALYZE TABLE ${database}.${tableDef.name} COMPUTE STATISTICS NOSCAN"
        val statements = if (c.analyzeAfterClone) Seq(create, analyze) else Seq(create)
        Some(Statements(statements))
    }
  }
}
