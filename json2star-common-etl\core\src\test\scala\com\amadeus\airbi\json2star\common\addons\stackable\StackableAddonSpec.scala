package com.amadeus.airbi.json2star.common.addons.stackable

import com.amadeus.airbi.json2star.common.TablesDef
import com.amadeus.airbi.json2star.common.addons.base.Addon
import com.amadeus.airbi.json2star.common.addons.base.correlation.Correlation
import com.amadeus.airbi.json2star.common.addons.base.latest.Latest
import com.amadeus.airbi.json2star.common.addons.base.mapping.Mapping
import com.amadeus.airbi.json2star.common.addons.stackable.EnrichedSchemaMetadata.NoEnrichment
import com.amadeus.airbi.json2star.common.addons.stackable.dummy.DummyAddon
import com.amadeus.airbi.json2star.common.addons.stackable.weight.WeightConversionAddon
import com.amadeus.airbi.json2star.common.app.Json2StarApp.readMappingConfig
import com.amadeus.airbi.json2star.common.extdata.{DummyExtData, DummyExtDataType, ExtData, ExtDataType}
import com.amadeus.airbi.rawvault.common.application.config.{TableConfig, TablesConfig}
import com.amadeus.airbi.rawvault.common.testfwk.Json2StarSpec

class StackableAddonSpec extends Json2StarSpec {

  // Note: object created *ONLY* for *THIS* test
  // Here we don't care about the StackableAddons lookup method so we can reuse the existing Dummy config
  object EmptyAddon extends StackableAddon[Dummy] {
    override def getCompatibleBaseAddons: List[Addon[_]] = List(Mapping)
    override def getRequiredExtData: List[ExtDataType] = List()
    override def validate(c: TablesConfig, t: TableConfig, addonConfig: StackableAddonConfig): Unit = ()
  }

  val mappingFile = "stackable/dummy_mapping.conf"
  val mapping: TablesConfig = readMappingConfig(mappingFile)

  import StackableAddonsSpec._

  "StackableAddon" should "return the right type in getConfig or raise an exception" in {
    // this is a Dummy Addon config as a base type
    val stackableAddonConfig = getTableDef(mapping, "FACT_OK_HISTO").table.stackableAddons.head

    DummyAddon.getConfig(stackableAddonConfig).isInstanceOf[Dummy] shouldBe true

    assertThrows[IllegalStateException](
      WeightConversionAddon.getConfig(stackableAddonConfig)
    )
  }

  it should "correctly detect if a stackable addon is compatible with a base addon" in {
    EmptyAddon.getCompatibleBaseAddons shouldBe List(Mapping)
    EmptyAddon.isCompatibleWith(Mapping) shouldBe true
    EmptyAddon.isCompatibleWith(Latest) shouldBe false
    EmptyAddon.isCompatibleWith(Correlation) shouldBe false
  }

  it should "do nothing in enrichBatch" in {
    import spark.implicits._
    val df = Seq(1, 2).toDF
    val actual = EmptyAddon.enrichBatch(df, Dummy("ok"), null)
    assertSmallDataFrameEquality(actual, df)
  }

  it should "do nothing in enrichTableRows" in {
    val expected = List(Map("a" -> "b"))
    val actual = EmptyAddon.enrichTableRows(expected, Dummy("ok"), null, null, null) // scalastyle:off
    actual shouldBe expected
  }

  it should "do nothing enrichSchemaMetadata" in {
    val tables = mapping.copy(tables = mapping.tables.filter(_.name == "FACT_OK_HISTO"))
    val tableDefs = TablesDef.consolidate(tables)
    val tableOk = tables.tables.head
    val enrichedMetadata = EmptyAddon.enrichSchemaMetadata(tables, tableOk, Dummy("ok"), tableDefs.tables.head.schema)
    enrichedMetadata shouldBe NoEnrichment
  }

  it should "use external data in enrichTableRows to transform rows" in {
    val inputRows = List(Map("col_name" -> "value", "col_name_enriched" -> ""))
    val extData = ExtData(Map(DummyExtDataType -> DummyExtData.load()))
    val expected = List(Map("col_name" -> "value", "col_name_enriched" -> "enrich(value, external_data_value)"))

    val actual =
      DummyAddon.enrichTableRows(
        inputRows,
        addonConfig = Dummy("enrich_with_external_data", srcCol = Some("col_name"), dstCol = Some("col_name_enriched")),
        null,
        null,
        extData
      ) // scalastyle:off
    actual shouldBe expected
  }

  it should "fail if the external data is not available" in {
    val inputRows = List(Map("col_name" -> "value", "col_name_enriched" -> ""))
    val extData = ExtData(Map())

    val thrown = intercept[Exception](
      DummyAddon.enrichTableRows(
        inputRows,
        addonConfig = Dummy("enrich_with_external_data", srcCol = Some("col_name"), dstCol = Some("col_name_enriched")),
        null,
        null,
        extData
      ) // scalastyle:off
    )

    thrown.getMessage shouldBe "No ExtDataType DummyExtDataType in the ExtData. Available are "
  }

}
