package com.amadeus.airbi.rawvault.common.vault.generators

import com.amadeus.airbi.rawvault.common.config.BlockJsonAlias
import com.amadeus.airbi.rawvault.common.processors.BlockResults.{
  BlockResult,
  CartesianBlockResult,
  CartesianBranches,
  JSON,
  JsonpathBlockResult,
  RecordsList,
  RecordsSeq
}

object PreRowGenerator {
  def generatePreRow(br: BlockResult): RecordsSeq[Attempt[PreRow]] = {
    br match {
      case j: JsonpathBlockResult => resolveJson(j)
      case c: CartesianBlockResult => resolveCartesian(c)
    }
  }

  // json
  private def intoPreRow(
    children: RecordsSeq[Attempt[BlockResult]],
    m: (BlockJsonAlias, JSON)
  ): RecordsList[Attempt[PreRow]] = {
    children.flatMap {
      case Right(br) =>
        generatePreRow(br).map(_.map(p => m :: p))
      case Left(msg) =>
        Seq(Left(msg))
    }.toList
  }

  private def resolveJson(j: JsonpathBlockResult): RecordsSeq[Attempt[PreRow]] = {
    val currentAliasJson = (j.alias -> j.json)
    if (j.leaf) { // leaf
      val preRow: PreRow = List(currentAliasJson)
      List(Right(preRow))
    } else {
      if (j.children.nonEmpty) { // enrich target data structure of each child with this result
        intoPreRow(j.children, currentAliasJson)
      } else { // not leaf, nor children present
        List()
      }
    }
  }

  // cartesian
  private def cartesianProduct[T](s: CartesianBranches[RecordsSeq[T]]): RecordsSeq[Seq[T]] =
    s.foldLeft(Seq(Seq.empty[T]))((b, a) => b.flatMap(i => a.map(j => i ++ Seq(j))))

  private def resolveCartesian(c: CartesianBlockResult): RecordsSeq[Attempt[PreRow]] = {
    val resolvedBranches: CartesianBranches[RecordsSeq[Attempt[PreRow]]] = c.children.map { cartesianBranch =>
      val branchAttempts: RecordsSeq[Attempt[PreRow]] = cartesianBranch.flatMap {
        case Right(s) => generatePreRow(s)
        case Left(i) => List(Left(i))
      }
      branchAttempts
    }
    val failures: RecordsSeq[Attempt[PreRow]] =
      resolvedBranches.map(b => b.collect { case k: Left[ErrorMessage, PreRow] => k }).flatten
    val successes: CartesianBranches[RecordsSeq[PreRow]] = resolvedBranches.map(b => b.collect { case Right(v) => v })
    val exploded: Seq[PreRow] = cartesianProduct(successes).map(_.flatten.toList)
    exploded.map(Right[ErrorMessage, PreRow](_)) ++ failures
  }
}
