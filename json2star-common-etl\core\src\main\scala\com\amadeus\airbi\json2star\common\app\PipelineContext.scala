package com.amadeus.airbi.json2star.common.app

import com.amadeus.airbi.json2star.common.resize.{DatabricksUtils, ResizeActuator}
import com.amadeus.airbi.rawvault.common.RootConfig
import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.streaming.Trigger

import java.util.concurrent.Executors
import scala.concurrent.{ExecutionContext, ExecutionContextExecutorService}

class PipelineContext(
  val rootConfig: RootConfig,
  val sparkSession: SparkSession,
  @transient
  val databricksUtils: DatabricksUtils,
  val processingContext: ProcessingContext
) extends Serializable {

  @transient implicit lazy val xc: ExecutionContextExecutorService = {
    ExecutionContext.fromExecutorService(Executors.newFixedThreadPool(processingContext.processingParams.parallelism))
  }

  @transient
  val resizeActuator: Option[ResizeActuator] = ResizeActuator.fromRootConfig(rootConfig, databricksUtils)

  def trigger(): Trigger = {
    rootConfig.etl.stream.sink.trigger.toLowerCase match {
      case "once" => Trigger.Once()
      case "availablenow" => Trigger.AvailableNow()
      case interval => Trigger.ProcessingTime(interval)
    }
  }

  def shutdown(): Unit = {
    if (xc != null) {
      xc.shutdown()
    }
  }

}
