package com.amadeus.airbi.rawvault.common.config

import com.amadeus.airbi.json2star.common.{ColumnDef, Schema}
import pureconfig.ConfigReader
import pureconfig.generic.semiauto.deriveEnumerationReader

object GDPRZone {

  sealed trait GDPRZoneType

  case object Red extends GDPRZoneType

  case object Orange extends GDPRZoneType

  case object Green extends GDPRZoneType

  def toString(gdpr: GDPRZoneType): String = gdpr match {
    case Red => "red"
    case Orange => "orange"
    case Green => "green"
  }

  implicit val gdprZoneTypeConvert: ConfigReader[GDPRZoneType] = deriveEnumerationReader[GDPRZoneType]

  /** Get the most sensitive GDPR value from a list of values.
    *
    * Priority: Red, (none), Orange, Green
    */
  def getMostSensitiveValue(gs: Seq[Option[GDPRZoneType]]): Option[GDPRZoneType] = {
    if (gs.isEmpty) {
      None
    } else {
      val g = if (gs.contains(Some(Red))) {
        Some(Red)
      } else if (gs.contains(None)) {
        None
      } else if (gs.contains(Some(Orange))) {
        Some(Orange)
      } else {
        Some(Green)
      }
      g
    }
  }

  /** Get the most sensitive GDPR Zone from a seq of column definition
    * Note: GDPR zones in order of more sensitive are: Red, (no value), Orange, Green
    *
    * If there's no Red column and at least one column definition has no GDPR zone defined,
    * keep the overall GDPR zone unknown
    *
    *  @param tableName the table name
    * @param columns a list of column def
    * @return a gdpr zone if possible to define it or None
    */
  def getMostSensitive(tableName: String, columns: Seq[ColumnDef]): Option[GDPRZoneType] = {
    val gdprZonesFromCols = columns.map(_.consolidatedGdprZone(tableName))
    GDPRZone.getMostSensitiveValue(gdprZonesFromCols)
  }

  /** Get the GDPR zone for a column, using the following logic:
    * - gdpr-zone is present, use it
    * - otherwise use green if one of the following applies:
    *    - field belongs to DIM table
    *    - field name ends with _ID
    *    - field name is VERSION or LOAD_DATE
    *  - else use None
    */
  def getGDPRZone(fieldGdprZone: Option[GDPRZoneType], fieldName: String, tableName: String): Option[GDPRZoneType] = {
    def isGreenColumn(fieldName: String, tableName: String): Boolean = {
      tableName.startsWith(Schema.DIM_prefix) || fieldName.endsWith(ColumnDef.IdSuffix) || Seq(
        "VERSION",
        "LOAD_DATE"
      ).contains(fieldName)
    }

    if (fieldGdprZone.isDefined) {
      fieldGdprZone
    } else if (isGreenColumn(fieldName, tableName)) {
      Some(Green)
    } else {
      None
    }
  }

}
