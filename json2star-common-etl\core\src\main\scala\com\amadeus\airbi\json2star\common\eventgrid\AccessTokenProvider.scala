package com.amadeus.airbi.json2star.common.eventgrid

import com.amadeus.airbi.json2star.common.config.EventGridParams
import com.amadeus.airbi.json2star.common.resize.DatabricksUtils
import com.azure.core.credential.{AccessToken, TokenRequestContext}
import com.azure.core.http.policy.{HttpLogDetailLevel, HttpLogOptions}
import com.azure.identity.{ClientSecretCredential, ClientSecretCredentialBuilder}
import com.fasterxml.jackson.databind.ObjectMapper
import org.apache.http.client.HttpClient
import org.apache.http.client.entity.EntityBuilder
import org.apache.http.client.methods.RequestBuilder
import org.apache.http.entity.ContentType
import org.apache.http.impl.client.HttpClientBuilder
import org.apache.http.{HttpHeaders, HttpStatus}

import java.time.OffsetDateTime
import scala.util.{Failure, Success, Try}

object DefaultAccessTokenProvider extends AccessTokenProvider

trait AccessTokenProvider {
  private val AuthStep1Retry = 3
  private val AuthStep2Retry = 3

  private def getHttpLogOptions(logLevel: Option[String]): HttpLogOptions = {
    val hdl = logLevel.map(HttpLogDetailLevel.valueOf).getOrElse(HttpLogDetailLevel.BASIC)
    new HttpLogOptions().setLogLevel(hdl)
  }

  def httpClient: HttpClient = HttpClientBuilder.create().build()

  /** Retry a code block n times
    * @param n number of retries
    * @param code code block to retry
    * @tparam T type of the return value
    * @return the result of the code block
    */
  private def retry[T](n: Int)(code: => T): T = {
    Try(code) match {
      case Failure(e) => if (n > 1) retry(n - 1)(code) else throw e
      case Success(value) => value
    }
  }

  /** Step 1 of the authentication process
    * It performs the authentication with the Service Provider
    * * It retries more times based on AuthStep1Retry value
    *
    * @param tenantId tenant id of the Service Provider
    * @param clientId application id of the Service Provider
    * @param clientSecret secret to authenticate the Service Provider
    * @param httpLogDetailLevel optional http log level for the http client
    *
    * @return a AccessToken with its expiration time
    */
  def authStep1(
    tenantId: String,
    clientId: String,
    clientSecret: String,
    httpLogDetailLevel: Option[String]
  ): Try[AccessToken] = Try {
    // These values are required to create a ClientSecretCredential
    val secretCredential: ClientSecretCredential = new ClientSecretCredentialBuilder()
      .tenantId(tenantId)
      .clientId(clientId)
      .clientSecret(clientSecret)
      .httpLogOptions(getHttpLogOptions(httpLogDetailLevel))
      .maxRetry(AuthStep1Retry)
      .build()

    val tr = new TokenRequestContext().addScopes("https://management.azure.com/.default")
    val monoAccessToken = secretCredential.getToken(tr)
    val accessToken = monoAccessToken.block()
    accessToken
  }

  /** Step 2 of the authentication process
    * Call the REST API listTokens over the Managed Application
    * Refer to https://rndwww.nce.amadeus.net/git/projects/AGP/repos/pulse-datapush-eventhub/browse/src/main/java/com/amadeus/pulse/datapush/eventhub/oauth/ManagedAppAuthenticator.java?at=refs%2Ftags%2F1.88.0#58-70
    *
    * It retries more times based on AuthStep2Retry value
    *
    * @param managedAppResourceId  resource ID of the managed application
    * @param authorizationAudience authorization audience for the token
    * @param accessTokenStep1      access token from step 1 (given by the Service Provider)
    * @return a AccessToken with its expiration time
    */
  def authStep2(
    managedAppResourceId: String,
    authorizationAudience: String,
    accessTokenStep1: AccessToken
  ): Try[AccessToken] = Try {
    retry(AuthStep2Retry) {
      val uri = s"https://management.azure.com/$managedAppResourceId" +
        s"/listTokens?api-version=2019-07-01"

      val token1 = accessTokenStep1.getToken
      val req = RequestBuilder
        .post(uri)
        .addHeader(HttpHeaders.CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType)
        .addHeader(HttpHeaders.AUTHORIZATION, s"Bearer $token1")
        .setEntity(
          EntityBuilder.create
            .setText(
              s"{authorizationAudience: '$authorizationAudience'}"
            )
            .build
        )
        .build

      val httpResponse = httpClient.execute(req)

      if (httpResponse.getStatusLine.getStatusCode != HttpStatus.SC_OK) {
        throw new RuntimeException(s"ERROR: ${httpResponse.getStatusLine} ${httpResponse.getEntity.getContent}")
      }

      // Parse httpResponse
      val obj = new ObjectMapper()
      val json = obj.readTree(httpResponse.getEntity.getContent)
      val authResponse = json.get("value").get(0)
      val accessToken = authResponse.get("access_token").asText()
      val expiresOn: Long = authResponse.get("expires_on").asLong()

      val accessToken2 = new AccessToken(accessToken, OffsetDateTime.now().plusSeconds(expiresOn))
      accessToken2
    }
  }

  /** Get Access Token using the authentication method based on Managed Application
    *
    * @param evp parameters for the Event Grid
    * @param dbx a DatabricksUtils to get secrets from dbutils
    * @return an authenticated event grid client
    */
  def getAccessToken(
    evp: EventGridParams,
    dbx: DatabricksUtils
  ): Either[String, AccessToken] = {
    val authAudience = "https://eventgrid.azure.net"
    def formatError(prefix: String, e: Throwable): String = {
      e.printStackTrace()
      s"$prefix: ${e.getMessage}"
    }

    val maybeSecret = evp.serviceProviderAppSecretValue(dbx)
    val token1 = maybeSecret.flatMap { clientSecret =>
      authStep1(evp.serviceProviderTenantId, evp.serviceProviderAppId, clientSecret, evp.httpLogDetailLevel)
        .fold(
          e => Left(formatError(s"[event-grid] ERROR: failed step 1 auth on Service Provider", e)),
          Right(_)
        )
    }
    val token2 = token1.flatMap(t1 =>
      authStep2(evp.managedAppResourceId, authAudience, t1)
        .fold(
          e => Left(formatError("[event-grid] ERROR: failed step 2 auth on Managed Application", e)),
          Right(_)
        )
    )
    token2
  }

}
