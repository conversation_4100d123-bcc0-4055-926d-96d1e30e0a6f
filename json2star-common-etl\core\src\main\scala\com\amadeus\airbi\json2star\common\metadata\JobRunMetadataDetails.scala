package com.amadeus.airbi.json2star.common.metadata
import org.json4s.DefaultFormats
import org.json4s.jackson.Serialization.writePretty

/** Data quality metadata to add in the DETAILS column of the Job Run Metadata table.
  */
case class JobRunMetadataDetails(
  metrics: Map[String, AnyRef] = Map.empty
) {

  /** Add a new metric object to the job run metadata details.
    *
    * @param name metric object name
    * @param m if some object is passed, it is added to the metrics, otherwise nothing is done
    * @return
    */
  def withMetric(name: String, m: Option[AnyRef]): JobRunMetadataDetails = {
    m.map(metric => this.copy(metrics = this.metrics + (name -> metric))).getOrElse(this)
  }
}

object JobRunMetadataDetails {
  def asJson(d: JobRunMetadataDetails): String = {
    writePretty(d.metrics)(DefaultFormats)
  }

  def fromThrowable(t: Throwable): JobRunMetadataDetails = {
    JobRunMetadataDetails().withMetric("error", Some(t.getMessage))
  }
}
