package com.amadeus.airbi.rawvault.common.processors

import com.amadeus.airbi.rawvault.common.config.Blocks.{Block, CartesianBlock, JsonpathBlock}
import com.amadeus.airbi.rawvault.common.config.{BlockJsonPath, Blocks}
import BlockResults.{
  <PERSON>R<PERSON>ult,
  CartesianBlockR<PERSON>ult,
  JSON,
  JsonpathBlockResult
}
import com.amadeus.airbi.rawvault.common.vault.generators.{Attempt, ErrorMessage}
import com.jayway.jsonpath.spi.cache.{CacheProvider, NOOPCache}
import com.jayway.jsonpath.{Configuration, DocumentContext, JsonPath, PathNotFoundException, Option => JsonPathOption}

import java.util.Collections
import scala.annotation.{nowarn, tailrec}
import scala.util.{Failure, Success, Try}
import scala.collection.JavaConverters._

/** https://github.com/json-path/JsonPath
  */
object JsonProcessor {
  type JsonArray = net.minidev.json.JSONArray
  type JsonObject = java.util.LinkedHashMap[String, Any]

  val SINGLE_VALUE_CONFIGURATION: Configuration = {
    CacheProvider.setCache(new NOOPCache())
    Configuration.defaultConfiguration
      .addOptions(JsonPathOption.DEFAULT_PATH_LEAF_TO_NULL)
      // SUPPRESS_EXCEPTIONS avoids a costly exception-throw if jsonpath query finds no result
      // available since jayway:jsonpath:2.9.0 (not released yet)
      .addOptions(JsonPathOption.SUPPRESS_EXCEPTIONS)
  }

  def parseJsonObject(json: JsonObject, jpathConf: Configuration = SINGLE_VALUE_CONFIGURATION): DocumentContext = {
    JsonPath.using(jpathConf).parse(json)
  }

  def parseJsonString(json: String, jpathConf: Configuration = SINGLE_VALUE_CONFIGURATION): DocumentContext = {
    JsonPath.using(jpathConf).parse(json)
  }

  /** Read a json path from the [[DocumentContext]].
    * The return type depends on the configuration (ALWAYS_RETURN_LIST_CONFIGURATION, SINGLE_VALUE_CONFIGURATION) used
    * when generating the DocumentContext, and on the JsonProvider from the conf.
    *
    * For example, JsonSmartJsonProvider, which is the default JsonProvider, uses net.minidev.json.
    * So it should return (except in case of function path) either a net.minidev.json.JSONObject,
    * a net.minidev.json.JSONArray, or a net.minidev.json.JSONValue.
    *
    * @param jsonPath
    * @param jsonDocument
    * @return the attempt to read the field
    */
  def readJsonPath(jsonPath: BlockJsonPath, jsonDocument: DocumentContext): Try[Option[Any]] = {
    val attempt = Try {
      Option(jsonDocument.read[Any](jsonPath))
    }
    attempt.recover { case _: PathNotFoundException =>
      None
    }
  }

  /**
    * Generate a [[BlockResult]] tree from a query [[Blocks]] and a json
    * @param query in form of multi-level query
    * @param json parsed by jayway
    * @param jConf
    * @return the tree in form of [[BlockResult]]
    */
  def toBlockResultTree(
    query: Blocks,
    json: JSON,
    jConf: Configuration = JsonProcessor.SINGLE_VALUE_CONFIGURATION
  ): BlockResult = {
    BlockResults.root(
      j = json,
      c = calculateChildren(query, json, jConf)
    )
  }

  def calculateChildren(
    blocks: Blocks,
    json: JSON,
    jConf: Configuration
  ): Seq[Attempt[BlockResult]] = {
    val currBlock = blocks.headBlock
    currBlock match {
      case jQuery: JsonpathBlock =>
        val jsonChildren: Seq[Attempt[JSON]] = resolveJsonpathBlock(jQuery, json, jConf)
        val blockChildren: Seq[Attempt[BlockResult]] = jsonChildren.map {
          case Right(j) =>
            val leaf = !blocks.finerBlocks.isDefined
            val jpbr = JsonpathBlockResult(
              alias = jQuery.alias,
              path = jQuery.path,
              json = j,
              children = blocks.finerBlocks.toSeq.flatMap(calculateChildren(_, j, jConf)),
              leaf = leaf
            )
            Right(jpbr)
          case Left(e) =>
            Left[ErrorMessage, BlockResult](e)
        }
        blockChildren
      case cQuery: CartesianBlock if !blocks.finerBlocks.isDefined =>
        val c: List[Seq[Attempt[BlockResult]]] = cQuery.cartesian.map { jQuery =>
          calculateChildren(jQuery, json, jConf)
        }
        Seq(Right[ErrorMessage, BlockResult](CartesianBlockResult(c)))
      case _: CartesianBlock if blocks.finerBlocks.isDefined =>
        throw new IllegalArgumentException(s"Can't have other blocks after a CartesianBlock: ${blocks.finerBlocks}")
      case _ => throw new IllegalArgumentException("Not supported yet")
    }
  }

  private def resolveJsonpathBlock(block: JsonpathBlock, json: JSON, jConf: Configuration) = {
    val jsons: Seq[Attempt[JsonObject]] = explodeJsonObjects(block, json)
    val currLevelDocs: Seq[Attempt[JSON]] = jsons.map {
      case Right(js) => Right(JsonProcessor.parseJsonObject(js, jConf))
      case Left(msg) => Left(msg)
    }
    currLevelDocs
  }

  private def explodeJsonObjects(block: JsonpathBlock, document: DocumentContext): Seq[Attempt[JsonObject]] = {
    // will only use the head of the Blocks instance, the rest is for log messages only
    val readAttempt: Try[Option[Any]] = JsonProcessor.readJsonPath(
      block.path,
      document
    )
    val explodeIfArrayAttempt: Seq[Attempt[JsonObject]] = readAttempt match {
      case Success(Some(array: JsonArray)) =>
        val onlyObjects = {
          array.asScala.collect {
            // keep the object in the array as is
            case obj: JsonObject @nowarn => Right(obj)
            // transform the primitive type in the array into a json object with a single "value" field
            case p if p != null => Right(new JsonObject(Collections.singletonMap("value", p)))
            // discard recursively nested arrays
            case jarray: JsonArray => Left(s"Unsupported type of: '${jarray}' (parsing alias '${block.alias}')")
            // discard any other types in the array (like inner arrays, null values, etc.)
            case unsupportedType => Left(s"Unsupported array type (alias '${block.alias}'): '${unsupportedType}'")
          }
        }
        onlyObjects
      case Success(Some(jsonObj: JsonObject @nowarn)) =>
        Seq(Right(jsonObj))
      case Success(Some(v)) =>
        //To enable case a $.value returns a field directly in the mapping
        Seq(Right(new JsonObject(Collections.singletonMap("value", v))))
      case Success(None) =>
        Seq(Left(s"No result found at '${block}'"))
      case Failure(f) =>
        Seq(Left(s"Error while parsing at '${block}': ${f.getMessage}"))
    }
    explodeIfArrayAttempt
  }

  /** Define by a given criteria (expressed as a jsonpath) if the provided json is eligible or not
    */
  def isEligible(json: DocumentContext, criteria: String): Boolean = {
    val r = json.read[Any](criteria)
    // if result is not empty (by below definition) isEligibleRecord=true
    val result = r match {
      case jobj: JsonObject @nowarn => true // if result is a json object, eligible
      case jarray: JsonArray => !jarray.isEmpty // if result is an array, eligible iff array is not empty
      case p if p != null => true // if result is something else, eligible iff not null
      case _ => false // otherwise NOT eligible
    }
    result
  }

}
