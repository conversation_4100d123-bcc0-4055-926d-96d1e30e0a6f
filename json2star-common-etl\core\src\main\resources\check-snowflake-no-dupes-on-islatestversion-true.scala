import java.time.OffsetDateTime
import scala.collection.mutable.ListBuffer
import com.databricks.dbutils_v1.DBUtilsHolder.dbutils
import com.amadeus.airbi.json2star.common.validation.config.{ValidationRecord,ValidationConf}
import com.amadeus.airbi.json2star.common.config.SnowflakeParams
import org.apache.spark.sql.functions.col


import net.snowflake.spark.snowflake.Utils

case class Myresult(
                     name: String,
                     liste_pk: List[String]
                   )

case class MyQuery(
                    name: String,
                    query: String
                  )

val currentTimestamp = OffsetDateTime.now().toString
val task = dbutils.notebook().getContext().notebookPath.get.split("/").last
val vConfig = ValidationConf.apply(dbutils)

val SNOWFLAKE_SOURCE_NAME = "net.snowflake.spark.snowflake"

val phase = vConfig.phase
val customer = vConfig.appConfig.common.shard
val domain = vConfig.appConfig.common.domain
val domainVersion = vConfig.appConfig.common.domainVersion
val validationDb = vConfig.validationDatabase
val validationTable = vConfig.validationTable

val snowflakeParams = vConfig.appConfig.snowflakeParams match {
  case None => throw new IllegalArgumentException(s"AppConfigFile has missing parameters for snowflake")
  case Some(s) => s
}

val sfOptions = snowflakeParams.getSnowflakeOptions(dbutils)
val sfDatabase = sfOptions("sfDatabase")
val sfSchema = sfOptions("sfSchema").toUpperCase

val listTables = sqlContext.read
  .format(SNOWFLAKE_SOURCE_NAME)
  .options(sfOptions)
  .option(
    "query",
    s"""
    select table_name
    from ${sfDatabase}.information_schema.tables
    where table_schema='${sfSchema}';
    """
  )
  .load()
  .filter(col("table_name").endsWith("HISTO") and !col("table_name").startsWith("STAGING"))

val listPkByTable = listTables.map(r => {
  val name = r.getString(0)
  val table = Utils.runQuery(
    sfOptions,
    s"""
     describe table ${sfSchema}.${name};
     """
  )
  var liste_pk: List[String] = List.empty
  while (table.next) {
    val pk = table.getString("primary key").equalsIgnoreCase("Y")
    val cname = table.getString("name")
    if (pk & !cname.toUpperCase().startsWith("VERSION"))
      liste_pk = liste_pk ++ List(s"${cname}")
  }
  (name -> liste_pk)
}).map(r => Myresult(r._1, r._2))

val listQueryByTable = listPkByTable.map(row => {
  val name = row.name
  val list_pk = row.liste_pk
  val string_pk = list_pk.mkString(", ")
  val query = s"""
    select  ${string_pk}, count(*) as cpt from ${sfSchema}.${name}
    where is_last_version = true
    group by ${string_pk}
    having cpt >1
  """
  (name, query)
}).map(r => MyQuery(r._1, r._2))

val resultsByTable = listQueryByTable
  .map(r => {
    val q = s"""select count(*) as A_COUNT from (${r.query}) """
    val cpt = Utils.runQuery(sfOptions, q)
    cpt.next

    (r.name, cpt.getLong("A_COUNT"))
  }).collect

val listFailingTables = resultsByTable.filter(r => r._2>0 )

val testRecord = ValidationRecord(
  domain,
  domainVersion,
  customer,
  phase,
  currentTimestamp,
  task,
  s"Test 1/1 - Check, in SnowFlake ${sfSchema}, tables with records having multiple versions with isLatestVersion TRUE",
  listFailingTables.size == 0,
  listFailingTables.size,
  resultsByTable.size,
  listFailingTables.size.toFloat / resultsByTable.size,
  listFailingTables.map(r => r._1 + " => "  +r._2).mkString(", ")
)

val df = Seq(testRecord).toDF()
df.withColumn("fail_ratio", $"fail_ratio".cast("Decimal(3,2)"))
  .write
  .insertInto(s"${validationDb}.${validationTable}")