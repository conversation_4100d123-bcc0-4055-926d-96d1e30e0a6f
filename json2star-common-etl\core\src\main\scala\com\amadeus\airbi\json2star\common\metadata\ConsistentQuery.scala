package com.amadeus.airbi.json2star.common.metadata

import io.delta.tables.DeltaTable
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{DataFrame, SparkSession}

/** Sample trait for a star schema consumer query
  * Note: it has been migrated from https://rndwww.nce.amadeus.net/git/projects/DDL/repos/raw-vault-common-etl/browse/src/main/scala/com/amadeus/airbi/rawvault/common/metadata/VaultQuery.scala
  *
  * It uses the job run metadata to consume only consistent table entries.
  * For an example of concrete implementation see the ConsistentQuerySpec.
  */
trait ConsistentQuery {

  val spark: SparkSession

  val pivotTableName: String // the table the consumer streams from

  val checkpointLocation: String // path to the checkpoints used to stream the pivot table

  val domain: String

  /** Method to be provided by classes implementing this trait.
    *
    * It contains the the specific logic of the query.
    *
    * @param batch new lines from the pivot table
    * @param endDate end validity date for the current batch, to be used to read stable versions of other tables
    */
  def query(batch: DataFrame, endDate: String): Unit

  /** Get the latest stable version of a table before a given end date.
    */
  def readLastStableVersionBeforeEnd(deltaTableName: String)(endDate: String): DataFrame = {
    val tableHistory = DeltaTable.forName(deltaTableName).history()
    val maxVersionBeforeEnd = tableHistory.filter(s"timestamp<'$endDate'").agg(max("version")).first().getLong(0)
    val df = spark.read
      .format("delta")
      .option("versionAsOf", maxVersionBeforeEnd)
      .table(deltaTableName)
    df
  }

  /** Entry point of a vault query
    */
  def runQuery(): Unit = {
    spark.readStream
      .format("delta")
      .option("ignoreChanges", "true")
      .table("METADATA_JOB_RUN")
      .writeStream
      .option("checkpointLocation", checkpointLocation)
      .foreachBatch(readDataInPeriod _)
      .trigger(org.apache.spark.sql.streaming.Trigger.AvailableNow)
      .start()
      .awaitTermination()
  }

  private def readDataInPeriod(b: DataFrame, batchId: Long): Unit = {
    val finished = b.filter(s"DOMAIN='$domain' and STATUS = 'FINISHED'")
    if (!finished.isEmpty) {
      val batchMinMax = finished
        .agg(min("START_TIMESTAMP").alias("MIN_TIMESTAMP"), max("END_TIMESTAMP").alias("MAX_TIMESTAMP"))
        .select("MIN_TIMESTAMP", "MAX_TIMESTAMP")
      // batchMinMax.show(false)
      val f = batchMinMax.first()
      val start = f.getTimestamp(0).toString
      val end = f.getTimestamp(1).toString
      readChangesInPeriod(start, end)
    }
  }

  private def readChangesInPeriod(startDate: String, endDate: String): Unit = {
    val minMaxCommitTableTimestamp = spark.read
      .format("delta")
      .option("readChangeFeed", "true")
      .option("ignoreChanges", "true")
      .option("startingTimestamp", startDate)
      .table(pivotTableName)
      .filter(s"_commit_timestamp < '$endDate'")
      .agg(min("_commit_timestamp").alias("min"), max("_commit_timestamp").alias("max"))

    // minMaxCommitTableTimestamp.show(false)

    val f = minMaxCommitTableTimestamp.first()
    val minCommitTableTimestamp = f.getTimestamp(0).toString
    val maxCommitTableTimestamp = f.getTimestamp(1).toString

    val pivotDf = spark.read
      .format("delta")
      .option("readChangeFeed", "true")
      .option("ignoreChanges", "true")
      .option("startingTimestamp", minCommitTableTimestamp)
      .option("endingTimestamp", maxCommitTableTimestamp)
      .table(pivotTableName)
      .filter("_change_type = 'insert' or _change_type = 'update_postimage'") // check if needed
      .as("stream")

    // here you can do a query using the pivot table
    query(pivotDf, endDate)
  }

}
