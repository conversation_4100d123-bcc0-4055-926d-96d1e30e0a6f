package com.amadeus.airbi.rawvault.common.application.config

import com.amadeus.airbi.json2star.common.addons.stackable.StackableAddonConfig

/** Configuration representing a table (as from pureconfig)
  */
case class TableConfig(
  name: String,
  zorderColumns: List[String] = List.empty[String],
  // TODO: mapping, latest and correlation should be part of an `addons` case class
  mapping: Option[MappingConfig] = None,
  latest: Option[LatestConfig] = None,
  correlation: Option[Correlation.AssoTable] = None,
  sourceCorrelation: Option[SourceCorrelation.AssoTable] = None,
  tableSnowflake: Option[TableSnowflake] = None,
  tableSelectors: Either[Set[Set[String]], Set[String]] = Right(Set()),
  stackableAddons: List[StackableAddonConfig] = List.empty ,
  prefiller: List[DimPrefiller] = List.empty, // list because some tables are filled by multiple files
  subdomain: Option[String] = None,
  subdomainMainTable: Option[Boolean] = None
) {
  def isSelected(tablesSelectors: Set[String]): Boolean = {
    val selected = if (tableSelectors.merge.isEmpty) {
      true
    } else {
      tableSelectors match {
        case Right(selectorSimpleSet) => selectorSimpleSet.subsetOf(tablesSelectors)
        case Left(selectorOrSets) => selectorOrSets.foldLeft(false)((res, selectorSet) =>
          res || selectorSet.subsetOf(tablesSelectors))
      }
    }
    selected
  }
}
