package com.amadeus.airbi.json2star.common.addons.stackable.proration

import com.amadeus.ti.models.cdb.NumericAmount

object ProrationColumns {

  val DOCUMENT_CREATION_DATE = "DOCUMENT_CREATION_DATE"
  val SEQUENCE_NUMBER = "SEQUENCE_NUMBER"
  val SOLD_DEPARTURE_AIRPORT = "SOLD_DEPARTURE_AIRPORT"
  val SOLD_ARRIVAL_AIRPORT = "SOLD_ARRIVAL_AIRPORT"
  val ESTIMATED_PRORATED_FARE_ORIGINAL = "ESTIMATED_PRORATED_FARE_ORIGINAL"
  val ESTIMATED_PRORATED_FARE_CURRENCY_ORIGINAL = "ESTIMATED_PRORATED_FARE_CURRENCY_ORIGINAL"
  val ESTIMATED_PRORATED_FARE_ALGORITHM = "ESTIMATED_PRORATED_FARE_ALGORITHM"
  val INTERNAL_PRORATION_METADATA = "INTERNAL_PRORATION_METADATA"

  val inputCols: Seq[String] = List(
    DOCUMENT_CREATION_DATE,
    SEQUENCE_NUMBER,
    SOLD_DEPARTURE_AIRPORT,
    SOLD_ARRIVAL_AIRPORT
  )

  val outputCols: Seq[String] = List(
    ESTIMATED_PRORATED_FARE_ORIGINAL,
    ESTIMATED_PRORATED_FARE_CURRENCY_ORIGINAL,
    ESTIMATED_PRORATED_FARE_ALGORITHM,
    INTERNAL_PRORATION_METADATA
  )

  val mandatoryCols: Seq[String] = inputCols ++ outputCols

  def toCols(na: Option[NumericAmount], algoValue: String, meta: Option[ProrationMetadata]): Map[String, String] = {
    Map(
      ESTIMATED_PRORATED_FARE_ORIGINAL -> na.map(_.toDecimal.mkString).orNull,
      ESTIMATED_PRORATED_FARE_CURRENCY_ORIGINAL -> na.map(_.currency.code).orNull,
      ESTIMATED_PRORATED_FARE_ALGORITHM -> algoValue,
      INTERNAL_PRORATION_METADATA -> meta.map(ProrationMetadata.asJsonString).orNull
    )
  }
}
