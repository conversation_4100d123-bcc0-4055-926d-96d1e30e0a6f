package com.amadeus.airbi.json2star.common.addons.base.mapping.input

import com.amadeus.airbi.datalake.common.spark.SparkSqlSpecification
import com.amadeus.airbi.json2star.common.addons.base.mapping.{MappingAddonParams, Table}
import com.amadeus.airbi.json2star.common.addons.base.mapping.MappingAddonParams.{AutoloaderStart, StartConfig, StaticStart}
import com.amadeus.airbi.json2star.common.addons.base.mapping.Table.{Stats, TableRowType}
import com.amadeus.airbi.json2star.common.addons.base.mapping.input.InputFormat.InputDataFrame
import com.amadeus.airbi.json2star.common.addons.base.mapping.input.checkpoints.StartCheckpointsTable
import com.amadeus.airbi.json2star.common.addons.base.mapping.input.checkpoints.StartCheckpointsTable.ImmutableStartConfig
import com.amadeus.airbi.json2star.common.addons.base.mapping.input.checkpoints.StaticStartStep.FormatterYyyyMmDd
import com.amadeus.airbi.json2star.common.resize.DatabricksUtils
import com.amadeus.airbi.json2star.common.testfwk.MockDatabricksUtils
import com.amadeus.airbi.rawvault.common.testfwk.TmpDir
import org.apache.spark.sql.functions.lit
import org.apache.spark.sql.streaming.{DataStreamReader, Trigger}
import org.apache.spark.sql.{DataFrame, SparkSession}
import org.scalatest.BeforeAndAfterEach

import java.nio.file.{Files, Paths}
import java.sql.Timestamp
import java.time.{Instant, LocalDate}
import java.util.TimeZone

class StreamExecutorInputFormatSpec extends SparkSqlSpecification with BeforeAndAfterEach with TmpDir {
  TimeZone.setDefault(TimeZone.getTimeZone("UTC"))

  val Regex = ".*"
  val Day20: LocalDate = LocalDate.of(2024, 12, 20)

  def day20Minus(days: Int): LocalDate = Day20.minusDays(days)
  def day20Plus(days: Int): LocalDate = Day20.plusDays(days)
  def asPath(d: LocalDate): String = d.format(FormatterYyyyMmDd)

  override def beforeAll: Unit = {
    super.beforeAll
    spark.conf.set("spark.databricks.delta.allowArbitraryProperties.enabled", "true")
  }

  /** Test input class to mock autoloader locally
    *
    * It takes N files as input, and returns a DF containing the path to such files (instead of its content)
    */
  class InputFormatTest(
    startModeConf: StartConfig,
    spark: SparkSession,
    ts: Timestamp = Timestamp.valueOf(Day20.atStartOfDay())
  ) extends InputFormat with StreamExecutorInputFormat {
    //var streamingStarted: Option[Seq[String]] = None
    override def streamingDataFrameFrom(streamReader: DataStreamReader, paths: Seq[String]): InputDataFrame = {
      //streamingStarted = Some(paths)
      streamReader.format("rate-micro-batch").option("rowsPerBatch", 1).load().withColumn("files", lit(paths.mkString(","))) // any streaming dataframe
    }
    override def sparkSession: SparkSession = spark
    override def startMode: MappingAddonParams.StartConfig = startModeConf
    override def dbx: DatabricksUtils = new MockDatabricksUtils(2, "test-cluster", spark)
    override def pathsToDf(f: Seq[String]): DataFrame = {
      import spark.implicits._
      f.toDF("files")
    }
    override def nowTimestamp: Timestamp = ts
  }

  def processBatches(paths: Seq[String], checkpointDir: String, inputFormat: InputFormat): List[(String, Seq[String])] = {
    import spark.implicits._
    var batches: List[(String, Seq[String])] = List()
    inputFormat.processBatches(
      streamReader = spark.readStream,
      paths = paths, // Seq(dataBaseDir),
      checkpointLocation = checkpointDir,
      trigger = Trigger.AvailableNow(),
      generateTables = (df: InputDataFrame) => df.map(r => Table("table", Seq(Seq(r.getAs[String]("files"))), Stats.Zero)),
      processBatchFunction = (ds, id) => {
        val files: Seq[String] = ds.collect().flatMap(_.rows.head)
        batches = batches :+ (id, files)
      }
    )
    batches.toList
  }

  "StaticStartInputFormat" should "launch static start and then keep streaming" in withStrTmpDir { tmpDir =>
    val checkpointDir = tmpDir + "/checkpoint"
    val dataBaseDir = tmpDir + "/data"
    val inputFormat1 = new InputFormatTest(StaticStart(Regex, 2, None, None), spark, Timestamp.valueOf(Day20.atStartOfDay()))
    createDirectoriesDay16ToDay20(dataBaseDir)

    val batches1 = processBatches(Seq(dataBaseDir), checkpointDir, inputFormat1)

    // checks on the table
    val table = new StartCheckpointsTable(spark, checkpointDir)
    table.runQuery(Some("ROW_TYPE = 'BATCH_ROW'")).count() shouldBe 2
    table.runQuery(Some("ROW_TYPE = 'CONFIG_ROW'")).count() shouldBe 1
    table.runQuery(Some("ROW_TYPE = 'AUTOLOADER_ROW'")).count() shouldBe 0

    batches1 shouldBe List(
      "B0" -> Seq(
        dataBaseDir + s"/0/${asPath(day20Minus(4))}/*/*",
        dataBaseDir + s"/0/${asPath(day20Minus(3))}/*/*"
      ), // BATCH STATIC
      "B1" -> Seq(
        dataBaseDir + s"/0/${asPath(day20Minus(2))}/*/*",
        dataBaseDir + s"/0/${asPath(day20Minus(1))}/*/*"
      ), // BATCH STATIC
      "S0" -> Seq(dataBaseDir + "/0/2024/12/{20,21,22,23,24,25,26,27,28,29,30,31}") // STREAMING
    )

    // NEXT DAY

    val nextDay = Timestamp.valueOf(day20Plus(1).atStartOfDay())
    val inputFormat2 = new InputFormatTest(StaticStart(Regex, 2, None, None), spark, nextDay)
    val batches2 = processBatches(Seq(dataBaseDir), checkpointDir, inputFormat2)
    batches2 shouldBe List(
      "S1" -> Seq(dataBaseDir + "/0/2024/12/{20,21,22,23,24,25,26,27,28,29,30,31}") // STREAMING
    )
    table.runQuery(Some("ROW_TYPE = 'AUTOLOADER_ROW'")).count() shouldBe 0

    // NEXT MONTH

    val nextMonth = Timestamp.valueOf(day20Plus(20).atStartOfDay())
    val inputFormat3 = new InputFormatTest(StaticStart(Regex, 2, None, None), spark, nextMonth)
    val batches3 = processBatches(Seq(dataBaseDir), checkpointDir, inputFormat3)
    batches3 shouldBe List(
      "S2" -> Seq(dataBaseDir + "/0/2024/12/{20,21,22,23,24,25,26,27,28,29,30,31}"), // STREAMING
      "S3" -> Seq(dataBaseDir + "/0/2025/01/") // STREAMING
    )
    table.runQuery(Some("ROW_TYPE = 'AUTOLOADER_ROW'")).count() shouldBe 1

    // NEXT MONTH + 1 day

    val nextMonthPlus1 = Timestamp.valueOf(day20Plus(21).atStartOfDay())
    val inputFormat4 = new InputFormatTest(StaticStart(Regex, 2, None, None), spark, nextMonth)
    val batches4 = processBatches(Seq(dataBaseDir), checkpointDir, inputFormat4)
    batches4 shouldBe List(
      "S4" -> Seq(dataBaseDir + "/0/2025/01/") // STREAMING
    )
    table.runQuery(Some("ROW_TYPE = 'AUTOLOADER_ROW'")).count() shouldBe 1

  }

  it should "launch static start with a given start date and then keep streaming" in withStrTmpDir { tmpDir =>
    val checkpointDir = tmpDir + "/checkpoint"
    val dataBaseDir = tmpDir + "/data"
    val startDate = day20Minus(3)
    val inputFormat = new InputFormatTest(StaticStart(Regex, 2, Some(startDate), None), spark)
    createDirectoriesDay16ToDay20(dataBaseDir)
    // day 20

    import spark.implicits._

    var batches: List[(String, Seq[String])] = List()
    inputFormat.processBatches(
      streamReader = spark.readStream,
      paths = Seq(dataBaseDir),
      checkpointLocation = checkpointDir,
      trigger = Trigger.AvailableNow(),
      getTables = (df: InputDataFrame) => df.map(r => Table("table", Seq(Seq(r.getAs[String]("files"))), Stats.Zero)),
      processBatchFunction = (ds, id) => {
        val files: Seq[String] = ds.collect().flatMap(_.rows.head)
        batches = batches :+ (id, files)
      }
    )

    // checks on the table
    val table = new StartCheckpointsTable(spark, checkpointDir)
    table.runQuery(Some("ROW_TYPE = 'BATCH_ROW'")).count() shouldBe 2
    table.runQuery(Some("ROW_TYPE = 'CONFIG_ROW'")).count() shouldBe 1

    batches shouldBe List(
      "B0" -> Seq(
        dataBaseDir + s"/0/${asPath(day20Minus(3))}/*/*",
        dataBaseDir + s"/0/${asPath(day20Minus(2))}/*/*"
      ), // BATCH STATIC
      "B1" -> Seq(
        dataBaseDir + s"/0/${asPath(day20Minus(1))}/*/*"
      ), // BATCH STATIC
      "S0" -> Seq(dataBaseDir + "/0/2024/12/{20,21,22,23,24,25,26,27,28,29,30,31}") // STREAMING
    )
  }

  it should "launch static start recovering from a failure and then keep streaming" in withStrTmpDir { tmpDir =>
    val checkpointDir = tmpDir + "/checkpoint"
    val dataBaseDir = tmpDir + "/data"
    val startConfig = StaticStart(Regex, 2, None, None)
    val inputFormat = new InputFormatTest(startConfig, spark)
    createDirectoriesDay16ToDay20(dataBaseDir)
    // day 20

    // simulate the state of the table after 1 successful batch + a failure
    val table = new StartCheckpointsTable(spark, checkpointDir)

    val firstBatchDays = Seq(
      dataBaseDir + s"/0/${asPath(day20Minus(4))}/*/*",
      dataBaseDir + s"/0/${asPath(day20Minus(3))}/*/*"
    ).mkString(",")

    table.logBatch(
      firstProcessedDate = day20Minus(4),
      lastProcessedDate = day20Minus(3),
      inputDates = firstBatchDays,
      startTimestamp = Timestamp.from(Instant.now()),
      endTimestamp = Timestamp.from(Instant.now().plusSeconds(1)),
      staticStartFinished = false,
      config = ImmutableStartConfig.from(startConfig, Seq(dataBaseDir))
    )
    table.runQuery(Some("ROW_TYPE = 'BATCH_ROW'")).count() shouldBe 1

    import spark.implicits._
    // Start processing

    var batches: List[(String, Seq[String])] = List()
    inputFormat.processBatches(
      streamReader = spark.readStream,
      paths = Seq(dataBaseDir),
      checkpointLocation = checkpointDir,
      trigger = Trigger.AvailableNow(),
      getTables = (df: InputDataFrame) => df.map(r => Table("table", Seq(Seq(r.getAs[String]("files"))), Stats.Zero)),
      processBatchFunction = (ds, id) => {
        val files: Seq[String] = ds.collect().flatMap(_.rows.head)
        batches = batches :+ (id, files)
      }
    )

    // checks on the table after the process
    table.runQuery(Some("ROW_TYPE = 'CONFIG_ROW'")).count() shouldBe 1
    table.runQuery(Some("ROW_TYPE = 'BATCH_ROW'")).count() shouldBe 2

    batches shouldBe List(
      "B0" -> Seq(
        dataBaseDir + s"/0/${asPath(day20Minus(2))}/*/*",
        dataBaseDir + s"/0/${asPath(day20Minus(1))}/*/*"
      ), // BATCH STATIC
      "S0" -> Seq(dataBaseDir + "/0/2024/12/{20,21,22,23,24,25,26,27,28,29,30,31}") // STREAMING
    )
  }

  it should "not launch autoloader starting from a finished static start with fixed end date" in withStrTmpDir {
    tmpDir =>
      val checkpointDir = tmpDir + "/checkpoint"
      val dataBaseDir = tmpDir + "/data"
      createDirectoriesDay16ToDay20(dataBaseDir)
      // day 20

      // In this test we do:
      // - Part 1: a static start with fixed end date, only one batch, then a failure
      // - Part 2: after the failure we do the missing batch, but no streaming should take place here
      // - Part 3: rerunning the application will yield no operation

      val table = new StartCheckpointsTable(spark, checkpointDir)
      val endDate = day20Minus(2)
      val startConfig = StaticStart(Regex, 2, None, Some(endDate))
      val inputFormat = new InputFormatTest(startConfig, spark)

      // Part 1
      val firstBatchDays = Seq(
        dataBaseDir + s"/0/${asPath(day20Minus(4))}/*/*",
        dataBaseDir + s"/0/${asPath(day20Minus(3))}/*/*"
      ).mkString(",")

      table.logBatch(
        firstProcessedDate = day20Minus(4),
        lastProcessedDate = day20Minus(3),
        inputDates = firstBatchDays,
        startTimestamp = Timestamp.from(Instant.now()),
        endTimestamp = Timestamp.from(Instant.now().plusSeconds(1)),
        staticStartFinished = false,
        config = ImmutableStartConfig.from(startConfig, Seq(dataBaseDir))
      )
      table.runQuery(Some("ROW_TYPE = 'BATCH_ROW'")).count() shouldBe 1

      import spark.implicits._

      // Part 2
      var batches: List[(String, Seq[String])] = List()
      inputFormat.processBatches(
        streamReader = spark.readStream,
        paths = Seq(dataBaseDir),
        checkpointLocation = checkpointDir,
        trigger = Trigger.AvailableNow(),
        getTables = (df: InputDataFrame) => df.map(r => Table("table", Seq(Seq(r.getAs[String]("files"))), Stats.Zero)),
        processBatchFunction = (ds, id) => {
          val files: Seq[String] = ds.collect().flatMap(_.rows.head)
          batches = batches :+ (id, files)
        }
      )

      // checks on the table
      table.runQuery(Some("ROW_TYPE = 'BATCH_ROW'")).count() shouldBe 2
      table.runQuery(Some("ROW_TYPE = 'CONFIG_ROW'")).count() shouldBe 1

      batches shouldBe List(
        "B0" -> Seq(
          dataBaseDir + s"/0/${asPath(day20Minus(2))}/*/*"
        ) // BATCH STATIC
        // "S0" -> Seq() no entry like this for streaming is done here
      )

      // Part 3
      var newBatches: List[(Long, Seq[String])] = List()
      val newInputFormat = new InputFormatTest(startConfig, spark)
      newInputFormat.processBatches(
        streamReader = spark.readStream,
        paths = Seq(dataBaseDir),
        checkpointLocation = checkpointDir,
        trigger = Trigger.AvailableNow(),
        getTables = (df: InputDataFrame) => df.map(r => Table("table", Seq(Seq(r.getAs[String]("files"))), Stats.Zero)),
        processBatchFunction = (ds, id) => {
          val files: Seq[String] = ds.collect().flatMap(_.rows.head)
          batches = batches :+ (id, files)
        }
      )
      // no new batch
      newBatches shouldBe List.empty
      // still 2 batches in the table
      table.runQuery(Some("ROW_TYPE = 'BATCH_ROW'")).count() shouldBe 2
      // no streaming done
      //inputFormat.streamingStarted shouldBe None
  }

  it should "do only streaming when launched with autoloader start" in withStrTmpDir { tmpDir =>
    val checkpointDir = tmpDir + "/checkpoint"
    val dataBaseDir = tmpDir + "/data"
    val inputFormat = new InputFormatTest(AutoloaderStart(), spark)
    createDirectoriesDay16ToDay20(dataBaseDir)
    import spark.implicits._

    var batches: List[(String, Seq[String])] = List()
    inputFormat.processBatches(
      streamReader = spark.readStream,
      paths = Seq(dataBaseDir + "/0/*/*"),
      checkpointLocation = checkpointDir,
      trigger = Trigger.AvailableNow(),
      getTables = (df: InputDataFrame) => df.map(r => Table("table", Seq(Seq(r.getAs[String]("files"))), Stats.Zero)),
      processBatchFunction = (ds, id) => {
        val files: Seq[String] = ds.collect().flatMap(_.rows.head)
        batches = batches :+ (id, files)
      }
    )

    // checks on the table
    val table = new StartCheckpointsTable(spark, checkpointDir)
    table.runQuery(Some("ROW_TYPE = 'BATCH_ROW'")).count() shouldBe 0
    table.runQuery(Some("ROW_TYPE = 'CONFIG_ROW'")).count() shouldBe 1

    batches shouldBe List(
      "S0" -> Seq(dataBaseDir + "/0/*/*") // STREAMING only
    )
  }

  //

  private def createDirectoriesDay16ToDay20(dataBaseDir: String) = {
    Files.createDirectories(Paths.get(dataBaseDir + s"/0/${asPath(day20Minus(4))}/00/00/a.avro"))
    Files.createDirectories(Paths.get(dataBaseDir + s"/0/${asPath(day20Minus(3))}/00/00/a.avro"))
    Files.createDirectories(Paths.get(dataBaseDir + s"/0/${asPath(day20Minus(2))}/00/00/a.avro"))
    Files.createDirectories(Paths.get(dataBaseDir + s"/0/${asPath(day20Minus(1))}/00/00/a.avro"))
    Files.createDirectories(Paths.get(dataBaseDir + s"/0/${asPath(day20Minus(0))}/00/00/a.avro")) // day 20
  }

}
