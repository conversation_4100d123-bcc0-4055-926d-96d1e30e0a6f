package com.amadeus.airbi.json2star.common.eventgrid

import com.amadeus.airbi.json2star.common.app.Display.display
import com.amadeus.airbi.json2star.common.eventgrid.EventPublisher.logger
import com.amadeus.airbi.json2star.common.metadata.JobRunMetadataTable
import com.amadeus.airbi.json2star.common.resize.DatabricksUtils
import com.amadeus.airbi.rawvault.common.RootConfig
import com.typesafe.scalalogging.Logger
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{DataFrame, SparkSession}
import org.slf4j.LoggerFactory

import java.net.HttpURLConnection.HTTP_OK
import scala.util.{Failure, Success, Try}

case class EventPublisher(
  conf: RootConfig,
  spark: SparkSession,
  dbxUtils: DatabricksUtils,
  accessTokenProvider: AccessTokenProvider
) {

  private val domain: String = conf.etl.common.domain
  private val containerName: String = conf.etl.common.outputContainerName.getOrElse(
    throw new RuntimeException(
      "The 'common.output-container-name' has no container name. Check the application.conf file" +
        "Expected: abfss://<container>@<storage-account>.dfs.core.windows.net/"
    )
  )

  // Checkpoint path to read metadata table
  private val checkpointLocation =
    s"${conf.etl.stream.sink.checkpointLocation}/EventPublisher_from_${JobRunMetadataTable.Name}"

  def eventGridClient(): EventGridClient = EventGridClient(conf.eventGridParams.get, dbxUtils, accessTokenProvider)

  def clientSend(event: CloudEvent): Unit = {
    val response = eventGridClient().send(event)
    response match {
      case Failure(exception) =>
        val errorMsg = s"[event-grid] ERROR: ${exception.getMessage}"
        display(errorMsg, conf, logger)
        exception.printStackTrace()
        throw new RuntimeException(errorMsg)
      case Success(answer) if answer.getStatusLine.getStatusCode != HTTP_OK =>
        val errorMsg =
          s"[event-grid] ERROR: response is ${answer.getStatusLine.getStatusCode} ${answer.getEntity.getContent}"
        display(errorMsg, conf, logger)
        throw new RuntimeException(errorMsg)
      case _ =>
        display(s"[event-grid] FINISHED: notification is sent to ${conf.eventGridParams.get.topicUrl}", conf, logger)
    }
  }

  def sendEvent(): Unit = {
    spark.readStream
      .format("delta")
      .option("ignoreChanges", "true")
      .table(JobRunMetadataTable.Name)
      .writeStream
      .option("checkpointLocation", checkpointLocation)
      .foreachBatch(readDataAndSend _)
      .trigger(org.apache.spark.sql.streaming.Trigger.AvailableNow)
      .start()
      .awaitTermination()
  }

  private def readDataAndSend(b: DataFrame, batchId: Long): Unit = {
    val finished = b.filter(
      s"${JobRunMetadataTable.Columns.DOMAIN}='$domain' and ${JobRunMetadataTable.Columns.STATUS} = '${JobRunMetadataTable.Status.FINISHED}'"
    )
    if (!finished.isEmpty) {
      val batchMinMax = finished
        .agg(
          min(JobRunMetadataTable.Columns.DATA_START_TIMESTAMP).alias("MIN_TIMESTAMP"),
          max(JobRunMetadataTable.Columns.END_TIMESTAMP).alias("MAX_TIMESTAMP")
        )
        .select("MIN_TIMESTAMP", "MAX_TIMESTAMP")
      val f = batchMinMax.first()
      val start = f.getTimestamp(0)
      val end = f.getTimestamp(1)

      val lastJobRun = finished
        .filter(
          s"${JobRunMetadataTable.Columns.END_TIMESTAMP} = '${end}'"
        )
        .select(JobRunMetadataTable.Columns.JOB_ID, JobRunMetadataTable.Columns.JOB_RUN_ID)
        .first()

      val eventData = JDFEventData(
        conf = conf,
        jobId = lastJobRun.getString(0),
        jobRunId = lastJobRun.getString(1),
        startTimestamp = start,
        endTimestamp = end
      )

      val event = CloudEvent(subject = containerName, source = conf.eventGridParams.get.topicName, eventData)
      clientSend(event)
    }
  }
}

object EventPublisher {
  lazy val logger: Logger = Logger(LoggerFactory.getLogger(getClass.getName))

  def run(conf: RootConfig, spark: SparkSession, dbx: DatabricksUtils, atp: AccessTokenProvider): Unit = {
    conf.eventGridParams match {
      case Some(_) =>
        Try {
          val eventPublisher = EventPublisher(conf, spark, dbx, atp)
          eventPublisher.sendEvent()
        } match {
          case Success(_) => logger.info("EventPublisher finished")
          case Failure(e) =>
            if (conf.eventGridParams.get.isBlocking) {
              throw e
            }
            logger.error("EventPublisher failed", e)
        }
      case None =>
        logger.info(s"EventPublisher is disabled. Set 'event-grid-params' to enable it")
    }
  }
}
