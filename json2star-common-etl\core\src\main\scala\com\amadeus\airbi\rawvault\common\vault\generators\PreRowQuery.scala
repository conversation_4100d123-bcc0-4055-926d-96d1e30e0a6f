package com.amadeus.airbi.rawvault.common.vault.generators

import com.amadeus.airbi.rawvault.common.config.{BlockJsonAlias, BlockJsonPath, Blocks}

import scala.util.Try

case class PreRowQuery(
  path: BlockJsonPath,
  alias: BlockJsonAlias
) {
  def apply(d: PreRow): Try[Option[Any]] = {
    val k = Try {
      val json = d.collect{case (a, j) if a == alias => j}.headOption
      val v = json.flatMap(k => Option(k.read[Any](path)))
      v
    }
    k
  }
}

