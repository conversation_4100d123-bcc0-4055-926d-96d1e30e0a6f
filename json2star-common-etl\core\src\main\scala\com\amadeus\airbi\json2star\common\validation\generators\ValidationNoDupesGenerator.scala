package com.amadeus.airbi.json2star.common.validation.generators

import com.amadeus.airbi.json2star.common.TablesDef
import com.amadeus.airbi.json2star.common.validation.config.ValidationConfig

/** It generates the  statements  from the mapping file to validate there are no dupes in Delta Databricks Tables
  */
object ValidationNoDupesGenerator {

  /** Generate the SQL statement to initialize a Table
    *
    * @param conf     a Table Config - ongoing HubConfigBean
    * @param database database name
    * @param validParams validation parameters
    * @param checkOnlyLatest check only latest tables (e.g. to speed up the validation process, to check specific cases like the correlation)
    * @return a init SQL
    */
  def toCreateValidationRequest(
    conf: TablesDef,
    database: String,
    validParams: ValidationConfig,
    checkOnlyLatest: Boolean
  ): List[(String, String, String)] = {
    var number = 1
    val (histoTables, latestTables) = (conf.factTables ++ conf.assoTables).partition(_.schema.name.contains("HISTO"))
    val tablesToCheck = (if (checkOnlyLatest) latestTables else histoTables) ++ conf.dimTables ++ conf.otherTables
    tablesToCheck.map { table =>
      {
        val tableName = (table.schema.name)
        val primaryColumnNames = table.schema.keyColumns.map(_.name).toList
        val query = buildNoDupesCheck(database, tableName, primaryColumnNames, validParams = validParams)
        val task = s""" Number of duplicates in ${tableName} in star schema history :  """
        val funcCase =
          s""" Test ${number} - Check no duplicates in ${tableName} in star schema history """.stripMargin
        number = number + 1
        (task, funcCase, query)
      }
    }

  }

  def buildNoDupesCheck(
    database: String,
    tableName: String,
    primaryKeyColumns: List[String],
    validParams: ValidationConfig
  ): String = {

    s"""WITH total_records AS (select count(distinct ${primaryKeyColumns
      .mkString(",")}) AS nb_total_records from ${database}.${tableName}),
       | failed_records AS  ( select ${primaryKeyColumns.mkString(",")},count(*) as a_count
       |   from ${database}.${tableName} group by ${primaryKeyColumns.mkString(",")} having a_count > 1
       |   ),
       | counter_failed_records AS  (select count(*)  AS nb_failed_records from failed_records),
       | sample_failed_records AS(select concat_ws(',',collect_list(distinct failed)) AS fsample
       |   from (select concat_ws('-',${primaryKeyColumns.mkString(",")}) as failed from failed_records limit 5)
       |   )
       | select nb_total_records, nb_failed_records, fsample
       | from counter_failed_records, total_records, sample_failed_records""".stripMargin

  }

}
