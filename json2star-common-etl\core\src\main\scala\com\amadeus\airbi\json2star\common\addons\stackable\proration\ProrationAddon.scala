package com.amadeus.airbi.json2star.common.addons.stackable.proration

import com.amadeus.airbi.json2star.common.addons.base.Addon
import com.amadeus.airbi.json2star.common.addons.base.mapping.Mapping
import com.amadeus.airbi.json2star.common.addons.base.mapping.Table.KeyValueRow
import com.amadeus.airbi.json2star.common.addons.stackable.{
  Proration,
  StackableAddon,
  StackableAddonConfig,
  StackableAddonValidationException
}
import com.amadeus.airbi.json2star.common.extdata.{ExtData, ExtDataType, PorsExtData, PorsExtDataType}
import com.amadeus.airbi.rawvault.common.RootConfig
import com.amadeus.airbi.rawvault.common.application.config.{TableConfig, TablesConfig}
import com.jayway.jsonpath.DocumentContext

import scala.util.{Failure, Success, Try}

object ProrationAddon extends StackableAddon[Proration] {

  val NON_VALID_SEQUENCE_NUMBER = -1

  override def getCompatibleBaseAddons: List[Addon[_]] = List(Mapping)

  override def getRequiredExtData: List[ExtDataType] = List(PorsExtDataType)

  override def enrichTableRows(
    rows: List[KeyValueRow],
    addonConfig: StackableAddonConfig,
    jsonRoot: DocumentContext,
    rootConfig: RootConfig,
    extData: ExtData
  ): List[KeyValueRow] = {
    val enableMeta = rootConfig.etl.common.prorationEnableMetadataColumn
    val attempt = Try {
      val pors = extData.get[PorsExtData](PorsExtDataType)
      val config = getConfig(addonConfig)
      val input = ProrationInput.build(rows, config, jsonRoot, extData)
      val output = ProrationOutput.build(pors.data, input)
      rows.map(row => {
        val couponKey = ProrationInput.buildCouponKey(row)
        val proratedColumns = output.buildColumns(couponKey, enableMeta)
        row ++ proratedColumns
      })
    }
    attempt match {
      case Success(rows) => rows
      case Failure(e) => enrichTableRowsWhenFailure(rows, enableMeta, e)
    }
  }

  private def enrichTableRowsWhenFailure(
    rows: List[KeyValueRow],
    enableMeta: Boolean,
    e: Throwable
  ): List[KeyValueRow] = {
    rows.map(row => {
      val emptyProratedColumns = ProrationOutput.buildColumnsWhenFailure(e.toString, enableMeta)
      row ++ emptyProratedColumns
    })
  }

  override def validate(c: TablesConfig, t: TableConfig, addonConfig: StackableAddonConfig): Unit = {
    // base addon check
    t.mapping.getOrElse(exception(s"Base addon is not Mapping"))

    // check json paths: non empty and starting from root
    def validateJsonPath(path: String): Unit = {
      if (path.isEmpty || !path.startsWith("$.")) {
        exception(s"Invalid json path: '$path'")
      }
    }
    val config = getConfig(addonConfig)
    validateJsonPath(config.jsonPathFareCalc)
    validateJsonPath(config.jsonPathPriceCurrencyPayment)
    validateJsonPath(config.jsonPathPriceTotalPayment)
    validateJsonPath(config.jsonPathPriceTotalTaxesPayment)

    // check that expected columns are present in the mapping
    t.mapping.foreach(m => {
      val missingCols = ProrationColumns.mandatoryCols.diff(m.columns.map(_.name))
      if (missingCols.nonEmpty) {
        exception(s"Some mandatory columns missing: ${missingCols.mkString(", ")} ")
      }
    })
  }

  private def exception(msg: String): Unit = {
    throw new StackableAddonValidationException(s"[ProrationAddon] - $msg")
  }

}
