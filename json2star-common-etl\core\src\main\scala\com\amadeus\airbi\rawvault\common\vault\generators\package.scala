package com.amadeus.airbi.rawvault.common.vault

import com.amadeus.airbi.rawvault.common.config.BlockJsonAlias
import com.amadeus.airbi.rawvault.common.processors.BlockResults.JSON

import scala.util.Try

package object generators {
  type ErrorMessage = String
  type Attempt[T] = Either[ErrorMessage, T]
  type MultilevelJsonList[T] = List[T]

  type PreRow = MultilevelJsonList[(BlockJsonAlias, JSON)] // TODO will be created multiple times, but queried many more times, better use a Map

}
