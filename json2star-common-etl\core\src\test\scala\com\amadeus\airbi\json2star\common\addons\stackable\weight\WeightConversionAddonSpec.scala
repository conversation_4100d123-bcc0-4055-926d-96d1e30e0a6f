package com.amadeus.airbi.json2star.common.addons.stackable.weight

import com.amadeus.airbi.json2star.common.addons.base.mapping.Mapping
import com.amadeus.airbi.json2star.common.addons.stackable.StackableAddonValidationException
import com.amadeus.airbi.json2star.common.app.Json2StarApp.readMappingConfig
import com.amadeus.airbi.json2star.common.{MappingType, Origin, TablesDef}
import com.amadeus.airbi.rawvault.common.application.config.TablesConfig
import com.amadeus.airbi.rawvault.common.config.ColumnMetadataRule.Replace
import com.amadeus.airbi.rawvault.common.config.{ColumnMetadata, ColumnMetadataValue, GDPRZone}
import com.amadeus.airbi.rawvault.common.testfwk.Json2StarSpec

class WeightConversionAddonSpec extends Json2StarSpec {

  "WeightConversionAddon" should "only be compatible with Mapping" in {
    WeightConversionAddon.getCompatibleBaseAddons shouldBe List(Mapping)
  }

  def metadataValue(s: String): ColumnMetadataValue = ColumnMetadataValue(s, Replace)

  it should "enrich the schema metadata" in {
    val mappingFile = "datasets/weight_conversion/simplified_dcsbag.conf"
    val mapping: TablesConfig = readMappingConfig(mappingFile)
    val tableDefs = TablesDef.consolidate(mapping)
    val tableOk = mapping.tables.filter(_.name == "FACT_BAG_HISTO").head
    val weightConversionConfig = tableOk.stackableAddons.head
    val inputSchema = tableDefs.tables.filter(_.schema.name == "FACT_BAG_HISTO").head.schema
    val enrichedSchemaMetadata =
      WeightConversionAddon.enrichSchemaMetadata(mapping, tableOk, weightConversionConfig, inputSchema)

    enrichedSchemaMetadata.description shouldBe None
    enrichedSchemaMetadata.columnsMetadata.size shouldBe 4

    enrichedSchemaMetadata.columnsMetadata("WEIGHT_VALUE").origins shouldBe
      Some(List(Origin("$.mainResource.current.image.bags[*].weight.value", None, None, MappingType)))
    enrichedSchemaMetadata.columnsMetadata("WEIGHT_VALUE").meta.get.description.get shouldBe
      metadataValue(
        "Conversion of WEIGHT_VALUE_ORIGINAL from the unit defined in WEIGHT_UNIT_ORIGINAL to the one defined in WEIGHT_UNIT"
      )

    enrichedSchemaMetadata.columnsMetadata("WEIGHT_UNIT").origins shouldBe
      Some(List(Origin("$.mainResource.current.image.bags[*].weight.unit", None, None, MappingType)))
    enrichedSchemaMetadata.columnsMetadata("WEIGHT_UNIT").meta.get.description.get shouldBe
      metadataValue("Weight unit of WEIGHT_VALUE")

    enrichedSchemaMetadata.columnsMetadata("WEIGHT_VALUE_2").meta shouldBe
      Some(
        ColumnMetadata(
          Some(
            metadataValue(
              "Conversion of WEIGHT_VALUE_ORIGINAL_2 from the unit defined in WEIGHT_UNIT_ORIGINAL_2 to the one defined in WEIGHT_UNIT_2"
            )
          ),
          None,
          None,
          Some(GDPRZone.Red)
        )
      )
    enrichedSchemaMetadata.columnsMetadata("WEIGHT_VALUE_2").origins shouldBe
      Some(List(Origin("$.mainResource.current.image.bags[*].weight.value", None, None, MappingType)))

    enrichedSchemaMetadata.columnsMetadata("WEIGHT_UNIT_2").origins shouldBe
      Some(List(Origin("$.mainResource.current.image.bags[*].weight.unit", None, None, MappingType)))
    enrichedSchemaMetadata.columnsMetadata("WEIGHT_UNIT_2").meta.get.description.get shouldBe
      metadataValue("Weight unit of WEIGHT_VALUE_2")
  }

  it should "validate its configuration: valid config should be ok" in {
    val mappingFile = "datasets/weight_conversion/simplified_dcsbag.conf"
    val mapping: TablesConfig = readMappingConfig(mappingFile)
    val tables = mapping.copy(tables = mapping.tables.filter(_.name == "FACT_BAG_HISTO"))
    val tableOk = tables.tables.head
    // it must not throw
    WeightConversionAddon.validate(tables, tableOk, tableOk.stackableAddons.head)
  }

  it should "validate its configuration: fail if base addon is not Mapping" in {
    val mappingFile = "datasets/weight_conversion/weight_fail_mapping.conf"
    val mapping: TablesConfig = readMappingConfig(mappingFile)
    val tables = mapping.copy(tables = mapping.tables.filter(_.name == "FACT_FAIL"))
    val tableKo = tables.tables.head
    // it must throw because not mapping
    assertThrows[StackableAddonValidationException](
      WeightConversionAddon.validate(tables, tableKo, tableKo.stackableAddons.head)
    )
  }

  it should "validate its configuration: fail if any of the input columns is not defined" in {
    val mappingFile = "datasets/weight_conversion/weight_fail_mapping.conf"
    val mapping: TablesConfig = readMappingConfig(mappingFile)
    val tables = mapping.copy(tables = mapping.tables.filter(_.name == "FACT_FAIL_HISTO"))
    val tableKo = tables.tables.head
    // it must throw because column missing
    assertThrows[StackableAddonValidationException](
      WeightConversionAddon.validate(tables, tableKo, tableKo.stackableAddons.head)
    )
  }

  it should "enrich the table rows" in {
    val mappingFile = "datasets/weight_conversion/simplified_dcsbag.conf"
    val mapping: TablesConfig = readMappingConfig(mappingFile)
    val tables = mapping.copy(tables = mapping.tables.filter(_.name == "FACT_BAG_HISTO"))
    val tableOk = tables.tables.head
    val rc = rootConfigWithWeightUnit(Some(WeightConversionAddon.Kilograms))

    val rows = List(
      Map(
        "WEIGHT_VALUE_ORIGINAL" -> "1",
        "WEIGHT_UNIT_ORIGINAL" -> "POUNDS",
        "WEIGHT_VALUE_ORIGINAL_2" -> "1",
        "WEIGHT_UNIT_ORIGINAL_2" -> "POUNDS"
      )
    )
    val actual =
      WeightConversionAddon.enrichTableRows(rows, tableOk.stackableAddons.head, null, rc, null) // scalastyle:off
    actual shouldBe List(
      Map(
        "WEIGHT_VALUE_ORIGINAL" -> "1",
        "WEIGHT_UNIT_ORIGINAL" -> "POUNDS",
        "WEIGHT_VALUE" -> "0.45",
        "WEIGHT_UNIT" -> "KILOGRAMS",
        "WEIGHT_VALUE_ORIGINAL_2" -> "1",
        "WEIGHT_UNIT_ORIGINAL_2" -> "POUNDS",
        "WEIGHT_VALUE_2" -> "0.45",
        "WEIGHT_UNIT_2" -> "KILOGRAMS"
      )
    )
  }

  it should "enrich the table rows, ignoring nulls" in {
    val mappingFile = "datasets/weight_conversion/simplified_dcsbag.conf"
    val mapping: TablesConfig = readMappingConfig(mappingFile)
    val tables = mapping.copy(tables = mapping.tables.filter(_.name == "FACT_BAG_HISTO"))
    val tableOk = tables.tables.head
    val rc = rootConfigWithWeightUnit(Some(WeightConversionAddon.Kilograms))

    val rows = List(
      Map(
        // "WEIGHT_VALUE_ORIGINAL" -> null, (key not set)
        // "WEIGHT_UNIT_ORIGINAL" -> null, (key not set)
        "WEIGHT_VALUE_ORIGINAL_2" -> null, // set to null
        "WEIGHT_UNIT_ORIGINAL_2" -> null // set to null
      )
    )
    val actual =
      WeightConversionAddon.enrichTableRows(rows, tableOk.stackableAddons.head, null, rc, null) // scalastyle:off
    actual shouldBe List(
      Map(
        "WEIGHT_VALUE_ORIGINAL_2" -> null,
        "WEIGHT_UNIT_ORIGINAL_2" -> null,
        "WEIGHT_VALUE_2" -> null,
        "WEIGHT_UNIT_2" -> "KILOGRAMS"
      )
    )
  }

  private def rootConfigWithWeightUnit(unit: Option[String]) = {
    val dataDir = "datasets/weight_conversion/data/"
    val rc = rootConfig(dataDir, "some_db", isLight = DefaultIsLight)
    rc.copy(etl = rc.etl.copy(common = rc.etl.common.copy(homeWeightUnit = unit)))
  }

  it should "throw an exception if home weight unit is not defined or not supported" in {
    val mappingFile = "datasets/weight_conversion/simplified_dcsbag.conf"
    val mapping: TablesConfig = readMappingConfig(mappingFile)
    val tables = mapping.copy(tables = mapping.tables.filter(_.name == "FACT_BAG_HISTO"))
    val tableOk = tables.tables.head
    val rows = List(
      Map(
        "WEIGHT_VALUE_ORIGINAL" -> "1",
        "WEIGHT_UNIT_ORIGINAL" -> "POUNDS",
        "WEIGHT_VALUE_ORIGINAL_2" -> "1",
        "WEIGHT_UNIT_ORIGINAL_2" -> "POUNDS"
      )
    )

    val rc1 = rootConfigWithWeightUnit(None)
    assertThrows[StackableAddonValidationException](
      WeightConversionAddon.enrichTableRows(rows, tableOk.stackableAddons.head, null, rc1, null) // scalastyle:off
    )
    val rc2 = rootConfigWithWeightUnit(Some("WRONGUNIT"))
    assertThrows[StackableAddonValidationException](
      WeightConversionAddon.enrichTableRows(rows, tableOk.stackableAddons.head, null, rc2, null) // scalastyle:off
    )

  }

  it should "not enrich the table rows if source information is wrong" in {
    val mappingFile = "datasets/weight_conversion/simplified_dcsbag.conf"
    val mapping: TablesConfig = readMappingConfig(mappingFile)
    val tables = mapping.copy(tables = mapping.tables.filter(_.name == "FACT_BAG_HISTO"))
    val tableOk = tables.tables.head
    val rows = List(
      Map(
        "WEIGHT_VALUE_ORIGINAL" -> "1not-a-number",
        "WEIGHT_UNIT_ORIGINAL" -> "POUNDS",
        "WEIGHT_VALUE_ORIGINAL_2" -> "1",
        "WEIGHT_UNIT_ORIGINAL_2" -> "WRONGPOUNDS"
      )
    )

    val rc = rootConfigWithWeightUnit(Some(WeightConversionAddon.Kilograms))
    val actual =
      WeightConversionAddon.enrichTableRows(rows, tableOk.stackableAddons.head, null, rc, null) // scalastyle:off
    actual shouldBe List(
      Map(
        "WEIGHT_VALUE_ORIGINAL" -> "1not-a-number",
        "WEIGHT_UNIT_ORIGINAL" -> "POUNDS",
        "WEIGHT_VALUE" -> null, // conversion not done because original value is not a number
        "WEIGHT_UNIT" -> "KILOGRAMS",
        "WEIGHT_VALUE_ORIGINAL_2" -> "1",
        "WEIGHT_UNIT_ORIGINAL_2" -> "WRONGPOUNDS",
        "WEIGHT_VALUE_2" -> null, // conversion not done because original unit is not supported
        "WEIGHT_UNIT_2" -> "KILOGRAMS"
      )
    )

  }

}
