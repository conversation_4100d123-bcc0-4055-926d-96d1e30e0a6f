package com.amadeus.airbi.json2star.common.addons.base.correlation

import _root_.io.delta.tables.DeltaTable
import com.amadeus.airbi.json2star.common.app.Json2StarApp
import com.amadeus.airbi.json2star.common.app.Json2StarApp.readMappingConfig
import com.amadeus.airbi.rawvault.common.correlation.CorrelationLibrary
import com.amadeus.airbi.rawvault.common.testfwk.Json2StarSpec
import com.amadeus.airbi.rawvault.common.testfwk.TableNames._
import org.apache.spark.sql.DataFrame
import org.apache.spark.sql.catalyst.ScalaReflection
import org.apache.spark.sql.catalyst.encoders.RowEncoder
import org.apache.spark.sql.types.StructType

import java.io.File
import java.sql.Timestamp
import scala.reflect.io.Directory

class IntegrationCorrelationFullOneWaySpec extends Json2StarSpec {

  // correlation is not supported in light tests
  override def isLight: Boolean = false

  case class AssoAirSegmentPaxCouponHistoRow(
    AIR_SEGMENT_PAX_ID: String,
    COUPON_ID: String,
    VERSION_PNR: Long,
    VERSION_TRAVEL_DOCUMENT: Long,
    DATE_BEGIN: Option[Timestamp],
    DATE_END: Option[Timestamp],
    IS_LAST_VERSION: Option[Boolean]
  )

  case class AssoCouponAirSegmentPaxHistoRow(
    COUPON_ID: String,
    AIR_SEGMENT_PAX_ID: String,
    VERSION_TRAVEL_DOCUMENT: Long,
    VERSION_PNR: Long,
    DATE_BEGIN: Option[Timestamp],
    DATE_END: Option[Timestamp],
    IS_LAST_VERSION: Option[Boolean]
  )

  case class AssoAirSegmentPaxCouponRow(
    AIR_SEGMENT_PAX_ID: String,
    COUPON_ID: String,
    VERSION_PNR: Long,
    VERSION_TRAVEL_DOCUMENT: Long
  )

  case class AssoCouponAirSegmentPaxRow(
    COUPON_ID: String,
    AIR_SEGMENT_PAX_ID: String,
    VERSION_TRAVEL_DOCUMENT: Long,
    VERSION_PNR: Long
  )

  val mappingFilePnr = "src/test/resources/config/samples/pnr.conf"
  val mappingFileTkt = "src/test/resources/config/samples/tkt.conf"

  override def beforeAll: Unit = {
    super.beforeAll
    createTables(mappingFilePnr)
    createTables(mappingFileTkt)
  }

  override def beforeEach: Unit = {
    cleanTables(mappingFilePnr)
    cleanTables(mappingFileTkt)
  }

  it should "compute correlations PNR->TKT" taggedAs(SlowTestTag) in {
    val pathExpectedResults = "src/test/resources/datasets/12/expected_result/"

    val dataDirPnr = "datasets/12/data/pnr/"
    val rcPnr = rootConfig(dataDirPnr, inputDatabase, isLight = isLight)
    val directoryPnr = new Directory(new File(rcPnr.etl.stream.sink.checkpointLocation))
    directoryPnr.deleteRecursively()
    val mappingPnr = readMappingConfig(mappingFilePnr)
    Json2StarApp.run(spark, rcPnr, mappingPnr)
    // Tables generated: PNR_TKT_PARTIAL_CORR_PIT_NAME FACT_RESERVATION_HISTO_NAME FACT_AIR_SEGMENT_PAX_HISTO_NAME

    val dataDirTkt = "datasets/12/data/tkt/"
    val rcTkt = rootConfig(dataDirTkt, inputDatabase, isLight = isLight)
    val directoryTkt = new Directory(new File(rcTkt.etl.stream.sink.checkpointLocation))
    directoryTkt.deleteRecursively()
    val mappingTkt = readMappingConfig(mappingFileTkt)
    Json2StarApp.run(spark, rcTkt, mappingTkt)
    // Tables generated: FACT_TRAVEL_DOCUMENT_HISTO_NAME TKT_PNR_PARTIAL_CORR_PIT_NAME

    import spark.implicits._

    val correlationLibraryInstance = CorrelationLibrary(
      END_DATE = "DATE_END",
      START_DATE = "DATE_BEGIN",
      PARTIAL_CORR_VERSION = "VERSION",
      PIT_VERSION = "VERSION",
      PARTIAL_CORR_KEY = "AIR_SEGMENT_PAX_ID",
      CORRELATION_FIELD = "TRAVEL_DOCUMENT_ID", // this field must be in both tables to correlate
      PARTIAL_CORR_SECONDARY_KEY = "COUPON_ID",
      IS_LAST_PARTIAL = "IS_LAST_VERSION",
      IS_LAST_PIT = "IS_LAST_VERSION"
    )

    val actualCorrelationPnrTktDF: DataFrame =
      correlationLibraryInstance.processCorrelations(
        partialAssoDF = DeltaTable
          .forName(inputDatabase + "." + PNR_TKT_PARTIAL_CORR_PIT_NAME)
          .toDF,
        pitDF = DeltaTable
          .forName(inputDatabase + "." + FACT_TRAVEL_DOCUMENT_HISTO_NAME) // @TODO maybe use FACT_COUPON_HISTO instead
          .toDF
      )(spark, RowEncoder(ScalaReflection.schemaFor[AssoAirSegmentPaxCouponHistoRow].dataType.asInstanceOf[StructType]))
    //@TODO should use the schema coming from the mapping rather than the case class AssoAirSegmentPaxCouponHistoRow (which is defined for testing)
    // processCorrelations is intended to be called with it when integrated, so unit test should use the same

    // actualCorrelationPnrTktDF.printSchema()

    // actualCorrelationPnrTktDF.show(100, false)

    val expectedDf = spark.read
      .schema(actualCorrelationPnrTktDF.schema)
      .option("header", value = true)
      .csv(pathExpectedResults + "asso_air_segment_pax_coupon_histo.csv")
      .sort()

    actualCorrelationPnrTktDF.count() shouldBe expectedDf.count()

    assertSmallDataFrameEquality(
      actualCorrelationPnrTktDF
        .select(
          $"AIR_SEGMENT_PAX_ID",
          $"COUPON_ID",
          $"VERSION_PNR",
          $"VERSION_TRAVEL_DOCUMENT",
          $"DATE_BEGIN",
          $"DATE_END",
          $"IS_LAST_VERSION"
        )
        .toDF(),
      expectedDf,
      orderedComparison = false,
      ignoreNullable = true
    )
  }

  it should "compute correlations TKT->PNR" taggedAs(SlowTestTag) in {
    val pathExpectedResults = "src/test/resources/datasets/12/expected_result/"

    val dataDirPnr = "datasets/12/data/pnr/"
    val rcPnr = rootConfig(dataDirPnr, inputDatabase, isLight = isLight)
    val directoryPnr = new Directory(new File(rcPnr.etl.stream.sink.checkpointLocation))
    directoryPnr.deleteRecursively()
    val mappingPnr = readMappingConfig(mappingFilePnr)
    Json2StarApp.run(spark, rcPnr, mappingPnr)
    // Tables generated: PNR_TKT_PARTIAL_CORR_PIT_NAME FACT_RESERVATION_HISTO_NAME FACT_AIR_SEGMENT_PAX_HISTO_NAME

    val dataDirTkt = "datasets/12/data/tkt/"
    val rcTkt = rootConfig(dataDirTkt, inputDatabase, isLight = isLight)
    val directoryTkt = new Directory(new File(rcTkt.etl.stream.sink.checkpointLocation))
    directoryTkt.deleteRecursively()
    val mappingTkt = readMappingConfig(mappingFileTkt)
    Json2StarApp.run(spark, rcTkt, mappingTkt)
    // Tables generated: FACT_TRAVEL_DOCUMENT_HISTO_NAME TKT_PNR_PARTIAL_CORR_PIT_NAME

    import spark.implicits._

    // WAY #2
    val correlationTktPnrDF = CorrelationLibrary( // TKT(partial) PNR(pit)
      END_DATE = "DATE_END",
      START_DATE = "DATE_BEGIN",
      PARTIAL_CORR_VERSION = "VERSION",
      PIT_VERSION = "VERSION",
      PARTIAL_CORR_KEY = "COUPON_ID",
      CORRELATION_FIELD = "AIR_SEGMENT_PAX_ID",
      PARTIAL_CORR_SECONDARY_KEY = "AIR_SEGMENT_PAX_ID",
      IS_LAST_PARTIAL = "IS_LAST_VERSION",
      IS_LAST_PIT = "IS_LAST_VERSION"
    ).processCorrelations(
      DeltaTable
        .forName(inputDatabase + "." + TKT_PNR_PARTIAL_CORR_PIT_NAME)
        .toDF,
      DeltaTable
        .forName(inputDatabase + "." + FACT_AIR_SEGMENT_PAX_HISTO_NAME)
        .toDF
    )(spark, RowEncoder(ScalaReflection.schemaFor[AssoCouponAirSegmentPaxHistoRow].dataType.asInstanceOf[StructType]))
    //@TODO should use the schema coming from the mapping rather than the case class AssoCouponAirSegmentPaxHistoRow (which is defined for testing)
    // processCorrelations is intended to be called with it when integrated, so unit test should use the same

    // correlationTktPnrDF.show(100, false)

    val expectedTktPnrDf = spark.read
      .schema(ScalaReflection.schemaFor[AssoAirSegmentPaxCouponHistoRow].dataType.asInstanceOf[StructType])
      .option("header", value = true)
      .csv(pathExpectedResults + "asso_air_segment_pax_coupon_histo.csv")

    correlationTktPnrDF.count() shouldBe expectedTktPnrDf.count()

    assertSmallDataFrameEquality(
      correlationTktPnrDF
        .select(
          $"AIR_SEGMENT_PAX_ID",
          $"COUPON_ID",
          $"VERSION_PNR",
          $"VERSION_TRAVEL_DOCUMENT",
          $"DATE_BEGIN",
          $"DATE_END",
          $"IS_LAST_VERSION"
        )
        .toDF(),
      expectedTktPnrDf,
      orderedComparison = false,
      ignoreNullable = true
    )
  }

  it should "compute correlations PNR->TKT closure 13" taggedAs(SlowTestTag) in {
    val pathExpectedResults = "src/test/resources/datasets/13/expected_result/"

    val dataDirPnr = "datasets/13/data/pnr/"
    val rcPnr = rootConfig(dataDirPnr, inputDatabase, isLight = isLight)
    val directoryPnr = new Directory(new File(rcPnr.etl.stream.sink.checkpointLocation))
    directoryPnr.deleteRecursively()
    val mappingPnr = readMappingConfig(mappingFilePnr)
    Json2StarApp.run(spark, rcPnr, mappingPnr)
    // Tables generated: PNR_TKT_PARTIAL_CORR_PIT_NAME FACT_RESERVATION_HISTO_NAME FACT_AIR_SEGMENT_PAX_HISTO_NAME

    val dataDirTkt = "datasets/13/data/tkt/"
    val rcTkt = rootConfig(dataDirTkt, inputDatabase, isLight = isLight)
    val directoryTkt = new Directory(new File(rcTkt.etl.stream.sink.checkpointLocation))
    directoryTkt.deleteRecursively()
    val mappingTkt = readMappingConfig(mappingFileTkt)
    Json2StarApp.run(spark, rcTkt, mappingTkt)
    // Tables generated: FACT_TRAVEL_DOCUMENT_HISTO_NAME TKT_PNR_PARTIAL_CORR_PIT_NAME

    import spark.implicits._

    val correlationLibraryInstance = CorrelationLibrary(
      END_DATE = "DATE_END",
      START_DATE = "DATE_BEGIN",
      PARTIAL_CORR_VERSION = "VERSION",
      PIT_VERSION = "VERSION",
      PARTIAL_CORR_KEY = "AIR_SEGMENT_PAX_ID",
      CORRELATION_FIELD = "TRAVEL_DOCUMENT_ID", // this field must be in both tables to correlate
      PARTIAL_CORR_SECONDARY_KEY = "COUPON_ID",
      IS_LAST_PARTIAL = "IS_LAST_VERSION",
      IS_LAST_PIT = "IS_LAST_VERSION"
    )

    val actualCorrelationPnrTktDF: DataFrame =
      correlationLibraryInstance.processCorrelations(
        partialAssoDF = DeltaTable
          .forName(inputDatabase + "." + PNR_TKT_PARTIAL_CORR_PIT_NAME)
          .toDF,
        pitDF = DeltaTable
          .forName(inputDatabase + "." + FACT_TRAVEL_DOCUMENT_HISTO_NAME) // @TODO maybe use FACT_COUPON_HISTO instead
          .toDF
      )(spark, RowEncoder(ScalaReflection.schemaFor[AssoAirSegmentPaxCouponHistoRow].dataType.asInstanceOf[StructType]))
    //@TODO should use the schema coming from the mapping rather than the case class AssoAirSegmentPaxCouponHistoRow (which is defined for testing)
    // processCorrelations is intended to be called with it when integrated, so unit test should use the same

    // actualCorrelationPnrTktDF.show(100, false)

    val expectedDf = spark.read
      .schema(actualCorrelationPnrTktDF.schema)
      .option("header", value = true)
      .csv(pathExpectedResults + "asso_air_segment_pax_coupon_histo.csv")
      .sort()

    actualCorrelationPnrTktDF.count() shouldBe expectedDf.count()

    assertSmallDataFrameEquality(
      actualCorrelationPnrTktDF
        .select(
          $"AIR_SEGMENT_PAX_ID",
          $"COUPON_ID",
          $"VERSION_PNR",
          $"VERSION_TRAVEL_DOCUMENT",
          $"DATE_BEGIN",
          $"DATE_END",
          $"IS_LAST_VERSION"
        )
        .toDF(),
      expectedDf,
      orderedComparison = false,
      ignoreNullable = true
    )
  }

  it should "compute correlations PNR->TKT closure 14" taggedAs(SlowTestTag) in {
    val pathExpectedResults = "src/test/resources/datasets/14/expected_result/"

    val dataDirPnr = "datasets/14/data/pnr/"
    val rcPnr = rootConfig(dataDirPnr, inputDatabase, isLight = isLight)
    val directoryPnr = new Directory(new File(rcPnr.etl.stream.sink.checkpointLocation))
    directoryPnr.deleteRecursively()
    val mappingPnr = readMappingConfig(mappingFilePnr)
    Json2StarApp.run(spark, rcPnr, mappingPnr)
    // Tables generated: PNR_TKT_PARTIAL_CORR_PIT_NAME FACT_RESERVATION_HISTO_NAME FACT_AIR_SEGMENT_PAX_HISTO_NAME

    val dataDirTkt = "datasets/14/data/tkt/"
    val rcTkt = rootConfig(dataDirTkt, inputDatabase, isLight = isLight)
    val directoryTkt = new Directory(new File(rcTkt.etl.stream.sink.checkpointLocation))
    directoryTkt.deleteRecursively()
    val mappingTkt = readMappingConfig(mappingFileTkt)
    Json2StarApp.run(spark, rcTkt, mappingTkt)
    // Tables generated: FACT_TRAVEL_DOCUMENT_HISTO_NAME TKT_PNR_PARTIAL_CORR_PIT_NAME

    import spark.implicits._

    val correlationLibraryInstance = CorrelationLibrary(
      END_DATE = "DATE_END",
      START_DATE = "DATE_BEGIN",
      PARTIAL_CORR_VERSION = "VERSION",
      PIT_VERSION = "VERSION",
      PARTIAL_CORR_KEY = "AIR_SEGMENT_PAX_ID", // TODO
      CORRELATION_FIELD = "TRAVEL_DOCUMENT_ID", // TODO
      PARTIAL_CORR_SECONDARY_KEY = "COUPON_ID",
      IS_LAST_PARTIAL = "IS_LAST_VERSION",
      IS_LAST_PIT = "IS_LAST_VERSION"
    )

    val actualCorrelationPnrTktDF: DataFrame =
      correlationLibraryInstance.processCorrelations(
        partialAssoDF = DeltaTable
          .forName(inputDatabase + "." + PNR_TKT_PARTIAL_CORR_PIT_NAME)
          .toDF,
        pitDF = DeltaTable
          .forName(inputDatabase + "." + FACT_TRAVEL_DOCUMENT_HISTO_NAME) // @TODO maybe use FACT_COUPON_HISTO instead
          .toDF
      )(spark, RowEncoder(ScalaReflection.schemaFor[AssoAirSegmentPaxCouponHistoRow].dataType.asInstanceOf[StructType]))
    //@TODO should use the schema coming from the mapping rather than the case class AssoAirSegmentPaxCouponHistoRow (which is defined for testing)
    // processCorrelations is intended to be called with it when integrated, so unit test should use the same

    // actualCorrelationPnrTktDF.show(100, false)

    val expectedDf = spark.read
      .schema(actualCorrelationPnrTktDF.schema)
      .option("header", value = true)
      .csv(pathExpectedResults + "asso_air_segment_pax_coupon_histo.csv")
      .sort()

    actualCorrelationPnrTktDF.count() shouldBe expectedDf.count()

    assertSmallDataFrameEquality(
      actualCorrelationPnrTktDF
        .select(
          $"AIR_SEGMENT_PAX_ID",
          $"COUPON_ID",
          $"VERSION_PNR",
          $"VERSION_TRAVEL_DOCUMENT",
          $"DATE_BEGIN",
          $"DATE_END",
          $"IS_LAST_VERSION"
        )
        .toDF(),
      expectedDf,
      orderedComparison = false,
      ignoreNullable = true
    )
  }
}
