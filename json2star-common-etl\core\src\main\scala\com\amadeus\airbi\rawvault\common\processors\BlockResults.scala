package com.amadeus.airbi.rawvault.common.processors

import com.amadeus.airbi.rawvault.common.config.{BlockJsonAlias, BlockJsonPath}
import com.amadeus.airbi.rawvault.common.vault.generators.{Attempt, PreRowQuery}
import com.jayway.jsonpath.DocumentContext
import org.json4s.DefaultFormats
import org.json4s.jackson.Serialization.{writePretty => asJson}

import scala.util.Try

/**
  * The block results are nodes of a tree that represent the multi-level parsing
  * of a json object using the blocks.
  */
object BlockResults {

  val BlockJsonPathRoot: BlockJsonPath = "$"
  val BlockJsonAliasRoot: BlockJsonAlias = "root"

  type JSON = DocumentContext // a jayway parsed json document
  type CartesianBranches[T] = Seq[T] // a sequence of cartesian product branches
  type RecordsSeq[T] = Seq[T] // a sequence of records (target row granularity)
  type RecordsList[T] = List[T] // a list of records (target row granularity)

  sealed trait BlockResult {
    override def toString: String = asJson(this)(DefaultFormats)
  }

  /**
    * Result of parsing a json with one jsonpath block.
    * @param alias used when parsing this json
    * @param path jsonpath used when parsing this json
    * @param json resulting json object
    * @param children children (if more blocks were provided)
    * @param leaf of the tree, telling this is a node that should ultimately materialize as a row
    */
  case class JsonpathBlockResult(
    alias: BlockJsonAlias,
    path: BlockJsonPath,
    json: JSON,
    children: RecordsSeq[Attempt[BlockResult]],
    leaf: Boolean
  ) extends BlockResult

  /**
    * Result of parsing a json with a cartesian block
    * @param children contains the sequence of branches from where to perform the cartesian product
    */
  case class CartesianBlockResult(
    children: CartesianBranches[RecordsSeq[Attempt[BlockResult]]]
  ) extends BlockResult

  /**
    * Create a root [[BlockResult]]
    */
  def root(j: JSON, c: Seq[Attempt[BlockResult]]): JsonpathBlockResult = JsonpathBlockResult(
    alias = BlockJsonAliasRoot,
    path = BlockJsonPathRoot,
    json = j,
    children = c,
    leaf = false
  )

}
