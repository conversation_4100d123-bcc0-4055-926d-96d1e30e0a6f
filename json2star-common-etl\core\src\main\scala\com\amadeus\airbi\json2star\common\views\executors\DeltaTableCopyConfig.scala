package com.amadeus.airbi.json2star.common.views.executors

import com.amadeus.airbi.json2star.common.config.AppConfig

case class DeltaTableCopyConfig(
  appConfig: AppConfig,
  dryMode: Option[String],
  previousDatabase: String,
  tablesToReprocess: Seq[String],
  analyzeAfterClone: Boolean = true
)

object DeltaTableCopyConfig {

  /** Generate a config for DeltaTableCopySchemaExecutor from Command-Line Arguments
    * For app deployment:
    * Usage: APP --app-config-file application.init.conf --dry-mode DEBUG
    *
    * @param args the args array
    * @return a config
    */
  def apply(args: Array[String]): DeltaTableCopyConfig = {
    val argsConf = DeltaTableCopySchemaExecutorScallopConf(args)
    val appConfigFile = argsConf.appConfigFile()
    val appConfig = AppConfig(appConfigFile)
    val prParams = appConfig.assumePartialReprocessingParams()
    DeltaTableCopyConfig(
      appConfig = appConfig,
      dryMode = argsConf.dryMode.toOption,
      // Computes the previous database name from the current one by replacing the current version with the previous version in it
      previousDatabase = appConfig.common.outputDatabase.replace(appConfig.common.domainVersion, prParams.previousVersion),
      tablesToReprocess = prParams.tablesToReprocess
    )
  }

}
