%scala
import com.amadeus.airbi.json2star.common.views.executors.DataDictExecutor
import scala.io.Source

val appConfigFile = dbutils.widgets.get("appConfigFile")
val version = dbutils.widgets.get("version")
val rootLocation = dbutils.widgets.get("rootLocation")
val status = dbutils.widgets.get("status")

val tmpOutputFile = java.io.File.createTempFile("outputDocFile", ".md")
val tmpViewsFile = java.io.File.createTempFile("outputViewsFile", ".sql")

DataDictExecutor.main(
  Array(
    "--app-config-file-path", appConfigFile,
    "--jdf-status", status,
    "--output-jdf-file", tmpOutputFile.toString,
    "--output-jdf-views-file", tmpViewsFile.toString
  )
)

val uri = new java.net.URI(rootLocation)
val container = Option(uri.getUserInfo)

// Copy JDF data dictionary to final destination
val destPath = container match {
  case Some(c) => s"${uri.getScheme}://${c}@${uri.getHost}/_meta/data-dic_${c}_${version}.json" // actual mesh
  case None => s"${rootLocation}/_meta/data-dic_nomesh_${version}.json" // no mesh
}
val jdfFileSource = Source.fromFile(tmpOutputFile)
dbutils.fs.put(destPath, jdfFileSource.getLines.mkString("\n"), true)
jdfFileSource.close()

// Copy JDF views generation script to final destination
val destViewPath = container match {
  case Some(c) => s"${uri.getScheme}://${c}@${uri.getHost}/_views/views_${c}_${version}.sql" // actual mesh
  case None => s"${rootLocation}/_views/views_nomesh_${version}.sql" // no mesh
}
val jdfViewsFileSource = Source.fromFile(tmpViewsFile)
dbutils.fs.put(destViewPath, jdfViewsFileSource.getLines.mkString("\n"), true)
jdfViewsFileSource.close()