package com.amadeus.airbi.json2star.common.addons.stackable.currency

import com.amadeus.airbi.json2star.common.addons.base.Addon
import com.amadeus.airbi.json2star.common.addons.base.mapping.Mapping
import com.amadeus.airbi.json2star.common.addons.stackable._
import com.amadeus.airbi.json2star.common.extdata.ExtDataType
import com.amadeus.airbi.json2star.common.{ColumnDef, Origin, Schema}
import com.amadeus.airbi.rawvault.common.RootConfig
import com.amadeus.airbi.rawvault.common.application.config.{MappingConfig, TableConfig, TablesConfig}
import com.amadeus.airbi.rawvault.common.config.ColumnMetadataRule.Replace
import com.amadeus.airbi.rawvault.common.config.{ColumnMetadata, ColumnMetadataValue}
import io.delta.tables.DeltaTable
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{Data<PERSON>rame, Dataset, Row}

object CurrencyConversionAddon extends StackableAddon[CurrencyConversion] {

  val FromCurrency: String = "FROM_CURRENCY"
  val ToCurrency: String = "TO_CURRENCY"
  private val AdditionalCostPattern = "^[0-9.]+A$" // <price>A (e.g. 123.12A)
  private val EverythingExceptNumbersPattern = "([^0-9.]+)$"

  override def getCompatibleBaseAddons: List[Addon[_]] = List(Mapping)

  override def getRequiredExtData: List[ExtDataType] = List()

  /** Replace any existing description with a generated one.
    * Copy the rest of the metadata from src columns.
    * Leave the table description unchanged.
    */
  override def enrichSchemaMetadata(
    c: TablesConfig,
    t: TableConfig,
    addonConfig: StackableAddonConfig,
    schema: Schema
  ): EnrichedSchemaMetadata = {
    def m(col: Option[ColumnDef], d: String, o: Option[Seq[Origin]]): EnrichedColumnMetadata = {
      val descValue = Some(ColumnMetadataValue(d, Replace))
      val enrichedMeta = col
        .flatMap(_.meta)
        .map(m =>
          m.copy(
            description = descValue
          )
        )
        .orElse(Some(ColumnMetadata(description = descValue)))
      EnrichedColumnMetadata(meta = enrichedMeta, origins = o)
    }

    val config = getConfig(addonConfig)
    EnrichedSchemaMetadata(
      columnsMetadata = config.conversions.flatMap { cc =>
        val src = schema.columns.find(c => c.name == cc.srcCol)
        val srcUnit = schema.columns.find(c => c.name == cc.srcUnitCol)
        Seq(
          cc.dstCol -> m(
            src,
            s"Conversion of ${cc.srcCol} from the unit defined in ${cc.srcUnitCol} to the one defined in ${cc.dstUnitCol}" +
              s" at the date ${cc.dstDateCol} closest to the one defined in ${cc.srcDateCol}",
            src.map(_.origins)
          ),
          cc.dstUnitCol -> m(srcUnit, s"Currency of ${cc.dstCol}", srcUnit.map(_.origins))
        )
      }.toMap
    )
  }

  override def enrichBatch(b: DataFrame, addonConfig: StackableAddonConfig, rootConfig: RootConfig): DataFrame = {

    val currConvAppConfig =
      rootConfig.etl.common.homeCurrencyParams
        .getOrElse(fatalException("Missing 'home-currency-params' section in app configuration"))
    val currConvConfig = getConfig(addonConfig)

    val exchangeRatesDf =
      readRefDataTable(
        currConvAppConfig.database,
        currConvAppConfig.tablename,
        List(currConvAppConfig.currencyCode)
      ).toDF
        .as("exrates")

    exchangeRatesDf match {
      case t if t.isEmpty =>
        fatalException(
          s"Exchange Rate dataset has no data for the currency '${currConvAppConfig.currencyCode}'." +
            s"Check '${currConvAppConfig.database}.${currConvAppConfig}'"
        ); b
      case _ =>
        applyConversion(
          currConvConfig,
          exchangeRatesDf,
          b,
          currConvAppConfig.currencyCode,
          currConvAppConfig.noAdditionalCostWhitelist
        )
    }

  }

  private def applyConversion(
    config: CurrencyConversion,
    exchangeRatesDf: Dataset[Row],
    b: DataFrame,
    targetCurrency: String,
    noAdditionalCostWhitelist: Seq[String]
  ): DataFrame = {

    val result = config.conversions.foldLeft(b.as("batch"))((res, r) => {
      val df = res
        .join(
          exchangeRatesDf.as("exrates"),
          col(s"batch.${r.srcUnitCol}") === col(s"exrates.$FromCurrency") and
            col(s"batch.${r.srcDateCol}") >= to_date(col(s"exrates.DATE_BEGIN")) and
            (col(s"batch.${r.srcDateCol}") < to_date(col(s"exrates.DATE_END")) or col(s"exrates.DATE_END").isNull),
          "left_outer"
        )
        .withColumn(
          r.dstUnitCol,
          // this case if ORIGINAL_CURRENCY is equal to HOME_CURRENCY => same value as source currency
          when(col(r.srcUnitCol) === targetCurrency, col(r.srcUnitCol))
            //other cases
            .otherwise(col(s"exrates.$ToCurrency"))
        )
        .withColumn(r.dstDateCol, col("exrates.DATE_BEGIN"))

      val preDrop = applyBusinessRulesOnOriginalValue(r, df, targetCurrency, noAdditionalCostWhitelist)

      //We need to use foldLeft as drop() cannot take a list of columns and drop("alias.col") doesn't drop the column
      exchangeRatesDf.columns
        .map(name => col(s"exrates.$name"))
        .foldLeft(preDrop)((df, column) => df.drop(column))
    })
    result
  }

  /** Apply business rules on the original value:
    * 1. Refined the `original value`:
    *   - standard mode (not incremental mode) or all numeric -> keep the original value
    *   - additional cost -> extract the incremental value
    *   - no additional cost -> set to 0
    *   - otherwise -> null
    * 2. Compute the converted value (if the original value is null or alphanumeric, it will result in a null result)
    * 3. Extract the `increment flag` column (optional, only if `incremental mode`)
    *
    * `incremental mode` is set to true if a `dstIncrFlagCol` is specified in the config.
    *
    * @param c                         the addon config
    * @param df                        the dataset in which we will compute the conversion
    * @param targetCurrency            the target currency
    * @param noAdditionalCostWhitelist the whitelist of No Additional Cost labels
    * @return the dataset with conversion done
    */
  private def applyBusinessRulesOnOriginalValue(
    c: CurrencyConversionColumnConfig,
    df: Dataset[Row],
    targetCurrency: String,
    noAdditionalCostWhitelist: Seq[String]
  ): DataFrame = {
    val originalValue = col(c.srcCol)
    val incrementalMode = lit(c.dstIncrFlagCol.nonEmpty)

    val allNumeric = originalValue.cast("float").isNotNull
    val additionalCost = originalValue.rlike(AdditionalCostPattern)
    val knownLabels = originalValue.isInCollection(noAdditionalCostWhitelist)

    // handle incremental values
    val srcColNumeric = when(allNumeric, originalValue)
      .when(incrementalMode && additionalCost, originalValue.substr(lit(0), length(originalValue) - 1))
      // otherwise the value is set to null (e.g. negotiated fares)
      .cast("float") // if the original value is null or alphanumeric, it will result in a null result due to the cast

    val rateValue = when(col(c.srcUnitCol) === targetCurrency, lit(1.0))
      .otherwise(col("exrates.RATE") / pow(10, col("exrates.NUM_DECIMAL")))

    val dfWithConvertedValue = df.withColumn(
      c.dstCol,
      when(knownLabels, lit(0.0)) // forced to 0 in case the value is part of the no additional cost whitelist
        .otherwise(srcColNumeric * rateValue)
        .cast("float")
    )

    // compute the incremental flag column if specified
    c.dstIncrFlagCol.fold(dfWithConvertedValue) { dstIncrFlagCol =>
      dfWithConvertedValue.withColumn(
        dstIncrFlagCol,
        when(
          additionalCost || knownLabels,
          regexp_extract(originalValue, EverythingExceptNumbersPattern, 1)
        )
      )
    }
  }

  override def validate(c: TablesConfig, t: TableConfig, addonConfig: StackableAddonConfig): Unit = {
    // base addon check
    t.mapping.getOrElse(fatalException(s"Base addon is not Mapping"))

    // column conversions check
    val config = getConfig(addonConfig)
    for {
      mapping <- t.mapping
    } yield checkColumns(config, mapping)
  }

  private def checkColumns(c: CurrencyConversion, mapping: MappingConfig): Unit = {
    val addonCols = c.conversions.flatMap(cf =>
      Seq(cf.dstUnitCol, cf.dstCol, cf.srcUnitCol, cf.srcCol, cf.dstDateCol) ++ cf.dstIncrFlagCol
    )
    val cols = mapping.columns.map(_.name).toSet
    // column presence
    val notPresent = addonCols.filter(!cols.contains(_))
    if (notPresent.nonEmpty) {
      fatalException(
        s"Columns ${notPresent.mkString(", ")} are configured for currency conversion but not present in the mapping config"
      )
    }
  }

  private def fatalException(msg: String): Nothing = {
    throw new StackableAddonValidationException(s"[CurrencyConversionAddon] - $msg")
  }

  /** Read refData Exchange rates table and filter by the given target Currencies
    * @param databaseName reference data database name
    * @param tableName exchange rates table name
    * @param filterByTargetCurrencies currencies used to filter input data
    * @return dataframe for the given home currency
    */
  private def readRefDataTable(
    databaseName: String,
    tableName: String,
    filterByTargetCurrencies: List[String]
  ): DataFrame = {
    val tmp = DeltaTable
      .forName(s"$databaseName.$tableName")
      .toDF
    tmp.filter(r => filterByTargetCurrencies.contains(r.getAs[String](s"$ToCurrency")))
  }
}
