package com.amadeus.airbi.rawvault.common.vault.hashers

import com.amadeus.airbi.common.utils.HashHelper
import com.amadeus.airbi.datalake.common.spark.ColumnHelper
import org.apache.spark.sql.SparkSession

import scala.util.Try

object StarSchemaHasher {

  trait Hasher extends Serializable {
    def hashXS(s: String): Option[Long]
    def hashS(s: String): Option[String]
    def hashM(s: String): Option[String]
    def hashL(s: String): Option[String]
    def register(sparkSession: SparkSession): Unit = {
      sparkSession.udf.register("hashXS", hashXS _)
      sparkSession.udf.register("hashS", hashS _)
      sparkSession.udf.register("hashM", hashM _)
      sparkSession.udf.register("hashL", hashL _)
    }
  }

  object ShaHasher extends Hasher {
    private val HashSBeginIndex = 0
    private val HashSEndIndex = 16

    def hashXS(s: String): Option[Long] = { // dihEncode64
      Try(ColumnHelper.mapAlphanumStringToBigInt(s)).toOption
    }

    def hashS(s: String): Option[String] = { // diHash64
      Try(HashHelper.sha128Hash(s).substring(HashSBeginIndex, HashSEndIndex)).toOption
    }

    def hashM(s: String): Option[String] = { // diHash128
      Try(HashHelper.sha128Hash(s)).toOption
    }

    def hashL(s: String): Option[String] = { // diHash256
      Try(HashHelper.sha256Hash(s)).toOption
    }
  }

  object DebugHasher extends Hasher {
    def hashXS(s: String): Option[Long] = { // dihEncode64
      Try(ColumnHelper.mapAlphanumStringToBigInt(s.mkString("-"))).toOption // TODO remove the mkstring and fix tests
    }

    def hashS(s: String): Option[String] = { // diHash64
      s match {
        case "null" => None
        case null => None
        case _ =>  Some(s"hashS($s)")
      }
    }

    def hashM(s: String): Option[String] = { // diHash128
      s match {
        case "null" => None
        case null => None
        case _ =>  Some(s"hashM($s)")
      }
    }

    def hashL(s: String): Option[String] = { // diHash256
      s match {
        case "null" => None
        case null => None
        case _ =>  Some(s"hashL($s)")
      }
    }
  }

  def registerUdfs(sparkSession: SparkSession, debug: Boolean): Unit = {
    if (debug) {
      DebugHasher.register(sparkSession)
    } else {
      ShaHasher.register(sparkSession)
    }
  }

}
