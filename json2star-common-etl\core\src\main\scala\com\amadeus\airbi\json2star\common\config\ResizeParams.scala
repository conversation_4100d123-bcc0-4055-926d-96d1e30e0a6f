package com.amadeus.airbi.json2star.common.config

/** Object providing default values for Optimize configuration
  */
object ResizeParams {
  val DefaultTrafficPeakThreshold: Long = Long.MaxValue
  val DefaultNumWorkersDefault: Int = 1
  val DefaultNumWorkersPitHisto: Int = 1
  val DefaultNumWorkersTrafficPeak: Int = 1
  val DefaultNumWorkersOptimize: Int = 1
  val DefaultNumWorkersSfPush: Int = 1
}

/** Resize configuration
  */
case class ResizeParams(
  trafficPeakThreshold: Long = ResizeParams.DefaultTrafficPeakThreshold,
  numWorkersDefault: Int = ResizeParams.DefaultNumWorkersDefault,
  numWorkersPitHisto: Int = ResizeParams.DefaultNumWorkersPitHisto,
  numWorkersTrafficPeak: Int = ResizeParams.DefaultNumWorkersTrafficPeak,
  numWorkersOptimize: Int = ResizeParams.DefaultNumWorkersOptimize,
  numWorkersSfPush: Int = ResizeParams.DefaultNumWorkersSfPush
)
