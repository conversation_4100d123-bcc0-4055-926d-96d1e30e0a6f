package com.amadeus.airbi.rawvault.common.config

import com.amadeus.airbi.rawvault.common.vault.generators.PreRowQuery

object ColSources {

  /**
    * The recipe to build in a column value for a given row
    */
  sealed trait ColSource

  /** Source based on jsonpath queries applied to multi-level json
    */
  case class BlocksSource(blocks: List[PreRowQuery]) extends ColSource

  /** In case of multiple roots
    */
  case class RootSpecificSource(rootSpecific: List[SourceWithRoot]) extends ColSource {

    /** Returns a source without root, corresponding to the specified root
      */
    def asNonRootSpecificSource(root: String): Option[ColSource] = {
      rootSpecific.find { case SourceWithRoot(r, _, _) => r == root }.map(_.asSourceWithoutRoot())
    }
  }

  /** Constant value column source
    * @param literal the constant value
    */
  case class LiteralSource(literal: String) extends ColSource

  /** Empty column source
    */
  case class NullSource() extends ColSource

}
