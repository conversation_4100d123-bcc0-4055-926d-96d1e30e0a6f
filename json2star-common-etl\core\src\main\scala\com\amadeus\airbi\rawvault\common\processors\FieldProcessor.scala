package com.amadeus.airbi.rawvault.common.processors

import scala.collection.Seq

object FieldProcessor {

  val DEFAULT_SEP = "-"
  val DEFAULT_NULL_VALUE = "_"

  /**
    * Generate a field value from multiple field values
    */
  def compositeField(fields: String*): String = {
    // When we concatenate array of string values without any separator,
    // we won't be able to get the original value if its required.
    // Its being used for "TICKET_REPORTED_STATUSES" in SAT_PRICING_DETAILS
    // e.g - can have multiple values (reported or displayed)
    fields.mkString(DEFAULT_SEP)
  }

  /**
    * Check if there is only null values
    * @param fields
    * @return empty Seq[String] if only null values, else the input data (fields)
    */
  def processDefaultNullValues( fields : Seq[String]): Seq[String] = {
    if (fields.forall(_.equals(DEFAULT_NULL_VALUE))) {
      Seq()
    }
    else {
      fields
    }
  }
}
