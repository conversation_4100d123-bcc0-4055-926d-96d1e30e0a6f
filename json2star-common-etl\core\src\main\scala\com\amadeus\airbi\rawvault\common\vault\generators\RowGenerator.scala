package com.amadeus.airbi.rawvault.common.vault.generators

import com.amadeus.airbi.json2star.common.addons.base.mapping.Table.KeyValueRow
import com.amadeus.airbi.rawvault.common.config.Blocks.Block
import com.amadeus.airbi.rawvault.common.config.ColSources._
import com.amadeus.airbi.rawvault.common.config.ColumnConfig
import com.amadeus.airbi.rawvault.common.processors.FieldProcessor
import com.amadeus.airbi.rawvault.common.processors.JsonProcessor.{JsonArray, JsonObject}
import com.jayway.jsonpath.JsonPath
import com.typesafe.scalalogging.Logger
import org.slf4j.LoggerFactory

import scala.annotation.nowarn
import scala.collection.JavaConverters._
import scala.util.{Failure, Success}

object RowGenerator {

  @transient
  val logger: Logger = Logger(LoggerFactory.getLogger(getClass.getName))

  val LOAD_DATE_COLUMN_NAME = "LOAD_DATE"
  val ACCESS_LOAD_DATE_VALUE = s"@$LOAD_DATE_COLUMN_NAME"
  val RECORD_SOURCE_COLUMN_NAME = "RECORD_SOURCE"

  /** Resolve a field from the multi-level json [[PreRow]].
    *
    * This method resolves each [[Block]] expression extracting the level and json path
    * from it, and applying it to the [[BlockDocumentContexts]] provided.
    *
    * @param sources
    * @param j multi-level json
    * @return
    */
  private def resolveField(sources: List[PreRowQuery], j: PreRow): Option[String] = {
    val values = sources
      .flatMap { jsonPath =>
        jsonPath.apply(j) match {
          case Success(None) => Seq(jsonPath -> FieldProcessor.DEFAULT_NULL_VALUE).toMap
          case Success(json) => json.map(jsonPath -> _)
          case Failure(err) =>
            logger.error(s"Extracting path $jsonPath failed with exception.", err)
            None
        }
      }
      .flatMap {
        case (_, array: JsonArray) if array.isEmpty =>
          Seq(FieldProcessor.DEFAULT_NULL_VALUE)
        case (_, array: JsonArray) =>
          array.asScala
            .filterNot(v => v == null || v.isInstanceOf[JsonArray]) // nested arrays are not supported
            .map(_.toString)
        case (_, value: JsonObject @nowarn) => // json objects are stored as compact json strings
          Seq(JsonPath.parse(value).jsonString)
        case (_, value) => Seq(value.toString)
      }
    val valuesFiltered = FieldProcessor.processDefaultNullValues(values)
    if (valuesFiltered.length <= 1) {
      valuesFiltered.headOption
    } else {
      Some(FieldProcessor.compositeField(valuesFiltered: _*))
    }
  }

  /** Generate a row from a [[PreRow]]
    *
    * For each column in columns parameters, get the related value from the preRow
    * if one of the column is defined as a literal but we can't deduce the rest of the columns
    * from the jsonDoc because it is empty, the 'literal' field is not created
    * (to avoid empty rows in case of multi active satellites)
    *
    * @param columns columns fields to create
    * @param preRow where to find columns values
    * @param loadDate load date external field of row
    * @return
    */
  def generateRow(
    columns: Seq[ColumnConfig],
    preRow: PreRow,
    loadDate: String
  ): KeyValueRow = {
    val row = columns.flatMap { c =>
      c.sources match {
        case bs @ BlocksSource(_) =>
          resolveField(bs.blocks, preRow).map(c.name -> _)
        case LiteralSource(value) if value == ACCESS_LOAD_DATE_VALUE =>
          Some(c.name -> loadDate)
        case LiteralSource(value) =>
          Some(c.name -> value)
        case NullSource() => None
        case _: RootSpecificSource => throw new IllegalArgumentException("Not supported RootSpecificSource")
      }
    }.toMap
    row
  }

}
