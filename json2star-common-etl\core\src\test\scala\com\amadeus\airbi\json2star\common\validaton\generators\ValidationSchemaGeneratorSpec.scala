package com.amadeus.airbi.json2star.common.validaton.generators

import com.amadeus.airbi.common.utils.CommonSpec
import com.amadeus.airbi.json2star.common.TablesDef
import com.amadeus.airbi.json2star.common.app.Json2StarApp.readMappingConfig
import com.amadeus.airbi.json2star.common.validation.config.ValidationConfig
import com.amadeus.airbi.json2star.common.validation.generators.ValidationForeignKeyGenerator
import com.amadeus.airbi.rawvault.common.testfwk.ImplicitConversions.Converters

import java.time.OffsetDateTime

class ValidationSchemaGeneratorSpec extends CommonSpec {


  "ValidationSchemaGenerator.toCreateValidationRequest" should "generate validation SQL queries" in {
    val validParams = ValidationConfig(validationDatabase = "MY_DB",
      validationTablename = "validation_foreignKeys",
      domain = "mapping.conf".substring(0, "mapping.conf".length - 5).toUpperCase,
      domainVersion = "31_2_3",
      customer = "CRO",
      phase = "UNIT_TEST",
      currentTimestamp = OffsetDateTime.now().toString,
      daysBack = 1)
    val test = "validation/sample_9"
    val config = readMappingConfig(s"${test}/mapping.conf".toPath, Set("latest_selector"))
    val tables = TablesDef.consolidate(config)

    val sql = ValidationForeignKeyGenerator.toCreateValidationRequest(tables, database = "MY_DB",  validParams)
    sql.foreach(c => print(c))

    val expectedResult = List(
      """WITH
        |checkReferenceTime as (select max(load_date) - INTERVAL 1 HOUR as refTime from MY_DB.FACT_RESERVATION_HISTO)
        |insert into MY_DB.validation_foreignKeys
        |(select "MAPPING" as domain,
        |"31_2_3" as domain_version,
        |"CRO" as customer,
        |"UNIT_TEST" as phase,
        |'FACT_RESERVATION_HISTO-ETAG--FACT_DUMMY_HISTO-DUMMY_C' as foreign_key,
        |count (*) as nb_rows,
        |sum(decode(foreign_table.ETAG, null, 0, 1)) / count(*) as ratio_of_foreign_key,
        |sum(decode(primary_table.DUMMY_C, null, 0, 1)) / sum(decode(foreign_table.ETAG, null, 0, 1)) as quality_of_foreign_key,
        |now() as test_datetime
        |from MY_DB.FACT_RESERVATION_HISTO foreign_table
        |left outer join MY_DB.FACT_DUMMY_HISTO primary_table
        |on foreign_table.ETAG = primary_table.DUMMY_C
        |and primary_table.version = foreign_table.version, checkReferenceTime
        |where  to_timestamp(unix_timestamp(foreign_table.LOAD_DATE, "M/d/y h:m:s a"))  <= checkReferenceTime.reftime
        |and to_timestamp(unix_timestamp(foreign_table.LOAD_DATE, "M/d/y h:m:s a")) >= checkReferenceTime.reftime - INTERVAL 1 DAYS );"""
        .stripMargin,
      """WITH
        |checkReferenceTime as (select max(load_date) - INTERVAL 1 HOUR as refTime from MY_DB.FACT_RESERVATION_HISTO)
        |insert into MY_DB.validation_foreignKeys
        |(select "MAPPING" as domain,
        |"31_2_3" as domain_version,
        |"CRO" as customer,
        |"UNIT_TEST" as phase,
        |'FACT_RESERVATION_HISTO-POINT_OF_SALE_ID--DIM_POINT_OF_SALE-POINT_OF_SALE_ID' as foreign_key,
        |count (*) as nb_rows,
        |sum(decode(foreign_table.POINT_OF_SALE_ID, null, 0, 1)) / count(*) as ratio_of_foreign_key,
        |sum(decode(primary_table.POINT_OF_SALE_ID, null, 0, 1)) / sum(decode(foreign_table.POINT_OF_SALE_ID, null, 0, 1)) as quality_of_foreign_key,
        |now() as test_datetime
        |from MY_DB.FACT_RESERVATION_HISTO foreign_table
        |left outer join MY_DB.DIM_POINT_OF_SALE primary_table
        |on foreign_table.POINT_OF_SALE_ID = primary_table.POINT_OF_SALE_ID
        |, checkReferenceTime
        |where  to_timestamp(unix_timestamp(foreign_table.LOAD_DATE, "M/d/y h:m:s a"))  <= checkReferenceTime.reftime
        |and to_timestamp(unix_timestamp(foreign_table.LOAD_DATE, "M/d/y h:m:s a")) >= checkReferenceTime.reftime - INTERVAL 1 DAYS );"""
        .stripMargin,
      """WITH
        |checkReferenceTime as (select max(load_date) - INTERVAL 1 HOUR as refTime from MY_DB.FACT_RESERVATION_HISTO)
        |insert into MY_DB.validation_foreignKeys
        |(select "MAPPING" as domain,
        |"31_2_3" as domain_version,
        |"CRO" as customer,
        |"UNIT_TEST" as phase,
        |'FACT_RESERVATION_HISTO-SECONDARY_ID--FACT_SECONDARY_HISTO-SECONDARY_ID' as foreign_key,
        |count (*) as nb_rows,
        |sum(decode(foreign_table.SECONDARY_ID, null, 0, 1)) / count(*) as ratio_of_foreign_key,
        |sum(decode(primary_table.SECONDARY_ID, null, 0, 1)) / sum(decode(foreign_table.SECONDARY_ID, null, 0, 1)) as quality_of_foreign_key,
        |now() as test_datetime
        |from MY_DB.FACT_RESERVATION_HISTO foreign_table
        |left outer join MY_DB.FACT_SECONDARY_HISTO primary_table
        |on foreign_table.SECONDARY_ID = primary_table.SECONDARY_ID
        |and primary_table.version = foreign_table.version, checkReferenceTime
        |where  to_timestamp(unix_timestamp(foreign_table.LOAD_DATE, "M/d/y h:m:s a"))  <= checkReferenceTime.reftime
        |and to_timestamp(unix_timestamp(foreign_table.LOAD_DATE, "M/d/y h:m:s a")) >= checkReferenceTime.reftime - INTERVAL 1 DAYS );"""
        .stripMargin)

    sql should equal (expectedResult)
  }

  "ValidationSchemaGenerator.findTablePrimaryKeyColumnName1" should "generate correct primary key for FACT_RESERVATION" in {
    val test = "validation/sample_9"
    val config = readMappingConfig(s"${test}/mapping.conf".toPath, Set("latest_selector"))
    val tables = TablesDef.consolidate(config)
    val masterTable = tables.tables.filter(t => t.schema.name == "FACT_RESERVATION_HISTO").head
    val column = tables.tables.head.schema.columns(1)
    val key = ValidationForeignKeyGenerator.findTablePrimaryKeyColumnName(column, tables, "FACT_RESERVATION_HISTO", masterTable)
    val expectedResult = "RESERVATION_ID"

    key should equal(expectedResult)
  }

  "ValidationSchemaGenerator.findTablePrimaryKeyColumnName2" should "generate correct primary key for non primary FACT_HISTO table" in {
    val test = "validation/sample_9"
    val config = readMappingConfig(s"${test}/mapping.conf".toPath, Set("latest_selector"))
    val tables = TablesDef.consolidate(config)
    val masterTable = tables.tables.filter(t => t.schema.name == "FACT_RESERVATION_HISTO").head
    val column = tables.tables.head.schema.columns(1)
    val key = ValidationForeignKeyGenerator.findTablePrimaryKeyColumnName(column, tables, "FACT_SECONDARY_HISTO",masterTable)
    val expectedResult = "SECONDARY_ID"

    key should equal(expectedResult)
  }


  "ValidationSchemaGenerator.findTablePrimaryKeyColumnName3" should "not find DIM_DUMMY_FK table" in {
    val test = "validation/sample_9"
    val config = readMappingConfig(s"${test}/mapping.conf".toPath, Set("latest_selector"))
    val tables = TablesDef.consolidate(config)
    val masterTable = tables.tables.filter(t => t.schema.name == "FACT_RESERVATION_HISTO").head
    val column = tables.tables.head.schema.columns.filter(_.name=="DUMMY_FK_ID").head
    val key = ValidationForeignKeyGenerator.findTablePrimaryKeyColumnName(column, tables, "DIM_DUMMY_FK",masterTable)
    val expectedResult = ValidationForeignKeyGenerator.tmpValueNotFound

    key should equal(expectedResult)
  }

  "ValidationSchemaGenerator.findPrimaryTable" should "generate correct primary table" in {
    val test = "validation/sample_9"
    val config = readMappingConfig(s"${test}/mapping.conf".toPath, Set("latest_selector"))
    val tables = TablesDef.consolidate(config)
    val masterTable = tables.tables.filter(t => t.schema.name == "FACT_RESERVATION_HISTO").head
    val allTables = tables.tables.map(_.schema.name)

    val tableName1 = "FACT_RESERVATION_HISTO"
    val column1 = tables.tables.filter(table => table.schema.name == tableName1).head.schema.columns(6)
    val table1 = ValidationForeignKeyGenerator.findPrimaryTable("POINT_OF_SALE_ID",column1, allTables)
    val expectedResult1 = "DIM_POINT_OF_SALE"

    table1 should equal(expectedResult1)

    val column2 = tables.tables.filter(table => table.schema.name == tableName1).head.schema.columns(4)
    val table2 = ValidationForeignKeyGenerator.findPrimaryTable("ETAG", column2, allTables)
    val expectedResult2 = "FACT_DUMMY_HISTO"

    table2 should equal(expectedResult2)

    val column3 = tables.tables.filter(table => table.schema.name == tableName1).head.schema.columns(7)
    val table3 = ValidationForeignKeyGenerator.findPrimaryTable("SECONDARY_ID", column3, allTables)
    val expectedResult3 = "FACT_SECONDARY_HISTO"

    table3 should equal(expectedResult3)

    val column4 = tables.tables.filter(table => table.schema.name == tableName1).head.schema.columns(8)
    val table4 = ValidationForeignKeyGenerator.findPrimaryTable("RECORD_LOCATOR", column4, allTables)
    val expectedResult4 = "No table found"

    table4 should equal(expectedResult4)
  }

}
