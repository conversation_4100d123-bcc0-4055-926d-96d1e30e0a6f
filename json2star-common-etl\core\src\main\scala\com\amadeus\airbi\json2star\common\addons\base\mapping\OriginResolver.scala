package com.amadeus.airbi.json2star.common.addons.base.mapping

import com.amadeus.airbi.json2star.common.{LiteralType, MappingType, Origin}
import com.amadeus.airbi.rawvault.common.application.config.MappingConfig
import com.amadeus.airbi.rawvault.common.config.Blocks.{Cartesian<PERSON>lock, JsonpathBlock}
import com.amadeus.airbi.rawvault.common.config.ColSources.{BlocksSource, ColSource, LiteralSource, NullSource, RootSpecificSource}
import com.amadeus.airbi.rawvault.common.config._
import com.amadeus.airbi.rawvault.common.processors.BlockResults.{BlockJsonAliasRoot, BlockJsonPathRoot}
import com.amadeus.airbi.rawvault.common.vault.generators.PreRowQuery

object OriginResolver {

  val CompositeFieldSeparator = " - "
  val NullSourceLiteral = ""

  case class ColumnWithRoot(
    column: ColumnConfig,
    root: RootSource
  )

  def resolve(a: MappingConfig, col: ColumnConfig): Seq[Origin] = {
    // Seq of columns with root source
    val colsWithRoot = a.rootSources.flatMap(rs => {
      a.allColumns(rs).map(c => ColumnWithRoot(c, rs))
    })

    // Filter only our column
    val colWithRoot = colsWithRoot.filter { c => c.column.name == col.name }

    // Generate an origin (json path) for each of the root sources
    val origins = colWithRoot.map(c => {
      val p = path(c.root, c.column.sources)
      val sourceType = if (c.column.sources.isInstanceOf[LiteralSource]) LiteralType else MappingType
      Origin(p, c.column.expr, c.column.expr.map(ExpressionManager.generateExpression(_, p)), sourceType)
    })

    // Return only distinct non empty origins (in case two root sources yield the same resolved json path)
    origins.filterNot(_.raw.trim.isEmpty).distinct
  }

  private def path(rootSource: RootSource, sources: ColSource): String = {
    def p(rootSource: RootSource, sources: ColSource): String = {
      rootSource match {
        case b: BlocksRootSource =>
          sources match {
            case BlocksSource(blocks) => composite(fromBlocks(b.blocks, blocks))
            case LiteralSource(literal) => literal
            case NullSource() => NullSourceLiteral
            case _: RootSpecificSource =>
              throw new IllegalArgumentException("Resolving RootSpecificSource should not happen")
          }
        case _: NamedRootSource =>
          throw new IllegalArgumentException("Resolving NamedRootSource should not happen")
      }

    }

    rootSource match {
      case NamedRootSource(_, rs) => p(rs, sources)
      case rs => p(rs, sources)
    }
  }

  private def composite(s: Seq[String]): String = {
    s.mkString(CompositeFieldSeparator)
  }

  private def fromBlocks(b: List[Blocks.Block], cols: List[PreRowQuery]): List[String] = {
    cols.map(block => build(b, block).mkString)
  }

  private def build(b: List[Blocks.Block], col: PreRowQuery): Option[String] = {
    b match {
      case (_: JsonpathBlock) :: _ if col.alias == BlockJsonAliasRoot => // stop, root alias requested
        concat(BlockJsonPathRoot, Some(col.path))
      case (jb: JsonpathBlock) :: _ if col.alias == jb.alias => // stop, found the right alias
        concat(jb.path, Some(col.path))
      case (jb: JsonpathBlock) :: tail => // keep looking for the right alias
        concat(jb.path, build(tail, col))
      case (cb: CartesianBlock) :: Nil =>
        val branches = cb.cartesian.map(correctBranch => build(correctBranch.blocks.toList, col))
        val matchedBranch = branches.filter(_.isDefined).headOption.flatten
        matchedBranch // one branch (at most) is expected to match the alias requested
      case (_: CartesianBlock) :: rest =>
        throw new IllegalArgumentException(s"A cartesian block cannot be followed by other blocks: ${rest}")
      case Nil =>
        None
    }
  }

  private def concat(base: String, col: Option[String]): Option[String] =
    col.map(c => base + c.substring(1)) // remove initial $ from jsonpath
}
