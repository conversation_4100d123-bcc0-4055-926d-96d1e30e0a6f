# Readme

The files in the data/pnr and data/tkt folders correspond to the following versions of PNR USCK5S and TKT 6072160023101:

```
PNR 0
PNR 8
TKT 0
TKT 3
PNR 11
TKT 4
PNR 16
TKT 5
TKT 10
```

This PNR-TKT couple has been extracted from EY PDT data.
They represent a scenario where the TKT version in the correlation section of the PNR is always set to 0, 
leading to wrong correlations (using the old algorithm).