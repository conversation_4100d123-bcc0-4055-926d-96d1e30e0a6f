package com.amadeus.airbi.json2star.common.resize

import com.amadeus.airbi.json2star.common.config.ResizeParams
import com.amadeus.airbi.json2star.common.resize.ResizeLogic.{DoResize, DontResize, Resize}
import com.typesafe.scalalogging.Logger
import org.apache.spark.sql.SparkSession
import org.slf4j.LoggerFactory

/** Base trait for resize logic.
  *
  * It factorizes common logic when handling resize (e.g. checking for config definition, logging, etc.).
  *
  * Concrete implementations should extend the trait and implement the should resize method.
  * See 'MappingResizeLogic' for example.
  */
trait ResizeLogic {

  lazy val logger: Logger = Logger(LoggerFactory.getLogger(getClass.getName))

  def shouldResize(resizeParams: ResizeParams): Resize

  /** Check whether the resize configuration is defined then delegates other specific checks to concrete implementations.
    */
  def shouldResize(resizeParams: Option[ResizeParams]): Resize = {
    resizeParams match {
      case Some(rp) => shouldResize(rp)
      case None => DontResize("Resize Params not defined")
    }
  }

  /** Handle the cluster resize checking for common condition (e.g. resize configuration defined) and delegating the
    * specific checks to concrete implementations of the trait.
    *
    * @param sparkSession spark session
    * @param resizeActuator optional resize actuator
    */
  def handleClusterResize(sparkSession: SparkSession, resizeActuator: Option[ResizeActuator]): Unit = {
    val r = shouldResize(resizeActuator.map(_.config.resizeParams))
    r match {
      case DoResize(numWorkers, reason) => {
        logger.info(s"[resize] Resize to $numWorkers. Reason: $reason")
        resizeActuator.foreach(a => a.resize(sparkSession, numWorkers))
      }
      case DontResize(reason) => logger.info(s"[resize] Don't resize. Reason: $reason")
    }
  }

}

object ResizeLogic {

  sealed trait Resize
  case class DoResize(numWorkers: Int, reason: String) extends Resize
  case class DontResize(reason: String) extends Resize
}
