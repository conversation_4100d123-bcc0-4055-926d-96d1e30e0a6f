CREATE TABLE IF NOT EXISTS MY_DB.FACT_PASSENGER_HISTO (
	INTERNAL_ZORDER STRING NOT NULL,
	PASS<PERSON><PERSON>R_ID STRING NOT NULL,
	REFERENCE_KEY STRING NOT NULL,
	CPR_FEED_TYPE STRING,
	ETAG STRING,
	GROUP_NAME STRING,
	IS_MASTER_RECORD BOOLEAN,
	IS_SAME_PHYSICAL_CUSTOMER BOOLEAN,
	IS_SYSTEM_MARKED_SPC BOOLEAN,
	TYPE STRING,
	BIRTH_DATE DATE,
	BIRTH_PLACE STRING,
	PAX_TYPE STRING,
	<PERSON><PERSON>ER STRING,
	NATIONALITY STRING,
	SPECIAL_SEAT STRING,
	FIRST_NAME STRING,
	LAST_NAME STRING,
	TITLE STRING,
	RESIDENCE_COUNTRY STRING,
	AGE INT,
	STAFF_CATEGORY STRING,
	STAFF_COMPANY_CODE STRING,
	STAFF_COMPANY_NAME STRING,
	STAFF_ID STRING,
	STAFF_BOOKING_TYPE STRING,
	STAFF_JOINING_DATE TIMESTAMP,
	<PERSON>AFF_RELATIONSHIP STRING,
	STAFF_RETIREMENT_DATE TIMESTAMP,
	STAFF_TRANSFER_DAYS INT,
	STAFF_TRANSFERS_DURING_DAYS INT,
	RECORD_LOCATOR STRING,
	VERSION STRING,
	INTERNAL_DATE_BEGIN TIMESTAMP,
	DATE_BEGIN TIMESTAMP,
	DATE_END TIMESTAMP,
	IS_LAST_VERSION BOOLEAN,
	LOAD_DATE TIMESTAMP
)
USING DELTA PARTITIONED BY(IS_LAST_VERSION)
TBLPROPERTIES(delta.autoOptimize.optimizeWrite = true, delta.autoOptimize.autoCompact = true, delta.enableChangeDataFeed = true);

CREATE TABLE IF NOT EXISTS MY_DB.FACT_SECONDARY_HISTO (
	FACT_SECONDARY_ID STRING NOT NULL,
	REFERENCE_KEY STRING NOT NULL,
	VERSION STRING,
	DATE_BEGIN TIMESTAMP,
	DATE_END TIMESTAMP,
	IS_LAST_VERSION BOOLEAN,
	LOAD_DATE TIMESTAMP
)
USING DELTA PARTITIONED BY(IS_LAST_VERSION)
TBLPROPERTIES(delta.autoOptimize.optimizeWrite = true, delta.autoOptimize.autoCompact = true, delta.enableChangeDataFeed = true);

CREATE TABLE IF NOT EXISTS MY_DB.INTERNAL_MY_TABLE (
	BAG_GROUP_ID STRING NOT NULL,
	VERSION STRING,
	REF_BAG_GROUP_ID STRING NOT NULL,
	DATE_BEGIN TIMESTAMP NOT NULL,
	DATE_END TIMESTAMP,
	IS_LAST_VERSION BOOLEAN,
	LOAD_DATE TIMESTAMP
)
USING DELTA PARTITIONED BY(IS_LAST_VERSION)
TBLPROPERTIES(delta.autoOptimize.optimizeWrite = true, delta.autoOptimize.autoCompact = true, delta.enableChangeDataFeed = true);

CREATE VIEW IF NOT EXISTS MY_DB.FACT_PASSENGER
AS SELECT INTERNAL_ZORDER,PASSENGER_ID,REFERENCE_KEY,CPR_FEED_TYPE,ETAG,GROUP_NAME,IS_MASTER_RECORD,IS_SAME_PHYSICAL_CUSTOMER,IS_SYSTEM_MARKED_SPC,TYPE,BIRTH_DATE,BIRTH_PLACE,PAX_TYPE,GENDER,NATIONALITY,SPECIAL_SEAT,FIRST_NAME,LAST_NAME,TITLE,RESIDENCE_COUNTRY,AGE,STAFF_CATEGORY,STAFF_COMPANY_CODE,STAFF_COMPANY_NAME,STAFF_ID,STAFF_BOOKING_TYPE,STAFF_JOINING_DATE,STAFF_RELATIONSHIP,STAFF_RETIREMENT_DATE,STAFF_TRANSFER_DAYS,STAFF_TRANSFERS_DURING_DAYS,RECORD_LOCATOR,VERSION,INTERNAL_DATE_BEGIN,LOAD_DATE FROM FACT_PASSENGER_HISTO WHERE IS_LAST_VERSION=true
;