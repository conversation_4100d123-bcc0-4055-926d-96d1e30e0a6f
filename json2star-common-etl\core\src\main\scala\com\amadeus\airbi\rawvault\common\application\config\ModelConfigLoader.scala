package com.amadeus.airbi.rawvault.common.application.config

import com.amadeus.airbi.json2star.common.addons.base.AddonConfig
import com.amadeus.airbi.json2star.common.addons.stackable.{StackableAddonConfig, StackableAddons}
import com.amadeus.airbi.rawvault.common.config.Blocks.{Block, CartesianBlock, JsonpathBlock}
import com.amadeus.airbi.rawvault.common.config.ColSources.{BlocksSource, ColSource, LiteralSource, NullSource, RootSpecificSource}
import com.amadeus.airbi.rawvault.common.config._
import com.amadeus.airbi.rawvault.common.vault.generators.PreRowQuery
import com.jayway.jsonpath.JsonPath
import pureconfig.error.{CannotParse, ConfigReaderException, ConfigReaderFailures, ExceptionThrown}
import pureconfig.generic.ProductHint
import pureconfig.{ConfigCursor, ConfigReader, ConfigSource}

import scala.util.Try

object ModelConfigLoader {

  import pureconfig.generic.auto._

  implicit val columnTypeReader: ConfigReader[ColumnType.Value] = ConfigReader[String].map(ColumnType.withName)

  val cartesianBlockReader: ConfigReader[CartesianBlock] = implicitly[ConfigReader[CartesianBlock]]

  implicit val jsonpathBlockReader: ConfigReader[JsonpathBlock] = ConfigReader[Map[String, String]].emap { block =>
    val (alias, jpath) = block.head
    val j = Try(JsonPath.compile(jpath))
    j.fold(e => Left(ExceptionThrown(e)), b => Right(JsonpathBlock(alias = alias, path = jpath)))
  }

  implicit val preRowQueryReader: ConfigReader[PreRowQuery] = ConfigReader[Map[String, String]].emap { block =>
    val (alias, jpath) = block.head
    val j = Try(JsonPath.compile(jpath))
    j.fold(e => Left(ExceptionThrown(e)), b => Right(PreRowQuery(alias = alias, path = jpath)))
  }

  implicit val blockReader: ConfigReader[Block] = (cur: ConfigCursor) => {
    val jb = jsonpathBlockReader.from(cur)
    val cb = cartesianBlockReader.from(cur)
    (jb, cb) match {
      case (s @ Right(_), _) => s
      case (_, b @ Right(_)) => b
      case (Left(s), Left(b)) =>
        val cf = ConfigReaderFailures(
          CannotParse("Cannot identify block type (jsonpath or cartesian)", s.head.origin)
        )
        Left(cf ++ s ++ b)
    }
  }

  implicit val blocksReader: ConfigReader[Blocks] = ConfigReader[List[Block]].map(i => Blocks.from(i))

  val blocksRootSourceReader: ConfigReader[BlocksRootSource] = ConfigReader[BlocksRootSource].emap { i =>
    BlocksRootSource.validate(i).fold(e => Left(ExceptionThrown(e)), b => Right(b))
  }

  implicit val rootSourceReader: ConfigReader[RootSource] = (cur: ConfigCursor) => {
    val blocks = blocksRootSourceReader.from(cur)
    val named = namedRootSourceReader.from(cur)
    (blocks, named) match {
      case (b @ Right(_), _) => b
      case (_, n @ Right(_)) => n
      case (Left(b), Left(n)) =>
        val cf =
          ConfigReaderFailures(CannotParse("One of 'blocks' or `named` must be provided", b.head.origin))
        Left(cf ++ b ++ n)
    }
  }

  // This needs to be declared after the `implicit val rootSourceReader above` as NamedRootSource contains a RootSource
  val namedRootSourceReader: ConfigReader[NamedRootSource] =
    ConfigReader.forProduct2("name", "rs") { (s, rs) =>
      NamedRootSource(s, rs)
    }

  implicit val sourceWithRootReader: ConfigReader[SourceWithRoot] =
    ConfigReader
      .forProduct3("rs-name", "blocks", "literal")((r, b, l) => SourceWithRoot(r, b, l))
      .emap(i => SourceWithRoot.validate(i).fold(e => Left(ExceptionThrown(e)), b => Right(b)))

  // enforces the fact that NullSource must be empty (otherwise it's always ok and other sources errors are swallowed)
  implicit val nullSourceHint: ProductHint[NullSource] = ProductHint[NullSource](allowUnknownKeys = false)

  // Custom reader for Sources needed to avoid having to specify redundant 'kind' key, for instance:
  // {"kind": "mapping-source", "mapping": [ ... ] }
  // {"kind": "literal-source", "literal": [ ... ] }
  // The different kinds of config are read with the following precedence: mapping, literal, blocks
  implicit val columnConfigReader: ConfigReader[ColSource] = (cur: ConfigCursor) => {
    val literal = ConfigReader[LiteralSource].from(cur).map(identity)
    val blocks = ConfigReader[BlocksSource].from(cur).map(identity)
    val rootSpecific = ConfigReader[RootSpecificSource].from(cur).map(identity)
    val nullSource = ConfigReader[NullSource].from(cur).map(identity)
    (literal, blocks, rootSpecific, nullSource) match {
      case (l @ Right(_), _, _, _) => l
      case (_, b @ Right(_), _, _) => b
      case (_, _, r @ Right(_), _) => r
      case (_, _, _, n @ Right(_)) => n
      case (Left(l), Left(b), Left(r), Left(n)) =>
        val cf =
          ConfigReaderFailures(
            CannotParse("Use 'literal', 'blocks', 'root-specific' or leave empty", b.head.origin)
          )
        Left(cf ++ l ++ b ++ r ++ n)
    }
  }

  implicit def eitherReader[A: ConfigReader, B: ConfigReader]: ConfigReader[Either[A, B]] =
    new ConfigReader[Either[A, B]] {
      def from(cur: ConfigCursor) = {
        // try left, if fail try right
        val tmp = ConfigReader[A].from(cur).map(Left(_))
        tmp match {
          case Left(err) => ConfigReader[B].from(cur).map(Right(_))
          case r => r
        }
      }
    }


  // Intended for use in tests
  def fromResources(path: String): TablesConfig =
    ConfigSource
      .resources(path)
      .loadOrThrow[TablesConfig]

  def fromResourcesOrFile(path: String): TablesConfig = {
    val res = ConfigSource.resources(path).load[TablesConfig]
    val fil = ConfigSource.file(path).load[TablesConfig]
    (res, fil) match {
      case (Right(r), _) => r
      case (_, Right(f)) => f
      case (Left(r), Left(f)) =>
        throw new ConfigReaderException[TablesConfig](r ++ f)
    }
  }

  /** Load the model applying the tableSelectors
    *
    * @param path path (either resource or filesystem) to the model configuration file
    * @param tableSelectors set of table selectors
    *  @param disabledStackableAddons set of disabled stackable addons
    * @return [[TablesConfig]] with the selected tables, having only enabled stackable addons
    */
  // TODO align methods here (where to load from: resources, file, ...) + select or not
  def defaultLoad(path: String, tableSelectors: Set[String], disabledStackableAddons: Set[String]): TablesConfig = {
    val conf: TablesConfig = fromResourcesOrFile(path)
    val selectedTables = conf.selected(tableSelectors)
    StackableAddons.filterStackableAddons(selectedTables, disabledStackableAddons)
  }

}
