package com.amadeus.airbi.json2star.common.integration

import com.amadeus.airbi.datalake.common.spark.DefaultLocalSparkInstance
import com.amadeus.airbi.json2star.common.app.{Json2StarApp, SnowflakePushApp}
import com.amadeus.airbi.json2star.common.app.Json2StarApp.readMappingConfig
import com.amadeus.airbi.json2star.common.config.PartialReprocessingParams
import com.amadeus.airbi.rawvault.common.testfwk.Json2StarSpec
import com.amadeus.airbi.rawvault.common.testfwk.snowflake.{MockSnowflakeContext, SnowflakeSpec}
import org.apache.commons.io.FileUtils

import java.io.File
import java.nio.file.FileSystems
import scala.reflect.io.Directory

class IntegrationSnowflakePushAppSpec extends Json2StarSpec with SnowflakeSpec {

  val mappingFile = "src/test/resources/datasets/sfpush_multiple_batches/simple_mapping.conf"
  val tableNames: Seq[ResourcePath] = getTableNames(mappingFile)
  val partialReprocessingParams: Option[PartialReprocessingParams] = Some(PartialReprocessingParams("0", Seq("FACT_AIR_SEGMENT_PAX_HISTO")))

  override val SourceDatabase: ResourcePath = inputDatabase
  override val SnowflakeDatabase: ResourcePath = s"${SourceDatabase}_${SnowflakeSuffix}".toLowerCase

  // test with multiple batches
  override def isLight: Boolean = false

  override def beforeAll: Unit = {
    super.beforeAll

    // Create the Snowflake database
    FileUtils.deleteDirectory(
      FileSystems.getDefault.getPath(DefaultLocalSparkInstance.warehouseDir, SnowflakeDatabase + ".db").toFile
    )
    createDatabaseWithSuffix(dbSuffix = SnowflakeSuffix)
    createTables(mappingFile, dbSuffix = Some(SnowflakeSuffix))

    // Create the feed database tables (and set such DB as default)
    createTables(mappingFile)
  }

  override def beforeEach: Unit = {
    cleanTables(tableNames)
    cleanTables(tableNames, dbSuffix = Some(SnowflakeSuffix))
  }


  SnowflakePushApp.getClass.getName should "Correctly mirror to snowflake for upstream feeds with multiple batches" in withTmpDir { tmp =>
    val dataDir = "datasets/sfpush_multiple_batches/data/"
    val pathExpectedResults = "src/test/resources/" + dataDir + "expected_results/"
    val rca = rootConfig(dataDir + "/a/", inputDatabase, isLight = isLight)
    val mapping = readMappingConfig(mappingFile)

    val etlCheckpointDir = new Directory(new File(rca.etl.stream.sink.checkpointLocation))
    val sfPushCheckpointDir = tmp.toString
    val snowflakeContext = MockSnowflakeContext(spark, MockSnowflakeQueryRunner, SourceDatabase, sfPushCheckpointDir)

    // First batch
    etlCheckpointDir.deleteRecursively()
    Json2StarApp.run(spark, rca, mapping)
    checkTablesContent(tableNames, pathExpectedResults + "a/")
    SnowflakePushApp.run(spark, snowflakeContext, mapping)
    checkTablesContent(tableNames, pathExpectedResults + "a/", dbSuffix = Some(SnowflakeSuffix))

    // Second batch
    etlCheckpointDir.deleteRecursively()
    val rcb = rootConfig(dataDir + "/b/", inputDatabase, isLight = isLight)
    Json2StarApp.run(spark, rcb, mapping)
    checkTablesContent(tableNames, pathExpectedResults + "b/")
    SnowflakePushApp.run(spark, snowflakeContext, mapping)
    checkTablesContent(tableNames, pathExpectedResults + "b/", dbSuffix = Some(SnowflakeSuffix))
  }

  it should "Correctly mirror to snowflake for upstream feeds with purge" in withTmpDir { tmp =>
    val dataDir = "datasets/sfpush_purge/data/"
    val pathExpectedResults = "src/test/resources/" + dataDir + "expected_results/"
    val rca = rootConfig(dataDir + "/a/", inputDatabase, isLight = isLight)
    val mapping = readMappingConfig(mappingFile)

    val etlCheckpointDir = new Directory(new File(rca.etl.stream.sink.checkpointLocation))
    val sfPushCheckpointDir = tmp.toString
    val snowflakeContext = MockSnowflakeContext(spark, MockSnowflakeQueryRunner, SourceDatabase, sfPushCheckpointDir)

    // J2S batch
    etlCheckpointDir.deleteRecursively()
    Json2StarApp.run(spark, rca, mapping)
    checkTablesContent(tableNames, pathExpectedResults + "a/")
    SnowflakePushApp.run(spark, snowflakeContext, mapping)
    checkTablesContent(tableNames, pathExpectedResults + "a/", dbSuffix = Some(SnowflakeSuffix))

    // Simulate the purge of AAABBB-2022-05-22 records (in master and secondary)
    tableNames.map(tableName =>
      spark.sql(s"DELETE FROM $SourceDatabase.$tableName WHERE RESERVATION_ID = 'hashM(AAABBB-2022-05-22)'")
    )

    SnowflakePushApp.run(spark, snowflakeContext, mapping)
    checkTablesContent(tableNames, pathExpectedResults + "b/", dbSuffix = Some(SnowflakeSuffix))
  }


  it should "throw an exception if a table that is not reprocessed doesn't have clone in its history" in withTmpDir { tmp =>
    val dataDir = "datasets/sfpush_partial_reproc/data/"
    val rca = rootConfig(dataDir + "/a/", inputDatabase, isLight = isLight)
    val mapping = readMappingConfig(mappingFile)

    val etlCheckpointDir = new Directory(new File(rca.etl.stream.sink.checkpointLocation))
    val sfPushCheckpointDir = tmp.toString
    val snowflakeContext = MockSnowflakeContext(spark, MockSnowflakeQueryRunner, SourceDatabase, sfPushCheckpointDir)

    // First batch
    etlCheckpointDir.deleteRecursively()
    Json2StarApp.run(spark, rca, mapping)

    an[RuntimeException] should be thrownBy {
      SnowflakePushApp.run(
        spark,
        snowflakeContext,
        mapping,
        partialReprocessingParams = partialReprocessingParams
      )
    }

  }

  /** Scenario :
   * In case of partial reprocessing, the non-reprocessed tables are cloned.
   * Therefor their history might contain some records + 1 record for the clone.
   * In this case the SnowflakePushApp should only push the modifications that were made after the clone (i.e. ignore the first batch).
   */
  it should "Correctly mirror to snowflake with partial reprocessing and not mirror all input" in withTmpDir { tmp =>
    val dataDir = "datasets/sfpush_partial_reproc/data/"
    val pathExpectedResults = "src/test/resources/" + dataDir + "expected_results/"
    val rca = rootConfig(dataDir + "/a/", inputDatabase, isLight = isLight)
    val mapping = readMappingConfig(mappingFile)

    val etlCheckpointDir = new Directory(new File(rca.etl.stream.sink.checkpointLocation))
    val sfPushCheckpointDir = tmp.toString
    val snowflakeContext = MockSnowflakeContext(spark, MockSnowflakeQueryRunner, SourceDatabase, sfPushCheckpointDir)

    // First batch
    etlCheckpointDir.deleteRecursively()
    Json2StarApp.run(spark, rca, mapping)

    spark.sql("ALTER TABLE FACT_RESERVATION_HISTO RENAME TO FACT_RESERVATION_HISTO_OLD")

    // Copy of non-reprocessed delta tables
    // Note : in PRD we use Deep Clone that is not available for our delta.io version.
    // However, shallow clone as the same effect on the history of the table.
    spark.sql("CREATE OR REPLACE TABLE FACT_RESERVATION_HISTO SHALLOW CLONE FACT_RESERVATION_HISTO_OLD;")

    // There must be 1 version (the clone)
    spark.sql("DESCRIBE HISTORY FACT_RESERVATION_HISTO").count() should be(1)

    // Second batch
    etlCheckpointDir.deleteRecursively()
    val rcb = rootConfig(dataDir + "/b/", inputDatabase, isLight = isLight)
    Json2StarApp.run(spark, rcb, mapping)

    // The app only pushes the records from the second batch for FACT_RESERVATION_HISTO
    SnowflakePushApp.run(
      spark,
      snowflakeContext,
      mapping,
      partialReprocessingParams = partialReprocessingParams
    )

    checkTablesContent(tableNames, pathExpectedResults + "a/", dbSuffix = Some(SnowflakeSuffix))

  }


  /** Scenario :
   * In case of partial reprocessing, the non-reprocessed tables are cloned.
   * Therefor their history contains only one record 0 being the clone.
   * If there is no second batch, the app should not crash even if it starts reading from version 1 (which doesn't exist)
   * This test would crash if spark.databricks.delta.changeDataFeed.timestampOutOfRange.enabled was not set to true
   */
  it should "not crash in case of partial reprocessing without second batch" in withTmpDir { tmp =>
    val dataDir = "datasets/sfpush_partial_reproc/data/"
    val pathExpectedResults = "src/test/resources/" + dataDir + "expected_results/"
    val rca = rootConfig(dataDir + "/a/", inputDatabase, isLight = isLight)
    val mapping = readMappingConfig(mappingFile)

    val etlCheckpointDir = new Directory(new File(rca.etl.stream.sink.checkpointLocation))
    val sfPushCheckpointDir = tmp.toString
    val snowflakeContext = MockSnowflakeContext(spark, MockSnowflakeQueryRunner, SourceDatabase, sfPushCheckpointDir)

    // First batch
    etlCheckpointDir.deleteRecursively()
    Json2StarApp.run(spark, rca, mapping)

    // Add an entry to the table
    spark.sql("ALTER TABLE FACT_RESERVATION_HISTO RENAME TO FACT_RESERVATION_HISTO_OLD_2")
    
    // Copy of non-reprocessed delta tables
    // Note : in PRD we use Deep Clone that is not available for our delta.io version.
    // However, shallow clone as the same effect on the history of the table.
    spark.sql("CREATE OR REPLACE TABLE FACT_RESERVATION_HISTO SHALLOW CLONE FACT_RESERVATION_HISTO_OLD_2;")

    // There must be 2 versions (create and clone)
    spark.sql("DESCRIBE HISTORY FACT_RESERVATION_HISTO").count() should be(1)

    // The app shouldn't push the anything for FACT_RESERVATION_HISTO
    SnowflakePushApp.run(
      spark,
      snowflakeContext,
      mapping,
      partialReprocessingParams = partialReprocessingParams
    )

    checkTablesContent(tableNames, pathExpectedResults + "b/", dbSuffix = Some(SnowflakeSuffix))

  }

}
