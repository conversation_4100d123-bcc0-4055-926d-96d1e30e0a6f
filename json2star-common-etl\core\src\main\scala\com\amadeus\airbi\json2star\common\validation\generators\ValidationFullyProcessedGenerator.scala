package com.amadeus.airbi.json2star.common.validation.generators

import com.amadeus.airbi.json2star.common.TablesDef
import com.amadeus.airbi.json2star.common.addons.base.mapping.input.AvroDecoder
import com.amadeus.airbi.json2star.common.validation.config.ValidationConfig
import com.databricks.dbutils_v1.DBUtilsHolder.dbutils
import org.apache.spark.sql.{Row, SparkSession}
import org.apache.spark.sql.catalyst.encoders.RowEncoder
import org.apache.spark.sql.functions.{col, from_json, input_file_name, lit, schema_of_json}
import org.apache.spark.sql.types.{StringType, StructField, StructType, TimestampType}

import java.nio.file.Files
import java.time.{ZoneId, ZonedDateTime}
import java.time.format.DateTimeFormatter

/** It generates the  statements  from the mapping file to validate all Files from EventHub ere processed
  */
object ValidationFullyProcessedGenerator {

  /** Generate the SQL statement to initialize a Table
    *
    * @param conf     a Table Config - ongoing HubConfigBean
    * @param database database name
    * @return a init SQL
    */
  def toCreateValidationRequest(
    conf: TablesDef,
    database: String,
    validParams: ValidationConfig
  ): List[(String, String, String)] = {
    var number = 1
    val masterTable = conf.tables
      .filter(table =>
        table.table.mapping.isDefined
          && table.table.mapping.get.masterPit.isDefined
      )
      .head

    val tableName = (masterTable.schema.name)
    val primaryColumnNames = masterTable.schema.keyColumns.map(_.name).toList
    val query = buildCheck(database, tableName, primaryColumnNames, validParams = validParams)
    val task = s""" Check input feed fully processed in ${tableName} in star schema history  """
    val funcCase =
      s"""Test 1 - Check ${validParams.domain} feed for ${validParams.customer} fully processed into ${masterTable.schema.name}""".stripMargin
    number = number + 1
    List((task, funcCase, query))

  }

  def buildCheck(
    database: String,
    tableName: String,
    primaryKeyColumns: List[String],
    validParams: ValidationConfig
  ): String = {

    s"""WITH
       | checkReferenceTime as (select max(load_date) - INTERVAL 1 HOUR as refTime from ${database}.${tableName}),
       | STAR_SCHEMA as ( select ${primaryKeyColumns.mkString(",")}, REFERENCE_KEY from ${database}.${tableName}),
       | FAILED_RECORDS AS  (
       |  select RAW_DATA.*
       |  from RAW_DATA,checkReferenceTime
       |  where to_timestamp(unix_timestamp(LOAD_DATE, "M/d/y h:m:s a"))  <= checkReferenceTime.reftime
       |  and not exists
       |  (
       |  select 1 from STAR_SCHEMA
       |  where
       |  RAW_DATA.version = STAR_SCHEMA.version
       |  and RAW_DATA.REFERENCE_KEY = STAR_SCHEMA.reference_key
       |  )
       | ),
       | total_records AS (select count(*) AS nb_total_records from RAW_DATA),
       | counter_failed_records AS  (select count(*)  AS nb_failed_records from FAILED_RECORDS),
       | sample_failed_records AS(select concat_ws(',',collect_list(distinct failed)) AS fsample
       |  from (select concat_ws('-',REFERENCE_KEY,VERSION) as failed from FAILED_RECORDS limit 5)
       | )
       | select nb_total_records, nb_failed_records, fsample
       | from counter_failed_records, total_records, sample_failed_records
       | ;""".stripMargin

  }

  def createTempRawDataTable(spark: SparkSession, database: String, myFeed: String, daysBack: Int, domain: String): Long = {
    import spark.implicits._
    spark.catalog.setCurrentDatabase(database)
    val baseUrlRegex =
      if (spark.sparkContext.isLocal) {raw"(\w+)*/(\w+)*/(\w+)*/(\w+)*/(\w+)*/".r} // Only for unit tests
      else {raw"abfss://\w+(-\w+)*@\w+(\.\w+)*/\w+(-\w+)*/\w+(-\w+)*/".r}
    val baseUrls = baseUrlRegex.findAllMatchIn(myFeed).map(_.toString.dropRight(1)).toList
    val paths = baseUrls.flatMap(baseUrl => {
      (daysBack.toInt to 0 by -1).map(n => {
        val date_formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd")
        try {
          if (spark.sparkContext.isLocal) { // Only for unit tests
            val curr_date = date_formatter format ZonedDateTime.parse("2024-03-25T23:00:00Z").minusDays(n)
            Files.list(new java.io.File(s"${baseUrl}/0/${curr_date}").toPath)
            s"${baseUrl}/0/${curr_date}/*"
          } else {
            val curr_date = date_formatter format ZonedDateTime.now(ZoneId.of("UTC")).minusDays(n)
            dbutils.fs.ls(s"${baseUrl}/0/${curr_date}")
            s"${baseUrl}/*/${curr_date}/*/*"
          }
        } catch {
          case e: java.io.FileNotFoundException => "TO_REMOVE"
          case e: java.nio.file.NoSuchFileException => "TO_REMOVE"
        }
      })
    })
    val pathsFiltered = paths filterNot "TO_REMOVE".==

    print(pathsFiltered)

    val parseData = if (pathsFiltered.length > 0) {
      val df = spark.read
        .format("avro")
        .load(pathsFiltered: _*)
        .toDF()

      val myDf = df.map(row => Row(AvroDecoder.readBody(row), row.get(2)))(
        RowEncoder(
          StructType(
            StructField("bodyAsJsonString", StringType, nullable = true) :: StructField(
              "loadDate",
              StringType,
              nullable = true
            ) :: Nil
          )
        )
      )

      val goodEvents = myDf.filter(col("bodyAsJsonString").contains("current")).cache()

      // get the schema
      val mySchematmp = domain match {
        case "SKDJ2S" => goodEvents
          .filter(
            col("bodyAsJsonString").contains("image")
              && col("bodyAsJsonString").contains("event")
              && col("bodyAsJsonString").contains("timestamp")
              && col("bodyAsJsonString").contains("version")
          )
        case "TKTEMD" => goodEvents
          .filter(
            col("bodyAsJsonString").contains("image")
              && col("bodyAsJsonString").contains("latestEvent")
              && col("bodyAsJsonString").contains("version")
          )
        case "INV" => goodEvents
          .filter(
            col("bodyAsJsonString").contains("image")
              && col("bodyAsJsonString").contains("inventoryFileSnapshotDateTime")
              && col("bodyAsJsonString").contains("version")
          )
        case _ => goodEvents
          .filter(
            col("bodyAsJsonString").contains("image")
              && col("bodyAsJsonString").contains("lastModification")
              && col("bodyAsJsonString").contains("version")
          )
      }
      val mySchema = mySchematmp.select(col("bodyAsJsonString"))
        .first
        .getString(0)

      val jsonResults = goodEvents
        .select(
          col("bodyAsJsonString"),
          from_json(col("bodyAsJsonString"), schema_of_json(lit(mySchema))).as("BodyParsed"),
          $"loadDate"
        )
        .cache()

      // Data //
      // ----------- //
      jsonResults
        .withColumn("REFERENCE_KEY", $"BodyParsed.mainResource.current.image.id")
        .withColumn("version", $"BodyParsed.mainResource.current.image.version")
        .withColumn("valid_from_date", domain match {
          case "SKDJ2S" => $"BodyParsed.mainResource.current.image.event.timestamp"
          case "TKTEMD" => $"BodyParsed.mainResource.current.image.latestEvent.dateTime"
          case "INV" => $"BodyParsed.mainResource.current.image.inventoryFileSnapshotDateTime"
          case _ => $"BodyParsed.mainResource.current.image.lastModification.dateTime"
        })
        .withColumn("filename", input_file_name)
        .withColumnRenamed("loadDate", "load_date")
        .drop("bodyAsJsonString")
        .drop("BodyParsed")
    } else {
      val my_schema = StructType(Seq(
        StructField("filename", StringType),
        StructField("valid_from_date", TimestampType),
        StructField("load_date", TimestampType),
        StructField("version", StringType),
        StructField("REFERENCE_KEY", StringType)
      ))
      spark.createDataFrame(spark.sparkContext.emptyRDD[Row], my_schema)
    }
    parseData.createOrReplaceTempView("RAW_DATA")

    parseData.count()
  }
}
