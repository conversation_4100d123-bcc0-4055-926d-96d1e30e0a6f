%scala
import com.amadeus.airbi.json2star.common.validation.executors.{ValidationExecutor, ValidationSchemaExecutor}
import com.amadeus.airbi.json2star.common.validation.config.CheckType
import scala.io.Source

val appConfigFile = dbutils.widgets.get("appConfigFile")
val valDatabase = dbutils.widgets.get("valDatabase")
val valTableName = dbutils.widgets.get("valTableName")
val testType = CheckType.withName(dbutils.widgets.get("testType"))
val daysBack = dbutils.widgets.get("daysBack")
val phase = dbutils.widgets.get("phase")
val maxPartitionBytes = dbutils.widgets.get("maxPartitionBytes")
val inputFeed = try {
  dbutils.widgets.get("inputFeed")
} catch {
  case _: Exception => ""
}

spark.conf.set("spark.sql.files.maxPartitionBytes", maxPartitionBytes)

testType match {
  case CheckType.NO_DUPES | CheckType.FULLY_PROCESSED | CheckType.NO_DUPES_LATEST =>
    ValidationExecutor.main(Array(appConfigFile, valDatabase, valTableName, daysBack, testType.toString(), phase,inputFeed))
  case CheckType.FOREIGN_KEYS =>
    ValidationSchemaExecutor.main(Array(appConfigFile, valDatabase,valTableName,daysBack, testType.toString(),phase,inputFeed))
}
