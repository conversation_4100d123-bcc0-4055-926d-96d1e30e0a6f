package com.amadeus.airbi.json2star.common.addons.base.correlation

import com.amadeus.airbi.datalake.common.spark.MergeHelper
import com.amadeus.airbi.json2star.common.addons.base.correlation.CorrelationPipeline._
import com.amadeus.airbi.json2star.common.app.Display.displayDuration
import com.amadeus.airbi.json2star.common.app.TemporaryTable
import com.amadeus.airbi.json2star.common.app.TemporaryTable.dropDefaultDeltaTable
import com.amadeus.airbi.rawvault.common.RootConfig
import com.amadeus.airbi.rawvault.common.application.config.Correlation.AssoTable
import com.amadeus.airbi.rawvault.common.application.config.TableConfig
import com.amadeus.airbi.rawvault.common.correlation.Correlation2Ways
import com.amadeus.airbi.rawvault.common.correlation.Correlation2Ways.logger
import io.delta.tables.DeltaTable
import org.apache.spark.sql.functions.{col, expr, lit}
import org.apache.spark.sql.{DataFrame, SparkSession}

import scala.concurrent.{ExecutionContext, Future}

object CorrelationPipeline {
  sealed trait SourceDomain

  case object DomainA extends SourceDomain

  case object DomainB extends SourceDomain
}

class CorrelationPipeline(spark: SparkSession, conf: RootConfig, table: TableConfig, correlation: AssoTable) {
  private val baseCheckpointLocation = conf.etl.stream.sink.checkpointLocation

  // @formatter:off
  private val domainA = correlation.domainA.name                            // e.g. PNR
  private val domainB = correlation.domainB.name                            // e.g. TKT
  private val dbA = conf.inputDatabases(domainA)
  private val dbB = conf.inputDatabases(domainB)

  private val pitA = tableName(dbA, correlation.domainA.pit.table)          // e.g. PNR.FACT_RESERVATION_HISTO
  private val partialA = tableName(dbA, correlation.domainA.partial.table)  // e.g. PNR.INTERNAL_ASSO_AIR_SEGMENT_PAX_COUPON_HISTO
  private val pitB = tableName(dbB, correlation.domainB.pit.table)          // e.g. TKT.FACT_TRAVEL_DOCUMENT_HISTO
  private val partialB = tableName(dbB, correlation.domainB.partial.table)  // e.g. TKT.INTERNAL_ASSO_AIR_SEGMENT_PAX_COUPON_HISTO
  private val linkBA = correlation.correlationFieldBToA                     // e.g. RESERVATION_ID
  private val linkAB = correlation.correlationFieldAToB                     // e.g. TRAVEL_DOCUMENT_ID
  private val fineGrainedKeyA = correlation.domainA.partial.partialCorrKey  // e.g. AIR_SEGMENT_PAX_ID
  private val fineGrainedKeyB = correlation.domainB.partial.partialCorrKey  // e.g. COUPON_ID

  // Key of Partial Table A and B --> used for the union+dedup operation
  private val keyPartialA = Seq(correlation.domainA.partial.partialCorrKey, correlation.domainA.partial.partialCorrSecondaryKey, correlation.domainA.partial.partialCorrVersion)
  private val keyPartialB = Seq(correlation.domainB.partial.partialCorrKey, correlation.domainB.partial.partialCorrSecondaryKey, correlation.domainB.partial.partialCorrVersion)

  // Key of Target Table --> used for the union+dedup operation
  private val keyAsso = Seq(correlation.target.domainAKey.name, correlation.target.domainBKey.name, correlation.target.domainAVersion.name, correlation.target.domainBVersion.name)

  private val internalDeleteFlag = "delete_flag"

  // @formatter:on

  def runStream()(implicit ec: ExecutionContext): Future[Unit] = Future {
    val displayFlag = conf.processingParams.displayMainEvents
    displayDuration(s"CORRCMP\tTABLE=${table.name}" + _, logger, displayFlag) {

      logger.info(s"Running stream for computed correlation table ${table.name}")
      /**
       * STEP 1 - Build CORR IDS table from 4 upstream tables
       */
      val corrIds = new CorrelationIdsTable(conf.etl.common.outputDatabase, table.name)
      corrIds.createTableIfNotExists(spark)

      Seq(pitA, partialA).foreach { srcTable => // run stream for each source table of domain A
        corrIds.insertCorrIds(spark, srcTable, linkBA, DomainA, baseCheckpointLocation)
      }

      Seq(pitB, partialB).foreach { srcTable => // run stream for each source table of domain B
        corrIds.insertCorrIds(spark, srcTable, linkAB, DomainB, baseCheckpointLocation)
      }

      /**
       * STEP 2 - Use IDS Table to Merge updates in the asso table
       */
      corrIds.streamCorrIds(spark, baseCheckpointLocation, updateAssoFromIds)
    }
  }

  def withTmpDeltaTable(df: DataFrame, tmpTableNamePrefix: String, batchId: Long)(code: DataFrame => Unit): Unit = {
    val tmpTableName = TemporaryTable.temporaryTableName(s"${tmpTableNamePrefix}_BATCH_${batchId}")
    try {
      spark.sparkContext.setJobGroup(
        s"BATCH=$batchId",
        s"TYPE=CORRCMP-PERSIST_${tmpTableNamePrefix} TABLE=${table.name}"
      )
      val persistedDf = TemporaryTable.toDefaultDeltaTable(df, tmpTableName)(spark)
      code(persistedDf)
    } finally {
      dropDefaultDeltaTable(tmpTableName)(spark)
    }
  }

  private def updateAssoFromIds(batchIds: DataFrame, batchId: Long): Unit = {
    import CorrelationIdsTable.Columns._
    logger.info(s"Updating ${table.name} from IDS (batch $batchId)")
    // Get unique IDs from the batch to reduce data to process
    val idsADf = batchIds.select(col(DOMAIN_A_ID)).distinct()
    val idsBDf = batchIds.select(col(DOMAIN_B_ID)) distinct()
    withTmpDeltaTable(idsADf, "IDS_A", batchId) { idsADelta: DataFrame =>
      withTmpDeltaTable(idsBDf, "IDS_B", batchId) { idsBDelta: DataFrame =>
        // Step 2.1 - Read partials with ids at coarse lvl for domains A and B
        val filterPartialOnA = s"partial.${linkBA} = ids.${DOMAIN_A_ID}"
        val filterPartialOnB = s"partial.${linkAB} = ids.${DOMAIN_B_ID}"

        // Read PARTIAL table from domain A
        val fPartialAonA = spark.read.table(partialA).as("partial").join(idsADelta.as("ids"), expr(filterPartialOnA), "leftsemi")
        val fPartialAonB = spark.read.table(partialA).as("partial").join(idsBDelta.as("ids"), expr(filterPartialOnB), "leftsemi")
        val fPartialADf = MergeHelper.dedupBatchRecordsRowNumber(
          (fPartialAonA union fPartialAonB), keyPartialA, Seq(expr(correlation.domainA.partial.startDate).desc_nulls_last))

        // Read PARTIAL table from domain B
        val fPartialBonA = spark.read.table(partialB).as("partial").join(idsADelta.as("ids"), expr(filterPartialOnA), "leftsemi")
        val fPartialBonB = spark.read.table(partialB).as("partial").join(idsBDelta.as("ids"), expr(filterPartialOnB), "leftsemi")
        val fPartialBDf = MergeHelper.dedupBatchRecordsRowNumber(
          (fPartialBonA union fPartialBonB), keyPartialB, Seq(expr(correlation.domainB.partial.startDate).desc_nulls_last))

        // Persist filtered PARTIAL table from domain A and B
        withTmpDeltaTable(fPartialADf, "PARTIAL_A", batchId) { fPartialA: DataFrame =>
          withTmpDeltaTable(fPartialBDf, "PARTIAL_B", batchId) { fPartialB: DataFrame =>
            // Step 2.2 - read pits with ids from filtered partial tables
            val sameKeyA = s"pit.${linkBA} = partialIds.${linkBA}"
            val sameKeyB = s"pit.${linkAB} = partialIds.${linkAB}"

            // Read A.PIT table keeping only rows related to the keys in B.PARTIAL
            // This is done to have end_date and version from A.PIT
            val fPartialBKeysDf = fPartialB.select(col(linkBA)).distinct()
            withTmpDeltaTable(fPartialBKeysDf, "IDS_A_FROM_PARTIAL_B", batchId) { fPartialBKeysDelta =>
              val fPitA = spark.read.table(pitA).as("pit").join(fPartialBKeysDelta.as("partialIds"), expr(sameKeyA), "leftsemi")

              // Read PIT table from domain B (symmetric)
              val fPartialAKeysDf = fPartialA.select(col(linkAB)).distinct()
              withTmpDeltaTable(fPartialAKeysDf, "IDS_B_FROM_PARTIAL_A", batchId) { fPartialAKeysDelta =>
                val fPitB = spark.read.table(pitB).as("pit").join(fPartialAKeysDelta.as("partialIds"), expr(sameKeyB), "leftsemi")

                // Step 2.3 - Apply correlation logic to compute period of validity
                val intermediateAtoB =
                  Correlation2Ways.intermediateDataframe(spark, correlation, fPartialA, fPitB, Correlation2Ways.DomainAtoDomainB)
                val intermediateBtoA =
                  Correlation2Ways.intermediateDataframe(spark, correlation, fPartialB, fPitA, Correlation2Ways.DomainBtoDomainA)

                // Step 2.4 - Union and de-duplicate intermediate dataframes
                val union = intermediateAtoB union intermediateBtoA

                // de-duplication using the criteria from the config and giving priority to non-null assoAttributesSeq
                val dedupCols = (correlation.target.ifDupeTakeHighest ++ correlation.target.assoAttributesSeq.map(_.name))
                val sanitizedPatch = MergeHelper.dedupBatchRecordsRowNumber(union, keyAsso, dedupCols.map(e => expr(e).desc_nulls_last))

                // Step 2.5 - Merge patch as rows to delete (matching IDs of partial) + rows to insert (new patch)
                val deleteFinerGrainedKeys =
                  (fPartialA.select(fineGrainedKeyA, fineGrainedKeyB).union(fPartialB.select(fineGrainedKeyA, fineGrainedKeyB))).distinct()
                spark.sparkContext.setJobGroup(
                  s"BATCH=$batchId",
                  s"TYPE=CORRCMP-MERGE TABLE=${table.name}"
                )

                val toDeleteAndAppend = buildPatch(deleteFinerGrainedKeys, sanitizedPatch)
                singleMerge(table.name, toDeleteAndAppend)
              }
            }
          }
        }
      }
    }
  }

  private def buildPatch(deleteDf: DataFrame, insertDf: DataFrame): DataFrame = {
    val toInsert = insertDf.withColumn(internalDeleteFlag, lit(false))
    val toDelete = toInsert.columns.diff(deleteDf.columns).foldLeft(deleteDf) {
      (acc, col) => acc.withColumn(col, lit(null))
    }.withColumn(internalDeleteFlag, lit(true))

    toInsert union toDelete.select(toInsert.schema.fieldNames.map(col): _*)
  }

  private def singleMerge(targetTable: String, patch: DataFrame): Unit = {
    val columnsMap = DeltaTable.forName(targetTable).toDF.columns.map {
      col => col -> s"src.$col"
    }.toMap
    DeltaTable.forName(targetTable).alias("dst")
      .merge(
        patch.alias("src"),
        s"src.${fineGrainedKeyA} = dst.${fineGrainedKeyA} AND src.${fineGrainedKeyB} = dst.${fineGrainedKeyB} and src.${internalDeleteFlag} = true"
      )
      .whenMatched()
      .delete()
      .whenNotMatched(s"src.${internalDeleteFlag} = false")
      .insertExpr(columnsMap)
      .execute()
  }

  // build fully qualified table name, including the DB
  private def tableName(db: String, table: String) = s"$db.$table"

}