package com.amadeus.airbi.json2star.common.app

import com.amadeus.airbi.json2star.common.addons.base.mapping.ExpressionManager.generateExpression
import com.amadeus.airbi.json2star.common.app.Json2StarApp.readMappingConfig
import com.amadeus.airbi.json2star.common.config.{
  AppConfig,
  DimPrefillerFileParams,
  DimPrefillerParams,
  SnowflakeParams
}
import com.amadeus.airbi.json2star.common.prefiller.PrefillerConfig
import com.amadeus.airbi.rawvault.common.JobInfo
import com.amadeus.airbi.rawvault.common.application.config.{DimPrefiller, TableConfig, TablesConfig}
import com.amadeus.airbi.rawvault.common.config.ColSources.LiteralSource
import com.amadeus.airbi.rawvault.common.vault.hashers.StarSchemaHasher
import com.typesafe.scalalogging.Logger
import io.delta.tables.DeltaTable
import org.apache.spark.sql.functions.{col, expr, lit}
import org.apache.spark.sql.{DataFrame, SparkSession}
import org.json4s.DefaultFormats
import org.slf4j.LoggerFactory
import org.json4s.jackson.Serialization.{write => asJson}
import org.rogach.scallop.{ScallopConf, ScallopOption}

import java.io.File
import java.sql.Timestamp
import java.time.Instant
import java.time.format.DateTimeFormatter

case class RefDataPrefillerAppScallopConf(arguments: Seq[String]) extends ScallopConf(arguments) {
  val appConfigFile: ScallopOption[String] = opt[String](required = true)
  verify()
}
object RefDataPrefillerApp {
  @transient
  lazy val logger: Logger = Logger(LoggerFactory.getLogger(getClass.getName))
  val StandardDateTimeFormat = DateTimeFormatter.ISO_INSTANT
  val LOAD_DATE_COLUMN_NAME = "LOAD_DATE"
  val RECORD_SOURCE_COLUMN_NAME = "RECORD_SOURCE"
  def main(args: Array[String]): Unit = {
    val argsSc = RefDataPrefillerAppScallopConf(args)
    val appConfigFile = argsSc.appConfigFile()
    val prefillerLoadDate = Timestamp.from(Instant.now)
    val prefillerConfig = PrefillerConfig.apply(appConfigFile, prefillerLoadDate)

    logger.info(s"--- Starting Ref Data Prefiller pipeline ---")
    val spark: SparkSession = SparkSession
      .builder()
      .getOrCreate()

    val model = readMappingConfig(prefillerConfig.mappingConfFilePath, prefillerConfig.tablesSelectors)

    run(spark, prefillerConfig, model)

  }

  /** For each table with prefiller conf, convert data to dim data and append to existing table
    *  Example
    *  Dim table defined as
    *   (PASSENGER_TYPE_ID, PASSENGER_CODE, PASSENGER_LABEL, ADDITIONAL_COLUMN_NOT_PROVIDED_BY_SRC, ADDITIONAL_COLUMN_INIT_BY_LITERAL)
    *         input csv src :
    *           ( CSV_PASSENGER_CODE, CSV_PASSENGER_LABEL, CSV_NOT_TO_BE_USED)
    *           ( "ADT", "ADULTE", "irrelevant")
    *           ( "BB", "BABY", "irrelevant bb")
    *        output dim :
    *           (PASSENGER_TYPE_ID, PASSENGER_CODE, PASSENGER_LABEL, ADDITIONAL_COLUMN_NOT_PROVIDED_BY_SRC, ADDITIONAL_COLUMN_INIT_BY_LITERAL)
    *           ( hashS("ADT"),"ADT", "ADULTE", null, LITERAL_AS_DEFINED_IN_DIM_MAPPING)
    *           ( hashS("BB"), "BB", "BABY", null, LITERAL_AS_DEFINED_IN_DIM_MAPPING)
    *
    * @param spark
    * @param prefillerConfig params from app-config-file
    * @param tablesConf model from domain.conf
    */
  def run(spark: SparkSession, prefillerConfig: PrefillerConfig, tablesConf: TablesConfig): Unit = {
    val database = prefillerConfig.common.outputDatabase

    // Register custom UDFs before doing anything
    StarSchemaHasher.registerUdfs(
      spark,
      prefillerConfig.processingParams.isDefined && prefillerConfig.processingParams.get.hashDebug
    )

    val toBePrefilledTables: List[TableConfig] = tablesConf
      .selected(prefillerConfig.tablesSelectors)
      .tables
      .filter(t => t.mapping.isDefined && !t.prefiller.isEmpty)

    toBePrefilledTables.foreach(t =>
      t.prefiller.foreach(p => {
        val folder = prefillerConfig.prefillerParams.folderPath //databricks

        prefillerConfig.prefillerParams.inputFiles.get(p.dataSourceKey) match {
          case Some(f) =>
            try {
              val dfFullDimSchema = readSrcFileIntoDimDf(spark, t, p, folder, f, prefillerConfig.loadDate)
              prefillerMerge(database + "." + t.name, dfFullDimSchema, t.mapping.get.merge.keyColumns)
            } catch {
              case e: Throwable =>
                logger.error(s"### ERROR: while reading dataSourceKey ${p.dataSourceKey} : " + e.getMessage)
            }
          case _ => logger.error(s"### ERROR: dataSourceKey ${p.dataSourceKey}  does not exists in appConfFile ")

        }
      })
    )

  }

  def readSrcFileIntoDimDf(
    spark: SparkSession,
    t: TableConfig,
    p: DimPrefiller,
    folderPath: String,
    fileParam: DimPrefillerFileParams,
    prefillerLoadDate: Timestamp
  ): DataFrame = {
    val srcPrefix = "SRCTMP_"

    val dfFromSrc = spark.read
      .format(fileParam.format)
      .options(fileParam.options)
      .load(folderPath + "/" + fileParam.path)

    val dimSchema = t.mapping.get.allOrderedColumns
    val srcSchema = dfFromSrc.schema.fields.map(_.name).toSet
    //prefix all existing columns with SRCTMP
    val dfRenamedFromSrc = srcSchema.foldLeft(dfFromSrc)((df, cf) => df.withColumnRenamed(cf, srcPrefix + cf))
    //don't  forget to drop unused column form inputFile
    val toBeDropped = p.ColumnFillerMap.values.toSet.filter(n => srcSchema.contains(srcPrefix + n)).toArray
    val dfFromSrcWihtoutUnusedColumns = dfRenamedFromSrc.drop(toBeDropped: _*)

    //convert Columns + apply expressions
    val dfConvertedToDim = p.columnFiller
      .foldLeft(dfFromSrcWihtoutUnusedColumns)((df, cf) => {
        val tmp = df.withColumn(cf.dimCol, col(srcPrefix + cf.srcCol))

        val exprV = dimSchema.filter(_.name == cf.dimCol) match {
          case Seq(first) => first.expr
          case _ => None
        }
        exprV match {
          case Some(exprValue) => tmp.withColumn(cf.dimCol, expr(generateExpression(exprValue, cf.dimCol)))
          case _ => tmp
        }
      })
      .drop(dfFromSrcWihtoutUnusedColumns.schema.fields.map(_.name).toArray: _*)

    //add DIM columns not provided by src file
    val dfFullDimSchema = dimSchema.foldLeft(dfConvertedToDim)((dfAcc, colConf) => {
      val tmpSchema = dfConvertedToDim.schema.fields.map(_.name)
      if (!tmpSchema.contains(colConf.name)) {
        dfAcc.withColumn(
          colConf.name,
          colConf.name match {
            case RECORD_SOURCE_COLUMN_NAME => lit(p.dataSourceKey)
            case _ =>
              colConf.sources match {
                case LiteralSource(value) => lit(value)
                case _ => lit(null)
              }
          }
        )
      } else {
        dfAcc
      }
    })

    //adding LOAD_DATE & DATA_SOURCE
    val loadDate = prefillerLoadDate
    dfFullDimSchema.withColumn(LOAD_DATE_COLUMN_NAME, lit(loadDate))

  }

  def buildConfigs(args: Array[String]): (AppConfig, DimPrefillerParams, TablesConfig) = {
    val argsSc = SnowflakePushAppScallopConf(args)
    val appConfigFile = argsSc.appConfigFile()
    val jobId = argsSc.jobId()
    val runId = argsSc.runId()

    val appConfig = AppConfig(appConfigFile)
    val prefillerParams = appConfig.assumeRefDataPrefillerParams(Some(jobId), Some(runId))
    val model = readMappingConfig(appConfig.modelConfFile, appConfig.tablesSelectors)

    logger.info(s"### J2S APP CONFIG: ${asJson(appConfig)(DefaultFormats)}")
    logger.info(s"### REF DATA PREFILLER CONFIG: ${asJson(prefillerParams)(DefaultFormats)}")
    logger.info(s"### MODEL: ${asJson(model)(DefaultFormats)}")

    (appConfig, prefillerParams, model)
  }

  def prefillerMerge(deltaName: String, batchDf: DataFrame, keyColumns: List[String]): Unit = {
    val delta = DeltaTable.forName(deltaName)
    delta
      .as("delta")
      .merge(batchDf.as("batch"), keyColumns.map(field => s"delta.$field <=> batch.$field").mkString(" and "))
      .whenMatched()
      .updateAll()
      .whenNotMatched()
      .insertAll()
      .execute()

  }

}
