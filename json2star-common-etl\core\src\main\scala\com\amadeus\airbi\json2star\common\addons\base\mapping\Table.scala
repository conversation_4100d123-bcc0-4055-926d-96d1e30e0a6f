package com.amadeus.airbi.json2star.common.addons.base.mapping

import com.amadeus.airbi.json2star.common.addons.base.mapping.Table.{Stats, TableRowType}

object Table {

  type TableRowType = Seq[String] //Map[String, String] // Needed a well defined type for Spark Serialization
  type KeyValueRow = Map[String, String]


  case class RowDropCoordinates(recordId: String, table: String)
  sealed trait TableRowDropReason {
    def toDebugString: String = s"Record dropped: $this"
  }
  case class MandatoryColMissing(coord: RowDropCoordinates, reason: String) extends TableRowDropReason
  case class AllPrimaryKeysNull(coord: RowDropCoordinates, reason: String) extends TableRowDropReason

  object Stats {
    val Zero: Stats = Stats(
      rowDiscardedCosPrimaryKeyNull = 0,
      rowDiscardedCosMandatoryCols = 0
    )
  }


  /**
    * Stats collected while generating a table
    *
    * @param rowDiscardedCosPrimaryKeyNull number of rows discarded because all fields of the Primary Key were Null
    * @param rowDiscardedCosMandatoryCols number of rows discarded because at least one mandatory column was Null
    */
  case class Stats(
    rowDiscardedCosPrimaryKeyNull: Int,
    rowDiscardedCosMandatoryCols: Int
  ) {
    def aggregate(other: Stats): Stats = Stats(
      rowDiscardedCosPrimaryKeyNull = other.rowDiscardedCosPrimaryKeyNull + rowDiscardedCosPrimaryKeyNull,
      rowDiscardedCosMandatoryCols = other.rowDiscardedCosMandatoryCols + rowDiscardedCosMandatoryCols
    )
  }
}

case class Table(name: String, rows: Seq[TableRowType], stats: Stats)
