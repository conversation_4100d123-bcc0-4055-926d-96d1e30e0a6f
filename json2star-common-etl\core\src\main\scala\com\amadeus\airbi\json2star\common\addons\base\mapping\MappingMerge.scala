package com.amadeus.airbi.json2star.common.addons.base.mapping

import com.amadeus.airbi.json2star.common.addons.stackable.StackableAddonConfig
import com.amadeus.airbi.json2star.common.addons.stackable.StackableAddons.stackableAddonLookup
import com.amadeus.airbi.json2star.common.app.TemporaryTable.{dropDefaultDeltaTable, temporaryTableName, toDefaultDeltaTable}
import com.amadeus.airbi.rawvault.common.RootConfig
import com.amadeus.airbi.rawvault.common.application.config.Merge
import com.typesafe.scalalogging.Logger
import io.delta.tables.{DeltaMergeBuilder, DeltaTable}
import org.apache.spark.sql.{DataFrame, SparkSession}
import org.slf4j.LoggerFactory

import scala.util.{Failure, Success, Try}

object MappingMerge {

  lazy val logger: Logger = Logger(LoggerFactory.getLogger(getClass.getName))

  /** Merge function with the following parameters:
    * - target table
    * - input dataframe
    * - merge keys
    */
  type MergeFunction = (String, DataFrame, Merge) => Unit

  /** Wrapper method applying the stackable addons enrichment to the input batch before merging.
    *
    * It may be configured to rerun the merge without stackable addons in case of merge failure (cf. fatalStackableAddonsErrors)
    *
    * It accepts a merge function.
    */
  def mergeWithStackableAddons(
    rootConfig: RootConfig,
    deltaName: String,
    batchDf: DataFrame,
    merge: Merge,
    stackableAddons: List[StackableAddonConfig],
    mergeFunc: MergeFunction,
    tableAccess: TableAccess
  )(implicit spark: SparkSession): Unit = {
    val actualMergeFunc = tableAccess.mergeFunction(mergeFunc, rootConfig)
    val cols = tableAccess.tableMapping.conf.columns
    val result = Try {
      val enrichedBatchDf =
        stackableAddons.foldLeft(batchDf)((b, a) => stackableAddonLookup(a).enrichBatch(b, a, rootConfig))
      actualMergeFunc(deltaName, ExpressionManager.applyPostExpressions(enrichedBatchDf, cols), merge)
    }

    val fatalStackableAddonsErrors = rootConfig.etl.common.fatalStackableAddonsErrors

    result match {
      case Success(_) => ()
      case Failure(f) =>
        val msg =
          s"Exception at merge of table $deltaName applying stackable addons and fatal-stackable-addons-errors = $fatalStackableAddonsErrors"
        if (!fatalStackableAddonsErrors) {
          logger.warn(s"$msg, retrying without addons")
          actualMergeFunc(deltaName, batchDf, merge)
        } else {
          logger.error(msg)
          throw new Exception(msg, f)
        }
    }
  }

  /** Merge method allowing to specify a condition on the is-last column in the target table.
    *
    * The method assumes that:
    * - we are doing a delta on delta merge.
    * - the input dataframe has no dupes (otherwise there can be an exception at merge)
    *
    * @param isLast     is last column name
    * @param isLastVal  optional boolean value to put in the is-last condition, if none is passed, no last condition is set at merge
    * @param deltaName  target master pit Delta table
    * @param batchNewDf source pitted new versions dataframe read from Delta
    * @param merge      merge columns
    */
  def smartMerge(
    isLast: String,
    isLastVal: Option[Boolean]
  )(deltaName: String, batchNewDf: DataFrame, merge: Merge): Unit = {
    val delta = DeltaTable.forName(deltaName)
    val keyCondition = merge.keyColumns.map(field => s"delta.$field <=> batch.$field")
    val lastCondition = isLastVal.map(v => s"delta.$isLast = $v").toList
    delta
      .as("delta")
      .merge(batchNewDf.as("batch"), (lastCondition ++ keyCondition).mkString(" and "))
      .whenMatched()
      .updateAll()
      .whenNotMatched()
      .insertAll()
      .execute()
  }

  /** Simple merge method, without any condition on is-last column.
    * It inserts new records and updates existing ones.
    *
    * @param enablePreMergeDeltaPersist if set to true, it persists the input batch on delta before merge
    * @param deltaName target delta table
    * @param batchDf input batch
    * @param merge merge keys
    */
  def simpleMerge(
    enablePreMergeDeltaPersist: Boolean
  )(
    deltaName: String,
    batchDf: DataFrame,
    merge: Merge
  )(implicit sparkSession: SparkSession): Unit = {
    simpleMergeBuilder(mergeUpsert, enablePreMergeDeltaPersist)(deltaName, batchDf, merge)
  }

  /** Simple merge method, without any condition on is-last column.
    *  It inserts only new records, without updating existing ones.
    *
    * @param enablePreMergeDeltaPersist if set to true, it persists the input batch on delta before merge
    * @param deltaName                  target delta table
    * @param batchDf                    input batch
    * @param merge                      merge keys
    */
  def simpleMergeInsertOnly(
    enablePreMergeDeltaPersist: Boolean
  )(
    deltaName: String,
    batchDf: DataFrame,
    merge: Merge
  )(implicit sparkSession: SparkSession): Unit = {
    simpleMergeBuilder(mergeInsertOnly, enablePreMergeDeltaPersist)(deltaName, batchDf, merge)
  }

  private def simpleMergeBuilder(
    mergeExec: DeltaMergeBuilder => Unit,
    enablePreMergeDeltaPersist: Boolean
  )(
    deltaName: String,
    batchDf: DataFrame,
    merge: Merge
  )(implicit sparkSession: SparkSession): Unit = {
    val delta = DeltaTable.forName(deltaName)
    val tmpTableName = temporaryTableName(deltaName)
    try {
      val batch = if (enablePreMergeDeltaPersist) {
        toDefaultDeltaTable(batchDf, tmpTableName)(sparkSession)
      } else {
        batchDf
      }
      mergeExec(
        delta
          .as("delta")
          .merge(batch.as("batch"), merge.keyColumns.map(field => s"delta.$field <=> batch.$field").mkString(" and "))
      )
    } finally {
      if (enablePreMergeDeltaPersist) {
        dropDefaultDeltaTable(tmpTableName)(sparkSession)
      }
    }
  }

  private def mergeInsertOnly(dmb: DeltaMergeBuilder): Unit = {
    dmb
      .whenNotMatched()
      .insertAll()
      .execute()
  }

  private def mergeUpsert(dmb: DeltaMergeBuilder): Unit = {
    dmb
      .whenMatched()
      .updateAll()
      .whenNotMatched()
      .insertAll()
      .execute()
  }

}
