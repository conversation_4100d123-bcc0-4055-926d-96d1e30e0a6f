package com.amadeus.airbi.rawvault.common.correlation

import com.amadeus.airbi.rawvault.common.correlation.CorrelationLibrary.{PLUS_INFINITY, VersionType}
import com.google.common.collect.Range
import org.apache.spark.sql._
import org.apache.spark.sql.functions.col
import org.apache.spark.sql.types.LongType

import java.sql.Timestamp
import scala.math.Ordered.orderingToOrdered

// TODO rename to Correlation1Way
class CorrelationLibrary protected (
  val END_DATE: String,
  val START_DATE: String,
  val PARTIAL_CORR_VERSION: String,
  val PIT_VERSION: String,
  val PARTIAL_CORR_KEY: String,
  val PIT_CORR_KEY: Option[String],
  val CORRELATION_FIELD: String, // this field must be in both tables to correlate
  val PARTIAL_CORR_SECONDARY_KEY: String,
  val IS_LAST_PARTIAL: String,
  val IS_LAST_PIT: String
) extends Serializable {

  implicit def ordered: Ordering[Timestamp] = (x: Timestamp, y: Timestamp) => x compareTo y

  /** This function computes the asso records for a given fine-grained functional entity.
    * The functional entity is defined by an identifier (like a RLOC-date for PNR, or ticket number for TKT, ...). Such
    * identifier must be present in the partial table records, and in the master pit table records.
    *
    * The input is:
    * - all histo records for such entity coming from the fact in domain A (named masterPitRecords, even though is a simple fact histo) (would be TKT)
    * - association records for such given functional entity in domain B(named partialCorrRecords) (would be PNR)
    *
    * The assumptions:
    *  'overlappingTktPitRecords' is already filtered on matching (TKT, COUPON) upstream
    *  'assoTablePartial' contains only records for given (PNR, TKT, COUPON)
    *  inputs are sorted Seq[Row] by VERSION
    * All these assumptions are ensured by the COGROUP operation
    *
    * PARTIAL_CORR_KEY would be AIR_SEGMENT_PAX_ID (or PNR_ID?)
    * PARTIAL_CORR_SECONDARY_KEY would be COUPON_ID
    * CORRELATION_FIELD would be TRAVEL_DOCUMENT_ID (aka TKT_ID)
    *
    * @param partialCorrRecords should have at least (PNR_ID, TKT_ID, COUPON_ID, START_DATE, END_DATE, VERSION(PNR))
    * @param masterPitRecords should have at least (TKT_ID, COUPON_ID, START_DATE, END_DATE, VERSION(TKT))
    * @param partialCorrAttributeFields columns names of the partial corr table taken as is
    * @param pitCorrAttributeFields columns names of the pit  table taken as is
    * @return (PNR, TKT, COUPON, START, END, VERSION(PNR), VERSION(TKT)) maybe also (START_P, END_P, START_T, END_T) fpr control
    */
  @SuppressWarnings(Array("scala:S3776"))
  def computeNewCorr(
    partialCorrRecords: Seq[Row],
    masterPitRecords: Seq[Row],
    partialCorrAttributeFields: Seq[String] = Seq(),
    pitCorrAttributeFields: Seq[String] = Seq()
  ): Seq[Row] = {

    var partial = partialCorrRecords
    var masterpit = masterPitRecords
    var result = Seq[Row]()

    while (partial.nonEmpty && masterpit.nonEmpty) { // masterpit
      val (partialHead +: partialTail, pitHead +: pitTail) = (partial, masterpit)

      val partialEndDate = Option(partialHead.getAs[Timestamp](END_DATE)).getOrElse(PLUS_INFINITY)
      val pitEndDate = Option(pitHead.getAs[Timestamp](END_DATE)).getOrElse(PLUS_INFINITY)

      if (
        partialHead.getAs[Timestamp](START_DATE) <= pitEndDate &&
        pitHead.getAs[Timestamp](START_DATE) <= partialEndDate
      ) { // *overlap*
        // if overlap, compute the overlap by calling intersection
        val validRange = Range
          .closed(partialHead.getAs[Timestamp](START_DATE), partialEndDate)
          .intersection(Range.closed(pitHead.getAs[Timestamp](START_DATE), pitEndDate))
        if (!validRange.lowerEndpoint().equals(validRange.upperEndpoint())) {
          result = Row.fromSeq(
            Seq(
              partialHead.getAs[String](PARTIAL_CORR_KEY), // partial.corr_key
              partialHead.getAs[String](PARTIAL_CORR_SECONDARY_KEY), // partial.corr_sec_key
              partialHead.getAs[VersionType](PARTIAL_CORR_VERSION), // partial.VERSION
              pitHead.getAs[VersionType](PIT_VERSION), // pit.VERSION
              validRange.lowerEndpoint(), // computed
              validRange.upperEndpoint(), // computed
              partialHead.getAs[Boolean](IS_LAST_PARTIAL) && pitHead.getAs[Boolean](IS_LAST_PIT) // computed
            ) ++ partialCorrAttributeFields.map(partialHead.getAs[AnyVal](_)) // partial.attributes
              ++ pitCorrAttributeFields.map(pitHead.getAs[AnyVal](_)) // pit.attributes
          ) +: result // prepend is more efficient O(1) than append O(N)
        }
      }
      if (partialEndDate < pitEndDate) {
        partial = partialTail
      } else if (partialEndDate > pitEndDate) {
        masterpit = pitTail
      } else {
        partial = partialTail
        masterpit = pitTail
      }
    }
    result
  }

  /** This method computes correlations between 2 entities from different domains
    *  (e.g. the ASSO_LEG_DELIVERY_BAG_LEG_DELIVERY_HISTO table has correlations between DCSPAX.LEG_DELIVERY and DCSBAG.BAG_LEG_DELIVERY)
    * The output is
    *  - ASSO DataFrame at lvl DOMAIN_A_KEY, DOMAIN_A_VERSION, DOMAIN_B_KEY, DOMAIN_B_VERSION
    *    (e.g. the ASSO DataFrame is at lvl DCSPAX.LEG_DELIVERY, DCSPAX.VERSION, DCSBAG.BAG_LEG_DELIVERY, DCSBAG.VERSION)
    *
    * The processing flow is configured by parameters set in the `CorrelationLibrary` class
    * to select fields according to the kind of correlations.
    * (e.g. DIH_CORRELATION has PARTIAL always at the same granularity level than the output ASSO)
    * (e.g. DAAS_CORRELATION can have PARTIAL at higher granularity level than the output ASSO)
    *
    * The input is
    *  - PARTIAL DataFrame with all correlations as seen from 1 domain for the common CORRELATION_FIELD
    *      (e.g. for EXT_CORR - DCSBAG.PARTIAL all rows for 1 PAX at lvl BAG_LEG_DELIVERY,LEG_DELIVERY)
    *      (e.g. for INT_CORR - DCSBAG.PARTIAL all rows for 1 LEG at lvl BAG_LEG_DELIVERY,LEG)
    *      (note: for INT_CORR rows are at LEG lvl - higher lvl than LEG_DELIVERY: 1 leg can have more leg deliveries)
    *
    *  - PIT DataFrame with all histo records for the common CORRELATION_FIELD
    *      (e.g.  for EXT_CORR - DCSPAX.PIT all rows for 1 PAX at lvl LEG_DELIVERY)
    *      (e.g.  for INT_CORR - DCSPAX.PIT all rows for 1 LEG at lvl LEG_DELIVERY)
    *
    * The processing is done using these operations:
    * -  Spark cogroup to put together rows of PARTIAL and PIT having the same CORRELATION_FIELD
    *     (e.g. for EXT_CORR - at lvl PAX)
    *     (e.g. for INT_CORR - at lvl LEG)
    *
    * within each group it aggregates rows at the finer level of granularity (ASSO lvl)
    * - for PARTIAL aggregate by PARTIAL_CORR_SECONDARY_KEY, PARTIAL_CORR_KEY
    *    (e.g. for EXT_CORR by BAG_LEG_DELIVERY,LEG_DELIVERY)   - same lvl as ASSO lvl
    *    (e.g. for INT_CORR by BAG_LEG_DELIVERY,LEG)            - higher lvl than ASSO lvl
    *
    * - for PIT aggregate by PIT_CORR_KEY if defined else keep as is
    *     (e.g. for EXT_CORR by PAX)                            - higher lvl than ASSO lvl
    *     (e.g. for INT_CORR by LEG_DELIVERY)                   - same lvl as ASSO lvl
    *
    * for each subgroup it computes the new correlations
    *
    * @param partialAssoDF  DataFrame with the partial correlations
    * @param pitDF     DataFrame with the pit records
    * @param assoAttributeFieldsFromPartial columns to keep from the partialAssoDF
    * @param assoAttributeFieldsFromPit columns to keep from the pitDF
    * @return the DataFrame with the computed correlations
    */
  def processCorrelations(
    partialAssoDF: DataFrame,
    pitDF: DataFrame,
    assoAttributeFieldsFromPartial: Seq[String] = Seq(),
    assoAttributeFieldsFromPit: Seq[String] = Seq()
  )(implicit
    spark: SparkSession,
    en: Encoder[Row] // need the rowencoder + output schema
  ): DataFrame = {
    import spark.implicits._
    val partialByCorrId = partialAssoDF
      .withColumn(PARTIAL_CORR_VERSION, col(PARTIAL_CORR_VERSION).cast(LongType))
      .groupByKey(_.getAs[String](CORRELATION_FIELD))

    val pitByCorrId = pitDF
      .withColumn(PIT_VERSION, col(PIT_VERSION).cast(LongType))
      .groupByKey(_.getAs[String](CORRELATION_FIELD))

    partialByCorrId
      .cogroup(pitByCorrId) {
        // e.g. still coarse grained (multiple coupons/air_seg_pax for the grouped TKT)
        case (_, partial, pit) =>
          // Aggregate PARTIAL by PARTIAL_CORR_SECONDARY_KEY, PARTIAL_CORR_KEY
          val partialByKeysSorted = partial.toSeq
            .groupBy(x => (x.getAs[String](PARTIAL_CORR_SECONDARY_KEY), x.getAs[String](PARTIAL_CORR_KEY)))
            .values
            .map(_.sortBy(_.getAs[VersionType](PARTIAL_CORR_VERSION)))

          // Aggregate PIT by PIT_CORR_KEY if defined else keep as is
          val pitByKeys = PIT_CORR_KEY match {
            case None =>
              List(pit.toSeq)
            case Some(pitCorrKey) =>
              val rowsByPitCorrKey = pit.toSeq
                .groupBy(x => x.getAs[String](pitCorrKey))
                .values
                .map(_.sortBy(_.getAs[VersionType](PIT_VERSION)))
              rowsByPitCorrKey
          }
          val pitByKeySorted = pitByKeys.map(_.sortBy(_.getAs[VersionType](PIT_VERSION)))

          /** Generate correlations within each group of PARTIAL keys
            * Example: ASSO_LEG_DELIVERY_BAG_LEG_DELIVERY_HISTO (INT_CORR)
            * Given:
            * within the cogroup, rows have all the same leg
            *   partial = [bag1del1_leg1, bag1del2_leg1, bag2del1_leg1]
            *   pit     = [leg1del1, leg1del2]
            * when group partial by leg,bagdel:
            *   partialByKeysSorted = [ [bag1del1_leg1], [bag1del2_leg1], [bag2del1_leg1] ]
            * when group pit by legdel:
            *   pitByKeySorted = [ [leg1del1], [leg1del2] ]
            *
            *  compute corr as partialByKeysSorted x pitByKeySorted
            */
          partialByKeysSorted.flatMap(partialAssoRows =>
            pitByKeySorted.flatMap(pitRows =>
              computeNewCorr(
                partialCorrRecords =
                  partialAssoRows, // we have only one of a kind ( CORRELATION_FIELD, PARTIAL_CORR_SECONDARY_KEY)
                // as PARTIAL_CORR_SECONDARY_KEY is unique, PARTIAL_CORR_KEY is also unique
                masterPitRecords = pitRows,
                partialCorrAttributeFields = assoAttributeFieldsFromPartial,
                pitCorrAttributeFields = assoAttributeFieldsFromPit
              )
            )
          )
      }
  }

  override def toString: String = s"CorrelationLibrary(" +
    s"CORRELATION_FIELD=$CORRELATION_FIELD, " +
    s"PARTIAL_CORR_KEY=$PARTIAL_CORR_KEY, " +
    s"PARTIAL_CORR_SECONDARY_KEY=$PARTIAL_CORR_SECONDARY_KEY, ...)"
}

object CorrelationLibrary {
  type VersionType = Long

  val PLUS_INFINITY: Timestamp = Timestamp.valueOf("9999-01-01 00:00:00.000")

  def apply(
    END_DATE: String,
    START_DATE: String,
    PARTIAL_CORR_VERSION: String,
    PIT_VERSION: String,
    PARTIAL_CORR_KEY: String,
    PIT_CORR_KEY: Option[String] = None,
    CORRELATION_FIELD: String,
    PARTIAL_CORR_SECONDARY_KEY: String,
    IS_LAST_PARTIAL: String,
    IS_LAST_PIT: String
  ): CorrelationLibrary = {
    new CorrelationLibrary(
      END_DATE,
      START_DATE,
      PARTIAL_CORR_VERSION,
      PIT_VERSION,
      PARTIAL_CORR_KEY,
      PIT_CORR_KEY,
      CORRELATION_FIELD,
      PARTIAL_CORR_SECONDARY_KEY,
      IS_LAST_PARTIAL,
      IS_LAST_PIT
    )
  }
}
