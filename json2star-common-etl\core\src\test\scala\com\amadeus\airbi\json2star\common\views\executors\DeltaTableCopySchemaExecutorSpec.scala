package com.amadeus.airbi.json2star.common.views.executors

import com.amadeus.airbi.common.utils.CommonSpec
import com.amadeus.airbi.rawvault.common.testfwk.TmpDir

import java.nio.file.{Files, Paths}
import scala.io.Source

class DeltaTableCopySchemaExecutorSpec extends CommonSpec with TmpDir {

  def getArgs(inputConfig: String, outputSQL: String): Array[String] = Array(
    "--app-config-file",
    inputConfig,
    "--dry-mode",
    outputSQL
  )

  DeltaTableCopySchemaExecutor.getClass.getName should "produce the expected SQL output" in withTmpDir { tmpDir =>

    val appConfigFile = getClass.getClassLoader
      .getResource("views/sample_11/app.conf")
      .getPath
    val outFile = Files.createFile(Paths.get(tmpDir + "/copy.sql")).toString

    DeltaTableCopySchemaExecutor.main(getArgs(appConfigFile, outFile))

    val sql = Source.fromFile(outFile).getLines().mkString("\n")
    val newDb = "DB_CUSTOMER_PHASE_FEED_VERSION"
    val oldDb = "DB_CUSTOMER_PHASE_FEED_PREVIOUS_VERSION"
    sql should include(s"CREATE OR REPLACE TABLE ${newDb}.FACT_PASSENGER_HISTO CLONE ${oldDb}.FACT_PASSENGER_HISTO")
    sql should include(s"CREATE OR REPLACE TABLE ${newDb}.FACT_SECONDARY_HISTO CLONE ${oldDb}.FACT_SECONDARY_HISTO")
    sql should not include("INTERNAL_MY_TABLE")
  }
}
