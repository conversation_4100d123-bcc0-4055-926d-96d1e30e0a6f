package com.amadeus.airbi.json2star.common.addons.stackable

import com.amadeus.airbi.json2star.common.app.Json2StarApp
import com.amadeus.airbi.json2star.common.app.Json2StarApp.readMappingConfig
import com.amadeus.airbi.rawvault.common.testfwk.Json2StarSpec

import java.io.File
import scala.reflect.io.Directory
import scala.util.Try

class IntegrationErrorHandlingSpec extends Json2StarSpec {

  val selectors: Set[String] = Set("fail_closure", "fail_batch")

  val mappingFile = "stackable/error_handling_mapping.conf"

  override def beforeAll: Unit = {
    super.beforeAll
    createTables(mappingFile, selectors)
  }

  override def beforeEach: Unit = {
    cleanTables(mappingFile)
  }

  // Note: EventToStar.transform() swallows any errors in the closure, so they are always not fatal
  "IntegrationErrorHandlingSpec" should "always recover from stackable addon closure errors" in {
    val dataDir = "stackable/data/"
    val rc = rootConfigStackableAddonFatal(dataDir, fatal = true, "fail_closure")
    val directory = new Directory(new File(rc.etl.stream.sink.checkpointLocation))
    directory.deleteRecursively()
    val mapping = readMappingConfig(mappingFile, rc.tablesSelectors)

    assert(Try(Json2StarApp.run(spark, rc, mapping)).isSuccess)
    directory.deleteRecursively()
  }

  "IntegrationErrorHandlingSpec" should "not recover from stackable batch addon errors if fatal=true" in {
    val dataDir = "stackable/data/"
    val rc = rootConfigStackableAddonFatal(dataDir, fatal = true, "fail_batch")
    val directory = new Directory(new File(rc.etl.stream.sink.checkpointLocation))
    directory.deleteRecursively()
    val mapping = readMappingConfig(mappingFile, rc.tablesSelectors)

    assertThrows[Exception](
      Json2StarApp.run(spark, rc, mapping)
    )
  }

  "IntegrationErrorHandlingSpec" should "recover from stackable addon batch errors if fatal=false" in {
    val dataDir = "stackable/data/"
    val pathExpectedResults = "src/test/resources/" + dataDir + "expected_results/"
    val rc = rootConfigStackableAddonFatal(dataDir, fatal = false, "fail_batch")
    val directory = new Directory(new File(rc.etl.stream.sink.checkpointLocation))
    directory.deleteRecursively()
    val mapping = readMappingConfig(mappingFile, rc.tablesSelectors)

    Json2StarApp.run(spark, rc, mapping)
    checkTableContent("FACT_OK_HISTO_2", _ => pathExpectedResults, model = Some(mapping))
    directory.deleteRecursively()
  }

  private def rootConfigStackableAddonFatal(dataDir: String, fatal: Boolean, selector: String) = {
    val rc = rootConfig(dataDir, inputDatabase, isLight = DefaultIsLight)
    rc.copy(
      etl = rc.etl.copy(
        common = rc.etl.common.copy(fatalStackableAddonsErrors = fatal)
      ),
      tablesSelectors = Set(selector)
    )
  }
}
