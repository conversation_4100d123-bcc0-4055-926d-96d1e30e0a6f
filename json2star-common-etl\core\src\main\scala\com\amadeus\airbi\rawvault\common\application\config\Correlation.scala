package com.amadeus.airbi.rawvault.common.application.config

import com.amadeus.airbi.json2star.common.addons.base.AddonConfig
import com.amadeus.airbi.rawvault.common.config.{ColumnMetadata, FKeyRelationship}

object Correlation {

  /** Settings describing the input tables (and columns) to use to compute the correlation
    *
    * @param name    name of the domain (for logging purposes)
    * @param partial settings related to the partial correlation table of this domain
    * @param pit     settings related to the PIT tables of this domain
    */
  case class Domain(
    name: String,
    partial: Partial,
    pit: Pit
  )

  /** Description of the partial table used as input for the correlation computation
    *
    * This table must be a secondary PIT table (so contain start and end date, and is last, ...).
    *
    * @param table                   name of the table
    * @param startDate               name of the column with the start date (comes from PIT algorithm)
    * @param endDate                 name of the column with the end date (comes from PIT algorithm)
    * @param partialCorrVersion      name of the column with the version of its own domain
    * @param partialCorrKey          name of the column of correlation key in this domain
    *                                in the source table/s, privilege using isMandatory=false to allow for closure detection
    * @param partialCorrSecondaryKey name of the column of correlation key in the other domain
    *                                in the source table/s, privilege using isMandatory=false to allow for closure detection
    * @param isLast                  name of the column containing the PIT is last flag
    */
  case class Partial(
    table: String,
    startDate: String,
    endDate: String,
    partialCorrVersion: String,
    partialCorrKey: String,
    partialCorrSecondaryKey: String,
    isLast: String
  )

  /** Description of the PIT table used as input for the correlation computation
    *
    * This table must be a master PIT table.
    *
    * @param table      name of this table
    * @param pitCorrKey optional parameter to specify column used to generate correlations at finer level when partial table is at higher level
   *                    For more details about its usage, see CorrelationLibrary.processCorrelations documentation
    * @param pitVersion name of the column containing the version field of this domain
    * @param isLast     name of the column containing the PIT is last flag
    */
  case class Pit(
    table: String,
    pitVersion: String,
    pitCorrKey: Option[String] = None,
    isLast: String
  )

  /** Settings defining the target correlation table (its column names, dedup criteria, ...)
    *
    * @param domainAKey     name of the key column (together with the secondaryKey define the granularity of
    *                       either partial table) that by convention belongs to domain A
    *                       in the source table/s, privilege using isMandatory=false to allow for closure detection
    * @param domainBKey   name of secondary key column, that by convention belongs to domain B (see above,
    *                       key and secondaryKey can be found in either partial table)
    *                       in the source table/s, privilege using isMandatory=false to allow for closure detection
    * @param domainAVersion name that will be given to the column containing the version of the entity of domain A
    * @param domainBVersion same for domain B
    * @param assoAttributes names of the columns from the asso table taken as is
    * @param startDate      name of the column with the beginning of the correlation validity
    * @param endDate        name of the column with the end of the correlation validity
    * @param isLast         name of the column telling if this record represents a valid relation (or an obsolete one in case of false)
    * @param ifDupeTakeHighest
    */
  case class Target(
    domainAKey: CorrelationColumnConfig,
    domainBKey: CorrelationColumnConfig,
    domainAVersion: CorrelationColumnConfig,
    domainBVersion: CorrelationColumnConfig,
    assoAttributes: Option[Seq[CorrelationColumnConfig]],
    startDate: String,
    endDate: String,
    isLast: String,
    ifDupeTakeHighest: Seq[String]
  ) {
    @transient lazy val assoAttributesSeq = assoAttributes.getOrElse(Seq())
  }

  case class Source(
    domainAPit: Option[String],
    domainBPit: Option[String]
  )

  /** Configuration specific to correlation association table columns
    *
    * This may converge in the future with the existing ColumnConfig if needed.
    */
  case class CorrelationColumnConfig(
    name: String,
    source: Option[Source],
    fk: Option[Seq[FKeyRelationship]],
    meta: Option[ColumnMetadata]
  ) {
    val srcColPitA: Option[String] = source.flatMap(_.domainAPit)
    val srcColPitB: Option[String] = source.flatMap(_.domainBPit)
  }

  /** Represents the configuration required to generate a correlation association table
    *
    * It involves 2 domains.
    *
    * @param domainA              one of the domains involved in the correlation (PNR, TKT, ...)
    * @param domainB              the other domain
    * @param correlationFieldAToB field present in both tables domainA:partial & domainB:pit used to cogroup them together (coarse grained key for B)
    * @param correlationFieldBToA field present in both tables domainA:pit & domainB:partial used to cogroup them together (coarse grained key for A)
    * @param target               configuration related to the fields of the output association table
    */
  case class AssoTable(
    domainA: Domain,
    domainB: Domain,
    correlationFieldAToB: String,
    correlationFieldBToA: String,
    target: Target
  ) extends AddonConfig {
    lazy val (attrPitColumns, attrPartialColumns) = target.assoAttributesSeq.partition { c =>
      c.srcColPitA.isDefined || c.srcColPitB.isDefined
    }
    lazy val pitAFields: Seq[String] = (target.domainAKey +: attrPitColumns).flatMap(_.srcColPitA)
    lazy val pitBFields: Seq[String] = (target.domainBKey +: attrPitColumns).flatMap(_.srcColPitB)
  }
}
