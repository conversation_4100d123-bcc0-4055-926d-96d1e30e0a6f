{
  "tables": [
    {
      "name": "FACT_PASSENGER_DUMMY",
      "table-selectors": ["dummy_selector"]
      "latest": {
        "histo-table-name": "FACT_PASSENGER_HISTO"
      }
    },
    {
      "name": "FACT_PASSENGER",
      "table-selectors": ["latest_selector"]
      "latest": {
        "histo-table-name": "FACT_PASSENGER_HISTO"
      }
    },
    {
      "name": "FACT_PASSENGER_HISTO",
      "subdomain": "Passenger",
      "subdomain-main-table": "true",
      "mapping": {
        "merge": {
          "key-columns": ["PASSENGER_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}]}],
        "columns": [
          {"name": "INTERNAL_ZORDER", "column-type": "binaryStrColumn",  "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "PASSENGER_ID", "column-type": "binaryStrColumn",  "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}},
          {"name": "CPR_FEED_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.cprFeedType"}]}},
          {"name": "ETAG", "fk": [{"schema": "DUMMY_S", "table":"FACT_DUMMY_HISTO", "column":"DUMMY_C"}], "column-type": "strColumn", "sources": {"blocks": [{"base": "$.etag"}]}},
          {"name": "GROUP_NAME", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.groupName"}]}},
          {"name": "IS_MASTER_RECORD", "column-type": "booleanColumn", "sources": {"blocks": [{"base": "$.isMasterRecord"}]}},
          {"name": "IS_SAME_PHYSICAL_CUSTOMER", "column-type": "booleanColumn", "sources": {"blocks": [{"base": "$.isSamePhysicalCustomer"}]}},
          {"name": "IS_SYSTEM_MARKED_SPC", "column-type": "booleanColumn", "sources": {"blocks": [{"base": "$.isSystemMarkedSPC"}]}},
          {"name": "TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.type"}]}},
          {"name": "BIRTH_DATE", "column-type": "dateColumn", "sources": {"blocks": [{"base": "$.passenger.dateOfBirth[*].timings[?(!(@.qualifier=~/.*ZULU/))]"}]}},
          {"name": "BIRTH_PLACE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.passenger.placeOfBirth"}]}},
          {"name": "PAX_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.passenger.flightPassengerType"}]}},
          {"name": "GENDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.passenger.gender"}]}},
          {"name": "NATIONALITY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.passenger.nationality"}]}},
          {"name": "SPECIAL_SEAT", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.passenger.specialSeat"}]}},
          {"name": "FIRST_NAME", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.passenger.name.firstName"}]}},
          {"name": "LAST_NAME", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.passenger.name.lastName"}]}},
          {"name": "TITLE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.passenger.name.title"}]}},
          {"name": "RESIDENCE_COUNTRY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.passenger.countryOfResidence"}]}},
          {"name": "AGE", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.passenger.age"}]}},
          {"name": "STAFF_CATEGORY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.staff.category"}]}},
          {"name": "STAFF_COMPANY_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.staff.companyCode"}]}},
          {"name": "STAFF_COMPANY_NAME", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.staff.companyName"}]}},
          {"name": "STAFF_ID", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.staff.id"}]}},
          {"name": "STAFF_BOOKING_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.staff.idType"}]}},
          {"name": "STAFF_JOINING_DATE", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.staff.joiningDate"}]}},
          {"name": "STAFF_RELATIONSHIP", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.staff.relationshipType"}]}},
          {"name": "STAFF_RETIREMENT_DATE", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.staff.retirementDate"}]}},
          {"name": "STAFF_TRANSFER_DAYS", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.staff.transferDays"}]}},
          {"name": "STAFF_TRANSFERS_DURING_DAYS", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.staff.transfersDuringDay"}]}},
          {"name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}},
          {"name": "VERSION", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.version"}]}},
          {"name": "INTERNAL_DATE_BEGIN", "column-type": "timestampColumn", "sources": {}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
        ],
        "pit": {
          "type": "master-pit-table",
          "master" : {
            "pit-key": "PASSENGER_ID",
            "pit-version": "VERSION",
            "valid-from": "DATE_BEGIN",
            "valid-to": "DATE_END",
            "is-last": "IS_LAST_VERSION"
          }
        }},
      "table-snowflake": {
        "cluster-by": ["date_trunc('WEEK', DATE_BEGIN)"]
      }
    },
    {
      "name": "FACT_SECONDARY_HISTO",
      "subdomain": "Passenger",
      "mapping": {
        "merge": {
          "key-columns": ["FACT_SECONDARY_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}]}],
        "columns": [
          {"name": "FACT_SECONDARY_ID", "column-type": "binaryStrColumn",  "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}},
          {"name": "VERSION", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.version"}]}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
        ],
        "pit": {
          "type": "secondary-pit-table"
        }},
      "table-snowflake": {
        "cluster-by": ["to_char(DATE_BEGIN,'yyyy-MM')"]
      }
    },
    {
      "name": "INTERNAL_MY_TABLE",
      "mapping": {
        "merge": {
          "key-columns": [ "BAG_GROUP_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [{ "blocks": [
          {"base": "$.correlatedResourcesCurrent.DCSBAG-DCSPAX"},
          {"corr": "$.correlations[*]"}
        ]}],
        "columns": [
          {"name": "BAG_GROUP_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "VERSION", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.version"}]}},
          {"name": "REF_BAG_GROUP_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"root": "$.mainResource.current.image.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
        ],
        "pit": {
          "type": "secondary-pit-table"
        }
      }
    }
  ],
  "links": [ ]
}