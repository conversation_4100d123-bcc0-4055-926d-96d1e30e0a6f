{
  "defaultComment" : "A coment here",
  "partition-spec" : {
    "key" : "PNR_CREATION_DATE",
    "column-name": "PART_PNR_CREATION_MONTH",
    "expr": "date_format(PNR_CREATION_DATE, \"yyyy-MM\")"
  },
  "tables": [
    {
      "name": "FACT_RESERVATION_HISTO", // table with multiple simple root source
      "mapping" : {
        "merge": {
          "key-columns": ["RESERVATION_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}]}, {"blocks": [{"base":"$.mainResource.current.image.dummy"}]}],
        "columns": [
          {"name": "RESERVATION_ID", "is-mandatory": "true", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "VERSION", "is-mandatory": "true", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.version"}]}},
          {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}]}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
        ],
        "pit": {
          "type": "master-pit-table",
          "master": {
            "pit-key": "RESERVATION_ID",
            "pit-version": "VERSION",
            "valid-from": "DATE_BEGIN",
            "valid-to": "DATE_END",
            "is-last": "IS_LAST_VERSION"
          }
        },
        "description": {
          "description": "It contains information related to the booking on PNR-level.",
          "granularity": "1 PNR, version"
        }
      },
    },
    {
      "name": "DIM_POINT_OF_SALE", // table with multiple block root source
      "mapping": {
        "merge": {
          "key-columns": ["POINT_OF_SALE_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "name" : "owner", "rs" : {"blocks": [{"pos": "$.mainResource.current.image.owner"},{"office" : "$.office"}]}},
          { "name" : "creation", "rs" : { "blocks": [{"pos": "$.mainResource.current.image.creation.pointOfSale"},{"office" : "$.office"}]}},
          { "name" : "lastModification", "rs" : { "blocks": [{"pos": "$.mainResource.current.image.lastModification.pointOfSale"},{"office" : "$.office"}]}}
        ],
        "columns": [
          { "name": "COMMON_COLUMN", "column-type": "strColumn","sources": {"blocks": [{"office": "$.id"}]} },
          {
            "name": "POINT_OF_SALE_ID", "is-mandatory": "true", "column-type": "strColumn", "sources": { "root-specific": [
              {"rs-name": "owner", "blocks": [{"office": "$.id"}, {"pos": "$.login.cityCode"}]},
              {"rs-name": "creation", "blocks": [{"office": "$.id"}, {"pos": "$.login.cityCode"}]},
              {"rs-name": "lastModification", "blocks": [{"office": "$.id"}, {"pos": "$.login.iataNumber"}]},
            ]},
            "expr": "hashM({0})"
          },
          {
            "name": "COL_PRESENT_ONLY_IN_A_ROOT", "column-type": "strColumn", "sources": { "root-specific": [
                {"rs-name": "owner", "blocks": [{"office": "$.id"}]}
              ]}
          }
        ],
        "pit": {
          "type": "no-pit-table"
        }}
    },
    {
      "name": "DIM_CARRIER", // table with multi block sources (no specific columns, only common columns)
      "mapping": {
        "merge": {
          "key-columns": ["CARRIER_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [{"companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.marketing.flightDesignator.carrierCode"}]},
          { "blocks": [{"companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.operating.flightDesignator.carrierCode"}]},
          { "blocks": [{"companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].flightSegment.marketing.flightDesignator.carrierCode"}]}
        ],
        "columns": [
          {"name": "CARRIER_ID", "is-mandatory": "true", "column-type": "longColumn", "sources": {"blocks": [{"companyCode": "$.value"}]},"expr": "hashXS({0})" },
          {"name": "CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"companyCode": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }}
    }
  ]
}