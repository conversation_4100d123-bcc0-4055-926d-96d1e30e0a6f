Sample files:

- MH-PRD_load-date=01-10-2024.json
  - example crafted from the first test case in com.amadeus.ti.reports.coupons.proration.ProrationSpec
  - corresponding fare calc:
    ```scala
    "EWR B6 BOS64.19UL07AE2C 9K ACK147.81MIPX 9K BOS157.12MIP B6 DCA Q8.79 219.89VH03AE6U USD597.80END ZP EWR4.00BOS0.00ACK0.00BOS4.00XF EWR4.5BOS4.5XT8.00ZP9.00XF"
    ```
  - expected prorations:
    - coupon 1 =  64.19 USD
    - coupon 2 = 147.81 USD
    - coupon 3 = 157.12 USD
    - coupon 4 = 219.89 USD  (219.89 (prorated) + 8.79 (surcharge))

- MH-PRD-no-fare-calc_load-date=01-10-2024.json
  - same as MH-PRD_load-date=01-10-2024.json with
  - fare calc = empty
  - total = 647.80 USD
  - taxes = 50.00 USD
The difference is 647.80 - 50.00 = 597.80 USD

  - expected prorations:
    - the total distance is 1191.5 km
    - coupon 1 =  153.34 USD (EWR-BOS)
    - coupon 2 =   69.02 USD (BOS-ACK)
    - coupon 3 =   69.02 USD (ACK-BOS)
    - coupon 4 =  306.41 USD (BOS-DCA)
      The total price is 597.79 US
```