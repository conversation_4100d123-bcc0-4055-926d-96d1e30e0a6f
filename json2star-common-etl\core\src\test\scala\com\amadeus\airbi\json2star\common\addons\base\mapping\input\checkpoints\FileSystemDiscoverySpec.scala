package com.amadeus.airbi.json2star.common.addons.base.mapping.input.checkpoints

import com.amadeus.airbi.common.utils.CommonSpec
import com.amadeus.airbi.datalake.common.spark.SparkSqlSpecification
import com.amadeus.airbi.rawvault.common.testfwk.TmpDir
import com.amadeus.airbi.json2star.common.testfwk.MockDatabricksUtils

import java.nio.file.{Files, Paths}
import java.time.LocalDate
import scala.util.Success

class FileSystemDiscoverySpec extends SparkSqlSpecification with TmpDir {

  val mockDatabricksUtils = new MockDatabricksUtils(1, "id", spark)

  "FileSystemDiscovery" should "discover partitions" in withTmpDir { tmpDir =>
    val basePath = tmpDir + "/base/path"
    Files.createDirectories(Paths.get(basePath + "/0"))
    Files.createDirectories(Paths.get(basePath + "/1"))
    Files.createDirectories(Paths.get(basePath + "/2"))
    FileSystemDiscovery.discoverPartitions(basePath, mockDatabricksUtils) shouldBe Seq(0, 1, 2)
  }

  it should "fail if partitions are not integer" in withTmpDir { tmpDir =>
    val basePath = tmpDir + "/base/path"
    Files.createDirectories(Paths.get(basePath + "/zero"))
    Files.createDirectories(Paths.get(basePath + "/1"))
    Files.createDirectories(Paths.get(basePath + "/2"))
    val e = intercept[NumberFormatException] {
      FileSystemDiscovery.discoverPartitions(basePath, mockDatabricksUtils)
    }
  }

  it should "discover a start date" in withTmpDir { tmpDir =>
    val basePath = tmpDir + "/base/path"
    Files.createDirectories(Paths.get(basePath + "/0/2022/11/10"))
    Files.createDirectories(Paths.get(basePath + "/1/2022/11/10"))
    Files.createDirectories(Paths.get(basePath + "/0/2022/11/11"))
    Files.createDirectories(Paths.get(basePath + "/0/2022/11/12"))
    Files.createDirectories(Paths.get(basePath + "/0/2023/01/01"))
    Files.createDirectories(Paths.get(basePath + "/0/2023/01/02"))
    FileSystemDiscovery.discoverStartDate(basePath, mockDatabricksUtils) shouldBe LocalDate.of(2022, 11, 10)
  }

  it should "fail if no start date discovered" in withTmpDir { tmpDir =>
    val basePath = tmpDir + "/base/path"
    Files.createDirectories(Paths.get(basePath + "/0/2023"))
    val e = intercept[IllegalStateException](FileSystemDiscovery.discoverStartDate(basePath, mockDatabricksUtils))
    e.getMessage should include(s"Could not discover start date for path: ${basePath}")
  }
}
