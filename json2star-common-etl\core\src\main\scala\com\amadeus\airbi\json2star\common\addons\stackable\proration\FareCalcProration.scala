package com.amadeus.airbi.json2star.common.addons.stackable.proration

import com.amadeus.airbi.json2star.common.addons.stackable.proration.ProrationInput.CouponKey
import com.amadeus.airbi.json2star.common.extdata.PorsExtData
import com.amadeus.ti.models.currency.Currency
import com.amadeus.ti.models.por.{Por, PorsRef}
import com.amadeus.ti.parsers.farecalc.FareCalcParser
import com.amadeus.ti.reports.coupons.proration.FareCalc

import java.time.LocalDate
import scala.util.{Failure, Success}

object FareCalcProration {

  val StepPrefix = "FARE_CALC"

  /** Compute the prorated coupons of a ticket using the fare calc line
    * It returns the map of coupon key and ProratedCoupon with the prorated amount or a status error
    *
    * @param porsRef pors geographical reference data
    * @param fareCalcLine fare calculation line
    * @param issueDate ticket issue date
    * @param currencyValue payment currency
    * @param itinerary the seq of coupon key
    * @return a map of prorated coupons
    */
  def computeProratedCoupons(
    porsRef: PorsRef,
    fareCalcLine: String,
    issueDate: LocalDate,
    currencyValue: String,
    itinerary: Seq[CouponKey]
  ): Map[CouponKey, FclProratedCoupon] = {
    val cpns = fareCalcLine.trim match {
      // Fare Calc Line is empty --> return empty with error msg
      case rawFcl if rawFcl.isEmpty =>
        itinerary.map(c =>
          (c, FclProratedCouponFailure(StepStatus(s"$StepPrefix-input-empty", s"ERROR: fare calc is empty")))
        )
      // Fare Calc Line is present --> parse
      case rawFcl =>
        val maybeFareCalc = FareCalcParser.parse(rawFcl)
        maybeFareCalc match {
          // FCL Parsing is failure --> return empty with error msg
          case Failure(notParsed) =>
            itinerary.map(c =>
              (c, FclProratedCouponFailure(StepStatus(s"$StepPrefix-ti-libs", s"ERROR: ${notParsed.getMessage}")))
            )
          //  FCL Parsing is success --> build the fare calc table by coupon keys
          case Success(parsedFareCalc) =>
            val tktItinerary = itinerary.map(c => (c.departureAirport, c.arrivalAirport))
            val fareCalcTable =
              new FareCalc(porsRef, issueDate, Currency(currencyValue), Some(parsedFareCalc), tktItinerary)

            val couponsWithAmount = itinerary.map { c =>
              val amount = getAmountByCoupon(
                porsRef,
                fareCalcTable,
                issueDate,
                c.sequenceNumber,
                c.departureAirport,
                c.arrivalAirport
              )
              (c, amount)
            }
            couponsWithAmount
        }
    }
    cpns.toMap
  }

  private def getAmountByCoupon(
    porsRef: PorsRef,
    fareCalcTable: FareCalc,
    issueDate: LocalDate,
    couponNumber: Int,
    boardIataCode: String,
    offIataCode: String
  ): FclProratedCoupon = {
    val cpnCities = PorsExtData.applyFunc(
      porsRef,
      boardIataCode,
      offIataCode,
      issueDate,
      (b: Por, o: Por) => (b.cityCode, o.cityCode)
    )
    val amount = cpnCities match {
      case Right((b, o)) =>
        val cityPair = (couponNumber, b, o)
        // lookup by city in the fareCalcTable
        val prorated = fareCalcTable.prorateCoupon(cityPair)
        prorated match {
          case None => FclProratedCouponFailure(StepStatus(s"$StepPrefix-cpn-not-found", s"ERROR: ${cityPair}"))
          case Some(amount) => FclProratedCouponSuccess(amount)
        }
      case Left(err) => FclProratedCouponFailure(StepStatus(s"$StepPrefix-pors-missing", s"ERROR: ${err}"))
    }
    amount
  }

}
