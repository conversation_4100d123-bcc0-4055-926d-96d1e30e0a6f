package com.amadeus.airbi.json2star.common.validation.executors

import com.amadeus.airbi.json2star.common.TablesDef
import com.amadeus.airbi.json2star.common.validation.config.{CheckType, ValidationConf, ValidationConfig, ValidationRecord}
import com.amadeus.airbi.json2star.common.validation.generators.{ValidationFullyProcessedGenerator, ValidationNoDupesGenerator}
import com.amadeus.airbi.rawvault.common.application.config.ModelConfigLoader
import com.typesafe.scalalogging.Logger
import io.delta.tables.DeltaTable
import org.apache.spark.SparkConf
import org.apache.spark.sql.SparkSession
import org.slf4j.LoggerFactory

import java.time.OffsetDateTime
import scala.collection.mutable.ListBuffer

object ValidationExecutor {

  val DefaultRootLocation = "/user/hive/warehouse/TRASH"

  @transient
  lazy val logger: Logger = Logger(LoggerFactory.getLogger(getClass.getName))



  def main(args: Array[String]): Unit = {

    implicit val spark: SparkSession = SparkSession
      .builder()
      .config(
        new SparkConf()
          .set("spark.databricks.delta.properties.defaults.autoOptimize.optimizeWrite", "true")
          .set("spark.databricks.delta.properties.defaults.autoOptimize.autoCompact", "true")
      )
      .getOrCreate()
    val vConfig = ValidationConf.apply(args)
    run(spark, vConfig)
  }

  def run(spark: SparkSession, vConfig: ValidationConf,
          restrictedTableSet: List[String] = List.empty[String] // needed to optimize test run
   ): Unit = {

    val tablesDef = getTableList(
      vConfig.appConfig.modelConfFile,
      vConfig.appConfig.tablesSelectors,
      vConfig.appConfig.disabledStackableAddons,
      restrictedTableSet
    )

    val validParams = ValidationConfig(
      validationDatabase = vConfig.validationDatabase,
      validationTablename = vConfig.validationTable,
      domain = vConfig.appConfig.common.domain,
      domainVersion = vConfig.appConfig.common.domainVersion,
      customer = vConfig.appConfig.common.shard,
      phase = vConfig.phase,
      currentTimestamp = OffsetDateTime.now().toString,
      daysBack = vConfig.validationDaysBack
    )

    val queries = getQueries(spark, tablesDef, vConfig, validParams)

    val testRecords = runQueries(spark, queries, validParams)

    writeValidationResults(spark, validParams, testRecords)
  }

  /**
    *
    * @param spark spark session
    * @param conf tables from mappig conf file
    * @param vConfig Validation job configuration
    * @param validParams parameters to create the validation query
    * @return list of queries to be run for validation
    */
  def getQueries(
    spark: SparkSession,
    conf: TablesDef,
    vConfig: ValidationConf,
    validParams: ValidationConfig
  ): List[(String, String, String)] = {
    val queries =
      CheckType.withName(vConfig.checkType) match {
        case CheckType.DUMMY =>
          var number = 1
          conf.tables.map { table => {
            val tableName = (table.schema.name)
            val query = s"""select ${number} nb_total_records, 1 nb_failed_records,"toto1,toto2" fsample""".stripMargin
            val task = s"""  dummy test for ${tableName} in star schema history :  """
            val funcCase = s"""Test ${number} - Dummy check for ${tableName} in star schema history""".stripMargin
            number = number + 1
            (task, funcCase, query)
            }
          }
        case CheckType.NO_DUPES =>
          ValidationNoDupesGenerator.toCreateValidationRequest(
            conf,
            vConfig.appConfig.common.outputDatabase,
            validParams,
            checkOnlyLatest = false
          )
        case CheckType.NO_DUPES_LATEST =>
          ValidationNoDupesGenerator.toCreateValidationRequest(
            conf,
            vConfig.appConfig.common.outputDatabase,
            validParams,
            checkOnlyLatest = true
          )
        case CheckType.FULLY_PROCESSED => {
          ValidationFullyProcessedGenerator.createTempRawDataTable(
            spark,
            vConfig.appConfig.common.outputDatabase,
            vConfig.inputFeed,
            validParams.daysBack,
            validParams.domain
          )
          ValidationFullyProcessedGenerator.toCreateValidationRequest(
            conf,
            vConfig.appConfig.common.outputDatabase,
            validParams
          )
        }
      }
    if (queries.isEmpty || queries.exists(_._3.isEmpty)) {
      logger.error(s"No Checks generated from this file ${vConfig.appConfig.modelConfFile}")
      logger.error(s"Check SQL content is ${queries.mkString("\n")}")
      throw new Exception("ERROR VALIDATION CHECK GENERATOR")
    }
    queries
  }

  def getTableList(mappingConfFile: String, selectors: Set[String],
                   disabledStackableAddons: Set[String],
                   restrictedTableSet: List[String]): TablesDef = {
    val originalMappingConf = ModelConfigLoader.defaultLoad(mappingConfFile, selectors, disabledStackableAddons)
    val mappingConfToUse = if (restrictedTableSet.isEmpty) {
      originalMappingConf
    } else {
      originalMappingConf.copy(tables = originalMappingConf.tables.filter(t => restrictedTableSet.contains(t.name)))
    }
    TablesDef.consolidate(mappingConfToUse)
  }

  def runQueries(
    spark: SparkSession,
    queries: List[(String, String, String)],
    validationConfig: ValidationConfig
  ): ListBuffer[ValidationRecord] = {

    var testRecords: ListBuffer[ValidationRecord] = ListBuffer()

    queries.foreach { validationQuery =>
      logger.info(s"${getClass.getName} - Run Validation Query: ${validationQuery}")
      val result = spark.sql(validationQuery._3).head()
      result.schema.printTreeString()
      val res = result.getValuesMap(Seq("nb_total_records", "nb_failed_records", "fsample"))
      val countTotal =
        if (res.get("nb_total_records").toList.isEmpty) 0L
        else {
          if (res.get("nb_total_records").toList.head.isInstanceOf[Int]) res.getOrElse("nb_total_records", 0).toLong
          else res.getOrElse("nb_total_records", 0L)
        }
      val countFailed =
        if (res.get("nb_total_records").toList.isEmpty) 0L
        else {
          if (res.get("nb_failed_records").toList.head.isInstanceOf[Int]) res.getOrElse("nb_failed_records", 0).toLong
          else res.getOrElse("nb_failed_records", 0L)
        }
      val failedValues = res.getOrElse("fsample", "")

      testRecords += ValidationRecord(
        validationConfig.domain,
        validationConfig.domainVersion,
        validationConfig.customer,
        validationConfig.phase,
        validationConfig.currentTimestamp,
        validationQuery._2,
        validationQuery._1,
        countFailed == 0,
        countFailed,
        countTotal,
        countFailed.toFloat / countTotal,
        if (countFailed > 0) {
          countFailed + "  failures in star schema history. Sample of impacted primary_keys : " + failedValues
        }
        else {
          ""
        }
      )
    }
    testRecords
  }

  def writeValidationResults(
    spark: SparkSession,
    validParams: ValidationConfig,
    testRecords: ListBuffer[ValidationRecord]
  ): Unit = {
    import spark.sqlContext.implicits._
    val df = testRecords.toDF()

    val tableSchema = df.schema
    val tableName = s"${validParams.validationDatabase}.${validParams.validationTablename}"
    DeltaTable
      .createIfNotExists(spark)
      .tableName(tableName)
      .addColumns(tableSchema)
      .execute()

    df.withColumn("fail_ratio", $"fail_ratio".cast("Decimal(3,2)"))
      .write
      .insertInto(s"${tableName}")
  }

}
