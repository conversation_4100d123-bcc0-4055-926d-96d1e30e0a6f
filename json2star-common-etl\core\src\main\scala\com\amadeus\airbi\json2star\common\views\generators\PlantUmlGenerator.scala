package com.amadeus.airbi.json2star.common.views.generators

import com.amadeus.airbi.json2star.common.{ColumnDef, TableDef, TablesDef}
import com.amadeus.airbi.json2star.common.views.generators.PlantUmlDSL._
import com.amadeus.airbi.rawvault.common.application.config.ModelConfigLoader
import com.amadeus.airbi.rawvault.common.config.ColumnType
import com.typesafe.scalalogging.Logger
import net.sourceforge.plantuml.{FileFormat, FileFormatOption, SourceStringReader}
import org.slf4j.LoggerFactory

import java.io.ByteArrayOutputStream
import scala.collection.immutable.ListSet

/** It generates the PlantUML input to create the Physical model diagrams from the mapping file
  */
object PlantUmlGenerator {

  lazy val logger: Logger = Logger(LoggerFactory.getLogger(getClass.getName))

  case class Config(
    domain: String,
    version: String,
    modelConfFile: String,
    tablesSelectors: Set[String],
    disabledStackableAddons: Set[String],
    enableGlobalLevel: Boolean,
    enableSubdomainLevel: <PERSON>olean,
    enableExternalDomainLevel: Boolean
  )

  // Source: https://brand-marketing-center.internal.amadeus.com/amadeus/brand-assets
  private val subdomainBackgroundColors = List("#E4C7FF", "#FFD7D7", "#C8FFC0", "#FFC5F9", "#FFFEB0", "#C5D5F9")
  private val externalDomainBackgroundColor = "#DDDDDD"

  // Regex used to filter out columns and foreign keys
  private val ignoreRegex = "^INTERNAL_".r

  private case class Context(
    config: Config,
    tablesDef: TablesDef
  ) {
    val factHistoTables: List[TableDef] = tablesDef.factTables.filter(_.schema.name.endsWith("_HISTO"))
    val dimTables: List[TableDef] = tablesDef.dimTables
    val assoHistoTables: List[TableDef] = tablesDef.assoTables.filter(_.schema.name.endsWith("_HISTO"))

    val tableToSubdomainMap: Map[String, Option[String]] = tablesDef.tables.map(_.schema).map(s => s.name -> s.subdomain).toMap
    val subdomains: Set[String] = tablesDef.tables.map(_.schema.subdomain).flatten.to[ListSet]

    private def getSubdomainMainTable(tablesDef: TablesDef, subdomain: String): Option[String] = {
      tablesDef.tables.map(_.schema).find(s => s.subdomain.contains(subdomain) && s.subdomainMainTable).map(_.name)
    }
    val subdomainToMainTableMap: Map[String, Option[String]] = subdomains.map(s => s -> getSubdomainMainTable(tablesDef, s)).toMap

    private val masterPitTable: Option[TableDef] = tablesDef.tables.find(_.table.mapping.flatMap(_.masterPit).isDefined)
    val mainSubdomain: Option[String] = masterPitTable.flatMap(_.schema.subdomain)
    val mainTable: Option[String] = masterPitTable.map(_.schema.name)

    val subdomainToBackgroundColorMap: Map[String, String] =
      subdomains.zip(Stream.continually(subdomainBackgroundColors.toStream).flatten.take(subdomains.size).toList).toMap

    def areInSameSubdomain(tables: String*): Boolean = {
      tables.flatMap(tableToSubdomainMap(_)).toSet.size == 1
    }
    def isSubdomainMainTable(table: String): Boolean = {
      subdomainToMainTableMap.exists(_._2.contains(table))
    }
  }

  private val header: String =
    s"""hide circle
       |hide <<assotable>> stereotype
       |hide <<dimtable>> stereotype
       |hide <<maintable>> stereotype
       |hide <<mainsubdomain>> stereotype
       |hide methods
       |left to right direction
       |
       |!define TABLE_GRADIENT_BACKGROUND #F2F2F2-fcffd6
       |!define MAIN_TABLE_GRADIENT_HEADER Orange-%lighten("Yellow", 60)
       |
       |!function $$pk($$content)
       |!return "<color:#ff0000>" + $$content + "</color>"
       |!endfunction
       |
       |!function $$fk($$content)
       |!return "<color:#0000ff>" + $$content + "</color>"
       |!endfunction
       |
       |skinparam {
       |    DefaultFontName Monospaced
       |    ranksep 250
       |    linetype polyline
       |    tabSize 4
       |    HyperlinkUnderline false
       |    HyperlinkColor #0000ff
       |}
       |
       |skinparam frame {
       |    FontSize 28
       |    FontSize<<mainsubdomain>> 34
       |    BorderThickness<<mainsubdomain>> 3
       |}
       |
       |skinparam class {
       |    BackgroundColor TABLE_GRADIENT_BACKGROUND
       |    HeaderBackgroundColor %lighten("Crimson", 40)
       |    HeaderBackgroundColor<<maintable>> MAIN_TABLE_GRADIENT_HEADER
       |    HeaderBackgroundColor<<dimtable>> LightBlue
       |    HeaderBackgroundColor<<assotable>> %lighten("LimeGreen", 30)
       |    ColorArrowSeparationSpace Red
       |    BorderColor Black
       |    BorderColor<<maintable>> MediumBlue
       |    BorderThickness<<maintable>> 3
       |    ArrowColor Blue
       |    FontSize 16
       |    FontSize<<maintable>> 20
       |    FontStyle Bold
      |}""".stripMargin

  /**
    * Generate PlantUML input from the config
    *
    * @param config the configuration of the PlantUmlGenerator
    * @return a map of PlantUML input file name to input file content
    */
  def from(config: Config): Map[String, String] = {
    val tablesConfig =
      ModelConfigLoader.defaultLoad(config.modelConfFile, config.tablesSelectors, config.disabledStackableAddons)
    val tablesDef = TablesDef.consolidate(tablesConfig)
    val context = Context(config, tablesDef)
    List(
      if (config.enableGlobalLevel) generateGlobalInput(context) else Map.empty[String, String],
      if (config.enableSubdomainLevel) generateSubdomainInputs(context) else Map.empty[String, String],
      if (config.enableExternalDomainLevel) generateExternalDomainInputs(context) else Map.empty[String, String]
    ).reduce(_ ++ _)
  }

  /**
    * Converts a PlantUML input map (as returned by `from(Config)` into a corresponding SVG map where each PlantUML input
    * has been converted into a diagram
    *
    * @param input a PlantUML input map
    * @return the corresponding PlantUML diagram map
    */
  def convertToSvg(input: Map[String, String]): Map[String, Array[Byte]] = {
    input.map { case (fileName, plantUmlInput) => {
      val svgFileName = fileName.replace(".plantuml", ".svg")
      val reader = new SourceStringReader(plantUmlInput)
      val baos = new ByteArrayOutputStream()
      reader.outputImage(baos, new FileFormatOption(FileFormat.SVG))
      svgFileName -> baos.toByteArray
    }}
  }

  /**
    * Generate PlantUML input for the global diagram of the domain
    *
    * @param context the context
    * @return a one-element map of PlantUML input file name to input file content
    */
  private def generateGlobalInput(context: Context): Map[String, String] = {
    /*
    The result includes the following:
    - All fact tables of all subdomains (grouped into frames by subdomain)
    - Fact tables without a subdomain
    - All asso tables
    - All dim tables
    - Direct relationships between fact tables (except those pointing to the main table (i.e. the master PIT table) with some exceptions)
    - Indirect relationships between fact tables (via asso tables)
    - All referenced tables from external domains
    It does NOT include (for the sake of readability):
    - Relationships between fact and dim tables
   */
    val title = s"star-schema_${context.config.domain}_v${context.config.version}"
    val subdomainFrames = context.subdomains.toList.map(subdomain => {
      val stereotype = if (context.mainSubdomain.contains(subdomain)) Some("mainsubdomain") else None
      val color = context.subdomainToBackgroundColorMap(subdomain)
      val factEntities = getEntities(context.factHistoTables, context, Some(subdomain))
      Frame(subdomain, stereotype, color, factEntities)
    })
    val freeFactEntities = getEntities(context.factHistoTables, context, noSubdomain = true)
    if (freeFactEntities.length > 0) {
      logger.info(s"Found ${freeFactEntities.length} fact table(s) without a subdomain: ${freeFactEntities.map(_.name).mkString(", ")}")
    }
    val assoEntities = getEntities(context.assoHistoTables, context)
    val dimEntities = getEntities(context.dimTables, context)
    val factToFactRelationships = getRelationshipsFromEntities(subdomainFrames.flatMap(_.entities) ++ freeFactEntities)
      .filter(r => r.toTable.startsWith("FACT_")
        // Relationships to the main table are not shown except:
        // - for internal relationships (inside a subdomain)
        // - or if the source of the relationship is the main table of its subdomain
          && (!context.mainTable.contains(r.toTable)
              || context.areInSameSubdomain(r.fromTable, r.toTable) || context.isSubdomainMainTable(r.fromTable)))
    val assoToFactRelationships = getRelationshipsFromEntities(assoEntities)
      .filter(_.toTable.startsWith("FACT_"))
      .groupBy(_.fromTable)
      .mapValues(relationships => {
        // Filters out relationships to the main table only when it leaves at least 2 relationships for each asso table.
        val filteredRelationships = relationships.filter(r => !context.mainTable.contains(r.toTable))
        if (filteredRelationships.length >= 2) filteredRelationships else relationships
      })
      .values.flatten.toList
    val externalFrames = getExternalFramesFromEntities(subdomainFrames.flatMap(_.entities) ++ freeFactEntities ++ assoEntities, context)

    val document = Document(
      title, header,
      subdomainFrames ++ externalFrames,
      freeFactEntities ++ assoEntities ++ dimEntities,
      factToFactRelationships ++ assoToFactRelationships
    )
    Map(s"$title.plantuml" -> document.render())
  }

  /**
    * Generate PlantUML input for each subdomain-specific diagram
    *
    * @param context the context
    * @return a multi-element map (1 per subdomain) of PlantUML input file name to input file content
    */
  private def generateSubdomainInputs(context: Context): Map[String, String] = {
    /*
    For each subdomain, the result includes the following:
    - All fact tables of the subdomain
    - Fact tables of other subdomains (or without a subdomain) that can be accessed either directly from the subdomain or indirectly via asso tables
    - Dim tables that can be accessed from the subdomain
    - Asso tables that connect the subdomain with other subdomains
    - All relationships within the subdomain
        plus outbound relationships to fact tables (except those pointing to the main table (i.e. the master PIT table)
            unless the source of the relationship is the main table of the subdomain)
        plus outbound relationships to dim tables
        plus relationships that originate from the asso tables that connect the subdomain with other subdomains
    - Tables from external domains referenced from the subdomain
   */
    context.subdomains.zipWithIndex.map { case (subdomain, subdomainIndex) => {
      val subdomainTitleComponent = ("%02d" format subdomainIndex) + fileNameSafe(subdomain)
      val title = s"star-schema_${context.config.domain}_${subdomainTitleComponent}_v${context.config.version}"
      val subdomainFactEntities = getEntities(context.factHistoTables, context, Some(subdomain))
      val subdomainFactTableNames = subdomainFactEntities.map(_.name)

      // Utility predicates for entities
      def hasLinkFromEntities(entities: List[Entity]): Entity => Boolean = e =>
        testFkInEntities(entities, fk => fk.targetTable.equals(e.name))
      def hasLinkFromSubdomain: Entity => Boolean = hasLinkFromEntities(subdomainFactEntities)
      // In the predicate below, we exclude links to the main table (unless the source of the link is the main table of its subdomain),
      // otherwise all the fact tables would always be linked to the main subdomain because they all have a link to the main table
      def hasLinkToSubdomain: Entity => Boolean = e =>
        testFkInEntity(e, fk => fk.targetSubdomain.contains(subdomain)
          && (!context.mainTable.contains(fk.targetTable) || context.isSubdomainMainTable(e.name)))

      val dimEntities = getEntities(context.dimTables, context)
        .filter(hasLinkFromSubdomain(_))
      val directlyLinkedFactEntities = getEntities(context.factHistoTables, context, excludedSubdomain = Some(subdomain))
        .filter(e => hasLinkToSubdomain(e) || hasLinkFromSubdomain(e))
      val assoEntities = getEntities(context.assoHistoTables, context)
        .filter(hasLinkToSubdomain(_))
      // The fact tables that are linked with the subdomain through asso tables
      val indirectlyLinkedFactEntities = getEntities(context.factHistoTables, context, excludedSubdomain = Some(subdomain))
        .filter(hasLinkFromEntities(assoEntities)(_))

      // Relationships that originate from the subdomain except those that point to the main table
      // (unless the relationship is internal to the subdomain or the source of the relationship is the main table of the subdomain)
      val internalAndOutboundRelationships = getRelationshipsFromEntities(subdomainFactEntities)
        .filter(r => !context.mainTable.contains(r.toTable)
            || context.areInSameSubdomain(r.fromTable, r.toTable) || context.isSubdomainMainTable(r.fromTable))
      // Relationships that originate from the directly linked fact entities except those that point to the main table
      // (unless the source of the relationship is the main table of its subdomain)
      val inboundRelationships = getRelationshipsFromEntities(directlyLinkedFactEntities)
        .filter(r => subdomainFactTableNames.contains(r.toTable) &&
          (!context.mainTable.contains(r.toTable) || context.isSubdomainMainTable(r.fromTable)))
      // Relationships that originate from the asso tables except those that point to the main table
      val assoRelationships = getRelationshipsFromEntities(assoEntities)
        .groupBy(_.fromTable)
        .mapValues(relationships => {
          // Filters out relationships to the main table only when it leaves at least 2 relationships to fact tables for each asso table.
          val filteredRelationships = relationships.filter(r => !context.mainTable.contains(r.toTable))
          if (filteredRelationships.map(r => if (r.toTable.startsWith("FACT_")) 1 else 0).fold(0)(_ + _) >= 2) {
            filteredRelationships
          } else {
            relationships
          }
        })
        .values.flatten.toList

      // Deduplicate fact tables (because they may appear as both directly linked and indirectly linked)
      val allLinkedFactEntities = (directlyLinkedFactEntities ++ indirectlyLinkedFactEntities).map(e => e.name -> e).toMap.values.toList

      val mainFrame = Frame(subdomain, Some("mainsubdomain"), context.subdomainToBackgroundColorMap(subdomain), subdomainFactEntities)
      case class EntityWithSubdomain(entity: Entity, subdomain: String)
      val otherSubdomainFrames = allLinkedFactEntities
        .flatMap(e => context.tableToSubdomainMap(e.name) match {
          case Some(subdomain) => Some(EntityWithSubdomain(e, subdomain))
          case _ => None
        })
        .groupBy(_.subdomain)
        .mapValues(_.map(_.entity))
        .map { case (subdomain, entities) => Frame(subdomain, None, context.subdomainToBackgroundColorMap(subdomain), entities) }
        .toList
      val freeFactEntities = allLinkedFactEntities
        .filter(e => context.tableToSubdomainMap(e.name).isEmpty)
      if (freeFactEntities.length > 0) {
        logger.info(s"Found ${freeFactEntities.length} fact table(s) without a subdomain referenced from subdomain $subdomain: "
          + freeFactEntities.map(_.name).mkString(", "))
      }
      val externalFrames = getExternalFramesFromEntities(mainFrame.entities ++ assoEntities, context)

      val document = Document(
        title, header,
        mainFrame :: otherSubdomainFrames ++ externalFrames,
        freeFactEntities ++ dimEntities ++ assoEntities,
        internalAndOutboundRelationships ++ inboundRelationships ++ assoRelationships
      )
      s"$title.plantuml" -> document.render()
    }}.toMap
  }

  /**
    * Generate PlantUML input for each external domain-specific diagram
    *
    * @param context the context
    * @return a multi-element map (1 per external domain) of PlantUML input file name to input file content
    */
  private def generateExternalDomainInputs(context: Context): Map[String, String] = {
    /*
    For each external domain, the result includes the following:
    - All tables of the external domain
    - Fact tables of the domain (with or without a subdomain) that have relationships with tables of the external domain (either directly or via asso tables)
    - Tables of other external domains that have relationships with tables of the external domain via asso tables
    - Asso tables that connect the external domain with the domain or other external domains
    - Direct relationships between fact tables of the domain and tables of the external domain
    - Indirect relationships between fact tables or tables of other external domains and tables of the external domain (via asso tables)
   */
    val subdomainFrames = context.subdomains.toList.map(subdomain => {
      val stereotype = if (context.mainSubdomain.contains(subdomain)) Some("mainsubdomain") else None
      val color = context.subdomainToBackgroundColorMap(subdomain)
      val factEntities = getEntities(context.factHistoTables, context, Some(subdomain))
      Frame(subdomain, stereotype, color, factEntities)
    })
    val freeFactEntities = getEntities(context.factHistoTables, context, noSubdomain = true)
    if (freeFactEntities.length > 0) {
      logger.info(s"Found ${freeFactEntities.length} fact table(s) without a subdomain: ${freeFactEntities.map(_.name).mkString(", ")}")
    }
    val assoEntities = getEntities(context.assoHistoTables, context)
    val externalDomainToFrameMap =
      getExternalFramesFromEntities(subdomainFrames.flatMap(_.entities) ++ freeFactEntities ++ assoEntities, context)
        .map(f => f.name -> f).toMap
    val externalDomains = externalDomainToFrameMap.keys.toList

    externalDomains.map(externalDomain => {
      val title = s"star-schema_${context.config.domain}_${fileNameSafe(externalDomain)}_v${context.config.version}"

      val mainFrame = externalDomainToFrameMap(externalDomain)
      val filteredAssoEntities = assoEntities.filter(e => testFkInEntity(e, _.targetSchema.contains(externalDomain)))
      def isLinkedToExternalDomain: Entity => Boolean = e =>
        testFkInEntity(e, _.targetSchema.contains(externalDomain)) ||
          testFkInEntities(filteredAssoEntities, fk => fk.targetSchema.contains(e.schema) && fk.targetTable.equals(e.name))
      val filteredSubdomainFrames = subdomainFrames.flatMap(_.filterEntities(isLinkedToExternalDomain(_)))
      val filteredFreeFactEntities = freeFactEntities.filter(isLinkedToExternalDomain(_))
      val filteredOtherExternalDomainFrames = externalDomainToFrameMap
        .filterKeys(!_.equals(externalDomain)).values
        .flatMap(_.filterEntities(isLinkedToExternalDomain(_)))

      val factRelationships = getRelationshipsFromEntities(filteredSubdomainFrames.flatMap(_.entities) ++ filteredFreeFactEntities)
        .filter(_.toSchema.contains(externalDomain))
        .map(_.removeColumns)
        .map(_.setDirection("up"))
      val assoRelationships = getRelationshipsFromEntities(filteredAssoEntities)
        .map(_.removeColumns)
        .map(_.setDirectionIf(_.toSchema.contains(externalDomain), "up"))

      val document = Document(
        title, header,
        mainFrame :: filteredSubdomainFrames ++ filteredOtherExternalDomainFrames,
        filteredFreeFactEntities ++ filteredAssoEntities,
        factRelationships ++ assoRelationships
      )
      s"$title.plantuml" -> document.render()
    }).toMap
  }

  /**
    * Transforms a string so that it is safe to include in a file name, by replacing spaces with underscores and removing
    * special characters
    *
    * @param input the input string
    * @return the result
    */
  private def fileNameSafe(input: String): String = {
    input.replaceAll(" ", "_").replaceAll("\\W", "")
  }

  /**
    * Gets PlantUML DSL entities from model tables
    *
    * @param tableDefs a list of TableDef objects
    * @param context the context
    * @param subdomain a subdomain to filter on (optional)
    * @param excludedSubdomain a subdomain to exclude (optional)
    * @param noSubdomain a boolean indicating if only the tables without a subdomain should be taken (optional, defaults to false)
    * @return a list of PlantUML DSL entity objects
    */
  private def getEntities(tableDefs: List[TableDef], context: Context,
                          subdomain: Option[String] = None,
                          excludedSubdomain: Option[String] = None,
                          noSubdomain: Boolean = false): List[Entity] = {
    require(subdomain.map(x => 1).getOrElse(0) + excludedSubdomain.map(x => 1).getOrElse(0) + (if (noSubdomain) 1 else 0) <= 1,
      "Only 1 filtering criterion may be specified")
    tableDefs
      .filter(tableDef =>
        (!subdomain.isDefined || subdomain.equals(tableDef.schema.subdomain))
          && (!excludedSubdomain.isDefined || !excludedSubdomain.equals(tableDef.schema.subdomain))
          && (!noSubdomain || tableDef.schema.subdomain.isEmpty))
      .map(tableDef => {
        val name = tableDef.schema.name
        val stereotype = tableDef.schema match {
          case s if s.isDim => Some("dimtable")
          case s if s.isAsso => Some("assotable")
          case _ => if (context.isSubdomainMainTable(name)) Some("maintable") else None
        }
        val columnDefs = tableDef.schema.columns.filter(c => ignoreRegex.findFirstMatchIn(c.name).isEmpty)
        val maxColumnNameLength = columnDefs.map(_.name.length).max
        val maxColumnTypeLength = columnDefs.map(formatColumnType(_).length).max
        val columns = columnDefs
          .map(columnDef =>
            Column(columnDef.name, formatColumnType(columnDef), columnDef.isMandatory, getKey(columnDef, context),
              maxColumnNameLength, maxColumnTypeLength))
        Entity(context.config.domain, name, stereotype, columns)
    })
  }

  /**
    * Formats a column type into a string to be displayed in the PlantUML diagram
    *
    * @param columnDef a ColumnDef
    * @return the formatted column type
    */
  private def formatColumnType(columnDef: ColumnDef): String = {
    columnDef.columnType match {
      case ColumnType.strColumn => "String"
      case ColumnType.intColumn => "Integer"
      case ColumnType.binaryStrColumn => "Binary"
      case other => other.toString.replace("Column", "").capitalize
    }
  }

  /**
    * Gets a PlantUML DSL key object for a column
    *
    * @param columnDef a ColumnDef
    * @param context the context
    * @return a PlantUmlDSL.Key (optional)
    */
  private def getKey(columnDef: ColumnDef, context: Context): Option[Key] = {
    val primaryKey = if (columnDef.belongsToPK) Some(PrimaryKey) else None
    val foreignKey = columnDef.fk.map(_.toList) match {
      case Some(fkRel :: _) =>
        Some(ForeignKey(
          fkRel.schema,
          if (fkRel.schema.isEmpty || fkRel.schema.contains(context.config.domain)) context.tableToSubdomainMap(fkRel.table) else None,
          // BDS-27525: If the target table name starts with "INTERNAL_", replace it with a generated name from the column name
          if (ignoreRegex.findFirstMatchIn(fkRel.table).isEmpty) fkRel.table else columnDef.name match {
            case name if name.endsWith("_ID") => "FACT_" + name.replace("_ID", "") + "_HISTO"
            case name if name.startsWith("VERSION_") => "FACT_" + name.replace("VERSION_", "") + "_HISTO"
            case _ => fkRel.table
          },
          fkRel.columnName))
      case _ => None
    }
    // The FK wins if the column is both FK and PK (i.e. in ASSO tables), except for VERSION
    (primaryKey, foreignKey) match {
      case (Some(_), Some(_)) => if (columnDef.name.equals("VERSION")) primaryKey else foreignKey
      case (Some(_), None)    => primaryKey
      case (None, Some(_))    => foreignKey
      case (None, None)       => None
    }
  }

  /**
    * Tests if some foreign key in an entity satisfies a predicate
    *
    * @param entity an entity
    * @param predicate a predicate
    * @return a Boolean
    */
  private def testFkInEntity(entity: Entity, predicate: ForeignKey => Boolean) = {
    entity.columns.find(_.key match {
      case Some(key) if key.isInstanceOf[ForeignKey] => predicate(key.asInstanceOf[ForeignKey])
      case _ => false
    }).isDefined
  }

  /**
    * Tests if some foreign key in some entity within a list of entities satisfies a predicate
    *
    * @param entities a list of entities
    * @param predicate a predicate
    * @return a Boolean
    */
  private def testFkInEntities(entities: List[Entity], predicate: ForeignKey => Boolean): Boolean = {
    entities.map(entity => testFkInEntity(entity, predicate)).fold(false)(_ || _)
  }

  /**
    * Gets PlantUML DSL relationships from entities
    *
    * @param entities a list of entities
    * @return a list of relationships
    */
  private def getRelationshipsFromEntities(entities: List[Entity]): List[Relationship] = {
    entities.flatMap(entity => {
      val fromTable = entity.name
      entity.columns.flatMap(column => {
        val fromColumn = column.name
        column.key match {
          case Some(ForeignKey(toSchema, _, toTable, toColumn)) if !toColumn.equals("VERSION") =>
            Some(Relationship(fromTable, Some(fromColumn), toSchema, toTable, Some(toColumn)))
          case _ =>
            None
        }
      })
    })
  }

  /**
    * Gets PlantUML DSL frames for external domains from entities.
    *
    * The frames will contain entities reflecting the external tables referenced by the foreign keys in the provided
    * list of entities
    *
    * @param entities a list of entities
    * @param context the context
    * @return a list of frames for external domains
    */
  private def getExternalFramesFromEntities(entities: List[Entity], context: Context): List[Frame] = {
    case class ExternalColumnInfo(targetSchema: String, targetTable: String, name: String, colType: String)
    entities
      .flatMap(_.columns)
      .filter(_.key match {
        case Some(ForeignKey(targetSchema, _, _, _)) => targetSchema.isDefined && !targetSchema.contains(context.config.domain)
        case _ => false
      })
      .map(c => {
        val key = c.key.get.asInstanceOf[ForeignKey]
        ExternalColumnInfo(key.targetSchema.get, key.targetTable, key.targetColumn, c.colType)
      })
      .distinct
      .groupBy(_.targetSchema)
      .map { case (schemaName, columns) =>
        val entities = columns.groupBy(_.targetTable)
          .mapValues(l => {
            val maxColumnNameLength = l.map(_.name.length).max
            val maxColumnTypeLength = l.map(_.colType.length).max
            l.map(c => Column(c.name, c.colType, true, Some(PrimaryKey), maxColumnNameLength, maxColumnTypeLength))
          })
          .map { case (tableName, columns) => Entity(schemaName, tableName, None, columns) }.toList
        schemaName -> entities
      }.map { case (schemaName, entities) => Frame(schemaName, None, externalDomainBackgroundColor, entities) }.toList
  }
}
