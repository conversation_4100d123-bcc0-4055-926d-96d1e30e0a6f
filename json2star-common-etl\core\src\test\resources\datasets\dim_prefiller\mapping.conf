{
  "ruleset": {
    "mapping": {
      "record-filter": "$.mainResource.current.image.bagsGroup"
    }
  },
  "tables": [{
        "name": "FACT_HISTO",
        "mapping": {
          "merge": {
            "key-columns": ["RESERVATION_ID", "ENVELOP_NB"],
            "if-dupe-take-higher": ["LOAD_DATE"]
          },
          "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}]}],
          "columns": [
            {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
            {"name": "ENVELOP_NB", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}},
            {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
            {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
            {"name": "IS_LAST_ENVELOP", "column-type": "booleanColumn", "sources": {}}
          ],
          "pit": {
            "type": "master-pit-table",
            "master" : {
              "pit-key": "RESERVATION_ID",
              "pit-version": "ENVELOP_NB",
              "valid-from": "DATE_BEGIN",
              "valid-to": "DATE_END",
              "is-last": "IS_LAST_ENVELOP"
            }
          }
        }
    },
    {
      "name": "DIM_PASSENGER_TYPE",
      "table-selectors": ["TEST_CORE"],
      "mapping": {
        "description": {"description": "test", "granularity": "1 test type"},
        "merge": {
          "key-columns": ["PASSENGER_TYPE_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [{"blocks": [{"base": "$.mainResource.current.image.travelers[*]"}]}],
        "columns": [
          {"name": "PASSENGER_TYPE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base":"$.passengerTypeCode"}]}, "expr": "hashS({0})",
            "meta": {"description": {"value": "Hash of the functional key (code)", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "PASSENGER_TYPE_CODE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base":"$.passengerTypeCode"}]}}
          {"name": "PASSENGER_TYPE_LABEL", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base":"$.passengerTypeCode"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      },
      "prefiller": [ {
        "data-source-key": "KEY1",
        "column-filler" : [
          {"dim-col" : "PASSENGER_TYPE_ID", "src-col" : "TYPE_CODE"},
          {"dim-col" :  "PASSENGER_TYPE_CODE", "src-col" : "TYPE_CODE"},
          {"dim-col" :  "PASSENGER_TYPE_LABEL", "src-col" : "TYPE_LABEL"}
        ]
      },
        {
        "data-source-key": "KEY2",
        "column-filler" : [
          {"dim-col" : "PASSENGER_TYPE_ID", "src-col" : "TYPE_CODE"},
          {"dim-col" :  "PASSENGER_TYPE_CODE", "src-col" : "TYPE_CODE"},
          {"dim-col" :  "PASSENGER_TYPE_LABEL", "src-col" : "TYPE_LABEL"}
        ]
      }
    ]
  },
    {
      "name": "DIM_PASSENGER_TYPE_1",
      "table-selectors": ["TEST_CORE"],
      "mapping": {
        "description": {"description": "test", "granularity": "1 test type"},
        "merge": {
          "key-columns": ["PASSENGER_TYPE_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [{"blocks": [{"base": "$.mainResource.current.image.travelers[*]"}]}],
        "columns": [
          {"name": "PASSENGER_TYPE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base":"$.passengerTypeCode"}]}, "expr": "hashS({0})",
            "meta": {"description": {"value": "Hash of the functional key (code)", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "PASSENGER_TYPE_CODE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base":"$.passengerTypeCode"}]}},
          {"name": "PASSENGER_TYPE_LABEL", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base":"$.passengerTypeCode"}]}},//default value, overridden
          {"name": "RECORD_SOURCE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"literal" : "DOMAIN_FEED"}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      },
      "prefiller": [ {
        "data-source-key": "PAX_TYPE",
        "column-filler" : [
          {"dim-col" : "PASSENGER_TYPE_ID", "src-col" : "CODE"},
          {"dim-col" :  "PASSENGER_TYPE_CODE", "src-col" : "CODE"},
          {"dim-col" :  "PASSENGER_TYPE_LABEL", "src-col" : "NAME"}
        ]
      }
      ]
    },
    {
      "name": "DIM_PASSENGER_TYPE_2",
      "table-selectors": ["TEST_CORE"],
      "mapping": {
        "description": {"description": "test", "granularity": "1 test type"},
        "merge": {
          "key-columns": ["PASSENGER_TYPE_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [{"blocks": [{"base": "$.mainResource.current.image.travelers[*]"}]}],
        "columns": [
          {"name": "PASSENGER_TYPE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base":"$.passengerTypeCode"}]}, "expr": "hashS({0})",
            "meta": {"description": {"value": "Hash of the functional key (code)", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "PASSENGER_TYPE_CODE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base":"$.passengerTypeCode"}]}},
          {"name": "PASSENGER_TYPE_LABEL", "column-type": "strColumn", "is-mandatory": "true", "sources": {}},//default value, overridden
          {"name": "RECORD_SOURCE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"literal" : "DOMAIN_FEED"}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      },
      "prefiller": [ {
        "data-source-key": "PAX_TYPE",
        "column-filler" : [
          {"dim-col" : "PASSENGER_TYPE_ID", "src-col" : "CODE"},
          {"dim-col" :  "PASSENGER_TYPE_CODE", "src-col" : "CODE"},
          {"dim-col" :  "PASSENGER_TYPE_LABEL", "src-col" : "NAME"}
        ]
      },{
        "data-source-key": "PAX_TYPE2",
        "column-filler" : [
          {"dim-col" : "PASSENGER_TYPE_ID", "src-col" : "CODE"},
          {"dim-col" :  "PASSENGER_TYPE_CODE", "src-col" : "CODE"},
          {"dim-col" :  "PASSENGER_TYPE_LABEL", "src-col" : "NAME"}
        ]
      }
      ]
    },
    {
      "name": "DIM_PASSENGER_TYPE_3",
      "table-selectors": ["TEST_CORE"],
      "mapping": {
        "description": {"description": "test", "granularity": "1 test type"},
        "merge": {
          "key-columns": ["PASSENGER_TYPE_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [{"blocks": [{"base": "$.mainResource.current.image.travelers[*]"}]}],
        "columns": [
          {"name": "PASSENGER_TYPE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base":"$.passengerTypeCode"}]}, "expr": "hashS({0})",
            "meta": {"description": {"value": "Hash of the functional key (code)", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "PASSENGER_TYPE_CODE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base":"$.passengerTypeCode"}]}},
          {"name": "PASSENGER_TYPE_LABEL", "column-type": "strColumn",  "sources": {}},
          {"name": "PASSENGER_TYPE_UNUSED", "column-type": "strColumn", "sources": {"blocks": [{"base":"$.passengerTypeCode"}]}},
          {"name": "PASSENGER_TYPE_LITERAL", "column-type": "strColumn", "is-mandatory": "true", "sources": {"literal": "TYPE_LITERAL"}},
          {"name": "RECORD_SOURCE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"literal" : "DOMAIN_FEED"}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      },
      "prefiller": [ {
        "data-source-key": "PAX_TYPE",
        "column-filler" : [
          {"dim-col" : "PASSENGER_TYPE_ID", "src-col" : "CODE"},
          {"dim-col" :  "PASSENGER_TYPE_CODE", "src-col" : "CODE"},
          {"dim-col" :  "PASSENGER_TYPE_LABEL", "src-col" : "NAME"}
        ]
      }
      ]
    },
    {
      "name": "DIM_PASSENGER_TYPE_4",
      "table-selectors": ["TEST_CORE"],
      "mapping": {
        "description": {"description": "test", "granularity": "1 test type"},
        "merge": {
          "key-columns": ["PASSENGER_TYPE_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [{"blocks": [{"base": "$.mainResource.current.image.travelers[*]"}]}],
        "columns": [
          {"name": "PASSENGER_TYPE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base":"$.passengerTypeCode"}]}, "expr": "hashS({0})",
            "meta": {"description": {"value": "Hash of the functional key (code)", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "PASSENGER_TYPE_CODE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base":"$.passengerTypeCode"}]}},
          {"name": "PASSENGER_TYPE_LABEL", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base":"$.passengerTypeCode"}]}},
          {"name": "RECORD_SOURCE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"literal" : "DOMAIN_FEED"}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      },
      "prefiller": [ {
        "data-source-key": "PAX_TYPE",
        "column-filler" : [
          {"dim-col" : "PASSENGER_TYPE_ID", "src-col" : "CODE"},
          {"dim-col" :  "PASSENGER_TYPE_CODE", "src-col" : "CODE"},
          {"dim-col" :  "PASSENGER_TYPE_LABEL", "src-col" : "NAME"}
        ]
      }
      ]
    },
    {
      "name": "DIM_PASSENGER_TYPE_5",
      "table-selectors": ["TEST_CORE"],
      "mapping": {
        "description": {"description": "test", "granularity": "1 test type"},
        "merge": {
          "key-columns": ["PASSENGER_TYPE_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [{"blocks": [{"base": "$.mainResource.current.image.travelers[*]"}]}],
        "columns": [
          {"name": "PASSENGER_TYPE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base":"$.passengerTypeCode"}]}, "expr": "hashS({0})",
            "meta": {"description": {"value": "Hash of the functional key (code)", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "PASSENGER_TYPE_CODE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base":"$.passengerTypeCode"}]}},
          {"name": "PASSENGER_TYPE_LABEL", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base":"$.passengerTypeCode"}]}},
          {"name": "RECORD_SOURCE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"literal" : "FROM_FEED_DATA"}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      },
      "prefiller": [ {
        "data-source-key": "FROM_REF_DATA",
        "column-filler" : [
          {"dim-col" : "PASSENGER_TYPE_ID", "src-col" : "CODE"},
          {"dim-col" :  "PASSENGER_TYPE_CODE", "src-col" : "CODE"},
          {"dim-col" :  "PASSENGER_TYPE_LABEL", "src-col" : "NAME"}
        ]
      }
      ]
    }
  ]
}
