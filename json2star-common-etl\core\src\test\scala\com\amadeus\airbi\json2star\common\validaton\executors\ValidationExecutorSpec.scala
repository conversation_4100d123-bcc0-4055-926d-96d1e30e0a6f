package com.amadeus.airbi.json2star.common.validaton.executors

import com.amadeus.airbi.json2star.common.app.Json2StarApp.readMappingConfig
import com.amadeus.airbi.json2star.common.config.AppConfig
import com.amadeus.airbi.json2star.common.validation.config.{ValidationConf, ValidationConfig}
import com.amadeus.airbi.json2star.common.validation.executors.ValidationExecutor
import com.amadeus.airbi.rawvault.common.testfwk.ImplicitConversions.Converters
import com.amadeus.airbi.rawvault.common.testfwk.Json2StarSpec

import java.time.OffsetDateTime

class ValidationExecutorSpec extends Json2StarSpec {

  // validation executor goes beyond the scope of Mapping addon (the only one supported by light tests)
  override def isLight: Boolean = false

  "ValidationExecutor" should "getTableList " in {
    val test = "validation/sample_1"
    val config = readMappingConfig(s"${test}/mapping.conf".toPath, Set("latest_selector"))
    val appConf = AppConfig("validation/sample_1/app.conf")
    val result = ValidationExecutor.getTableList(
      s"${test}/mapping.conf".toPath,
      appConf.tablesSelectors,
      appConf.disabledStackableAddons,
      List.empty
    )

    val actual = result.tables.map(t => t.table.name)
    val expected = List("FACT_PASSENGER", "FACT_PASSENGER_HISTO", "FACT_SECONDARY_HISTO")
    actual should equal(expected)
  }

  it should "getAndRunQueries " in {
    val test = "validation/sample_1"
    val appConf = AppConfig("validation/sample_1/app.conf")
    val tablesDef = ValidationExecutor.getTableList(
      s"${test}/mapping.conf".toPath,
      appConf.tablesSelectors,
      appConf.disabledStackableAddons,
      List.empty
    )
    val vConfig = ValidationConf(
      appConf,
      inputDatabase,
      "test_results",
      1,
      "DUMMY",
      "LOCAL",
      ""
    )
    val validParams = ValidationConfig(
      validationDatabase = inputDatabase,
      validationTablename = "toto",
      domain = vConfig.appConfig.common.domain,
      domainVersion = vConfig.appConfig.common.domainVersion,
      customer = vConfig.appConfig.common.shard,
      phase = vConfig.appConfig.common.domain,
      currentTimestamp = OffsetDateTime.now().toString,
      daysBack = vConfig.validationDaysBack
    )
    val result = ValidationExecutor.getQueries(spark, tablesDef, vConfig, validParams)

    val actual = result.map(t => t._2)
    val expected = List(
      "Test 1 - Dummy check for FACT_PASSENGER in star schema history",
      "Test 2 - Dummy check for FACT_PASSENGER_HISTO in star schema history",
      "Test 3 - Dummy check for FACT_SECONDARY_HISTO in star schema history"
    )
    actual should equal(expected)

    val afterRunResults = ValidationExecutor.runQueries(spark, result, validParams)
    val actualAfterRunResults = afterRunResults.map(t => t.fail_ratio).toList
    val expecteAfterRunResults: List[Float] = List(1.0.toFloat, 0.5.toFloat, 0.33333334.toFloat)
    actualAfterRunResults should equal(expecteAfterRunResults)
  }

  it should "run " in {
    val dataDir = "validation/sample_1"
    val pathExpectedResults = "src/test/resources/" + dataDir + "/expected_results/"
    val appConf = AppConfig(dataDir + "/app.conf")
    val vConfig = ValidationConf(
      appConf,
      inputDatabase,
      "test_results",
      1,
      "DUMMY",
      "LOCAL",
      ""
    )
    ValidationExecutor.run(spark, vConfig)
    checkTableContent("test_results", pathExpectedResults + _ + ".csv", false, untestedColumns = Array("test_time"))

  }

}
