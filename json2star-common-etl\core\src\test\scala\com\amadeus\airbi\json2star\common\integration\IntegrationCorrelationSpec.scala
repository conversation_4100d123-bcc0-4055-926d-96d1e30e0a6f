package com.amadeus.airbi.json2star.common.integration

import com.amadeus.airbi.datalake.common.spark.DefaultLocalSparkInstance
import com.amadeus.airbi.json2star.common.app.Json2StarApp
import com.amadeus.airbi.json2star.common.app.Json2StarApp.readMappingConfig
import com.amadeus.airbi.rawvault.common.RootConfig
import com.amadeus.airbi.rawvault.common.testfwk.Json2StarSpec
import org.apache.commons.io.FileUtils

import java.io.File
import java.nio.file.FileSystems
import scala.reflect.io.Directory

class IntegrationCorrelationSpec extends Json2StarSpec {

  override def isLight: Boolean = false

  val tablesSelectors: Set[String] = Set("PNR_ACTIVE", "TKTEMD_ACTIVE", "DAAS_CORRELATION")

  val pnr = "pnr"
  val tktemd = "tktemd"
  val mappingFilePnr: String = "datasets/correlation_purge/config/pnr.conf"
  val mappingFileTktemd: String = "datasets/correlation_purge/config/tktemd.conf"
  val mappingFileCorr = "datasets/correlation_purge/config/corr.conf"

  lazy val pnrTableNames: List[String] = getTableNames(mappingFilePnr, tablesSelectors)
  lazy val tktemdTableNames: List[String] = getTableNames(mappingFileTktemd, tablesSelectors)
  lazy val correlationTableNames: List[String] = getTableNames(mappingFileCorr, tablesSelectors)

  override def beforeAll: Unit = {
    super.beforeAll
    createDatabaseWithSuffix(pnr)
    createDatabaseWithSuffix(tktemd)
    createTables(mappingFileCorr, tablesSelectors, restrictedTableSet = correlationTableNames)
    createTables(mappingFilePnr, tablesSelectors, pnrTableNames, Some(pnr))
    createTables(mappingFileTktemd, tablesSelectors, tktemdTableNames, Some(tktemd))
  }

  override def afterAll: Unit = {
    // delete database directories
    val warehouseDir = DefaultLocalSparkInstance.warehouseDir
    FileUtils.deleteDirectory(FileSystems.getDefault.getPath(warehouseDir).toFile)
  }

  override def beforeEach: Unit = {
    cleanTables(pnrTableNames, Some(pnr))
    cleanTables(tktemdTableNames, Some(tktemd))
    cleanTables(correlationTableNames, None)
  }

  private def getCorrConfig: RootConfig = {
    rootConfig(
      "",
      inputDatabase,
      label = "_corr",
      isLight = isLight
    ).copy(inputDatabases =
      Map(
        "PNR" -> (inputDatabase + "_pnr"),
        "TKTEMD" -> (inputDatabase + "_tktemd")
      )
    )
  }

  private def runFeed(
    feed: String,
    tableNames: Seq[String],
    confFile: String,
    testFolder: String = "correlation_purge",
    createExpected: Boolean = false
  ): Unit = {
    logger.trace(s"feed: $feed ")
    val pathExpectedResults = s"src/test/resources/datasets/${testFolder}/${feed.toLowerCase}/expected_results/"
    val dataDir = s"datasets/${testFolder}/${feed.toLowerCase}/data/"
    val mapping = readMappingConfig(confFile, tablesSelectors)
    val rc = rootConfig(
      dataDir,
      s"${inputDatabase}_${feed.toLowerCase}",
      tablesSelectors = tablesSelectors,
      isLight = isLight
    )

    val checkpointLocation = rc.etl.stream.sink.checkpointLocation
    val directory = new Directory(new File(checkpointLocation))
    directory.deleteRecursively()
    Json2StarApp.run(spark, rc, mapping)
    // Note: for simple feeds (pnr, tkt), we are simulating streaming running batches on different folders
    // so the checkpoint location MUST BE cleaned at each batch
    directory.deleteRecursively()
    // check only correlation tables
    checkTablesContent(tableNames, pathExpectedResults, createExpected, Some(feed))
  }

  it should "Correlate and correctly propagate manual deletes (single version) and purge on a single upstream" taggedAs (SlowTestTag) in {
    val testFolder = "correlation_purge"
    // Prepare correlation run
    val corrMapping = readMappingConfig(mappingFileCorr)
    val corrConfig = getCorrConfig
    val checkpointLocation = corrConfig.etl.stream.sink.checkpointLocation
    val corrCheckpointDir = new Directory(new File(checkpointLocation))
    // Note: as correlation consumes and writes in the same DB all along the test, the checkpoint location MUST NOT be
    // cleaned between different batches (it's cleaned only at the beginning and at the end).
    corrCheckpointDir.deleteRecursively()

    // PNR: Tables generated: FACT_RESERVATION_HISTO and INTERNAL tables
    runFeed(feed = "PNR", pnrTableNames, mappingFilePnr, testFolder)

    // TKT: Tables generated: FACT_TRAVEL_DOCUMENT_HISTO and INTERNAL tables
    runFeed(feed = "TKTEMD", tktemdTableNames, mappingFileTktemd, testFolder)

    // ---
    // Step 1: correlation
    // ---

    val pathExpectedResultsCorr = s"src/test/resources/datasets/${testFolder}/expected_results/"
    Json2StarApp.run(spark, corrConfig, corrMapping)
    checkTablesContent(correlationTableNames, pathExpectedResultsCorr + "/step1/")

    // ---
    // Step 2: manual delete of a single version + recompute correlations
    // ---

    // Simulate the manual delete of a single version (version 8) of USCK5S-2022-06-29
    pnrTableNames.map(tableName =>
      spark.sql(
        s"DELETE FROM ${inputDatabase}_$pnr.$tableName WHERE RESERVATION_ID = 'hashM(USCK5S-2022-06-29)' and VERSION=8"
      )
    )
    Json2StarApp.run(spark, corrConfig, corrMapping)
    checkTablesContent(correlationTableNames, pathExpectedResultsCorr + "/step2/")

    // ---
    // Step 3: purge on a single upstream feed + recompute correlations
    // ---

    // Simulate the purge of AAAAAA-2022-06-29 records (in master and secondary)
    pnrTableNames.map(tableName =>
      spark.sql(s"DELETE FROM ${inputDatabase}_$pnr.$tableName WHERE RESERVATION_ID = 'hashM(AAAAAA-2022-06-29)'")
    )
    // Note: if the purge was done on both upstreams (i.e. purging TRAVEL_DOCUMENT_ID='hashM(6666666666666-2022-06-29)
    // from all TKTEMD tables) recomputing correlation would not mirror the purge, and correlation records would remain
    // in the ASSO table.
    Json2StarApp.run(spark, corrConfig, corrMapping)
    checkTablesContent(correlationTableNames, pathExpectedResultsCorr + "/step3/")

  }

}
