package com.amadeus.airbi.json2star.common

import com.amadeus.airbi.json2star.common.Schema._
import com.amadeus.airbi.rawvault.common.application.config.TableDescription
import com.amadeus.airbi.rawvault.common.config.GDPRZone
import com.amadeus.airbi.rawvault.common.config.GDPRZone.GDPRZoneType

/** Schema definition
  *
  * This is the input for the generation of tables views (e.g., SQL, Snowflake, Confluence, ...)
  *
  * @param name name of the table
  * @param columns columns of the table
  * @param description description of the table
  * @param partitionColumn name of the column used for partitioning the table
  * @param kind the type of the table (materialized, view, ...)
  * @param subdomain subdomain of the table
  * @param subdomainMainTable boolean indicating if the table is the main table of the subdomain (or one of the main
  *                           tables if there are multiple ones)
  */
case class Schema(
  name: String,
  columns: List[ColumnDef],
  description: Option[TableDescription],
  partitionColumn: Option[String] = None,
  kind: TableKind,
  subdomain: Option[String] = None,
  subdomainMainTable: Boolean = false
) {
  def drop(n: String): Schema = this.copy(columns = columns.filterNot(_.name == n))

  val keyColumns: Seq[ColumnDef] = columns.filter(_.belongsToPK)

  /** The GDPR zone of the table computed from the GDPR zone of columns
    * Note: all GDPR zones must be provided at column level
    * otherwise it is not possible to compute a GDPR Zone for the table
    *
    * @return a option of GDPRZoneType
    */
  def gdprZone: Option[GDPRZoneType] = GDPRZone.getMostSensitive(name, columns)

  def isInternal: Boolean = name.toUpperCase().startsWith(INTERNAL_prefix)
  def isFact: Boolean = name.toUpperCase().startsWith(FACT_prefix)
  def isDim: Boolean = name.toUpperCase().startsWith(DIM_prefix)
  def isAsso: Boolean = name.toUpperCase().startsWith(ASSO_prefix)

}

object Schema {
  val FACT_prefix = "FACT_"
  val DIM_prefix = "DIM_"
  val ASSO_prefix = "ASSO_"
  val INTERNAL_prefix = "INTERNAL_"

  sealed trait TableKind {
    def name: String
  }
  case object Materialized extends TableKind {
    override def name: String = "MATERIALIZED"
  }
  case class View(query: String) extends TableKind {
    override def name: String = "VIEW"
  }
}
