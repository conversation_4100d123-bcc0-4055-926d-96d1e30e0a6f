package com.amadeus.airbi.json2star.common.config

import com.amadeus.airbi.json2star.common.snowflake.{AzureConfig, Clear, Secret, SnowflakeVal}
import com.databricks.dbutils_v1.DBUtilsV1
import net.snowflake.spark.snowflake.Parameters
import org.json4s.DefaultFormats
import org.json4s.jackson.Serialization.{write => asJson}

import scala.util.{Failure, Success, Try}
case class SnowflakeParams(
  sparkConf: Map[String, String] = Map.empty[String, String],
  stream: AppConfig.Stream,
  streamOptions: Map[String, String] = Map.empty[String, String],
  snowflakeConf: Map[String, SnowflakeVal],
  queryTag: Map[String, String] = Map.empty[String, String],
  azureConf: AzureConfig,
  displayEvents: Boolean = true,
  //scalastyle:off
  stagingRetentionDays: Int = 7
  //scalastyle:on
) {

  // Get the Snowflake options
  def getSnowflakeOptions(dbutils: DBUtilsV1): Map[String, String] = {
    val sfOptions = snowflakeConf
      // Retrieves actual values from secrets
      .map { case (k, v) =>
        v match {
          case Clear(value) => k -> value
          case Secret(secretName) =>
            val value = if (secretName == "local") {
              // used to run tests locally
              "local-value"
            } else {
              Try(dbutils.secrets.get(azureConf.secretScope, secretName)) match {
                case Success(v) => v
                case Failure(e) =>
                  throw new Exception(s"Exception ${e.getMessage}. Can't find the value $secretName in dbutils")
              }
            }
            k -> value
        }
      }
      // Converts keys from "xxx" format to "sfXxx" format
      .map {
        case kv @ ("pem_private_key", _) => kv
        case (k, v) => ("sf" + k.capitalize) -> v
      }

    sfOptions + {
      "query_tag" -> asJson(queryTag)(DefaultFormats)
    } + {
      // Added for the issue https://github.com/snowflakedb/spark-snowflake/issues/537
      Parameters.PARAM_INTERNAL_EXECUTE_QUERY_IN_SYNC_MODE -> "true"
    }
  }

}
