package com.amadeus.airbi.json2star.common.views.executors

import com.amadeus.airbi.json2star.common.TablesDef
import com.amadeus.airbi.json2star.common.views.generators.DeltaTableCopySchemaGenerator
import com.amadeus.airbi.rawvault.common.application.config.ModelConfigLoader
import com.typesafe.scalalogging.Logger
import org.apache.spark.SparkConf
import org.apache.spark.sql.SparkSession
import org.rogach.scallop.{ScallopConf, ScallopOption}
import org.slf4j.LoggerFactory

import java.io.{BufferedWriter, File, FileWriter}

case class DeltaTableCopySchemaExecutorScallopConf(arguments: Seq[String]) extends ScallopConf(arguments) {
  val appConfigFile: ScallopOption[String] = opt[String](required = true)
  val dryMode: ScallopOption[String] = opt[String](required = false)
  verify()
}

/**
  * This executor is in charge of copying reused tables from the previous deployment.
  */
object DeltaTableCopySchemaExecutor {

  @transient
  lazy val logger: Logger = Logger(LoggerFactory.getLogger(getClass.getName))

  lazy val spark: SparkSession = SparkSession
    .builder()
    .config(
      new SparkConf()
        .set("spark.databricks.delta.properties.defaults.autoOptimize.optimizeWrite", "true")
        .set("spark.databricks.delta.properties.defaults.autoOptimize.autoCompact", "true")
    )
    .getOrCreate()

  def main(args: Array[String]): Unit = {
    val config = DeltaTableCopyConfig(args)
    run(config)
  }

  def run(
     config: DeltaTableCopyConfig
  ) {
    val appConfig = config.appConfig
    val mappingConf = ModelConfigLoader.defaultLoad(
      appConfig.modelConfFile, appConfig.tablesSelectors, appConfig.disabledStackableAddons
    )
    val tablesDef = TablesDef.consolidate(mappingConf)

    val deltaOptions = appConfig.stream.sink.deltaOptions
    val dtc = new DeltaTableCopySchemaGenerator(config)
    val queries = dtc.toInitSql(tablesDef, appConfig.common.outputDatabase, deltaOptions)

    if (queries.isEmpty) {
      logger.error(s"Wrong SQL generated from the file ${config.appConfig.modelConfFile}")
      logger.error(s"Wrong SQL content is ${queries.mkString("\n")}")
      throw new Exception("ERROR COPY SQL GENERATOR")
    }

    logger.info(s"${getClass.getName} - Copying ${queries.size} tables...")
    queries.par.foreach { query =>
      query.statements.foreach(s => run(s, config))
    }
  }

  /** Run the query
    * According to the value of dryMode - It supports
    * - when None                     --> it runs the query with Spark
    * - when a file name is provided  --> it writes to the file (supports only local filesystem)
    * - when the DEBUG is provided    --> it prints to stdout
    *
    * @param query   query to run
    * @param config a DeltaTableCopyConfig
    */
  def run(query: String, config: DeltaTableCopyConfig): Unit = {
    logger.info(s"Run copy query: $query")
    config.dryMode match {
      case None => // run query using Spark
        spark.sparkContext.setJobGroup("COPY", s"${query}") // name the query
        spark.sql(query)
      case Some(outFile) if outFile.toUpperCase == "DEBUG" => // print query without executing it
        println(query) // scalastyle:ignore
      case Some(outFile) => // write query to a file (for testing)
        // Append to a file
        val bw = new BufferedWriter(new FileWriter(new File(outFile), true))
        bw.write(query + "\n")
        bw.close()
    }
  }
}
