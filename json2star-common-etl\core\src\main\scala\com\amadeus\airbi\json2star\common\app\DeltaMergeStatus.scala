package com.amadeus.airbi.json2star.common.app

import com.typesafe.scalalogging.Logger

object DeltaMergeStatus {

  sealed trait MergeStatus {
    def toDebugString: String
  }
  case class MergeOk(tableName: String) extends MergeStatus {
    override def toDebugString: String = s"Merge OK for table: $tableName"
  }
  case class MergeFailed(tableName: String, reason: String, exception: Throwable) extends MergeStatus {
    override def toDebugString: String =  s"Merge failed for table: $tableName: $reason"
  }

  def logMergeStatuses(statuses: Seq[MergeStatus], logger: Logger): Seq[RuntimeException] = {
    statuses.flatMap {
      case ok: MergeOk =>
        logger.trace(s"Merge succeeded: ${ok.toDebugString}")
        None
      case failed: MergeFailed =>
        logger.error(s"Unexpected merge failure: ${failed.toDebugString}", failed.exception)
        Some(new RuntimeException(s"Unexpected merge failure ${failed.toDebugString}", failed.exception))
    }
  }

}
