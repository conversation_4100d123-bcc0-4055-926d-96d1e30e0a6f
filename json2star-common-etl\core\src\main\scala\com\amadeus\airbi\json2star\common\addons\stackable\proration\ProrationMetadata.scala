package com.amadeus.airbi.json2star.common.addons.stackable.proration

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.scala.DefaultScalaModule

case class ProrationMetadata(
  input: Option[ProrationInput] = None,
  tktStatus: Option[String] = None,
  exceptionMsg: Option[String] = None,
  inputStep: Option[StepStatus] = None,
  fclStep: Option[StepStatus] = None,
  dstStep: Option[StepStatus] = None
)
case class StepStatus(
  value: String,
  details: String
)

object ProrationMetadata {
  // Instantiate once instead at each method call
  private val mapper = new ObjectMapper()
  mapper.registerModule(DefaultScalaModule)
  mapper.registerModule(new JavaTimeModule());
  def asJsonString(pm: ProrationMetadata): String = {
    // for pretty print - use mapper.writerWithDefaultPrettyPrinter().writeValueAsString(this)
    mapper.writeValueAsString(pm)
  }
}
