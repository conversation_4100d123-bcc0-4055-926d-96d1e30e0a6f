package com.amadeus.airbi.rawvault.common.application.config

import com.amadeus.airbi.json2star.common.addons.base.AddonConfig
import com.amadeus.airbi.rawvault.common.config.ColumnConfig

object SourceCorrelation {

  /** Settings describing the input tables (and columns) to use to compute a one source correlation
    *
    * @param name              name of the domain (for logging purposes)
    * @param tableName         input table from this domain
    * @param versionColumnName name of the version column used in this input table for 2ndary pit computaion
    */
  case class InputDomain(
    name: String,
    tableName: String,
    versionColumnName: String
  )

  /** Settings defining the target correlation table (its column names, dedup criteria, ...)
    *
    * @param columns           list of all the output columns
    * @param domainAKey               name of the key column (together with the secondaryKey define the granularity of
    *                          either partial table) that by convention belongs to domain A
    *                          in the source table/s, privilege using isMandatory=false to allow for closure detection
    * @param domainBKey      name of secondary key column, that by convention belongs to domain B
    * @param domainAVersion    name that will be given to the column containing the version of the entity of domain A
    * @param domainBVersion    name that will be given to the column containing the version of the entity of domain B
    * @param startDate         name of the column with the beginning of the correlation validity
    * @param endDate           name of the column with the end of the correlation validity
    * @param isLast            name of the column telling if this record represents a valid relation (or an obsolete one in case of false)
    * @param ifDupeTakeHighest deduplication criteria
    */
  case class Target(
    columns: Seq[ColumnConfig],
    domainAKey: String,
    domainBKey: String,
    domainAVersion: String,
    domainBVersion: String,
    startDate: String,
    endDate: String,
    isLast: String,
    ifDupeTakeHighest: Seq[String]
  )

  /** Represents the configuration required to generate a source correlation association table
    *
    * It involves 2 domains.
    *
    * @param domainA one of the domains involved in the correlation (PNR, TKT, ...)
    * @param domainB the other domain
    * @param target  configuration related to the fields of the output association table
    */
  case class AssoTable(
    domainA: InputDomain,
    domainB: String,
    target: Target
  ) extends AddonConfig
}
