package com.amadeus.airbi.json2star.common.addons.base.correlation

import com.amadeus.airbi.datalake.common.spark.MergeHelper
import com.amadeus.airbi.rawvault.common.RootConfig
import com.amadeus.airbi.rawvault.common.application.config.SourceCorrelation.AssoTable
import com.amadeus.airbi.rawvault.common.application.config.TableConfig
import com.typesafe.scalalogging.Logger
import io.delta.tables.DeltaTable
import org.apache.spark.sql.functions.col
import org.apache.spark.sql.streaming.{OutputMode, Trigger}
import org.apache.spark.sql.{Column, DataFrame, SparkSession}
import org.slf4j.LoggerFactory

import scala.concurrent.{ExecutionContext, Future}

object SourceCorrelationPipeline {

  lazy val logger: Logger = Logger(LoggerFactory.getLogger(getClass.getName))

  private def trigger(rootConfig: RootConfig): Trigger = {
    rootConfig.etl.stream.sink.trigger.toLowerCase match {
      case "once" => Trigger.Once()
      case "availablenow" => Trigger.AvailableNow()
      case interval => Trigger.ProcessingTime(interval)
    }
  }

  private def queryName(targetTable: String, streamingTable: String) =
    s"TYPE=CORRSRC TABLE=$targetTable STREAMING=$streamingTable"

  def runAndAwaitStream(
    spark: SparkSession,
    rootConfig: RootConfig,
    table: TableConfig,
    correlation: AssoTable
  )(implicit ec: ExecutionContext): Future[Unit] = Future {

    logger.info(s"Running stream for source correlation table ${table.name}")

    // build fully qualified table name, including the DB
    def t(db: String, table: String) = s"$db.$table"

    val dbA = rootConfig.inputDatabases(correlation.domainA.name)
    val stream = rootConfig.etl.stream
    val inputTable = t(dbA, correlation.domainA.tableName)

    val qn = queryName(targetTable = table.name, streamingTable = inputTable)
    spark.readStream
      .option("readChangeFeed", "true")
      .format("delta")
      .table(s"${inputTable}")
      //renaming simple ""VERSION" Column from input table to official domainA version column
      .withColumnRenamed(correlation.domainA.versionColumnName, correlation.target.domainAVersion)
      .writeStream
      .option("checkpointLocation", stream.sink.checkpointLocation + '/' + table.name)
      .foreachBatch(processIncrement(table) _)
      .queryName(qn)
      .outputMode(OutputMode.Update)
      .trigger(trigger(rootConfig))
      .start()
      .awaitTermination()
  }

  def processIncrement(table: TableConfig)(b: DataFrame, batchId: Long): Unit = {
    val assoTable = table.sourceCorrelation.get
    val equalOperator = "<=>"

    val keyColumns = Seq(
      assoTable.target.domainAKey,
      assoTable.target.domainBKey,
      assoTable.target.domainAVersion,
      assoTable.target.domainBVersion
    )

    // deduplication using delta commit version
    val batchDedup = dedupLogic(b, keyColumns)

    //to drop all input columns not used in target table
    val inputFieldNames = batchDedup.schema.fieldNames
    val outputFieldNames = assoTable.target.columns.map(c => c.name)
    val colsToDrop = inputFieldNames.filter(!outputFieldNames.contains(_))
    val batch = batchDedup.drop(colsToDrop: _*).as("src")

    val fieldNamesArr = batch.schema.fieldNames

    // create update statement
    val updateClause: Map[String, Column] = fieldNamesArr.iterator
      .filterNot(x => keyColumns.contains(x))
      .map(x => x -> col(s"src.$x"))
      .toMap

    // create insert statement
    val insertClause: Map[String, Column] = fieldNamesArr.iterator
      .map(x => x -> col(s"src.$x"))
      .toMap

    // merge
    val delta = DeltaTable.forName(table.name)
    val qn =
      queryName(targetTable = table.name, streamingTable = assoTable.domainA.name + "." + assoTable.domainA.tableName)
    delta.toDF.sparkSession.sparkContext.setJobDescription(s"${qn}")

    delta
      .as("dst")
      .merge(
        batch,
        keyColumns
          .map(field => s"dst.$field $equalOperator src.$field")
          .mkString(" and ")
      )
      .whenMatched(
        // understanding: when we match on the key
        // --> and the new record is equal to the old one --> do nothing
        // --> and the new record is different from the old one --> update
        MergeHelper.matchedCondition(
          batch.schema,
          keyColumns.toSet
        )
      )
      .update(updateClause)
      .whenNotMatched()
      .insert(insertClause)
      .execute()
  }

  /** This method will deduplicate the feed Dataframe: based on key columns,
    * only the records with the most recent commit version will be kept.
    * It expects CDF columns (_change_type, _commit_version, _commit_timestamp) present.
    * @param b input Dataframe with CDF columns
    * @param keyColumns table key columns
    * @return deduplicated Dataframe without CDF columns
    */
  def dedupLogic(b: DataFrame, keyColumns: Seq[String]): DataFrame = {
    // ignore the previous value
    val filteredBatch = b.filter(col("_change_type") =!= "update_preimage")

    MergeHelper.dedupBatchRecordsRowNumber(
      batch = filteredBatch,
      deduplicateKey = keyColumns,
      discriminatingColumns = Seq(col("_commit_version").desc), // keep the most recent commit version
      droppedColumnNames = Seq("LOAD_DATE", "_change_type", "_commit_version", "_commit_timestamp")
    )
  }
}
