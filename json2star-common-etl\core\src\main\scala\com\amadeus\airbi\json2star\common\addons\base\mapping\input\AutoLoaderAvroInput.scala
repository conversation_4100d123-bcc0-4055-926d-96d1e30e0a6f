package com.amadeus.airbi.json2star.common.addons.base.mapping.input

import com.amadeus.airbi.json2star.common.addons.base.mapping.MappingAddonParams
import com.amadeus.airbi.json2star.common.addons.base.mapping.MappingAddonParams.AutoloaderConfig
import com.amadeus.airbi.json2star.common.resize.DatabricksUtils
import com.typesafe.scalalogging.Logger
import org.apache.spark.sql.streaming.DataStreamReader
import org.apache.spark.sql.{DataFrame, SparkSession}
import org.slf4j.LoggerFactory

import scala.util.Try

/** Concrete implementation of `InputFormat`, also implementing `StreamExecutorInputFormat`.
  *
  * It is an input format able consume avro data, executing the plan defined by a `StreamPlanner`.
  * Streaming is done using Databricks Auto Loader
 * https://learn.microsoft.com/en-us/azure/databricks/ingestion/cloud-object-storage/auto-loader/
  */
case class AutoLoaderAvroInput(spark: SparkSession, dbx: DatabricksUtils, conf: AutoloaderConfig)
    extends AvroDecoder
    with StreamExecutorInputFormat {

  lazy val logger: Logger = Logger(LoggerFactory.getLogger(getClass.getName))

  override def sparkSession: SparkSession = spark
  override def startMode: MappingAddonParams.StartConfig = conf.startConfig
  override def pathsToDf(f: Seq[String]): DataFrame = {
    val tries = f.map(p => Try(sparkSession.read.format("avro").load(p).withMetadata()))
    val (success, failures) = tries.partition(_.isSuccess)
    failures.foreach(f => logger.warn(s"Failed to load avro folder: ${f.failed.get.getMessage}"))
    // use reduceOption to handle the case where there are no successes or all dataframes are empty
    success.map(_.get).reduceOption(_ union _).getOrElse(sparkSession.emptyDataFrame)
  }

  // $COVERAGE-OFF$ autoloader cloudFiles is not supported locally
  def streamingDataFrameFrom(streamReader: DataStreamReader, paths: Seq[String]): DataFrame = {
    val configuredStreamReader = streamReader
      .schema(avroSchema)
      .format("cloudFiles")
      .options(
        (Map[String, Any](
          "cloudFiles.format" -> "avro",
          "cloudFiles.useIncrementalListing" -> true,
          "cloudFiles.includeExistingFiles" -> conf.includeExistingFiles,
          "cloudFiles.useNotifications" -> conf.useNotifications
        )
          ++ conf.backfillInterval.map("cloudFiles.backfillInterval" -> _)
          ++ conf.maxFilesPerTrigger.map("cloudFiles.maxFilesPerTrigger" -> _)
          ++ conf.maxBytesPerTrigger.map("cloudFiles.maxBytesPerTrigger" -> _))
          .mapValues(_.toString)
      )
    paths
      .filter(_.trim.nonEmpty)
      .map(configuredStreamReader.load(_).withMetadata())
      .reduce(_ union _)
  }
  // $COVERAGE-OFF$

}
