package com.amadeus.airbi.json2star.common.addons.base.mapping.input.checkpoints

import com.amadeus.airbi.datalake.common.spark.SparkSqlSpecification
import com.amadeus.airbi.json2star.common.addons.base.mapping.MappingAddonParams.{AutoloaderStart, StaticStart}
import com.amadeus.airbi.json2star.common.addons.base.mapping.input.checkpoints.StartCheckpointsTable.{ImmutableStartConfig, ImmutableStaticStart}
import com.amadeus.airbi.json2star.common.addons.base.mapping.input.checkpoints.StaticStartStep.FormatterYyyyMmDd
import com.amadeus.airbi.json2star.common.testfwk.MockDatabricksUtils
import com.amadeus.airbi.rawvault.common.testfwk.TmpDir
import org.scalatest.BeforeAndAfterEach

import java.nio.file.{Files, Paths}
import java.sql.Timestamp
import java.time.{Instant, LocalDate, LocalDateTime, YearMonth}
import java.util.TimeZone

class StreamPlannerSpec extends SparkSqlSpecification with BeforeAndAfterEach with TmpDir {
  TimeZone.setDefault(TimeZone.getTimeZone("UTC"))

  val DefaultAzureRegex: String = "abfss://[^@]+@[^/]+/evhns-[^/]+/evh-[^/]+"
  val Regex = ".*"

  val YearMonth10: YearMonth = YearMonth.of(2024, 11)
  val EpochTimestamp: Timestamp = Timestamp.from(Instant.EPOCH) // WHY? it should be
  val Day10: LocalDate = LocalDate.of(2022, 1, 10)
  val Day10MidnightTimestamp: Timestamp = Timestamp.valueOf(Day10.atStartOfDay())
  def day10Minus(days: Int): LocalDate = Day10.minusDays(days)
  def asPath(d: LocalDate): String = d.format(FormatterYyyyMmDd)

  override def beforeAll: Unit = {
    super.beforeAll
    spark.conf.set("spark.databricks.delta.allowArbitraryProperties.enabled", "true")
  }

  "StreamPlanner" should "resolve autoloader step keeping input paths" in withStrTmpDir { tmpDir =>
    val table = new StartCheckpointsTable(spark, tmpDir + "/checkpoint")
    val mockDbx = new MockDatabricksUtils(2, "test-cluster", spark)
    val paths = Seq("/feed1", "/feed2")

    val result = StreamPlanner.resolve(table, paths, AutoloaderStart(), mockDbx, Day10MidnightTimestamp)

    result shouldBe Seq(AutoloaderStartStep(Seq("/feed1", "/feed2")))
  }

  // Config checks

  it should "store config if no stored config exists" in withStrTmpDir { tmpDir =>
    val table = new StartCheckpointsTable(spark, tmpDir + "/checkpoint")
    val inputConfig = ImmutableStartConfig.from(StaticStart(Regex, 2, None, None), Seq("/feed1"))
    val timestamp = Timestamp.from(Instant.now)

    StreamPlanner.ensureConfigExistsAndUnchangedOrStore(table, inputConfig, timestamp)

    val storedConfig = table.readStoredConfig()
    storedConfig shouldBe Some((timestamp, inputConfig))
  }

  it should "throw exception if stored config is different from input config" in withStrTmpDir { tmpDir =>
    val table = new StartCheckpointsTable(spark, tmpDir + "/checkpoint")
    val storedConfig = ImmutableStartConfig.from(StaticStart(Regex, 2, None, None), Seq("/feed"))
    val timestamp = Timestamp.from(Instant.now)
    table.storeConfig(storedConfig, timestamp)

    // path changed
    an[IllegalStateException] should be thrownBy {
      val inputConfig = ImmutableStartConfig.from(StaticStart(Regex, 2, None, None), Seq("/feedChanged"))
      StreamPlanner.ensureConfigExistsAndUnchangedOrStore(table, inputConfig, timestamp)
    }

    // start date changed
    an[IllegalStateException] should be thrownBy {
      val inputConfig = ImmutableStartConfig.from(StaticStart(Regex, 2, Some(Day10), None), Seq("/feed"))
      StreamPlanner.ensureConfigExistsAndUnchangedOrStore(table, inputConfig, timestamp)
    }

    // end date changed
    an[IllegalStateException] should be thrownBy {
      val inputConfig = ImmutableStartConfig.from(StaticStart(Regex, 2, None, Some(Day10)), Seq("/feed"))
      StreamPlanner.ensureConfigExistsAndUnchangedOrStore(table, inputConfig, timestamp)
    }

    // mode changed
    an[IllegalStateException] should be thrownBy {
      val inputConfig = ImmutableStartConfig.from(AutoloaderStart(), Seq("/feed"))
      StreamPlanner.ensureConfigExistsAndUnchangedOrStore(table, inputConfig, timestamp)
    }

  }

  // Processing steps computation

  it should "resolve static + autoloader steps when first time" in withStrTmpDir { tmpDir =>
    val currentDate = day10Minus(0) // today = d10
    val table = new StartCheckpointsTable(spark, tmpDir + "/checkpoint")
    val mockDbx = new MockDatabricksUtils(2, "test-cluster", spark)
    val basePath = tmpDir + "/base/path"
    val daysPerBatch = 2
    val paths = Seq("/feed1", "/feed2").map(p => s"$basePath$p")

    val staticStart = StaticStart(
      inputPathRegex = Regex,
      numberOfDaysPerBatch = daysPerBatch,
      startDate = None, // start date not provided, it will be discovered
      endDate = None // end date not provided, it will be today - 1
    )

    Files.createDirectories(Paths.get(basePath + s"/feed1/0/${asPath(day10Minus(2))}")) // feed1, 2 partitions
    Files.createDirectories(Paths.get(basePath + s"/feed1/0/${asPath(day10Minus(1))}")) // partition 0 from d10-2 to d10
    Files.createDirectories(Paths.get(basePath + s"/feed1/0/${asPath(day10Minus(0))}"))
    Files.createDirectories(Paths.get(basePath + s"/feed1/1/${asPath(day10Minus(1))}")) // partition 1 from d10-1 to d10
    Files.createDirectories(Paths.get(basePath + s"/feed1/1/${asPath(day10Minus(0))}"))

    Files.createDirectories(Paths.get(basePath + s"/feed2/0/${asPath(day10Minus(1))}")) // feed2, 1 partition
    Files.createDirectories(Paths.get(basePath + s"/feed2/0/${asPath(day10Minus(0))}")) // partition 0 from d10-1 to d10

    // In this test, we check the nominal scenario of first launch of static start + autoloader.
    // We expect the resulting steps to be:
    // - static: from discovery until d10-1 included
    // - autoloader: since d10 included until the end of the month (using a glob expression on the days)

    val result = StreamPlanner.resolve(table, paths, staticStart, mockDbx, Day10MidnightTimestamp)

    val expectedStaticInputs = Seq(
      StaticStartStepPath(basePath + "/feed1", Seq(0, 1)),
      StaticStartStepPath(basePath + "/feed2", Seq(0))
    )
    val expectedStaticStartStep = StaticStartStep(
      numberOfDaysPerBatch = daysPerBatch,
      paths = expectedStaticInputs,
      startDate = day10Minus(2), // discovered
      endDate = day10Minus(1) // computed as today - 1
    )
    val expectedAutoloaderStep = AutoloaderMonthStep.from(expectedStaticStartStep, None, currentDate)

    result shouldBe Seq(expectedStaticStartStep) ++ expectedAutoloaderStep
  }


  it should "resolve static + autoloader steps upon failure during static init" in withStrTmpDir { tmpDir =>
    val currentDate = day10Minus(0) // today = d10
    val table = new StartCheckpointsTable(spark, tmpDir + "/checkpoint")
    val mockDbx = new MockDatabricksUtils(2, "test-cluster", spark)
    val basePath = tmpDir + "/base/path"
    val daysPerBatch = 1
    val paths = Seq("/feed1", "/feed2").map(p => s"$basePath$p")

    val staticStart = StaticStart(Regex, daysPerBatch, None, None)

    Files.createDirectories(Paths.get(basePath + s"/feed1/0/${asPath(day10Minus(2))}")) // feed1, 1 partition
    Files.createDirectories(Paths.get(basePath + s"/feed1/0/${asPath(day10Minus(1))}")) // partition 0 from d10-2 to d10
    Files.createDirectories(Paths.get(basePath + s"/feed1/0/${asPath(day10Minus(0))}"))

    Files.createDirectories(Paths.get(basePath + s"/feed2/0/${asPath(day10Minus(3))}")) // feed2, 1 partition
    Files.createDirectories(Paths.get(basePath + s"/feed2/0/${asPath(day10Minus(2))}")) // partition 0 from d10-3 to d10
    Files.createDirectories(Paths.get(basePath + s"/feed2/0/${asPath(day10Minus(1))}")) // (starts one day before)
    Files.createDirectories(Paths.get(basePath + s"/feed2/0/${asPath(day10Minus(0))}"))

    // In this test, we check the scenario of first launch of static start + failure + restart + autoloader.
    // We expect the resulting steps to be:
    // - static: until failure (commit until d10-2)
    // - static: since d10-1 (failure) until d10-1 included
    // - autoloader: since d10 included

    val immutableStartConfig = ImmutableStartConfig.from(staticStart, paths)
    table.storeConfig(immutableStartConfig, Day10MidnightTimestamp)
    table.logBatch(
      day10Minus(3),
      day10Minus(2),
      "...",
      Day10MidnightTimestamp,
      Day10MidnightTimestamp,
      staticStartFinished = false,
      immutableStartConfig
    )
    // processed until d10-2 and failed...

    val result = StreamPlanner.resolve(table, paths, staticStart, mockDbx, Day10MidnightTimestamp)

    val expectedStaticInputs = Seq(
      StaticStartStepPath(basePath + "/feed1", Seq(0)),
      StaticStartStepPath(basePath + "/feed2", Seq(0))
    )
    val expectedStaticStartStep = StaticStartStep(
      numberOfDaysPerBatch = daysPerBatch,
      paths = expectedStaticInputs,
      startDate = day10Minus(1), // last date in checkpoints + 1
      endDate = day10Minus(1) // computed as today - 1
    )
    val expectedAutoloaderStep = AutoloaderMonthStep.from(expectedStaticStartStep, None /* no autoloader yet */, currentDate)

    result shouldBe Seq(expectedStaticStartStep) ++ expectedAutoloaderStep
  }

  it should "resolve static step, but no autoloader step, upon failure during static init, if there is end-date in config" in withStrTmpDir { tmpDir =>
//    val currentDate = day10Minus(0) // today = d10
    val endDate = day10Minus(1)
    val table = new StartCheckpointsTable(spark, tmpDir + "/checkpoint")
    val mockDbx = new MockDatabricksUtils(2, "test-cluster", spark)
    val basePath = tmpDir + "/base/path"
    val daysPerBatch = 1
    val paths = Seq("/feed1").map(p => s"$basePath$p")

    val staticStart = StaticStart(Regex, daysPerBatch, None, Some(endDate))

    Files.createDirectories(Paths.get(basePath + s"/feed1/0/${asPath(day10Minus(2))}")) // feed1, 1 partition
    Files.createDirectories(Paths.get(basePath + s"/feed1/0/${asPath(day10Minus(1))}")) // partition 0 from d10-2 to d10
    Files.createDirectories(Paths.get(basePath + s"/feed1/0/${asPath(day10Minus(0))}"))

    // In this test, we check a scenario with end-date in config with: first launch of static start + failure + restart.
    // We expect the resulting steps to be:
    // - static: until failure (commit until d10-2)
    // - static: since d10-1 (failure) until d10-1 included (end-date)
    // - no-autoloader because of end-date presence

    val immutableStartConfig = ImmutableStartConfig.from(staticStart, paths)
    table.storeConfig(immutableStartConfig, Day10MidnightTimestamp)
    table.logBatch(
      day10Minus(3),
      day10Minus(2),
      "...",
      Day10MidnightTimestamp,
      Day10MidnightTimestamp,
      staticStartFinished = false,
      immutableStartConfig
    )
    // processed until d10-2 and failed...

    val result = StreamPlanner.resolve(table, paths, staticStart, mockDbx, Day10MidnightTimestamp)

    val expectedStaticInputs = Seq(
      StaticStartStepPath(basePath + "/feed1", Seq(0))
    )
    val expectedStaticStartStep = StaticStartStep(
      numberOfDaysPerBatch = daysPerBatch,
      paths = expectedStaticInputs,
      startDate = day10Minus(1), // last date in checkpoints + 1
      endDate = day10Minus(1) // computed as today - 1
    )
    result shouldBe Seq(expectedStaticStartStep)
  }

  it should "return no step if static batches are finished and there is an end-date in config" in withStrTmpDir { tmpDir =>
    val currentDate = day10Minus(0) // today = d10
    val endDate = day10Minus(1)
    val basePath = tmpDir + "/base/path"
    val daysPerBatch = 2
    val table = new StartCheckpointsTable(spark, tmpDir + "/checkpoint")
    val mockDbx = new MockDatabricksUtils(1, "test-cluster", spark)
    val paths = Seq("/feed1").map(p => s"$basePath$p")
    val staticStart = StaticStart(Regex, daysPerBatch, None, Some(endDate))

    Files.createDirectories(Paths.get(basePath + s"/feed1/0/${asPath(day10Minus(2))}")) // feed1, 1 partition

    // In this test, we check a scenario with end-date in config where static start finished.
    // We expect the resulting steps to be empty, because there's nothing left to do (idempotent runs when end-date is present)

    val immutableStartConfig = ImmutableStartConfig.from(staticStart, paths)
    table.storeConfig(immutableStartConfig, Day10MidnightTimestamp)
    table.logBatch(
      firstProcessedDate = day10Minus(2),
      lastProcessedDate = day10Minus(1),
      inputDates = "...",
      startTimestamp = Day10MidnightTimestamp,
      endTimestamp = Day10MidnightTimestamp,
      staticStartFinished = true, // it finished
      config = immutableStartConfig
    )

    val result = StreamPlanner.resolve(table, paths, staticStart, mockDbx, Day10MidnightTimestamp)

    result shouldBe Seq()
  }

  it should "return AutoloaderMonthStep if static batches are finished but no autoloader run yet" in withStrTmpDir { tmpDir =>
    val currentDate = day10Minus(0) // today = d10
    val basePath = tmpDir + "/base/path"
    val daysPerBatch = 2
    val table = new StartCheckpointsTable(spark, tmpDir + "/checkpoint")
    val mockDbx = new MockDatabricksUtils(1, "test-cluster", spark)
    val paths = Seq("/feed1").map(p => s"$basePath$p")
    val staticStart = StaticStart(Regex, daysPerBatch, None, None)

    Files.createDirectories(Paths.get(basePath + s"/feed1/0/${asPath(day10Minus(2))}")) // feed1, 1 partition

    // In this test, we check the scenario of static start finished and then autoloader picking up
    // We expect the resulting steps to be:
    // - autoloader: since d0 included until the end of the month

    val immutableStartConfig = ImmutableStartConfig.from(staticStart, paths)
    table.storeConfig(immutableStartConfig, Day10MidnightTimestamp)
    table.logBatch(
      firstProcessedDate = day10Minus(2),
      lastProcessedDate = day10Minus(1),
      inputDates = "...",
      startTimestamp = Day10MidnightTimestamp,
      endTimestamp = Day10MidnightTimestamp,
      staticStartFinished = true, // it finished
      config = immutableStartConfig
    )

    val result = StreamPlanner.resolve(table, paths, staticStart, mockDbx, Day10MidnightTimestamp)

    val expectedStep = AutoloaderMonthStep.from(
      staticStartEndDate = day10Minus(1),
      staticStartPaths = Seq(StaticStartStepPath(basePath + "/feed1", Seq(0))),
      lastAutoloaderDate = None,
      currentDate = currentDate
    )
    result shouldBe expectedStep
  }

  // Dates checks in config

  it should "throw an exception if start date is after end date" in {
    val startDate = Some(LocalDate.of(2023, 1, 2))
    val endDate = Some(LocalDate.of(2023, 1, 1))
    val now = Timestamp.valueOf(LocalDateTime.now())
    val inputConfig = ImmutableStaticStart(Regex, startDate, endDate, Seq.empty)

    an[IllegalArgumentException] should be thrownBy {
      StreamPlanner.ensureInputConfigIsValid(inputConfig, now)
    }
  }

  it should "throw an exception if end date is equal to or after today" in {
    val today = LocalDate.now()
    val endDate = Some(today)
    val now = Timestamp.valueOf(today.atStartOfDay())
    val inputConfig = ImmutableStaticStart(Regex, None, endDate, Seq.empty)

    an[IllegalArgumentException] should be thrownBy {
      StreamPlanner.ensureInputConfigIsValid(inputConfig, now)
    }
  }

  it should "throw an exception if start date is equal to or after today" in {
    val today = LocalDate.now()
    val startDate = Some(today)
    val now = Timestamp.valueOf(today.atStartOfDay())
    val inputConfig = ImmutableStaticStart(Regex, startDate, None, Seq.empty)

    an[IllegalArgumentException] should be thrownBy {
      StreamPlanner.ensureInputConfigIsValid(inputConfig, now)
    }
  }

  it should "not throw an exception for valid dates" in {
    val startDate = Some(LocalDate.of(2023, 1, 1))
    val endDate = Some(LocalDate.of(2023, 1, 2))
    val now = Timestamp.valueOf(LocalDateTime.now())
    val inputConfig = ImmutableStaticStart(Regex, startDate, endDate, Seq.empty)

    noException should be thrownBy {
      StreamPlanner.ensureInputConfigIsValid(inputConfig, now)
    }
  }

  it should "throw an exception if the input paths do not follow the regex for static start" in {
    val startDate = Some(LocalDate.of(2023, 1, 1))
    val endDate = Some(LocalDate.of(2023, 1, 2))
    val now = Timestamp.valueOf(LocalDateTime.now())
    val inputConfig = ImmutableStaticStart(DefaultAzureRegex, startDate, endDate, Seq.empty)

    val paths = Seq(
      Seq("abfss://container@storage-account/evhns-nsname/evh-ehname/"),
      Seq(
        "abfss://container-tkt@storage-account/evhns-nsname/evh-ehname",
        "abfss://container-emd@storage-account/evhns-nsname/evh-ehname/extra"
      ),
      Seq("abfss://container@storage-account/evhns-nsname/evh-ehname/[0-9]")
    )

    paths.foreach { ps =>
      an[IllegalArgumentException] should be thrownBy {
        StreamPlanner.ensureInputConfigIsValid(inputConfig.copy(inputPaths = ps), now)
      }
    }

    noException should be thrownBy {
      val validConfig = inputConfig.copy(inputPaths = Seq("abfss://container@storage-account/evhns-nsname/evh-ehname"))
      StreamPlanner.ensureInputConfigIsValid(validConfig, now)
    }

  }
}
