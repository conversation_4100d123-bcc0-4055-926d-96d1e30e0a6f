package com.amadeus.airbi.json2star.common.views.executors

import com.amadeus.airbi.common.utils.CommonSpec
import com.amadeus.airbi.rawvault.common.testfwk.TmpDir

import java.nio.file.{Files, Paths}
import scala.io.Source

class SnowflakeSchemaExecutorSpec extends CommonSpec with TmpDir {

  def getArgs(cmd: String, inputConfig: String, outputSQL: String): Array[String] = Array(
    "--app-config-file",
    inputConfig,
    "--command",
    cmd,
    "--dry-mode",
    outputSQL
  )

  SnowflakeSchemaExecutor.getClass.getName should "run the SnowflakeSchemaExecutor in mode CREATE" in withTmpDir { tmpDir =>

    val appConfigFile = getClass.getClassLoader
      .getResource("views/sample_1/app.conf")
      .getPath
    val outFile = Files.createFile(Paths.get(tmpDir + "/create.sql")).toString

    SnowflakeSchemaExecutor.main(getArgs("CREATE", appConfigFile, outFile))

    val sql = Source.fromFile(outFile).getLines().mkString("\n")
    sql should include("CREATE DATABASE IF NOT EXISTS my_db")
    sql should include("CREATE SCHEMA IF NOT EXISTS my_db.my_schema")
    sql should include("CREATE TABLE my_db.my_schema.FACT_PASSENGER")
    sql should not include("INTERNAL_MY_TABLE")
  }

  it should "run the SnowflakeSchemaExecutor in mode DROP" in withTmpDir { tmpDir =>
    val appConfigFile = getClass.getClassLoader
      .getResource("views/sample_1/app.conf")
      .getPath
    val outFile = Files.createFile(Paths.get(tmpDir + "/drop.sql")).toString

    SnowflakeSchemaExecutor.main(getArgs("DROP", appConfigFile, outFile))

    val expectedSql = Source.fromFile(outFile).getLines.mkString("\n")
    expectedSql should include("DROP SCHEMA IF EXISTS my_db.my_schema")
  }

  it should "run the SnowflakeSchemaExecutor in mode COPY" in withTmpDir { tmpDir =>

    val appConfigFile = getClass.getClassLoader
      .getResource("views/sample_11/app.conf")
      .getPath
    val outFile = Files.createFile(Paths.get(tmpDir + "/copy.sql")).toString

    SnowflakeSchemaExecutor.main(getArgs("COPY", appConfigFile, outFile))

    val sql = Source.fromFile(outFile).getLines().mkString("\n")
    val newSchema = "DATA_CUSTOMER_PHASE.DWH_DOMAIN_VERSION"
    val oldSchema = "DATA_CUSTOMER_PHASE.DWH_DOMAIN_PREVIOUS_VERSION"
    sql should include(s"CREATE OR REPLACE TABLE ${newSchema}.FACT_PASSENGER_HISTO CLONE ${oldSchema}.FACT_PASSENGER_HISTO;")
    sql should include(s"CREATE OR REPLACE TABLE ${newSchema}.FACT_SECONDARY_HISTO CLONE ${oldSchema}.FACT_SECONDARY_HISTO;")
    sql should not include("INTERNAL_MY_TABLE")
  }
}
