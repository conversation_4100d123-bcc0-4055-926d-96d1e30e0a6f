package com.amadeus.airbi.json2star.common.snowflake

object CDFColumns extends Enumeration {
  type CDFColumns = CDFColumnVal
  protected case class CDFColumnVal(_name: String, _type: String) extends super.Val {}

  import scala.language.implicitConversions
  implicit def valueToCDFColumnVal(x: Value): CDFColumnVal = x.asInstanceOf[CDFColumnVal]

  def names: Set[String] = CDFColumns.values.map(value => value._name)

  val _change_type = CDFColumnVal("_change_type", "STRING")
  val _commit_version = CDFColumnVal("_commit_version", "BIGINT")
  val _commit_timestamp = CDFColumnVal("_commit_timestamp", "TIMESTAMP")

}

object ChangeTypeValue extends Enumeration {
  type ChangeTypeValue = Value

  val update_preimage = Value("update_preimage")
  val update_postimage = Value("update_postimage")
  val insert = Value("insert")
  val delete = Value("delete")

}
