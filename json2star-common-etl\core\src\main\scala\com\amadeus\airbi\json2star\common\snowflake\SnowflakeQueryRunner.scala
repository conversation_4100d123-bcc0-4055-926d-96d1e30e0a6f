package com.amadeus.airbi.json2star.common.snowflake
import com.typesafe.scalalogging.Logger
import net.snowflake.spark.snowflake.Utils
import net.snowflake.spark.snowflake.Utils.SNOWFLAKE_SOURCE_SHORT_NAME
import org.apache.spark.sql.{DataFrame, SaveMode}

import java.sql.ResultSet

trait SnowflakeQueryRunner {

  def getTargetTableName(tableName: String): String = tableName

  def runQuery(options: Map[String, String], query: String): ResultSet = {
    Utils.runQuery(options, query)
  }

  def createTransientStagingTable(options: Map[String, String], tableName: String, appId: String): String = {
    val randomString = SnowflakeConnector.randomStringGenerator.generate(6)
    val stagingTableName = s"STAGING_${randomString}_${appId.replace("-", "_")}_$tableName"
    Utils.runQuery(options, s"create transient table $stagingTableName like $tableName")
    stagingTableName
  }

  def appendToStagingTable(options: Map[String, String], df: DataFrame, stagingTableName: String): String = {
    df.write
      .format(SNOWFLAKE_SOURCE_SHORT_NAME)
      .option("column_mapping", "name")
      .options(options)
      .option("dbtable", stagingTableName)
      .mode(SaveMode.Append)
      .save()
    Utils.getLastCopyLoad
  }

  def logResultSet(tableName: String, result: ResultSet, logger: Logger): Unit = {
    val resultNext = result.next
    val rowsOpsCount: (Long, Long) =
      if (resultNext) {
        (
          if (result.getMetaData.getColumnCount >= 1) result.getLong("number of rows inserted") else 0,
          if (result.getMetaData.getColumnCount >= 2) result.getLong("number of rows updated") else 0
        )
      } else {
        (0, 0)
      }

    // success = rows inserted > 0 or rows updated > 0 (to check)
    if (!resultNext) {
      logger.warn(s"WARNING: merge request on table $tableName sent not result")
    } else if (resultNext && (rowsOpsCount._1 > 0 || rowsOpsCount._2 > 0)) {
      logger.info(s"""Merge successful: ${rowsOpsCount._1} rows inserted, ${rowsOpsCount._2} rows updated""")
    } else {
      logger.warn(s"WARNING: merge request made no change in table $tableName")
    }
  }

}

object SnowflakeQueryRunner extends SnowflakeQueryRunner
