{
  "defaultComment" : "A coment here"
  "tables": [
    {
        "name": "FACT_TABLE_1",
        "mapping" : {
          "merge": {
            "key-columns": ["RESERVATION_ID", "VERSION"],
            "if-dupe-take-higher": ["LOAD_DATE"]
          },
          "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}]}],
          "columns": [
            {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
            {"name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
            {"name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.reference"}]}},
            {"name": "VERSION", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.version"}]}},
            {"name": "NIP", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.nip"}]}},
            {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
            {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
            {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
          ],
          "pit": {
            "type": "master-pit-table",
            "master" : {
              "pit-key": "RESERVATION_ID",
              "pit-version": "VERSION",
              "valid-from": "DATE_BEGIN",
              "valid-to": "DATE_END",
              "is-last": "IS_LAST_VERSION"
            }
          }
        }
    },
    {
      "name": "FACT_TABLE_2",
      "mapping" : {
        "merge": {
          "key-columns": ["AIR_SEGMENT_PAX_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [{ "blocks": [
          {"all": "$"},
          {"prod": "$.mainResource.current.image.products[?(@.subType == 'AIR')]"},
          {"tr": "$.travelers[*]"}
        ]}],
        "columns": [
          {"name": "AIR_SEGMENT_PAX_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"prod": "$.id"}, {"tr": "$.id"}]}, "expr":  "hashM({0})"},
          {"name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [{"prod": "$.id"}, {"tr": "$.id"}]}},
          {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "sources":{"blocks": [{"root": "$.mainResource.current.image.id"}]}, "expr": "hashM({0})"},
          {"name": "PRODUCT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"prod": "$.id"}]}}, // ADDED one for filtering the delivery
          {"name": "VERSION", "column-type": "intColumn", "sources": {"blocks": [{"root" : "$.mainResource.current.image.version"}]}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"root" : "$.mainResource.current.image.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
            , "meta": {"description": {"value": "Some description", "rule": "replace"}, "gdpr-zone": "green"}
          }
        ],
        "pit": {
          "type": "secondary-pit-table"
        }
      }
    },
    {
      "name": "FACT_TABLE_3",
      "mapping" : {
        "merge": {
          "key-columns": ["AIR_SEGMENT_PAX_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [{ "blocks": [
          {"all": "$"},
          {"prod": "$.mainResource.current.image.products[?(@.subType == 'AIR')]"},
          {"tr": "$.travelers[*]"}
        ]}],
        "columns": [
          {"name": "AIR_SEGMENT_PAX_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"prod": "$.id"}, {"tr": "$.id"}]}, "expr":  "hashM({0})"},
          {"name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [{"prod": "$.id"}, {"tr": "$.id"}]}},
          {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "sources":{"blocks": [{"root": "$.mainResource.current.image.id"}]}, "expr": "hashM({0})"},
          {"name": "PRODUCT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"prod": "$.id"}]}}, // ADDED one for filtering the delivery
          {"name": "VERSION", "column-type": "intColumn", "sources": {"blocks": [{"root" : "$.mainResource.current.image.version"}]}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"root" : "$.mainResource.current.image.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
        ],
        "pit": {
          "type": "secondary-pit-table"
        }
      }
    }
  ]
}