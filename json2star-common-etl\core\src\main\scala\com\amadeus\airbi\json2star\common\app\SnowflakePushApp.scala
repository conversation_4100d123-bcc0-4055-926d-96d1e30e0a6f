package com.amadeus.airbi.json2star.common.app

import com.amadeus.airbi.json2star.common.Schema.Materialized
import com.amadeus.airbi.json2star.common.TablesDef
import com.amadeus.airbi.json2star.common.app.Display.display
import com.amadeus.airbi.json2star.common.app.Json2StarApp.readMappingConfig
import com.amadeus.airbi.json2star.common.config.{AppConfig, PartialReprocessingParams, ProcessingParams, SnowflakeParams}
import com.amadeus.airbi.json2star.common.resize.{DefaultDatabricksUtils, ResizeActuator}
import com.amadeus.airbi.json2star.common.snowflake.{SnowflakeContext, SnowflakeResizeLogic}
import com.amadeus.airbi.rawvault.common.application.config.TablesConfig
import com.amadeus.airbi.rawvault.common.vault.spark.EventsListener
import com.typesafe.scalalogging.Logger
import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.functions.{col, current_date, datediff, upper}
import org.apache.spark.sql.streaming.StreamingQuery
import org.json4s.DefaultFormats
import org.json4s.jackson.Serialization.{write => asJson}
import org.rogach.scallop.{ScallopConf, ScallopOption}
import org.slf4j.LoggerFactory

import java.time.{Duration, LocalDateTime}

case class SnowflakePushAppScallopConf(arguments: Seq[String]) extends ScallopConf(arguments) {
  val appConfigFile: ScallopOption[String] = opt[String](required = true)
  val jobId: ScallopOption[String] = opt[String](required = true)
  val runId: ScallopOption[String] = opt[String](required = true)
  verify()
}

object SnowflakePushApp {
  @transient
  lazy val logger: Logger = Logger(LoggerFactory.getLogger(getClass.getName))

  def main(args: Array[String]): Unit = {
    val (appConfig, sfParams, model) = buildConfigs(args)

    logger.info(s"--- Starting Snowflake Push pipeline ---")
    val spark: SparkSession = SparkSession
      .builder()
      .getOrCreate()

    val snowflakeContext =
      new SnowflakeContext(spark, sfParams, appConfig.common.outputDatabase, DefaultDatabricksUtils, appConfig)
    val eventsListener = new EventsListener(appConfig.processingParams.getOrElse(ProcessingParams()))
    spark.sparkContext.addSparkListener(eventsListener)

    val partialReprocessParams = appConfig.partialReprocessingParams

    run(spark, snowflakeContext, model, partialReprocessParams)
    eventsListener.report()
  }

  def run(
    spark: SparkSession,
    snowflake: SnowflakeContext,
    tablesConf: TablesConfig,
    partialReprocessingParams: Option[PartialReprocessingParams] = None
  ): Unit = {

    // Set the spark option to enable to be able to use startingVersion in case of init and partial reprocessing
    // This option doesn't impact the App in case of full reprocessing or regular runs
    spark.conf.set("spark.databricks.delta.changeDataFeed.timestampOutOfRange.enabled", "true")

    // Resize cluster
    val resizeActuator = ResizeActuator.fromAppConfig(snowflake.appConfig, snowflake.databricksUtils)
    SnowflakeResizeLogic.handleClusterResize(spark, resizeActuator)

    // Consolidate table definitions from the configuration
    val tablesDef = TablesDef.consolidate(tablesConf)

    // Generate streaming queries to mirror all tables
    val squeries = runMirrorForAll(snowflake, tablesDef, partialReprocessingParams)
    display(s"Tables to mirror: ${squeries.size}", displayMainEvents = true, logger)

    awaitForAll(squeries, spark)

    // run clean up to remove obsolete staging tables
    runCleanUp(snowflake)

    // run suspend at end of the process
    runSuspend(snowflake)
  }

  def runSuspend(
    snowflake: SnowflakeContext
  ): Unit = {
    val warehouse = snowflake.sfOptions("sfWarehouse")
    val query = s"ALTER WAREHOUSE ${warehouse} SUSPEND;"
    snowflake.runQuery(query)
  }

  def runCleanUp(
    snowflake: SnowflakeContext
  ): Unit = {
    val stagingTables = snowflake
      .runShowTables()
      .where(upper(col("TABLE_SCHEMA")) === snowflake.sfSchema.toUpperCase)
      .where(upper(col("TABLE_NAME")).rlike("^STAGING_.*_APP.*"))
      .where(datediff(current_date(), col("CREATED")) > snowflake.stagingRetentionDays)
      .select(col("TABLE_NAME"))
      .collect()
      .map(_.getAs[String](0))

    if (stagingTables.nonEmpty) {
      display(s"Staging table to clean: ${stagingTables.length}", snowflake.displayEvents, logger)
      stagingTables.foreach { sfTable =>
        val query = s"DROP TABLE ${snowflake.sfDatabase}.${snowflake.sfSchema}.${sfTable};"
        display(s"Cleaning staging table ${sfTable}", snowflake.displayEvents, logger)
        snowflake.runQuery(query)
      }
    }
  }

  def awaitForAll(squeries: Seq[StreamingQuery], spark: SparkSession): Unit = {

    if (squeries.isEmpty) {
      throw new Exception("No tables to mirror in Snowflake !")
    }

    val startTime = LocalDateTime.now()
    // Wait for all streaming queries to complete - checking each 10 sec if still any active query
    while (squeries.count(_.isActive) > 0) { // active waiting
      // Log information - if the Streaming Query is taking more than 1 hour
      if (Duration.between(startTime, LocalDateTime.now()).abs().compareTo(Duration.ofHours(1)) > 0) {
        logger.debug(s"Launched active queries:")
        squeries
          .filter(_.isActive)
          .foreach(q => {
            logger.debug(s"\tname: ${q.name} id: ${q.id} status: ${q.status} duration: ${q.lastProgress.durationMs}")
          })
        logger.debug(s"All active queries:")
        spark.streams.active.foreach(q => {
          logger.debug(s"\tname: ${q.name} id: ${q.id} status: ${q.status} duration: ${q.lastProgress.durationMs}")
        })
      }

      spark.streams.awaitAnyTermination(timeoutMs = 10000)
    }
  }

  /** Run a seq of Spark Streaming Query for each table to mirror
    *
    * @param snowflake snowflake context
    * @param tablesDef tables definition
    * @return a seq of running Spark Streaming Query
    */
  def runMirrorForAll(
    snowflake: SnowflakeContext,
    tablesDef: TablesDef,
    partialReprocessingParams: Option[PartialReprocessingParams]
  ): Seq[StreamingQuery] = {
    val mirrorQueries = tablesDef.tables
      .filter { td =>
        td.schema.kind == Materialized && !td.schema.isInternal
      }
      .map { td =>
        val primaryKeys = td.schema.keyColumns.map(_.name).toSet
        partialReprocessingParams match {
          case Some(prParams) =>
            // If a table is in the tables to reprocess then it is not cloned
            val isCloned = ! prParams.tablesToReprocess.contains(td.table.name)
            snowflake.runMirror(td.schema.name, primaryKeys, isCloned = isCloned)
          case None => // If not defined then the partial reprocessing is not used
            snowflake.runMirror(td.schema.name, primaryKeys, isCloned = false)
        }
      }
    mirrorQueries
  }

  def buildConfigs(
    args: Array[String]
  ): (AppConfig, SnowflakeParams, TablesConfig) = {
    val argsSc = SnowflakePushAppScallopConf(args)
    val appConfigFile = argsSc.appConfigFile()
    val jobId = argsSc.jobId()
    val runId = argsSc.runId()

    val appConfig = AppConfig(appConfigFile)
    val sfParams = appConfig.assumeSnowflakeParams(Some(jobId), Some(runId))
    val model = readMappingConfig(appConfig.modelConfFile, appConfig.tablesSelectors)

    logger.info(s"### J2S APP CONFIG: ${asJson(appConfig)(DefaultFormats)}")
    logger.info(s"### SNOWFLAKE CONFIG: ${asJson(sfParams)(DefaultFormats)}")
    logger.info(s"### MODEL: ${asJson(model)(DefaultFormats)}")

    (appConfig, sfParams, model)
  }

}
