package com.amadeus.airbi.json2star.common.addons.stackable.proration

import com.amadeus.airbi.json2star.common.addons.stackable.proration.ProrationColumns.toCols
import com.amadeus.airbi.json2star.common.addons.stackable.proration.ProrationInput.CouponKey
import com.amadeus.airbi.json2star.common.addons.stackable.proration.ProrationOutput.{DstAlgo, FclAlgo, NoAlgo}
import com.amadeus.ti.models.cdb.NumericAmount
import com.amadeus.ti.models.por.PorsRef

import java.time.LocalDate

/** ProratedCoupon is a sealed trait that represents the result of the proration algorithm
  * The algorithm consists of FARE CALC proration step and if it fails, the DISTANCE proration step
  *
  * The possible output values are
  * - a FclProratedCouponSuccess - when the FARE CALC step is successful and DISTANCE step is not needed
  * - a DistProratedCouponSuccess - when the FARE CALC step has failed and DISTANCE step is successful
  * - a DistProratedCouponFailure - when the FARE CALC step has failed and DISTANCE step has failed
  *
  *  Note: - FclProratedCouponFailure is an intermediate value and is not part of the output, but used to track errors
  */
sealed trait ProratedCoupon
sealed trait FclProratedCoupon
case class FclProratedCouponSuccess(na: NumericAmount) extends FclProratedCoupon with ProratedCoupon
case class FclProratedCouponFailure(fclStep: StepStatus) extends FclProratedCoupon
sealed trait DistProratedCoupon
case class DistProratedCouponSuccess(na: NumericAmount, distStep: StepStatus, fclFailure: FclProratedCouponFailure)
    extends DistProratedCoupon
    with ProratedCoupon
case class DistProratedCouponFailure(distStep: StepStatus, fclFailure: FclProratedCouponFailure)
    extends DistProratedCoupon
    with ProratedCoupon

case class ProrationOutput(
  input: ProrationInput,
  coupons: Map[CouponKey, ProratedCoupon],
  inputError: Option[StepStatus] = None,
  tktStatus: String
) {

  /** Get the columns for the proration output
    * Columns are:
    * - ESTIMATED_PRORATED_FARE_ORIGINAL: prorated amount extracted from the fare calc line or by distance in fare calc currency
    * - ESTIMATED_PRORATED_FARE_CURRENCY_ORIGINAL:  original fare calc currency
    * - ESTIMATED_PRORATED_FARE_ALGORITHM:  algorithm value: FARE-CALC, DISTANCE or NONE in case of error
    * - INTERNAL_PRORATION_METADATA: internal column to gather error details
    *
    * @param couponKey an optional coupon key
    * @param enableMeta if true, the metadata column is filled
    * @return a map of column and value
    */
  def buildColumns(couponKey: Option[CouponKey], enableMeta: Boolean): Map[String, String] = {
    val maybeCpn = couponKey.flatMap(key => coupons.get(key))
    val meta = if (enableMeta) {
      Some(ProrationMetadata(Some(input), inputStep = inputError, tktStatus = Some(tktStatus)))
    } else {
      None
    }
    val outCols = maybeCpn match {
      case None => toCols(None, NoAlgo, meta)
      case Some(cpn) =>
        cpn match {
          case FclProratedCouponSuccess(na) => toCols(Some(na), FclAlgo, meta)
          case DistProratedCouponSuccess(na, distStep, fclFailure) =>
            toCols(Some(na), DstAlgo, meta.map(_.copy(fclStep = Some(fclFailure.fclStep), dstStep = Some(distStep))))
          case DistProratedCouponFailure(distStep, fclFailure) =>
            toCols(None, NoAlgo, meta.map(_.copy(fclStep = Some(fclFailure.fclStep), dstStep = Some(distStep))))
        }
    }
    outCols
  }
}

object ProrationOutput {
  val FclAlgo = "FARE CALC"
  val DstAlgo = "DISTANCE"
  val NoAlgo = "NONE"

  val PartialTktStatus = "PARTIAL"
  val FclTktStatus = s"${FclAlgo}-ALL"
  val DstTktStatus = s"${DstAlgo}-ALL"
  val NoTktStatus = s"${NoAlgo}-ALL"

  /** Build prorated amounts from the input data
    *
    * @param porsRef point of reference data
    * @param input the input data for teh proration algorithm
    * @return a ProrationOutput with prorated coupons and metadata
    */
  def build(porsRef: PorsRef, input: ProrationInput): ProrationOutput = {
    // Step 0 - Check Input data
    val inputError = checkInput(input.issueDate, input.paymentCurrency, input.itinerary)
    inputError match {
      case Some(error) =>
        ProrationOutput(input, coupons = Map.empty, inputError = Some(error), tktStatus = NoTktStatus)
      case None =>
        val inputSortedTktItinerary = input.itinerary.sortBy(_.sequenceNumber)
        val inputIssueDate = input.issueDate.get
        // Step 1 - Fare Calc Proration
        val fareCalcOut = FareCalcProration.computeProratedCoupons(
          porsRef,
          input.fareCalcLine,
          inputIssueDate,
          input.paymentCurrency,
          inputSortedTktItinerary
        )
        // Divide coupons in prorated and not prorated
        val fclProratedCoupons = fareCalcOut.collect { case (k, v: FclProratedCouponSuccess) => (k, v) }
        val noFclProratedCoupons = fareCalcOut.collect { case (k, v: FclProratedCouponFailure) => (k, v) }

        if (fclProratedCoupons.size == inputSortedTktItinerary.size) {
          ProrationOutput(
            input,
            coupons = fclProratedCoupons,
            tktStatus = FclTktStatus
          )
        } else {
          // Step 2 - Distance Proration
          val distOut = DistanceProration.computeProratedCoupons(
            porsRef,
            inputIssueDate,
            fclProratedCoupons,
            noFclProratedCoupons,
            input.paymentCurrency,
            input.paymentTotal,
            input.paymentTotalTaxes
          )

          ProrationOutput(
            input,
            coupons = distOut,
            tktStatus = computeTktStatus(input.itinerary, distOut)
          )
        }
    }
  }

  /** Build empty columns when an unhandled failure has happened in the proration algorithm
    *
    * @param exceptionMsg the exception message
    * @param enableMeta if true, the metadata column is filled
    * @return a map of column and its value
    */
  def buildColumnsWhenFailure(exceptionMsg: String, enableMeta: Boolean): Map[String, String] = {
    val meta = if (enableMeta) {
      Some(
        ProrationMetadata(
          tktStatus = Some(NoTktStatus),
          exceptionMsg = Some(exceptionMsg)
        )
      )
    } else {
      None
    }
    toCols(None, NoAlgo, meta)
  }

  private def computeTktStatus(keys: Seq[CouponKey], distOut: Map[CouponKey, ProratedCoupon]): String = {
    lazy val dstProratedNum = distOut.collect { case (k, v: DistProratedCouponSuccess) => (k, v) }.size
    lazy val noDstProratedNum = distOut.collect { case (k, v: DistProratedCouponFailure) => (k, v) }.size
    lazy val totNum = keys.size
    val tktStatus = if (dstProratedNum == totNum) {
      DstTktStatus
    } else if (noDstProratedNum == totNum) {
      NoTktStatus
    } else {
      PartialTktStatus
    }
    tktStatus
  }

  private def checkInput(
    issueDate: Option[LocalDate],
    currencyValue: String,
    itinerary: Seq[CouponKey]
  ): Option[StepStatus] = {
    val isIssueDateNull = issueDate.isEmpty
    val isCurrencyNull = currencyValue == null || currencyValue.trim.isEmpty
    val isItineraryNull = itinerary == null || itinerary.isEmpty

    val inputStatus = if (isIssueDateNull || isCurrencyNull || isItineraryNull) {
      Some(
        StepStatus(
          "input-check",
          s"ERROR: isIssueDateNull = ${isIssueDateNull} - isCurrencyNull = ${isCurrencyNull} - isItineraryNull = ${isItineraryNull}"
        )
      )
    } else {
      None
    }
    inputStatus
  }

}
