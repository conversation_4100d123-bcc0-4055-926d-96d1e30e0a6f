package com.amadeus.airbi.json2star.common.addons.stackable

/** As we want to leverage the automatic pure config sealed trait handling, we need to keep all the configs here.
  * To avoid this, we would need to write implicit custom config readers for each addon, which would oblige us to write
  * a lot of boilerplate code and have a more complex and difficult to maintain framework.
  */
sealed trait StackableAddonConfig

// dummy addon for testing purposes
case class Dummy(action: String, srcCol: Option[String] = None, dstCol: Option[String] = None)
    extends StackableAddonConfig

// currency conversion
case class CurrencyConversion(conversions: List[CurrencyConversionColumnConfig]) extends StackableAddonConfig
case class CurrencyConversionColumnConfig(
  srcCol: String,
  srcUnitCol: String,
  srcDateCol: String,
  dstCol: String,
  dstUnitCol: String,
  dstDateCol: String,
  dstIncrFlagCol: Option[String] = None
)

// weight conversion
case class WeightConversion(conversions: List[WeightConversionColumnConfig]) extends StackableAddonConfig
case class WeightConversionColumnConfig(
  srcCol: String,
  srcUnitCol: String,
  dstCol: String,
  dstUnitCol: String
)

// proration
case class Proration(
  jsonPathFareCalc: String,
  jsonPathPriceCurrencyPayment: String,
  jsonPathPriceTotalPayment: String,
  jsonPathPriceTotalTaxesPayment: String
) extends StackableAddonConfig
