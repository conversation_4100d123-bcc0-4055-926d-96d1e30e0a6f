package com.amadeus.airbi.json2star.common.eventgrid

import com.amadeus.airbi.json2star.common.config.EventGridParams
import com.amadeus.airbi.json2star.common.resize.DefaultDatabricksUtils
import com.amadeus.airbi.json2star.common.testfwk.MockDatabricksUtils
import com.amadeus.airbi.rawvault.common.testfwk.Json2StarSpec

class EventGridClientSpec extends Json2StarSpec {
  val mockDbx = new MockDatabricksUtils(numWorkers = 1, clusterId = "id-1", spark)

  it should "report error for missing dbx secret to start the authentication flow" in {
    val eventGrid = intercept[Exception] {
      EventGridClient(
        EventGridParams(
          topicUrl = "https://topic.url",
          topicName = "topic-name",
          serviceProviderTenantId = "tenant-id",
          serviceProviderAppId = "app-id",
          dbxSecretScope = "special-scope",
          dbxSecretKey = "strange-key",
          managedAppResourceId = "resource-id"
        ),
        DefaultDatabricksUtils,
        DefaultAccessTokenProvider
      )
    }
    eventGrid.getMessage should include(
      "[event-grid] ERROR: missing secret in databricks. Check scope 'special-scope' and key 'strange-key' in databricks secrets"
    )
  }

  it should "report error in step 1 of the authentication flow" in {
    val eventGrid = intercept[Exception] {
      EventGridClient(
        EventGridParams(
          topicUrl = "https://topic.url",
          topicName = "topic-name",
          serviceProviderTenantId = "tenant-id",
          serviceProviderAppId = "app-id",
          dbxSecretScope = "scope",
          dbxSecretKey = "key",
          managedAppResourceId = "resource-id"
        ),
        mockDbx,
        DefaultAccessTokenProvider
      )
    }

    eventGrid.getMessage should startWith("[event-grid] ERROR: failed step 1 auth on Service Provider")
  }

  it should "report error in step 2 of the authentication flow" in {
    val eventGrid = intercept[Exception] {
      EventGridClient(
        EventGridParams(
          topicUrl = "https://topic.url",
          topicName = "topic-name",
          serviceProviderTenantId = "tenant-id",
          serviceProviderAppId = "app-id",
          dbxSecretScope = "scope",
          dbxSecretKey = "key",
          managedAppResourceId = "resource-id"
        ),
        mockDbx,
        MockAccessTokenProviderWithError
      )
    }
    eventGrid.getMessage should startWith("[event-grid] ERROR: failed step 2 auth on Managed Application")
  }

  it should "run the authentication steps" in {
    val eventGrid = EventGridClient(
      EventGridParams(
        topicUrl = "https://topic.url",
        topicName = "topic-name",
        serviceProviderTenantId = "tenant-id",
        serviceProviderAppId = "app-id",
        dbxSecretScope = "scope",
        dbxSecretKey = "key",
        managedAppResourceId = "resource-id"
      ),
      mockDbx,
      MockAccessTokenProvider
    )
    eventGrid shouldBe a[EventGridClient]
  }
}
