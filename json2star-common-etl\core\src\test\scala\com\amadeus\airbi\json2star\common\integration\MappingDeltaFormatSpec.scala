package com.amadeus.airbi.json2star.common.integration

import com.amadeus.airbi.json2star.common.addons.base.mapping.MappingAddonParams.InputFormatDelta
import com.amadeus.airbi.json2star.common.app.Json2StarApp
import com.amadeus.airbi.json2star.common.app.Json2StarApp.readMappingConfig
import com.amadeus.airbi.rawvault.common.testfwk.Json2StarSpec

import java.sql.{Date, Timestamp}

class MappingDeltaFormatSpec extends Json2StarSpec {
  val modelFile = "datasets/format_delta/mapping.conf"
  val dataDir = "datasets/format_delta/data/"
  val tableDir = "datasets/format_delta/data/delta"
  val pathExpectedResults = "src/test/resources/" + dataDir + "/expected_results/"
  val TableNames: List[String] = getTableNames(modelFile)

  override def beforeAll: Unit = {
    super.beforeAll
    createTables(modelFile)
  }
  override def beforeEach: Unit = {
    cleanTables(TableNames)
  }

  "the engine" should "extract fields from the delta table" in withStrTmpDir { tmp =>
    val rc = rootConfig(
      datadir = tableDir,
      database = inputDatabase,
      isLight = isLight,
      checkpointPath = Some(tmp),
      inputFormat = Some(InputFormatDelta)
    )
    val model = readMappingConfig(modelFile)
    Json2StarApp.run(spark, rc, model)
    checkTablesContent(TableNames, pathExpectedResults, model = Some(model), createExpected = false)
  }
}
