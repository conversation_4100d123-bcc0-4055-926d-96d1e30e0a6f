package com.amadeus.airbi.json2star.common.addons.base.mapping.input

import com.amadeus.airbi.json2star.common.addons.base.mapping.MappingAddonParams.StartConfig
import com.amadeus.airbi.json2star.common.addons.base.mapping.Table
import com.amadeus.airbi.json2star.common.addons.base.mapping.input.InputFormat.InputDataFrame
import com.amadeus.airbi.json2star.common.addons.base.mapping.input.JsonFileInput.getClass
import com.amadeus.airbi.json2star.common.addons.base.mapping.input.checkpoints.StartCheckpointsTable.ImmutableStartConfig
import com.amadeus.airbi.json2star.common.addons.base.mapping.input.checkpoints.{
  AutoloaderMonthStep,
  AutoloaderStartStep,
  StartCheckpointsTable,
  StaticStartStep,
  StreamPlanner
}
import com.amadeus.airbi.json2star.common.resize.DatabricksUtils
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}
import org.apache.spark.sql.streaming.{DataStreamReader, Trigger}
import org.slf4j.{Lo<PERSON>, LoggerFactory}

import java.sql.Timestamp
import java.time.{Instant, YearMonth}

/** Trait defining an `InputFormat` that is able to execute a plan defined by a `StreamPlanner`.
  *
  * It overrides the `processBatches` method to process the steps resolved by the planner.
  */
trait StreamExecutorInputFormat extends InputFormat {

  private val logger: Logger = LoggerFactory.getLogger(getClass.getName)

  def sparkSession: SparkSession
  def startMode: StartConfig
  def dbx: DatabricksUtils
  def pathsToDf(f: Seq[String]): DataFrame
  def nowTimestamp: Timestamp = Timestamp.from(java.time.Instant.now())

  /** It calls the `StreamPlanner` to resolve the processing steps, and then it processes them.
    */
  override def processBatches(
    streamReader: DataStreamReader,
    paths: Seq[String],
    checkpointLocation: String,
    trigger: Trigger,
    getTables: (InputDataFrame) => Dataset[Table],
    processBatchFunction: (Dataset[Table], String) => Unit
  ): Unit = {
    val now = nowTimestamp
    val table = new StartCheckpointsTable(sparkSession, checkpointLocation)
    val resolvedSteps = StreamPlanner.resolve(table, paths, startMode, dbx, now)
    logger.info(s"Resolved steps: $resolvedSteps")
    resolvedSteps.foreach {
      case c @ AutoloaderStartStep(inputPaths) =>
        logger.info(s"Processing streaming step: $c")
        processBatchesStreaming(streamReader, inputPaths, checkpointLocation, trigger, getTables, processBatchFunction)

      case c @ AutoloaderMonthStep(inputPaths, _) =>
        logger.info(s"Processing streaming month step: $c")
        processBatchesStreaming(streamReader, inputPaths, checkpointLocation, trigger, getTables, processBatchFunction)
        val et = Timestamp.from(Instant.now())
        // if the month in the used input path is before the current month, we can log an autoloader row
        if (c.yearMonth.isBefore(YearMonth.from(now.toLocalDateTime))) {
          val immutableStartConfig = ImmutableStartConfig.from(startMode, inputPaths)
          table.logAutoloader(c.yearMonth.atEndOfMonth(), et, inputPaths, immutableStartConfig)
        }

      case c: StaticStartStep =>
        logger.info(s"Processing static start step: $c")
        val batches = c.batches
        batches.zipWithIndex.foreach { case (b, id) =>
          logger.info(s"Processing static batch: B$id $b")
          val df = pathsToDf(b.paths)
          val st = Timestamp.from(Instant.now())
          processBatchFunction(getTables(df), s"B${id}") // B as in Batch
          val et = Timestamp.from(Instant.now())
          val isLast = id == batches.size - 1
          val immutableStartConfig = ImmutableStartConfig.from(startMode, paths)
          table.logBatch(b.start, b.end, b.paths.mkString(","), st, et, isLast, immutableStartConfig)
        }
    }
  }
}
