# Readme

Test data for the Latest tables computation for Correlation Asso Histo tables.

Data has been extracted from 4Z PDT source.


## batch 1

We are using the following data for correlation :
```
PNR v9  N389UA
TKT v0  7492400944169
```


## batch 2 (update)

We add the following document
```
TKT v1  7492400944169
```
and then we have an update on tkt version into the record of the correlation latest.


### Note

With the information of the first 2 batches we know that the timeline between the different versions is the following:

- t0: TKT v0 start
- t1: TKT v0 end / TKT v1 start
- t3: PNR v9 start

From this, after batch 2, we see that there is no overlap between TKT v0 and PNR v9.
However, after having received only batch 1, we do not know yet the TKT v0 end date (so it is considered infinite) and
we log a line in the ASSO table for the correlation between TKT v0 and PNR v9.
This line disappears after receiving batch 2, thanks to the fix done here: https://rndwww.nce.amadeus.net/agile/browse/BDS-8944.

## batch 3 (delete)

We add the following document
```
TKT v2  7492400944169
```
and then the record in the correlation latest table disappears because in the tkr raw data for this version we no longer
have fine grain correlation information.

```
  "corrTktPnr": {
    "items": [
      {
        "pnrTicketingReferenceId": "N389UA-2022-05-13-OT-37",
        "pnrTravelerId": "N389UA-2022-05-13-PT-2"
      }
    ]
```

### Note

After batch 3, in the expected correlation ASSO tables (histo and latest), there is still a row with `hashM(null)`.
This comes from the corresponding line in `TKT_PNR_PARTIAL_CORR_PIT` (added for reference):

```
hashM(N389UA-2022-05-13),hashM(7492400944169-2022-05-13),hashM(_-N389UA-2022-05-13-PT-2),hashM(null),2,2022-05-13T13:51:01.565Z,,true,2022-05-13T00:00:00.000Z
```

Note that this line MUST NOT be discarded in the partial because it is the only way we have to detect a closure:
- this line has is-last=true
- this allows us to set is-last=false for the previous version of the ticket
- this, in turn, allows us to close the correlations involving this version of the ticket (closure)


