package com.amadeus.airbi.json2star.common.resize

import com.databricks.dbutils_v1.DBUtilsHolder.dbutils
import com.databricks.dbutils_v1.DBUtilsV1
import com.databricks.sdk.WorkspaceClient
import com.databricks.sdk.core.DatabricksConfig

/** Trait to wrap operations provided by Databricks (dbutils, workspace sdk).
  *
  * See 'MockDatabricksUtils' for usage in tests.
  */
trait DatabricksUtils {

  def getDBUtils: DBUtilsV1 = dbutils

  def getSecret(scope: String, key: String): String = {
    dbutils.secrets.get(scope, key)
  }

  def getWorkspaceClient(workspaceUrl: String, workspaceToken: String): WorkspaceClient = {
    val cfg = new DatabricksConfig()
      .setAuthType("pat") // default, token auth (pat)
      .setHost(workspaceUrl)
      .setToken(workspaceToken)
    new WorkspaceClient(cfg)
  }

  def listNames(path: String): Seq[String] = {
    dbutils.fs.ls(path).map(_.name.stripSuffix("/"))
  }

}

object DefaultDatabricksUtils extends DatabricksUtils
