package com.amadeus.airbi.json2star.common.addons.base.mapping

import com.amadeus.airbi.json2star.common.addons.base.mapping.MappingMerge.{logger, MergeFunction}
import com.amadeus.airbi.json2star.common.addons.base.mapping.MappingPipeline.TableMapping
import com.amadeus.airbi.rawvault.common.RootConfig
import com.amadeus.airbi.rawvault.common.application.config.Merge
import com.amadeus.airbi.rawvault.common.vault.spark.SchemaManager
import io.delta.tables.DeltaTable
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{Column, DataFrame, Row, SparkSession}

/** Class abstracting to client code if we are dealing with a delta table or a global temp view.
  * It is needed to have the possibility of run "light" tests, where we don't create delta tables.
  * Please refer to the [[Json2StarSpec]] documentation for more details on the "light" tests.
  * It supports only the Mapping addon for now.
  */
class TableAccess(
  val tableMapping: TableMapping
) {

  import TableAccess._

  val naming = new Naming(
    tableMapping.rootConfig.etl.common.outputDatabase,
    tableMapping.tableName,
    tableMapping.rootConfig.isLight
  )
  val columnNames: Seq[String] = tableMapping.conf.allOrderedColumnsWithLoadDate.map(c => c.name)
  val isLight: Boolean = tableMapping.rootConfig.isLight

  def databaseName: String = if (isLight) GlobalTempDB else naming.configDatabaseName
  def technicalName: String = if (isLight) s"${naming.configDatabaseName}_${naming.modelName}" else naming.modelName
  def fullName: String = s"$databaseName.$technicalName"
  def columns: Seq[Column] = columnNames.map(c => col(c))

  // get the DF from delta or reads the view (if present, it returns an empty DF if not)
  def toDF(implicit spark: SparkSession): DataFrame = {
    if (isLight) {
      if (spark.catalog.tableExists(GlobalTempDB, technicalName)) {
        spark.sql(s"select * from ${fullName}")
      } else {
        val schema = SchemaManager.computeTableSchema(tableMapping.conf)
        spark.createDataFrame(spark.sparkContext.emptyRDD[Row], schema)
      }
    } else {
      // read the delta table if not in light mode (PRD code)
      DeltaTable.forName(fullName).toDF
    }
  }

  /** Return a fake merge function in case of light tests, or the passed merge function otherwise.
    */
  def mergeFunction(
    mergeFunction: MergeFunction,
    rootConfig: RootConfig
  )(implicit spark: SparkSession): MergeFunction = if (rootConfig.isLight) fakeMerge else mergeFunction

  /** Fake merge function: it creates a global temp view with the batchDf, if it does not exist, or throws an exception
    */
  private def fakeMerge(
    deltaName: String,
    batchDf: DataFrame,
    merge: Merge
  )(implicit spark: SparkSession): Unit = {
    if (!spark.catalog.tableExists(GlobalTempDB, technicalName)) {
      // the view does not exist: first merge of single batch tests
      // break Spark's plan lineage, to avoid any links to temporary delta tables (PIT)
      val rows: Array[Row] = batchDf.collect()
      val newDf = spark.createDataFrame(spark.sparkContext.parallelize(rows), batchDf.schema)
      newDf.select(columns: _*).createGlobalTempView(technicalName)
    } else {
      if (batchDf.isEmpty) {
        // the view exists, but the batch is empty: second merge of a single batch test
        logger.info("Second merge in Pit logic: legit since batchDf is empty. Nothing to do when isLight=true.")
      } else {
        // the view exists and the batch is NOT empty: multi batch test or second merge in revised out of order case
        val msg = s"The view $technicalName exists and the batch to merge is not empty! " +
          s"Actual merges on existing views are not supported when isLight=true"
        logger.error(msg)
        throw new IllegalStateException(msg)
      }
    }
  }
}

object TableAccess {

  val GlobalTempDB: String = "global_temp"

  def apply(tableMapping: TableMapping): TableAccess = new TableAccess(tableMapping)

  /** Class to abstract the naming of the table/view and the database where it is stored.
   * It is needed to have the possibility of run "light" tests, where we don't create delta tables.
   * Please refer to the [[Json2StarSpec]] documentation for more details on the "light" tests.
   */
  class Naming(
    val configDatabaseName: String,
    val modelName: String,
    val isLight: Boolean
  ) {

    def databaseName: String = if (isLight) GlobalTempDB else configDatabaseName

    def technicalName: String = if (isLight) s"${configDatabaseName}_$modelName" else modelName

    def fullName: String = s"$databaseName.$technicalName"

    def deleteCmd: String = if (isLight) "DROP VIEW IF EXISTS" else "DELETE FROM"
  }

}
