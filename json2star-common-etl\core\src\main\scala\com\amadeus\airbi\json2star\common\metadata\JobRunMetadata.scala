package com.amadeus.airbi.json2star.common.metadata

import io.delta.tables.DeltaTable
import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.functions.{coalesce, col, lit, max}
import org.apache.spark.sql.types.StructType

import java.sql.Timestamp
import java.time.{Instant, ZoneId}

/** Job Run Metadata
  *
  * Note: it has been migrated with some enrichment from https://rndwww.nce.amadeus.net/git/projects/DDL/repos/raw-vault-common-etl/browse/src/main/scala/com/amadeus/airbi/rawvault/common/metadata?at=refs%2Ftags%2Fv7.0.1
  */
class JobRunMetadata(
  val spark: SparkSession,
  val databaseName: String,
  val domain: String,
  val jobId: String,
  val jobRunId: String,
  val enabled: Boolean = true
) {
  import JobRunMetadata._
  import JobRunMetadataTable._
  import spark.implicits._

  val DstAlias = "dst"

  val FullTableName = s"$databaseName.${JobRunMetadataTable.Name}"

  def logStart(): Unit = {
    if (enabled) {
      createMetadataTableIfNotExists()
      val dataStartTime = getDataStartTimestamp
      val startTime = getCurrentTimestamp
      val metadataTable = DeltaTable.forName(FullTableName)
      val rowDf = Seq(JobRunMetadataTable.Row(domain, jobId, jobRunId, Status.STARTED, dataStartTime, startTime)).toDF()
      metadataTable
        .as(DstAlias)
        .merge(rowDf, matchCondition())
        .whenNotMatched()
        .insertAll()
        .execute()
    }
  }

  def logSuccess(details: JobRunMetadataDetails): Unit = logEnd(Status.FINISHED, details)

  def logFailure(details: JobRunMetadataDetails): Unit = logEnd(Status.FAILED, details)

  private def logEnd(status: StatusString, details: JobRunMetadataDetails): Unit = {
    if (enabled) {
      createMetadataTableIfNotExists()
      val endTime = getCurrentTimestamp
      val metadataTable = DeltaTable.forName(FullTableName)
      metadataTable
        .as(DstAlias)
        .update(
          matchCondition(),
          Map(
            Columns.STATUS -> lit(status),
            Columns.END_TIMESTAMP -> lit(endTime),
            Columns.DETAILS -> lit(JobRunMetadataDetails.asJson(details))
          )
        )
    }
  }

  private def matchCondition() = {
    col(s"$DstAlias.${Columns.DOMAIN}") === domain and
      col(s"$DstAlias.${Columns.JOB_ID}") === jobId and
      col(s"$DstAlias.${Columns.JOB_RUN_ID}") === jobRunId
  }

  private def createMetadataTableIfNotExists(): Unit = {
    createTableIfNotExists(
      spark,
      FullTableName,
      JobRunMetadataTable.Schema
    )
  }

  private def getDataStartTimestamp: Timestamp = {
    val metadataTable = DeltaTable.forName(FullTableName)
    val timestamp = metadataTable.toDF
      .filter(s"${Columns.STATUS} = '${Status.FINISHED}'")
      .agg(max(s"${Columns.END_TIMESTAMP}").alias("_max_finished"))
      .withColumn("_start", coalesce(col("_max_finished"), lit(getCurrentTimestamp)))
      .first
      .getAs[Timestamp]("_start")
    timestamp
  }
}

object JobRunMetadata {

  /** Get the current timestamp in UTC
    */
  def getCurrentTimestamp: Timestamp = {
    Timestamp.from(Instant.now().atZone(ZoneId.of("UTC")).toInstant)
  }

  /** Create a table if it does not exist
    */
  def createTableIfNotExists(spark: SparkSession, tableName: String, schema: StructType): Unit = {
    DeltaTable
      .createIfNotExists(spark)
      .property("delta.autoOptimize.optimizeWrite", "true")
      .property("delta.autoOptimize.autoCompact", "true")
      .tableName(tableName)
      .addColumns(schema)
      .execute()
  }
}

object JobRunMetadataTable {
  val Name: String = "METADATA_JOB_RUN"

  object Columns {
    val DOMAIN = "DOMAIN"
    val JOB_ID = "JOB_ID"
    val JOB_RUN_ID = "JOB_RUN_ID"
    val STATUS = "STATUS"
    val DATA_START_TIMESTAMP = "DATA_START_TIMESTAMP"
    val START_TIMESTAMP = "START_TIMESTAMP"
    val END_TIMESTAMP = "END_TIMESTAMP"
    val DETAILS = "DETAILS"
  }

  case class Row(
    DOMAIN: String,
    JOB_ID: String,
    JOB_RUN_ID: String,
    STATUS: String,
    DATA_START_TIMESTAMP: Timestamp,
    START_TIMESTAMP: Timestamp,
    END_TIMESTAMP: Option[Timestamp] = None,
    DETAILS: Option[String] = None
  )

  val Ddl: String =
    s"""
      |  ${Columns.DOMAIN} STRING NOT NULL,
      |  ${Columns.JOB_ID} STRING NOT NULL,
      |  ${Columns.JOB_RUN_ID} STRING NOT NULL,
      |  ${Columns.STATUS} STRING NOT NULL,
      |  ${Columns.DATA_START_TIMESTAMP} TIMESTAMP NOT NULL,
      |  ${Columns.START_TIMESTAMP} TIMESTAMP NOT NULL,
      |  ${Columns.END_TIMESTAMP} TIMESTAMP,
      |  ${Columns.DETAILS} STRING
      |""".stripMargin

  val Schema: StructType = StructType.fromDDL(Ddl)

  type StatusString = String

  object Status {
    val STARTED: StatusString = "STARTED"
    val FINISHED: StatusString = "FINISHED"
    val FAILED: StatusString = "FAILED"
  }

}
