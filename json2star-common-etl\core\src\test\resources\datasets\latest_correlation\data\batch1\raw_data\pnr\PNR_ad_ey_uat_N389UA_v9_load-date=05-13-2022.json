{"mainResource": {"current": {"image": {"@type": "type.googleapis.com/com.amadeus.pulse.message.Pnr", "automatedProcesses": [{"applicableCarrierCode": "4Z", "code": "OK", "dateTime": "2022-05-13T00:00:00", "id": "N389UA-2022-05-13-OT-52", "isApplicableToInfants": false, "office": {"id": "JNB4Z08AA"}, "products": [{"id": "N389UA-2022-05-13-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "N389UA-2022-05-13-ST-3", "ref": "processedPnr.products", "type": "product"}], "travelers": [{"id": "N389UA-2022-05-13-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "automated-process"}], "contacts": [{"email": {"address": "<EMAIL>"}, "id": "N389UA-2022-05-13-OT-16", "purpose": ["STANDARD"], "travelerRefs": [{"id": "N389UA-2022-05-13-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "contact"}, {"id": "N389UA-2022-05-13-OT-17", "phone": {"number": "+33 65465465465-M"}, "purpose": ["STANDARD"], "travelerRefs": [{"id": "N389UA-2022-05-13-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "contact"}, {"id": "N389UA-2022-05-13-OT-18", "phone": {"category": "PERSONAL", "deviceType": "MOBILE", "number": "336546464646"}, "purpose": ["NOTIFICATION"], "travelerRefs": [{"id": "N389UA-2022-05-13-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "contact"}, {"address": {"lines": ["TEST/DTC//MANDELIEU//06210/ZA"]}, "id": "N389UA-2022-05-13-OT-21", "purpose": ["BILLING"], "travelerRefs": [{"id": "N389UA-2022-05-13-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "contact"}], "creation": {"comment": "AMADEUS INTERNET BOOKING-0001AA/JNB4Z08AA", "dateTime": "2022-05-13T13:45:00Z", "pointOfSale": {"login": {"cityCode": "JNB", "countryCode": "ZA", "initials": "AA", "numericSign": "0001"}, "office": {"agentType": "AIRLINE", "iataNumber": "77491024", "id": "JNB4Z08AA", "systemCode": "4Z"}}}, "fareElements": [{"code": "FE", "id": "N389UA-2022-05-13-OT-35", "text": "PAX ZAR420.00 NONREF - NONEND NONREF/CHG PENALTY APPLY", "type": "fare-element"}, {"code": "FV", "id": "N389UA-2022-05-13-OT-36", "text": "PAX 4Z", "type": "fare-element"}], "id": "N389UA-2022-05-13", "keywords": [{"code": "CEID", "id": "N389UA-2022-05-13-OT-33", "nip": 1, "products": [{"id": "N389UA-2022-05-13-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "N389UA-2022-05-13-ST-3", "ref": "processedPnr.products", "type": "product"}], "serviceProvider": {"code": "4Z"}, "status": "HK", "subType": "SPECIAL_KEYWORD", "text": "HZ78SPXKK7ENMEA8S554U5FAB", "travelers": [{"id": "N389UA-2022-05-13-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "service"}], "lastModification": {"comment": "AA-0001AA/JNB4Z08AA", "dateTime": "2022-05-13T13:51:00Z", "pointOfSale": {"login": {"cityCode": "JNB", "countryCode": "ZA", "dutyCode": "SU", "initials": "AA", "numericSign": "0001"}, "office": {"agentType": "AIRLINE", "iataNumber": "77491024", "id": "JNB4Z08AA", "systemCode": "4Z"}}}, "nip": 1, "owner": {"login": {"cityCode": "JNB", "countryCode": "ZA", "dutyCode": "SU", "initials": "AA"}, "office": {"agentType": "AIRLINE", "iataNumber": "77491024", "id": "JNB4Z08AA", "systemCode": "4Z"}}, "paymentMethods": [{"code": "FP", "formsOfPayment": [{"code": "na", "fopIndicator": "OLD", "freeText": "CCVI"}, {"code": "CC", "fopIndicator": "NEW", "freeText": "CCVIXXXXXXXXXXXX1003/0125*CV/A056879"}], "id": "N389UA-2022-05-13-OT-40", "isInfant": false, "products": [{"id": "N389UA-2022-05-13-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "N389UA-2022-05-13-ST-3", "ref": "processedPnr.products", "type": "product"}], "travelers": [{"id": "N389UA-2022-05-13-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "payment-method"}], "products": [{"airSegment": {"aircraft": {"aircraftType": "E90"}, "arrival": {"dateTime": "2022-07-22T15:20:00Z", "iataCode": "JNB", "localDateTime": "2022-07-22T17:20:00", "terminal": "B"}, "bookingStatusCode": "HK", "creation": {"dateTime": "2022-05-13T13:45:00Z", "pointOfSale": {"login": {"cityCode": "JNB", "countryCode": "ZA"}, "office": {"agentType": "WEB", "iataNumber": "77491024", "id": "JNB4Z08AA", "systemCode": "00"}}}, "departure": {"dateTime": "2022-07-22T13:15:00Z", "iataCode": "CPT", "localDateTime": "2022-07-22T15:15:00"}, "id": "2022-07-22-CPT-JNB", "isInformational": false, "marketing": {"bookingClass": {"cabin": {"code": "Y"}, "code": "W", "levelOfService": "ECONOMY", "subClass": {"code": 0, "pointOfSale": {"login": {"countryCode": "ZA"}, "office": {"systemCode": "4Z"}}}}, "flightDesignator": {"carrierCode": "4Z", "flightNumber": "920"}, "id": "4Z-920-2022-07-22-CPT-JNB", "isOpenNumber": false, "isPrime": true}}, "id": "N389UA-2022-05-13-ST-2", "subType": "AIR", "travelers": [{"id": "N389UA-2022-05-13-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"airSegment": {"aircraft": {"aircraftType": "E90"}, "arrival": {"dateTime": "2022-07-12T06:30:00Z", "iataCode": "CPT", "localDateTime": "2022-07-12T08:30:00"}, "bookingStatusCode": "HK", "creation": {"dateTime": "2022-05-13T13:50:00Z", "pointOfSale": {"login": {"cityCode": "JNB", "countryCode": "ZA"}, "office": {"agentType": "WEB", "iataNumber": "77491024", "id": "JNB4Z08AA", "systemCode": "00"}}}, "departure": {"dateTime": "2022-07-12T04:20:00Z", "iataCode": "JNB", "localDateTime": "2022-07-12T06:20:00", "terminal": "B"}, "id": "2022-07-12-JNB-CPT", "isInformational": false, "marketing": {"bookingClass": {"cabin": {"code": "Y"}, "code": "W", "levelOfService": "ECONOMY", "subClass": {"code": 0, "pointOfSale": {"login": {"countryCode": "ZA"}, "office": {"systemCode": "4Z"}}}}, "flightDesignator": {"carrierCode": "4Z", "flightNumber": "893"}, "id": "4Z-893-2022-07-12-JNB-CPT", "isOpenNumber": false, "isPrime": true}}, "id": "N389UA-2022-05-13-ST-3", "subType": "AIR", "travelers": [{"id": "N389UA-2022-05-13-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}], "purgeDate": {"date": "2022-07-26"}, "queuingOffice": {"id": "JNB4Z08AA"}, "reference": "N389UA", "remarks": [{"content": "MODETICKET", "id": "N389UA-2022-05-13-OT-19", "products": [{"id": "N389UA-2022-05-13-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "N389UA-2022-05-13-ST-3", "ref": "processedPnr.products", "type": "product"}], "subType": "RM", "travelers": [{"id": "N389UA-2022-05-13-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "remark"}, {"content": "TIDP5J1REDVAA", "id": "N389UA-2022-05-13-OT-27", "products": [{"id": "N389UA-2022-05-13-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "N389UA-2022-05-13-ST-3", "ref": "processedPnr.products", "type": "product"}], "subType": "RM", "travelers": [{"id": "N389UA-2022-05-13-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "remark"}, {"content": "PRICING ENTRY FXP/R,UP,JNB.JNB/A-W4ZOW", "id": "N389UA-2022-05-13-OT-38", "products": [{"id": "N389UA-2022-05-13-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "N389UA-2022-05-13-ST-3", "ref": "processedPnr.products", "type": "product"}], "subType": "RM", "travelers": [{"id": "N389UA-2022-05-13-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "remark"}, {"content": "FARE1779.92 ZAR", "id": "N389UA-2022-05-13-OT-41", "products": [{"id": "N389UA-2022-05-13-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "N389UA-2022-05-13-ST-3", "ref": "processedPnr.products", "type": "product"}], "subType": "RM", "travelers": [{"id": "N389UA-2022-05-13-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "remark"}, {"content": "REISSUE", "id": "N389UA-2022-05-13-OT-42", "products": [{"id": "N389UA-2022-05-13-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "N389UA-2022-05-13-ST-3", "ref": "processedPnr.products", "type": "product"}], "subType": "RM", "travelers": [{"id": "N389UA-2022-05-13-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "remark"}, {"content": "PENALTY", "id": "N389UA-2022-05-13-OT-43", "products": [{"id": "N389UA-2022-05-13-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "N389UA-2022-05-13-ST-3", "ref": "processedPnr.products", "type": "product"}], "subType": "RM", "travelers": [{"id": "N389UA-2022-05-13-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "remark"}, {"content": "PNR MODIFIED BY THE END USER (FRIDAY-MAY-13-2022)", "id": "N389UA-2022-05-13-OT-44", "products": [{"id": "N389UA-2022-05-13-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "N389UA-2022-05-13-ST-3", "ref": "processedPnr.products", "type": "product"}], "subType": "RM", "travelers": [{"id": "N389UA-2022-05-13-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "remark"}, {"content": "<IP_ADDRESS> *************", "id": "N389UA-2022-05-13-OT-45", "products": [{"id": "N389UA-2022-05-13-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "N389UA-2022-05-13-ST-3", "ref": "processedPnr.products", "type": "product"}], "subType": "RM", "travelers": [{"id": "N389UA-2022-05-13-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "remark"}], "sourcePublicationId": "2338", "ticketingReferences": [{"documents": [{"coupons": [{"product": {"id": "N389UA-2022-05-13-ST-3", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 1}, {"product": {"id": "N389UA-2022-05-13-ST-2", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 2}], "creation": {"dateTime": "2022-05-13T00:00:00Z", "pointOfSale": {"office": {"iataNumber": "77491024", "id": "JNB4Z08AA", "systemCode": "4Z"}}}, "documentType": "ETICKET", "price": {"currency": "ZAR", "total": "575.00"}, "primaryDocumentNumber": "7492400944170", "status": "ISSUED"}], "id": "N389UA-2022-05-13-OT-53", "isInfant": false, "products": [{"id": "N389UA-2022-05-13-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "N389UA-2022-05-13-ST-3", "ref": "processedPnr.products", "type": "product"}], "referenceTypeCode": "FA", "traveler": {"id": "N389UA-2022-05-13-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "ticketing-reference"}, {"documents": [{"coupons": [{"number": 1}], "creation": {"dateTime": "2022-05-13T00:00:00Z", "pointOfSale": {"office": {"iataNumber": "77491024"}}}, "documentNumber": "7492400944169", "documentType": "ETICKET", "status": "ORIGINAL"}], "id": "N389UA-2022-05-13-OT-37", "isInfant": false, "products": [{"id": "N389UA-2022-05-13-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "N389UA-2022-05-13-ST-3", "ref": "processedPnr.products", "type": "product"}], "referenceTypeCode": "FO", "traveler": {"id": "N389UA-2022-05-13-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "ticketing-reference"}], "travelers": [{"contacts": [{"id": "N389UA-2022-05-13-OT-16", "ref": "processedPnr.contacts", "type": "contact"}, {"id": "N389UA-2022-05-13-OT-17", "ref": "processedPnr.contacts", "type": "contact"}, {"id": "N389UA-2022-05-13-OT-18", "ref": "processedPnr.contacts", "type": "contact"}, {"id": "N389UA-2022-05-13-OT-21", "ref": "processedPnr.contacts", "type": "contact"}], "gender": "MALE", "id": "N389UA-2022-05-13-PT-2", "identityDocuments": [{"code": "DOCS", "creation": {"dateTime": "2022-05-13T13:48:00Z", "pointOfSale": {"office": {"id": "JNB4Z08AA"}}}, "document": {"birthDate": "1977-02-18", "documentType": "PASSPORT", "expiryDate": "2024-01-01", "gender": "FEMALE", "issuanceCountry": "FR", "name": {"firstName": "XAVIER", "fullName": "XAVIER MAUDINET", "lastName": "MAUDINET"}, "number": "**********"}, "id": "N389UA-2022-05-13-OT-20", "nip": 1, "serviceProvider": {"code": "4Z"}, "status": "HK", "subType": "SPECIAL_SERVICE_REQUEST", "text": "P/FR/**********//18FEB77/F/01JAN24/MAUDINET/XAVIER", "type": "service"}], "names": [{"firstName": "XAVIER", "lastName": "MAUDINET", "title": "MR"}], "passenger": {"uniqueIdentifier": "5103F08900026178"}, "passengerTypeCode": "ADT", "type": "stakeholder"}], "type": "pnr", "version": "9"}}, "id": "N389UA-2022-05-13", "previous": {"image": {"@type": "type.googleapis.com/com.amadeus.pulse.message.Pnr", "automatedProcesses": [{"code": "OK", "dateTime": "2022-05-13T00:00:00", "id": "N389UA-2022-05-13-OT-46", "isApplicableToInfants": false, "office": {"id": "JNB4Z08AA"}, "products": [{"id": "N389UA-2022-05-13-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "N389UA-2022-05-13-ST-3", "ref": "processedPnr.products", "type": "product"}], "travelers": [{"id": "N389UA-2022-05-13-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "automated-process"}, {"code": "OPW", "dateTime": "2022-05-13T15:45:00", "id": "N389UA-2022-05-13-OT-49", "office": {"id": "JNB4Z08AA"}, "products": [{"id": "N389UA-2022-05-13-ST-3", "ref": "processedPnr.products", "type": "product"}], "queue": {"category": "7", "number": "1"}, "text": "4Z REQUIRES TICKET ON OR BEFORE 16MAY:1545 JNB TIME ZONE", "type": "automated-process"}, {"cancellationRuleId": "797879818572", "code": "OPC", "dateTime": "2022-05-16T15:45:00", "id": "N389UA-2022-05-13-OT-48", "office": {"id": "JNB4Z08AA"}, "products": [{"id": "N389UA-2022-05-13-ST-3", "ref": "processedPnr.products", "type": "product"}], "queue": {"category": "8", "number": "1"}, "text": "4Z CANCELLATION DUE TO NO TICKET JNB TIME ZONE", "travelers": [{"id": "N389UA-2022-05-13-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "automated-process"}], "contacts": [{"email": {"address": "<EMAIL>"}, "id": "N389UA-2022-05-13-OT-16", "purpose": ["STANDARD"], "travelerRefs": [{"id": "N389UA-2022-05-13-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "contact"}, {"id": "N389UA-2022-05-13-OT-17", "phone": {"number": "+33 65465465465-M"}, "purpose": ["STANDARD"], "travelerRefs": [{"id": "N389UA-2022-05-13-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "contact"}, {"id": "N389UA-2022-05-13-OT-18", "phone": {"category": "PERSONAL", "deviceType": "MOBILE", "number": "336546464646"}, "purpose": ["NOTIFICATION"], "travelerRefs": [{"id": "N389UA-2022-05-13-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "contact"}, {"address": {"lines": ["TEST/DTC//MANDELIEU//06210/ZA"]}, "id": "N389UA-2022-05-13-OT-21", "purpose": ["BILLING"], "travelerRefs": [{"id": "N389UA-2022-05-13-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "contact"}], "creation": {"comment": "AMADEUS INTERNET BOOKING-0001AA/JNB4Z08AA", "dateTime": "2022-05-13T13:45:00Z", "pointOfSale": {"login": {"cityCode": "JNB", "countryCode": "ZA", "initials": "AA", "numericSign": "0001"}, "office": {"agentType": "AIRLINE", "iataNumber": "77491024", "id": "JNB4Z08AA", "systemCode": "4Z"}}}, "fareElements": [{"code": "FE", "id": "N389UA-2022-05-13-OT-35", "text": "PAX ZAR420.00 NONREF - NONEND NONREF/CHG PENALTY APPLY", "type": "fare-element"}, {"code": "FV", "id": "N389UA-2022-05-13-OT-36", "text": "PAX 4Z", "type": "fare-element"}], "id": "N389UA-2022-05-13", "keywords": [{"code": "CEID", "id": "N389UA-2022-05-13-OT-33", "nip": 1, "products": [{"id": "N389UA-2022-05-13-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "N389UA-2022-05-13-ST-3", "ref": "processedPnr.products", "type": "product"}], "serviceProvider": {"code": "4Z"}, "status": "HK", "subType": "SPECIAL_KEYWORD", "text": "HZ78SPXKK7ENMEA8S554U5FAB", "travelers": [{"id": "N389UA-2022-05-13-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "service"}], "lastModification": {"comment": "1APUB/ATL-0001AA/NCE1A0955", "dateTime": "2022-05-13T13:50:00Z", "pointOfSale": {"login": {"cityCode": "NCE", "countryCode": "FR", "dutyCode": "SU", "initials": "AA", "numericSign": "0001"}, "office": {"agentType": "AIRLINE", "iataNumber": "12345675", "id": "NCE1A0955", "systemCode": "1A"}}}, "nip": 1, "owner": {"login": {"cityCode": "JNB", "countryCode": "ZA", "dutyCode": "SU", "initials": "AA"}, "office": {"agentType": "AIRLINE", "iataNumber": "77491024", "id": "JNB4Z08AA", "systemCode": "4Z"}}, "paymentMethods": [{"code": "FP", "formsOfPayment": [{"code": "na", "fopIndicator": "OLD", "freeText": "CCVI"}, {"code": "CC", "fopIndicator": "NEW", "freeText": "CCVIXXXXXXXXXXXX1003/0125*CV/A056879"}], "id": "N389UA-2022-05-13-OT-40", "isInfant": false, "products": [{"id": "N389UA-2022-05-13-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "N389UA-2022-05-13-ST-3", "ref": "processedPnr.products", "type": "product"}], "travelers": [{"id": "N389UA-2022-05-13-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "payment-method"}], "products": [{"airSegment": {"aircraft": {"aircraftType": "E90"}, "arrival": {"dateTime": "2022-07-22T15:20:00Z", "iataCode": "JNB", "localDateTime": "2022-07-22T17:20:00", "terminal": "B"}, "bookingStatusCode": "HK", "creation": {"dateTime": "2022-05-13T13:45:00Z", "pointOfSale": {"login": {"cityCode": "JNB", "countryCode": "ZA"}, "office": {"agentType": "WEB", "iataNumber": "77491024", "id": "JNB4Z08AA", "systemCode": "00"}}}, "departure": {"dateTime": "2022-07-22T13:15:00Z", "iataCode": "CPT", "localDateTime": "2022-07-22T15:15:00"}, "id": "2022-07-22-CPT-JNB", "isInformational": false, "marketing": {"bookingClass": {"cabin": {"code": "Y"}, "code": "W", "levelOfService": "ECONOMY", "subClass": {"code": 0, "pointOfSale": {"login": {"countryCode": "ZA"}, "office": {"systemCode": "4Z"}}}}, "flightDesignator": {"carrierCode": "4Z", "flightNumber": "920"}, "id": "4Z-920-2022-07-22-CPT-JNB", "isOpenNumber": false, "isPrime": true}}, "id": "N389UA-2022-05-13-ST-2", "subType": "AIR", "travelers": [{"id": "N389UA-2022-05-13-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"airSegment": {"aircraft": {"aircraftType": "E90"}, "arrival": {"dateTime": "2022-07-12T06:30:00Z", "iataCode": "CPT", "localDateTime": "2022-07-12T08:30:00"}, "bookingStatusCode": "HK", "creation": {"dateTime": "2022-05-13T13:50:00Z", "pointOfSale": {"login": {"cityCode": "JNB", "countryCode": "ZA"}, "office": {"agentType": "WEB", "iataNumber": "77491024", "id": "JNB4Z08AA", "systemCode": "00"}}}, "departure": {"dateTime": "2022-07-12T04:20:00Z", "iataCode": "JNB", "localDateTime": "2022-07-12T06:20:00", "terminal": "B"}, "id": "2022-07-12-JNB-CPT", "isInformational": false, "marketing": {"bookingClass": {"cabin": {"code": "Y"}, "code": "W", "levelOfService": "ECONOMY", "subClass": {"code": 0, "pointOfSale": {"login": {"countryCode": "ZA"}, "office": {"systemCode": "4Z"}}}}, "flightDesignator": {"carrierCode": "4Z", "flightNumber": "893"}, "id": "4Z-893-2022-07-12-JNB-CPT", "isOpenNumber": false, "isPrime": true}}, "id": "N389UA-2022-05-13-ST-3", "subType": "AIR", "travelers": [{"id": "N389UA-2022-05-13-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}], "purgeDate": {"date": "2022-07-26"}, "queuingOffice": {"id": "JNB4Z08AA"}, "reference": "N389UA", "remarks": [{"content": "MODETICKET", "id": "N389UA-2022-05-13-OT-19", "products": [{"id": "N389UA-2022-05-13-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "N389UA-2022-05-13-ST-3", "ref": "processedPnr.products", "type": "product"}], "subType": "RM", "travelers": [{"id": "N389UA-2022-05-13-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "remark"}, {"content": "TIDP5J1REDVAA", "id": "N389UA-2022-05-13-OT-27", "products": [{"id": "N389UA-2022-05-13-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "N389UA-2022-05-13-ST-3", "ref": "processedPnr.products", "type": "product"}], "subType": "RM", "travelers": [{"id": "N389UA-2022-05-13-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "remark"}, {"content": "PRICING ENTRY FXP/R,UP,JNB.JNB/A-W4ZOW", "id": "N389UA-2022-05-13-OT-38", "products": [{"id": "N389UA-2022-05-13-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "N389UA-2022-05-13-ST-3", "ref": "processedPnr.products", "type": "product"}], "subType": "RM", "travelers": [{"id": "N389UA-2022-05-13-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "remark"}, {"content": "FARE1779.92 ZAR", "id": "N389UA-2022-05-13-OT-41", "products": [{"id": "N389UA-2022-05-13-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "N389UA-2022-05-13-ST-3", "ref": "processedPnr.products", "type": "product"}], "subType": "RM", "travelers": [{"id": "N389UA-2022-05-13-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "remark"}, {"content": "REISSUE", "id": "N389UA-2022-05-13-OT-42", "products": [{"id": "N389UA-2022-05-13-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "N389UA-2022-05-13-ST-3", "ref": "processedPnr.products", "type": "product"}], "subType": "RM", "travelers": [{"id": "N389UA-2022-05-13-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "remark"}, {"content": "PENALTY", "id": "N389UA-2022-05-13-OT-43", "products": [{"id": "N389UA-2022-05-13-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "N389UA-2022-05-13-ST-3", "ref": "processedPnr.products", "type": "product"}], "subType": "RM", "travelers": [{"id": "N389UA-2022-05-13-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "remark"}, {"content": "PNR MODIFIED BY THE END USER (FRIDAY-MAY-13-2022)", "id": "N389UA-2022-05-13-OT-44", "products": [{"id": "N389UA-2022-05-13-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "N389UA-2022-05-13-ST-3", "ref": "processedPnr.products", "type": "product"}], "subType": "RM", "travelers": [{"id": "N389UA-2022-05-13-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "remark"}, {"content": "<IP_ADDRESS> *************", "id": "N389UA-2022-05-13-OT-45", "products": [{"id": "N389UA-2022-05-13-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "N389UA-2022-05-13-ST-3", "ref": "processedPnr.products", "type": "product"}], "subType": "RM", "travelers": [{"id": "N389UA-2022-05-13-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "remark"}], "sourcePublicationId": "2338", "ticketingReferences": [{"documents": [{"coupons": [{"product": {"id": "N389UA-2022-05-13-ST-2", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 2}], "creation": {"dateTime": "2022-05-13T00:00:00Z", "pointOfSale": {"office": {"iataNumber": "77491024", "id": "JNB4Z08AA", "systemCode": "4Z"}}}, "documentType": "ETICKET", "price": {"currency": "ZAR", "total": "1779.92"}, "primaryDocumentNumber": "7492400944169", "status": "ISSUED"}], "id": "N389UA-2022-05-13-OT-31", "isInfant": false, "products": [{"id": "N389UA-2022-05-13-ST-2", "ref": "processedPnr.products", "type": "product"}], "referenceTypeCode": "FA", "traveler": {"id": "N389UA-2022-05-13-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "ticketing-reference"}, {"documents": [{"coupons": [{"number": 1}], "creation": {"dateTime": "2022-05-13T00:00:00Z", "pointOfSale": {"office": {"iataNumber": "77491024"}}}, "documentNumber": "7492400944169", "documentType": "ETICKET", "status": "ORIGINAL"}], "id": "N389UA-2022-05-13-OT-37", "isInfant": false, "products": [{"id": "N389UA-2022-05-13-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "N389UA-2022-05-13-ST-3", "ref": "processedPnr.products", "type": "product"}], "referenceTypeCode": "FO", "traveler": {"id": "N389UA-2022-05-13-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "ticketing-reference"}], "travelers": [{"contacts": [{"id": "N389UA-2022-05-13-OT-16", "ref": "processedPnr.contacts", "type": "contact"}, {"id": "N389UA-2022-05-13-OT-17", "ref": "processedPnr.contacts", "type": "contact"}, {"id": "N389UA-2022-05-13-OT-18", "ref": "processedPnr.contacts", "type": "contact"}, {"id": "N389UA-2022-05-13-OT-21", "ref": "processedPnr.contacts", "type": "contact"}], "gender": "MALE", "id": "N389UA-2022-05-13-PT-2", "identityDocuments": [{"code": "DOCS", "creation": {"dateTime": "2022-05-13T13:48:00Z", "pointOfSale": {"office": {"id": "JNB4Z08AA"}}}, "document": {"birthDate": "1977-02-18", "documentType": "PASSPORT", "expiryDate": "2024-01-01", "gender": "FEMALE", "issuanceCountry": "FR", "name": {"firstName": "XAVIER", "fullName": "XAVIER MAUDINET", "lastName": "MAUDINET"}, "number": "**********"}, "id": "N389UA-2022-05-13-OT-20", "nip": 1, "serviceProvider": {"code": "4Z"}, "status": "HK", "subType": "SPECIAL_SERVICE_REQUEST", "text": "P/FR/**********//18FEB77/F/01JAN24/MAUDINET/XAVIER", "type": "service"}], "names": [{"firstName": "XAVIER", "lastName": "MAUDINET", "title": "MR"}], "passenger": {"uniqueIdentifier": "5103F08900026178"}, "passengerTypeCode": "ADT", "type": "stakeholder"}], "type": "pnr", "version": "8"}}, "type": "com.amadeus.pulse.message.Pnr"}}