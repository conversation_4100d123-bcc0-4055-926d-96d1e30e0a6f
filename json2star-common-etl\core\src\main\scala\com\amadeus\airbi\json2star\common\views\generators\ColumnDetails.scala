package com.amadeus.airbi.json2star.common.views.generators

import com.amadeus.airbi.json2star.common.ColumnDef
import com.amadeus.airbi.json2star.common.views.lookups.{MetadataFields, YamlLookup}
import com.amadeus.airbi.rawvault.common.config.{ColumnMetadataRule, ColumnMetadataValue}

case class ColumnDetails(
  descriptions: Seq[String],
  examples: Seq[String],
  piiTypes: Seq[String]
)

object ColumnDetails {

  private def deduplicate(values: Seq[String]): Seq[String] = {
    values
      .map(_.trim)
      .filterNot(_.isEmpty)
      .distinct
  }

  /** Generate column details from the Metadata YAML configuration and the column definition
    * The column definition is defined from the mapping configuration
    *
    * @param maybeYamlLookup YAML Lookup for metadata fields if available
    * @param col       a column definition
    * @param tableName the name of the table
    *
    * @return the column details
    */
  def from(
    maybeYamlLookup: Option[YamlLookup],
    col: ColumnDef,
    tableName: String
  ): ColumnDetails = {

    def getValue(customValue: Option[ColumnMetadataValue], defaultValues: Seq[String]): Seq[String] = {
      customValue match {
        case None => deduplicate(defaultValues)
        case Some(ColumnMetadataValue(cValue, ColumnMetadataRule.Replace)) => Seq(cValue)
        case Some(ColumnMetadataValue(cValue, ColumnMetadataRule.Concat)) =>
          deduplicate(cValue +: defaultValues)
      }
    }

    val metaFields = maybeYamlLookup match {
      case None => Seq.empty[MetadataFields]
      case Some(yl) => MetadataFields.from(col, yl)
    }
    val descs = getValue(
      col.consolidatedMeta(tableName).flatMap(_.description),
      metaFields.flatMap(_.description)
    )
    val examples = getValue(
      col.consolidatedMeta(tableName).flatMap(_.example),
      metaFields.flatMap(_.examples)
    )
    val piiTypes = getValue(
      col.consolidatedMeta(tableName).flatMap(_.piiType),
      metaFields.flatMap(_.piiType)
    )

    ColumnDetails(descs, examples, piiTypes)
  }

}
