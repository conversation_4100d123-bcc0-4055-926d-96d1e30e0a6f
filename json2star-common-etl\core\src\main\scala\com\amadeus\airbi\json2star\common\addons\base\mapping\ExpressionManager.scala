package com.amadeus.airbi.json2star.common.addons.base.mapping

import com.amadeus.airbi.rawvault.common.config.ColumnConfig
import org.apache.spark.sql.DataFrame
import org.apache.spark.sql.functions.expr

/** Utility object for applying spark SQL expressions to columns.
  *
  */
object ExpressionManager {

  val COL = "{0}"
  val COL_CUSTOMER = "{CUSTOMER}"

  /**
    * Apply the expression before any stackable addons
    *
    * Mapping rules:
    * {0} identifies the src column (in case of column composed by many fields, it identifies the concatenation of those fields)
    * {1}, {2}, .., {N} identify the different components (in case of column composed by a single field {0} == {1})
    * {CUSTOMER} is a reference to the customer (rootConfig.etl.common.shard)
    * @param df dataframe to apply the expressions to
    * @param columns configurations
    * @return
    */
  def applyPreExpressions(df: DataFrame, columns: Seq[ColumnConfig], customer : String): DataFrame = {
    import org.apache.spark.sql.functions._
    val dfPostOp = columns
      .filter(_.expr.nonEmpty)
      .foldLeft(df){(df, c) =>
        df.withColumn(c.name, expr(generateExpression(c.expr.get, c.name, Some(customer))))
      }
    dfPostOp
  }

  def generateExpression(expr: String, body: String, customer : Option[String] = None): String = {
    val temp =  expr
      .replace(COL, body)
      // Temporary code here
      .replace("{1}", body) // only single field column expression are supported as of now, so {1} == {0}
      .replace("{2}", "null") // multi field not supported yet
    customer match {
      case Some(c) => temp.replace(COL_CUSTOMER, c)
      case None => temp
    }

  }

  /**
    * Apply the standard expressions to dataframe after all stackable addons (right before merge)
    *
    * @param df dataframe to apply the expressions to
    * @param columns configurations containing the post expressions
    * @return a dataframe with the columns with post expressions replaced
    */
  def applyPostExpressions(df: DataFrame, columns: Seq[ColumnConfig]): DataFrame = {
    val postExprColumns = columns.flatMap(c => c.postExpr.map(e => (c, e)))
    df.withColumns(colsMap = postExprColumns.map { case (cc, e) => cc.name -> expr(e) }.toMap) // replace columns
  }

}
