package com.amadeus.airbi.json2star.common.eventgrid

import com.amadeus.airbi.datalake.common.spark.SparkSqlSpecification
import com.amadeus.airbi.json2star.common.metadata.{
  JobRunMetadata,
  JobRunMetadataDetails,
  JobRunMetadataTable,
  MockQualityMetrics
}
import com.amadeus.airbi.json2star.common.resize.DatabricksUtils
import com.amadeus.airbi.json2star.common.testfwk.MockDatabricksUtils
import com.amadeus.airbi.rawvault.common.RootConfig
import com.amadeus.airbi.rawvault.common.testfwk.TestDataUtils
import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.functions._
import org.json4s.DefaultFormats
import org.json4s.jackson.JsonMethods
import org.json4s.jackson.Serialization.{write => asJson}
import org.scalatest.BeforeAndAfterEach

import java.io.File
import java.nio.file.Files
import java.util.TimeZone
import scala.io.Source
import scala.reflect.io.Directory

class EventPublisherSpec extends SparkSqlSpecification with BeforeAndAfterEach {

  implicit val formats: DefaultFormats.type = DefaultFormats

  val mockDbx = new MockDatabricksUtils(numWorkers = 1, clusterId = "id-1", spark)

  override def beforeAll: Unit = {
    super.beforeAll
    TimeZone.setDefault(TimeZone.getTimeZone("UTC"))
    spark.conf.set("spark.databricks.delta.allowArbitraryProperties.enabled", "true")
    spark.catalog.setCurrentDatabase(outputDatabase)
  }

  it should "create the event to publish to Event Grid Topic" in {

    val metadata = new JobRunMetadata(spark, outputDatabase, "DOMAIN", "my-id", "my-run-id")
    val jobId = metadata.jobId
    val jobRunId = metadata.jobRunId
    val startTimestamp = JobRunMetadata.getCurrentTimestamp
    val endTimestamp = JobRunMetadata.getCurrentTimestamp

    val appConfFile = getClass.getClassLoader
      .getResource("config/application-with-event-grid.conf")
      .getPath
    val rootConfig = RootConfig.fromArgs(Array("my-id", "my-run-id", appConfFile, "etl,optimize"))

    val inputJDFEventData = JDFEventData(rootConfig, jobId, jobRunId, startTimestamp, endTimestamp)
    val evt = CloudEvent(subject = "container-name", source = "topic-name", data = inputJDFEventData)
    evt.subject should ===("container-name")
    evt.`type` should ===("Amadeus.JDF.UpdateNotification")

    val outputJDFEventData = evt.data

    outputJDFEventData should ===(
      JDFEventData(
        "container-name",
        "1_0_1",
        "DB_DOMAIN_1_0_1",
        "DOMAIN",
        "my-id",
        "my-run-id",
        JDFEventData.timestampStr(startTimestamp),
        JDFEventData.timestampStr(endTimestamp)
      )
    )

  }

  it should "publish the correct batched event on Event Grid Topic in case of failure" in {
    val metrics = MockQualityMetrics.mappingMetrics
    val appConfFile = getClass.getClassLoader.getResource("config/application-with-event-grid.conf").getPath
    val rc = RootConfig.fromArgs(Array("my-id", "my-run-id", appConfFile, "etl,optimize"))
    val directory = new Directory(new File(rc.etl.stream.sink.checkpointLocation))
    directory.deleteRecursively()

    // Failure in the job, that should be ignored
    val metadata0 = new JobRunMetadata(spark, outputDatabase, rc.etl.common.domain, "my-id", "my-run-id-0")
    metadata0.logStart()
    metadata0.logFailure(JobRunMetadataDetails.fromThrowable(new Exception("test")))

    // First json2star run
    val metadata1 = new JobRunMetadata(spark, outputDatabase, rc.etl.common.domain, "my-id", "my-run-id-1")
    metadata1.logStart()
    metadata1.logSuccess(metrics)

    // Here we simulate a failure in the event grid send by not calling the sendEvent method

    // Second json2star run
    val metadata2 = new JobRunMetadata(spark, outputDatabase, rc.etl.common.domain, "my-id", "my-run-id-last")
    metadata2.logStart()
    metadata2.logSuccess(metrics)

    val expectedData = computeExpectedJDFEventData(rc)

    // here we successfully send to event grid
    val msgFile = Files.createTempFile("msg_file", ".json").toString
    //val msgFile = directory.toAbsolute.path + "/.msgFile"
    val eventPublisher = new MockEventPublisher(rc, spark, msgFile, mockDbx)
    eventPublisher.sendEvent()
    val actualData = readActualJDFEventData(msgFile)

    actualData should ===(expectedData)

    directory.deleteRecursively()
  }

  private def computeExpectedJDFEventData(rc: RootConfig): JDFEventData = {
    val finishedDf = spark.read
      .format("delta")
      .table(JobRunMetadataTable.Name)
      .filter(s"${JobRunMetadataTable.Columns.STATUS} = '${JobRunMetadataTable.Status.FINISHED}'")

    val batchMinMax = finishedDf
      .agg(
        min(JobRunMetadataTable.Columns.DATA_START_TIMESTAMP).alias("MIN_TIMESTAMP"),
        max(JobRunMetadataTable.Columns.END_TIMESTAMP).alias("MAX_TIMESTAMP")
      )
      .select("MIN_TIMESTAMP", "MAX_TIMESTAMP")
    val f = batchMinMax.first()
    val minTimestamp = f.getTimestamp(0)
    val maxTimestamp = f.getTimestamp(1)

    JDFEventData(
      conf = rc,
      jobId = "my-id",
      jobRunId = "my-run-id-last",
      startTimestamp = minTimestamp,
      endTimestamp = maxTimestamp
    )
  }

  private def readActualJDFEventData(file: String): JDFEventData = {
    val src = Source.fromFile(file)
    val actualContent = src.getLines.mkString
    src.close()
    val cloudEvent = JsonMethods.parse(actualContent).extract[CloudEvent]
    cloudEvent.data
  }

  it should "raise an exception if the authentication fails" in {
    val appConfFile = getClass.getClassLoader
      .getResource("config/application-with-event-grid.conf")
      .getPath
    val rootConfig = RootConfig.fromArgs(Array("my-id", "my-run-id", appConfFile, "etl,optimize"))

    val eventPublisher = EventPublisher(rootConfig, spark, mockDbx, MockAccessTokenProviderWithError)
    val caught = intercept[RuntimeException] {
      eventPublisher.eventGridClient()
    }
    caught.getMessage should include("[event-grid] ERROR")
  }

  it should "raise an exception if the send fails" in {
    val appConfFile = getClass.getClassLoader
      .getResource("config/application-with-event-grid.conf")
      .getPath
    val rootConfig = RootConfig.fromArgs(Array("my-id", "my-run-id", appConfFile, "etl,optimize"))

    val eventPublisher = EventPublisher(rootConfig, spark, mockDbx, MockAccessTokenProvider)
    val sampleEvent = CloudEvent(
      subject = "subject-name",
      source = "source-name",
      data = JDFEventData(
        "container-name",
        "1_0_1",
        "DB_DOMAIN_1_0_1",
        "DOMAIN",
        "my-id",
        "my-run-id",
        "2025-01-16T12:53:38.81Z",
        "2025-01-16T13:53:38.81Z"
      )
    )

    assertThrows[RuntimeException] {
      eventPublisher.clientSend(sampleEvent)
    }
  }

  it should "load event grid params from application conf" in {
    val appConfFile = getClass.getClassLoader
      .getResource("config/application-with-event-grid.conf")
      .getPath
    val rootConfig = RootConfig.fromArgs(Array("my-id", "my-run-id", appConfFile, "etl,optimize"))

    val eventGridParams = rootConfig.eventGridParams.get
    eventGridParams.topicUrl should ===(
      "https://evgn-we-tst-anm-AAAAAA.westeurope-1.eventgrid.azure.net/topics/evgnt-we-tst-anm-AAAAAA:publish?api-version=2023-11-01"
    )
    eventGridParams.topicName should ===("evgnt-we-tst-anm-AAAAAA")
    eventGridParams.serviceProviderAppId should ===("app-0000-0000-0000-123456789012")
    eventGridParams.serviceProviderTenantId should ===("tenant-1234-1234-1234-123456789012")
    eventGridParams.managedAppResourceId should ===(
      "/subscriptions/subscription-id/resourceGroups/resource-group-name/providers/Microsoft.Solutions/applications/managed-app-name"
    )
    eventGridParams.dbxSecretKey should ===("my-secret-key")
    eventGridParams.dbxSecretScope should ===("my-dbx-scope")
    eventGridParams.serviceProviderAppSecretValue(mockDbx) should ===(Right("mock for my-dbx-scope-my-secret-key"))
  }

}

class MockEventPublisher(
  override val conf: RootConfig,
  override val spark: SparkSession,
  file: String,
  override val dbxUtils: DatabricksUtils
) extends EventPublisher(conf, spark, dbxUtils, DefaultAccessTokenProvider) {

  /** Create a file with the content of the message instead of sending to event grid
    */
  override def clientSend(event: CloudEvent): Unit = {
    val json = asJson(event)(DefaultFormats)
    TestDataUtils.writeFile(file, json)
  }

}
