package com.amadeus.airbi.json2star.common.addons.base.mapping.input

import com.amadeus.airbi.json2star.common.addons.base.mapping.MappingAddonParams.AutoloaderConfig
import com.typesafe.scalalogging.Logger
import org.apache.spark.sql.functions.{col, regexp_extract, to_timestamp, when}
import org.apache.spark.sql.streaming.DataStreamReader
import org.apache.spark.sql.types._
import org.apache.spark.sql.{DataFrame, SparkSession}
import org.slf4j.LoggerFactory

/** Concrete implementation of `InputFormat`
  *
  * It is an input format able consume json data
  * Streaming is done using Databricks Auto Loader
  * https://learn.microsoft.com/en-us/azure/databricks/ingestion/cloud-object-storage/auto-loader/
  */
case class AutoLoaderMDMJsonInput(spark: SparkSession, conf: AutoloaderConfig) extends InputFormat {

  lazy val logger: Logger = Logger(LoggerFactory.getLogger(getClass.getName))

  // $COVERAGE-OFF$ autoloader cloudFiles is not supported locally
  def streamingDataFrameFrom(streamReader: DataStreamReader, paths: Seq[String]): DataFrame = {
    val configuredStreamReader = streamReader
      .schema(AutoLoaderMDMJsonInput.inputSchema)
      .format("cloudFiles")
      .option("pathGlobFilter", "*.json") // read all json files within the directory
      .options(
        (Map[String, Any](
          "cloudFiles.format" -> "binaryfile",
          "cloudFiles.useIncrementalListing" -> true,
          "cloudFiles.includeExistingFiles" -> conf.includeExistingFiles,
          "cloudFiles.useNotifications" -> conf.useNotifications
        )
          ++ conf.backfillInterval.map("cloudFiles.backfillInterval" -> _)
          ++ conf.maxFilesPerTrigger.map("cloudFiles.maxFilesPerTrigger" -> _)
          ++ conf.maxBytesPerTrigger.map("cloudFiles.maxBytesPerTrigger" -> _))
          .mapValues(_.toString)
      )

    val input = paths
      .filter(_.trim.nonEmpty)
      .map(configuredStreamReader.load)
      .reduce(_ union _)

    AutoLoaderMDMJsonInput.parse(input)
  }
  // $COVERAGE-OFF$
}

object AutoLoaderMDMJsonInput {

  val inputSchema: StructType = StructType(
    Array(
      StructField("path", StringType, nullable = true),
      StructField("modificationTime", TimestampType, nullable = true),
      StructField("length", LongType, nullable = true),
      StructField("content", BinaryType, nullable = true)
    )
  )

  def parse(input: DataFrame): DataFrame = {
    val pattern = ".*_(\\d{4}_\\d{2}_\\d{2}_\\d{2}_\\d{2}_\\d{2})\\.json"
    val timestampFormat = "yyyy_MM_dd_HH_mm_ss"

    input
      .withColumn(
        "file_date",
        when(
          col("path").rlike(pattern), // Check if the path matches the regex
          to_timestamp(regexp_extract(col("path"), pattern, 1), timestampFormat) // Extract and parse the timestamp
        ).otherwise(null) // Return null if the pattern does not match
      )
      .select(
        col("content").cast(StringType).as("BODY"),
        col("file_date").cast(TimestampType).as("LOAD_DATE"),
        col("file_date").cast(StringType).as("RECORD_ID")
      )
  }
}
