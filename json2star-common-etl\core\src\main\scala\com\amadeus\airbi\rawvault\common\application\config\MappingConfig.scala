package com.amadeus.airbi.rawvault.common.application.config

import com.amadeus.airbi.json2star.common.addons.base.AddonConfig
import com.amadeus.airbi.rawvault.common.config.ColSources.{NullSource, RootSpecificSource}
import com.amadeus.airbi.rawvault.common.config.{
  ColumnConfig,
  ColumnType,
  NamedRootSource,
  RootSource
}

case class MappingConfig(
  merge: Merge,
  rootSources: List[RootSource],
  columns: List[ColumnConfig],
  pit: Pit,
  description: Option[TableDescription] = None
) extends AddonConfig {

  val (masterPit, secondaryPit, noPit) = pit match {
    case m: MasterPitTable => (Some(m), None, None)
    case s: SecondaryPitTable => (None, Some(s), None)
    case n: NoPitTable => (None, None, Some(n))
  }

  lazy val allOrderedColumnsWithLoadDate: Seq[ColumnConfig] = {
    allOrderedColumns ++ loadDateCol
  }

  lazy val allOrderedColumns: Seq[ColumnConfig] = {
    columns ++ getFKeyColumns(columns)
  }

  /** Returns all the ordered columns for a given root source, converting root source specific sources into
    * plain (non root specific) sources.
    */
  def allColumns(rs: RootSource): Seq[ColumnConfig] = {
    val allColsForRs = allOrderedColumns.flatMap { c =>
      c.sources match {
        case r @ RootSpecificSource(_) =>
          rs match {
            case NamedRootSource(name, _) =>
              Some(
                c.copy(
                  sources = r.asNonRootSpecificSource(name).getOrElse(NullSource())
                )
              )
            case unnamed =>
              throw new IllegalStateException(
                s"For column '${c.name}' you are using root specific source '$r' with unnamed root source '$unnamed'"
              )
          }
        case _ => Some(c)
      }
    }
    allColsForRs
  }

  private def getFKeyColumns(cols: Seq[ColumnConfig]): Seq[ColumnConfig] = {
    cols.filter(_.createFk.isDefined).map(_.getFkeyColumn)
  }

  private def loadDateCol: Seq[ColumnConfig] = {
    if (merge.ifDupeTakeHigher.contains("LOAD_DATE")) {
      Seq(
        ColumnConfig(
          "LOAD_DATE",
          ColumnType.timestampColumn,
          sources = NullSource(),
          hasVariable = false,
          isMandatory = false,
          expr = None
        )
      )
    } else {
      Seq()
    }
  }

}
