@startuml star-schema_CORR_TKTEMD_v1_0_0

' Header
hide circle
hide <<assotable>> stereotype
hide <<dimtable>> stereotype
hide <<maintable>> stereotype
hide <<mainsubdomain>> stereotype
hide methods
left to right direction

!define TABLE_GRADIENT_BACKGROUND #F2F2F2-fcffd6
!define MAIN_TABLE_GRADIENT_HEADER Orange-%lighten("Yellow", 60)

!function $pk($content)
!return "<color:#ff0000>" + $content + "</color>"
!endfunction

!function $fk($content)
!return "<color:#0000ff>" + $content + "</color>"
!endfunction

skinparam {
    DefaultFontName Monospaced
    ranksep 250
    linetype polyline
    tabSize 4
    HyperlinkUnderline false
    HyperlinkColor #0000ff
}

skinparam frame {
    FontSize 28
    FontSize<<mainsubdomain>> 34
    BorderThickness<<mainsubdomain>> 3
}

skinparam class {
    BackgroundColor TABLE_GRADIENT_BACKGROUND
    HeaderBackgroundColor %lighten("Crimson", 40)
    HeaderBackgroundColor<<maintable>> MAIN_TABLE_GRADIENT_HEADER
    HeaderBackgroundColor<<dimtable>> LightBlue
    HeaderBackgroundColor<<assotable>> %lighten("LimeGreen", 30)
    ColorArrowSeparationSpace Red
    BorderColor Black
    BorderColor<<maintable>> MediumBlue
    BorderThickness<<maintable>> 3
    ArrowColor Blue
    FontSize 16
    FontSize<<maintable>> 20
    FontStyle Bold
}

' Frames
frame "TKTEMD" #DDDDDD {

entity "FACT_TRAVEL_DOCUMENT_HISTO" {
$pk("TRAVEL_DOCUMENT_ID  Binary  NN  PK")
$pk("VERSION             Long    NN  PK")
}

entity "FACT_COUPON_HISTO" {
$pk("COUPON_ID  Binary  NN  PK")
$pk("VERSION    Long    NN  PK")
}
}

frame "PNR" #DDDDDD {

entity "FACT_AIR_SEGMENT_PAX_HISTO" {
$pk("AIR_SEGMENT_PAX_ID  Binary  NN  PK")
$pk("VERSION             Long    NN  PK")
$pk("AIR_SEGMENT_ID      Binary  NN  PK")
}

entity "FACT_TRAVELER_HISTO" {
$pk("TRAVELER_ID  Binary  NN  PK")
$pk("VERSION      Long    NN  PK")
}

entity "FACT_SEATING_PAX_HISTO" {
$pk("SEATING_PAX_ID  Binary  NN  PK")
$pk("VERSION         Long    NN  PK")
}

entity "FACT_SERVICE_PAX_HISTO" {
$pk("SERVICE_PAX_ID  Binary  NN  PK")
$pk("VERSION         Long    NN  PK")
}

entity "FACT_RESERVATION_HISTO" {
$pk("RESERVATION_ID  Binary  NN  PK")
$pk("VERSION         Long    NN  PK")
}
}

frame "SKD" #DDDDDD {

entity "FACT_FLIGHT_DATE_HISTO" {
$pk("FLIGHT_DATE_ID  Binary  NN  PK")
$pk("VERSION         Long    NN  PK")
}

entity "FACT_FLIGHT_SEGMENT_HISTO" {
$pk("FLIGHT_SEGMENT_ID  Binary  NN  PK")
$pk("VERSION            Long    NN  PK")
}

entity "FACT_CODESHARE_FLIGHT_SEGMENT_HISTO" {
$pk("CODESHARE_FLIGHT_SEGMENT_ID  Binary  NN  PK")
$pk("VERSION                      Long    NN  PK")
}
}

frame "DCSPAX" #DDDDDD {

entity "FACT_PASSENGER_HISTO" {
$pk("PASSENGER_ID  Binary  NN  PK")
$pk("VERSION       Long    NN  PK")
}

entity "FACT_SEGMENT_DELIVERY_HISTO" {
$pk("SEGMENT_DELIVERY_ID  Binary  NN  PK")
$pk("VERSION              Long    NN  PK")
}

entity "FACT_SERVICE_DELIVERY_HISTO" {
$pk("SERVICE_DELIVERY_ID  Binary  NN  PK")
$pk("VERSION              Long    NN  PK")
}
}

' Free entities
entity "ASSO_RESERVATION_TRAVEL_DOCUMENT_HISTO"<<assotable>> {
$fk("[[#{PNR.FACT_RESERVATION_HISTO} RESERVATION_ID]]                 Binary         FK")
$fk("[[#{TKTEMD.FACT_TRAVEL_DOCUMENT_HISTO} TRAVEL_DOCUMENT_ID]]             Binary         FK")
$fk("[[#{PNR.FACT_RESERVATION_HISTO} VERSION_RESERVATION]]            Long       NN  FK")
$fk("[[#{TKTEMD.FACT_TRAVEL_DOCUMENT_HISTO} VERSION_TRAVEL_DOCUMENT]]        Long       NN  FK")
DATE_BEGIN                     Timestamp        
DATE_END                       Timestamp        
IS_LAST_VERSION                Boolean          
REFERENCE_KEY_RESERVATION      String           
REFERENCE_KEY_TRAVEL_DOCUMENT  String           
}

entity "ASSO_TRAVELER_TRAVEL_DOCUMENT_HISTO"<<assotable>> {
$fk("[[#{PNR.FACT_TRAVELER_HISTO} TRAVELER_ID]]                    Binary         FK")
$fk("[[#{TKTEMD.FACT_TRAVEL_DOCUMENT_HISTO} TRAVEL_DOCUMENT_ID]]             Binary         FK")
$fk("[[#{PNR.FACT_TRAVELER_HISTO} VERSION_RESERVATION]]            Long       NN  FK")
$fk("[[#{TKTEMD.FACT_TRAVEL_DOCUMENT_HISTO} VERSION_TRAVEL_DOCUMENT]]        Long       NN  FK")
DATE_BEGIN                     Timestamp        
DATE_END                       Timestamp        
IS_LAST_VERSION                Boolean          
REFERENCE_KEY_RESERVATION      String           
REFERENCE_KEY_TRAVELER         String           
REFERENCE_KEY_TRAVEL_DOCUMENT  String           
}

entity "ASSO_AIR_SEGMENT_PAX_COUPON_HISTO"<<assotable>> {
$fk("[[#{PNR.FACT_AIR_SEGMENT_PAX_HISTO} AIR_SEGMENT_PAX_ID]]             Binary         FK")
$fk("[[#{TKTEMD.FACT_COUPON_HISTO} COUPON_ID]]                      Binary         FK")
$fk("[[#{PNR.FACT_AIR_SEGMENT_PAX_HISTO} VERSION_RESERVATION]]            Long       NN  FK")
$fk("[[#{TKTEMD.FACT_COUPON_HISTO} VERSION_TRAVEL_DOCUMENT]]        Long       NN  FK")
DATE_BEGIN                     Timestamp        
DATE_END                       Timestamp        
IS_LAST_VERSION                Boolean          
REFERENCE_KEY_RESERVATION      String           
REFERENCE_KEY_AIR_SEGMENT_PAX  String           
REFERENCE_KEY_TRAVEL_DOCUMENT  String           
REFERENCE_KEY_COUPON           String           
}

entity "ASSO_SERVICE_PAX_COUPON_HISTO"<<assotable>> {
$fk("[[#{PNR.FACT_SERVICE_PAX_HISTO} SERVICE_PAX_ID]]                 Binary         FK")
$fk("[[#{TKTEMD.FACT_COUPON_HISTO} COUPON_ID]]                      Binary         FK")
$fk("[[#{PNR.FACT_SERVICE_PAX_HISTO} VERSION_RESERVATION]]            Long       NN  FK")
$fk("[[#{TKTEMD.FACT_COUPON_HISTO} VERSION_TRAVEL_DOCUMENT]]        Long       NN  FK")
DATE_BEGIN                     Timestamp        
DATE_END                       Timestamp        
IS_LAST_VERSION                Boolean          
REFERENCE_KEY_RESERVATION      String           
REFERENCE_KEY_SERVICE_PAX      String           
REFERENCE_KEY_TRAVEL_DOCUMENT  String           
REFERENCE_KEY_COUPON           String           
}

entity "ASSO_SEATING_PAX_COUPON_HISTO"<<assotable>> {
$fk("[[#{PNR.FACT_SEATING_PAX_HISTO} SEATING_PAX_ID]]                 Binary         FK")
$fk("[[#{TKTEMD.FACT_COUPON_HISTO} COUPON_ID]]                      Binary         FK")
$fk("[[#{PNR.FACT_SEATING_PAX_HISTO} VERSION_RESERVATION]]            Long       NN  FK")
$fk("[[#{TKTEMD.FACT_COUPON_HISTO} VERSION_TRAVEL_DOCUMENT]]        Long       NN  FK")
DATE_BEGIN                     Timestamp        
DATE_END                       Timestamp        
IS_LAST_VERSION                Boolean          
REFERENCE_KEY_RESERVATION      String           
REFERENCE_KEY_SEATING_PAX      String           
REFERENCE_KEY_TRAVEL_DOCUMENT  String           
REFERENCE_KEY_COUPON           String           
}

entity "ASSO_TRAVEL_DOCUMENT_DCS_PASSENGER_HISTO"<<assotable>> {
$fk("[[#{TKTEMD.FACT_TRAVEL_DOCUMENT_HISTO} TRAVEL_DOCUMENT_ID]]             Binary         FK")
$fk("[[#{DCSPAX.FACT_PASSENGER_HISTO} PASSENGER_ID]]                   Binary         FK")
$fk("[[#{TKTEMD.FACT_TRAVEL_DOCUMENT_HISTO} VERSION_TRAVEL_DOCUMENT]]        Long       NN  FK")
$fk("[[#{DCSPAX.FACT_PASSENGER_HISTO} VERSION_PASSENGER]]              Long       NN  FK")
DATE_BEGIN                     Timestamp        
DATE_END                       Timestamp        
IS_LAST_VERSION                Boolean          
REFERENCE_KEY_TRAVEL_DOCUMENT  String           
REFERENCE_KEY_PASSENGER        String           
}

entity "ASSO_COUPON_SEGMENT_DELIVERY_HISTO"<<assotable>> {
$fk("[[#{TKTEMD.FACT_COUPON_HISTO} COUPON_ID]]                       Binary         FK")
$fk("[[#{DCSPAX.FACT_SEGMENT_DELIVERY_HISTO} SEGMENT_DELIVERY_ID]]             Binary         FK")
$fk("[[#{TKTEMD.FACT_COUPON_HISTO} VERSION_TRAVEL_DOCUMENT]]         Long       NN  FK")
$fk("[[#{DCSPAX.FACT_SEGMENT_DELIVERY_HISTO} VERSION_PASSENGER]]               Long       NN  FK")
DATE_BEGIN                      Timestamp        
DATE_END                        Timestamp        
IS_LAST_VERSION                 Boolean          
REFERENCE_KEY_TRAVEL_DOCUMENT   String           
REFERENCE_KEY_COUPON            String           
REFERENCE_KEY_PASSENGER         String           
REFERENCE_KEY_SEGMENT_DELIVERY  String           
RELATED_TRAVEL_DOCUMENT_TYPE    String           
}

entity "ASSO_COUPON_SERVICE_DELIVERY_HISTO"<<assotable>> {
$fk("[[#{TKTEMD.FACT_COUPON_HISTO} COUPON_ID]]                       Binary         FK")
$fk("[[#{DCSPAX.FACT_SERVICE_DELIVERY_HISTO} SERVICE_DELIVERY_ID]]             Binary         FK")
$fk("[[#{TKTEMD.FACT_COUPON_HISTO} VERSION_TRAVEL_DOCUMENT]]         Long       NN  FK")
$fk("[[#{DCSPAX.FACT_SERVICE_DELIVERY_HISTO} VERSION_PASSENGER]]               Long       NN  FK")
DATE_BEGIN                      Timestamp        
DATE_END                        Timestamp        
IS_LAST_VERSION                 Boolean          
REFERENCE_KEY_TRAVEL_DOCUMENT   String           
REFERENCE_KEY_COUPON            String           
REFERENCE_KEY_PASSENGER         String           
REFERENCE_KEY_SERVICE_DELIVERY  String           
}

entity "ASSO_TRAVEL_DOCUMENT_FLIGHT_DATE_HISTO"<<assotable>> {
$fk("[[#{TKTEMD.FACT_TRAVEL_DOCUMENT_HISTO} TRAVEL_DOCUMENT_ID]]             Binary         FK")
$fk("[[#{SKD.FACT_FLIGHT_DATE_HISTO} FLIGHT_DATE_ID]]                 Binary         FK")
$fk("[[#{TKTEMD.FACT_TRAVEL_DOCUMENT_HISTO} VERSION_TRAVEL_DOCUMENT]]        Long       NN  FK")
$fk("[[#{SKD.FACT_FLIGHT_DATE_HISTO} VERSION_FLIGHT_DATE]]            Long       NN  FK")
DATE_BEGIN                     Timestamp        
DATE_END                       Timestamp        
IS_LAST_VERSION                Boolean          
REFERENCE_KEY_TRAVEL_DOCUMENT  String           
REFERENCE_KEY_FLIGHT_DATE      String           
}

entity "ASSO_COUPON_FLIGHT_SEGMENT_HISTO"<<assotable>> {
$fk("[[#{TKTEMD.FACT_COUPON_HISTO} COUPON_ID]]                      Binary         FK")
$fk("[[#{SKD.FACT_FLIGHT_SEGMENT_HISTO} FLIGHT_SEGMENT_ID]]              Binary         FK")
$fk("[[#{TKTEMD.FACT_COUPON_HISTO} VERSION_TRAVEL_DOCUMENT]]        Long       NN  FK")
$fk("[[#{SKD.FACT_FLIGHT_SEGMENT_HISTO} VERSION_FLIGHT_DATE]]            Long       NN  FK")
DATE_BEGIN                     Timestamp        
DATE_END                       Timestamp        
IS_LAST_VERSION                Boolean          
REFERENCE_KEY_TRAVEL_DOCUMENT  String           
REFERENCE_KEY_COUPON           String           
REFERENCE_KEY_FLIGHT_DATE      String           
REFERENCE_KEY_FLIGHT_SEGMENT   String           
}

entity "ASSO_COUPON_CODESHARE_FLIGHT_SEGMENT_HISTO"<<assotable>> {
$fk("[[#{TKTEMD.FACT_COUPON_HISTO} COUPON_ID]]                               Binary         FK")
$fk("[[#{SKD.FACT_CODESHARE_FLIGHT_SEGMENT_HISTO} CODESHARE_FLIGHT_SEGMENT_ID]]             Binary         FK")
$fk("[[#{TKTEMD.FACT_COUPON_HISTO} VERSION_TRAVEL_DOCUMENT]]                 Long       NN  FK")
$fk("[[#{SKD.FACT_CODESHARE_FLIGHT_SEGMENT_HISTO} VERSION_FLIGHT_DATE]]                     Long       NN  FK")
DATE_BEGIN                              Timestamp        
DATE_END                                Timestamp        
IS_LAST_VERSION                         Boolean          
REFERENCE_KEY_TRAVEL_DOCUMENT           String           
REFERENCE_KEY_COUPON                    String           
REFERENCE_KEY_FLIGHT_DATE               String           
REFERENCE_KEY_CODESHARE_FLIGHT_SEGMENT  String           
}

' Relationships
ASSO_RESERVATION_TRAVEL_DOCUMENT_HISTO --> PNR.FACT_RESERVATION_HISTO
ASSO_RESERVATION_TRAVEL_DOCUMENT_HISTO -up-> TKTEMD.FACT_TRAVEL_DOCUMENT_HISTO
ASSO_TRAVELER_TRAVEL_DOCUMENT_HISTO --> PNR.FACT_TRAVELER_HISTO
ASSO_TRAVELER_TRAVEL_DOCUMENT_HISTO -up-> TKTEMD.FACT_TRAVEL_DOCUMENT_HISTO
ASSO_AIR_SEGMENT_PAX_COUPON_HISTO --> PNR.FACT_AIR_SEGMENT_PAX_HISTO
ASSO_AIR_SEGMENT_PAX_COUPON_HISTO -up-> TKTEMD.FACT_COUPON_HISTO
ASSO_SERVICE_PAX_COUPON_HISTO --> PNR.FACT_SERVICE_PAX_HISTO
ASSO_SERVICE_PAX_COUPON_HISTO -up-> TKTEMD.FACT_COUPON_HISTO
ASSO_SEATING_PAX_COUPON_HISTO --> PNR.FACT_SEATING_PAX_HISTO
ASSO_SEATING_PAX_COUPON_HISTO -up-> TKTEMD.FACT_COUPON_HISTO
ASSO_TRAVEL_DOCUMENT_DCS_PASSENGER_HISTO -up-> TKTEMD.FACT_TRAVEL_DOCUMENT_HISTO
ASSO_TRAVEL_DOCUMENT_DCS_PASSENGER_HISTO --> DCSPAX.FACT_PASSENGER_HISTO
ASSO_COUPON_SEGMENT_DELIVERY_HISTO -up-> TKTEMD.FACT_COUPON_HISTO
ASSO_COUPON_SEGMENT_DELIVERY_HISTO --> DCSPAX.FACT_SEGMENT_DELIVERY_HISTO
ASSO_COUPON_SERVICE_DELIVERY_HISTO -up-> TKTEMD.FACT_COUPON_HISTO
ASSO_COUPON_SERVICE_DELIVERY_HISTO --> DCSPAX.FACT_SERVICE_DELIVERY_HISTO
ASSO_TRAVEL_DOCUMENT_FLIGHT_DATE_HISTO -up-> TKTEMD.FACT_TRAVEL_DOCUMENT_HISTO
ASSO_TRAVEL_DOCUMENT_FLIGHT_DATE_HISTO --> SKD.FACT_FLIGHT_DATE_HISTO
ASSO_COUPON_FLIGHT_SEGMENT_HISTO -up-> TKTEMD.FACT_COUPON_HISTO
ASSO_COUPON_FLIGHT_SEGMENT_HISTO --> SKD.FACT_FLIGHT_SEGMENT_HISTO
ASSO_COUPON_CODESHARE_FLIGHT_SEGMENT_HISTO -up-> TKTEMD.FACT_COUPON_HISTO
ASSO_COUPON_CODESHARE_FLIGHT_SEGMENT_HISTO --> SKD.FACT_CODESHARE_FLIGHT_SEGMENT_HISTO

@enduml
