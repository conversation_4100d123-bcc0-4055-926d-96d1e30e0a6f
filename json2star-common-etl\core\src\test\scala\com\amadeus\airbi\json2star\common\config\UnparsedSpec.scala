package com.amadeus.airbi.json2star.common.config

import com.amadeus.airbi.common.utils.CommonSpec
import pureconfig.ConfigSource
import pureconfig.error.ConfigReaderException
import pureconfig.generic.auto._

class UnparsedSpec extends CommonSpec {

  case class Base( // known by the core engine
    unknown: Unparsed
  )
  case class Delayed(config: Int)

  Unparsed.getClass.getName should s"delay parsing of unknown/unparsed field" in {
    // parse the top level
    val base = ConfigSource.string(""" unknown = { config = 8 } """).loadOrThrow[Base]
    base.unknown.parseAs[Delayed] should ===(Right(Delayed(8)))
  }

  it should s"fail gracefully when wrong type content" in {
    // parse the top level
    val base = ConfigSource.string(""" unknown = { config = "xx" } """).loadOrThrow[Base]
    base.unknown.parseAs[Delayed].left.get.toString() should include("WrongType")
  }

  it should s"fail gracefully when wrong content within unparsed object" in {
    // parse the top level
    val base = ConfigSource.string(""" unknown = { config_unexistent = "xx" } """).loadOrThrow[Base]
    base.unknown.parseAs[Delayed].left.get.prettyPrint() should include("Key not found: 'config'")
  }

  it should s"fail gracefully when unparsed attribute is not an object" in {
    val e = intercept[ConfigReaderException[Base]](ConfigSource.string(""" unknown = [ "something" ] """).loadOrThrow[Base])
    e.getMessage should include("Expected type OBJECT. Found LIST instead.")
  }

  it should s"represent correctly on toString" in {
    // parse the top level
    val base = ConfigSource.string(""" unknown = { config = 8 } """).loadOrThrow[Base]
    base.toString should include("""{"config":8}""")
  }

  case class RootConfDummy( // known by the core engine
    paramA: Option[String],
    paramB: Option[String],
    addons: Map[String, Unparsed]
  )
  case class Child(config: String) // unknown by the core engine

  it should s"delay parsing of unparsed field in map" in {
    // parse the top level
    val base = ConfigSource
      .string("""
param-a = "param A"
param-b = "param B"
addons = {
  addon1 = {
    config = "ext A"
  }
  addon2 = {
    config = "ext B"
  }
}
      """)
      .loadOrThrow[RootConfDummy]
    base.paramA should ===(Some("param A"))
    base.paramB should ===(Some("param B"))

    // parse the unparsed field by bits
    base.addons.get("addon1").map(_.parseAs[Child]) should ===(Some(Right(Child("ext A"))))
    base.addons.get("addon2").map(_.parseAs[Child]) === (Some(Right(Child("ext B"))))
  }

}
