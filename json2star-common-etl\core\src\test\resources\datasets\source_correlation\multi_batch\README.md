# Integration test for source correlation tables

---------------------------------------
SCENARIO - multi-batch - OK
1) when process json with 1 PAX
   TABLE: then in table there is
   pax1 v1 true (inserted row)

FILE: and then in file there is
folder is_last=true/
- file1     (new file)
    - pax1 v1 (inserted row)

CORR: and then consumer receives all rows from file1
- pax1 v1 true

2) when process json with PAX1-v2 (1 update only on PAX1)
   TABLE: then in table there is
   pax1 v1 false (updated row)
   pax1 v2 true  (inserted row)

FILE: then in file there is
folder is_last=true/
- file1 (unchanged file) -- marked in delta_log as obsolete to delete
    - pax1 v1
- file2_1 (new file)
    - pax1 v2        (inserted row)
folder is_last=false/
- file2_2 (new file)
    - pax1 v1 (updated row)

3) when process json with PAX1-v3 (1 update only on PAX1)
   TABLE: then in table there is
   pax1 v1 false (unchanged row)
   pax1 v2 false (updated row)
   pax1 v3 true  (inserted row)

FILE: then in file there is
folder is_last=true/
- file1 (unchanged file) -- marked in delta_log as obsolete to delete
    - pax1 v1
- file2_1 (unchanged file) -- marked in delta_log as obsolete to delete
    - pax1 v2
- file3_1 (new file)
    - pax1 v3        (inserted row)
      folder is_last=false/
- file2_2 (unchanged file) -- marked in delta_log as obsolete to delete
    - pax1 v1
- file3_2 (new file)
    - pax1 v2 (updated row)

CORR: and then consumer receives all rows from file2_1 and file2_2
- pax1 v1 false  (updated row)
- pax1 v2 false  (updated row)
- pax1 v3 true   (inserted row)