package com.amadeus.airbi.json2star.common.addons.base.latest

import com.amadeus.airbi.json2star.common.Schema.TableKind
import com.amadeus.airbi.json2star.common.addons.base.{correlation, Addon}
import com.amadeus.airbi.json2star.common.addons.base.correlation.{Correlation, SourceCorrelation}
import com.amadeus.airbi.json2star.common.addons.base.mapping.Mapping
import com.amadeus.airbi.json2star.common.{ColumnDef, Schema, TableDef}
import com.amadeus.airbi.rawvault.common.application.config._

object Latest extends Addon[LatestConfig] {

  private def dropColumns(s: Schema, cols: Seq[String]): Schema =
    s.copy(columns = s.columns.filterNot(c => cols.contains(c.name)))

  override def getConfig(t: TableConfig): Option[LatestConfig] = t.latest
  override def enrichSchema(c: TablesConfig, t: TableConfig, a: LatestConfig, l: Schema): Schema = {
    val srcHistoTable = new TableDef(c.tableByName(a.histoTableName), c)
    val srcHistoIsCorr = srcHistoTable.hasAddon(correlation.Correlation)
    val srcHistoIsMap = srcHistoTable.hasAddon(Mapping)
    val srcHistoIsSourceCorr = srcHistoTable.hasAddon(correlation.SourceCorrelation)

    val schema = (srcHistoIsCorr, srcHistoIsMap, srcHistoIsSourceCorr) match {
      case (Some(corr), None, None) => // source (histo) table is a correlation table
        // Primary Keys are defined by construction
        val keyCols = Seq(corr.addonConfig.target.domainAKey.name, corr.addonConfig.target.domainBKey.name)
        val c = corr.addonConfig.target
        val latestCols = dropColumns(srcHistoTable.schema, a.columnsToDrop)
          .drop(c.endDate)
          .drop(c.startDate)
          .drop(c.isLast)
          .columns
          .map { hc =>
            hc.copy(
              belongsToPK = keyCols.contains(hc.name),
              fk = if (hc.name == c.domainBVersion.name || hc.name == c.domainAVersion.name) None else fkFromHisto(hc)
            )
          }
        Schema(
          name = t.name,
          columns = latestCols,
          description = generateDescription(srcHistoTable.schema.name),
          kind = Schema.View(generateViewQuery(srcHistoTable, c.isLast, latestCols, a.additionalFilterExpression)),
          subdomain = l.subdomain,
          subdomainMainTable = l.subdomainMainTable
        )
      case (None, None, Some(sourceCorr)) => // source (histo) table is a source correlation table
        // Primary Keys are defined by construction
        val keyCols = Seq(sourceCorr.addonConfig.target.domainAKey, sourceCorr.addonConfig.target.domainBKey)
        val c = sourceCorr.addonConfig.target
        val latestCols =
          dropColumns(srcHistoTable.schema, a.columnsToDrop)
            .drop(c.endDate)
            .drop(c.startDate)
            .drop(c.isLast)
            .columns
            .map { hc =>
              hc.copy(
                belongsToPK = keyCols.contains(hc.name),
                fk = if (hc.name == c.domainBVersion || hc.name == c.domainAVersion) None else fkFromHisto(hc)
              )
            }
        Schema(
          name = t.name,
          columns = latestCols,
          description = generateDescription(srcHistoTable.schema.name),
          kind = Schema.View(generateViewQuery(srcHistoTable, c.isLast, latestCols, a.additionalFilterExpression)),
          subdomain = l.subdomain,
          subdomainMainTable = l.subdomainMainTable
        )
      case (None, Some(map), None) => // source (histo) table is a mapping table
        map.addonConfig.pit match {
          case MasterPitTable(master) =>
            generateSchemaFromMasterPit(t, srcHistoTable, master, a.additionalFilterExpression, a.columnsToDrop, l)
          case SecondaryPitTable() =>
            val masterName =
              srcHistoTable.general.tables.flatMap(_.mapping).flatMap(_.masterPit).head // assume only one pit master
            val master = masterName.master
            generateSchemaFromMasterPit(t, srcHistoTable, master, a.additionalFilterExpression, a.columnsToDrop, l)
          case NoPitTable() =>
            throw new IllegalArgumentException("Source with Mapping must contain Pit that is Master or Secondary!")
        }
      case _ => throw new IllegalArgumentException("Source table must be either Mapping or Correlation!")
    }
    schema
  }

  /** Builds the fk element from the histo table configuration, dropping the _HISTO suffix for the referenced table
    * (latest tables foreign keys reference other latest tables, and not histo tables).
    */
  private def fkFromHisto(c: ColumnDef) = {
    c.fk.map(l => l.map(f => f.copy(table = f.table.replaceAll("_HISTO$", ""))))
  }

  private def generateViewQuery(
    srcHistoTable: TableDef,
    isLastName: String,
    cols: List[ColumnDef],
    where: Option[String]
  ): String = {
    val extraWhere = where.map(w => s" AND ($w)").getOrElse("")
    s"SELECT ${cols.map(_.name).mkString(",")} FROM ${srcHistoTable.table.name} WHERE ${isLastName}=true$extraWhere"
  }

  private def generateSchemaFromMasterPit(
    t: TableConfig,
    srcHistoTable: TableDef,
    pitConf: PitTransformation,
    extraWhere: Option[String],
    columnsToDrop: Seq[String],
    l: Schema
  ) = {
    val cols =
      dropColumns(srcHistoTable.schema, columnsToDrop)
        .drop(pitConf.validTo)
        .drop(pitConf.validFrom)
        .drop(pitConf.isLast)
        .columns
        .map { c =>
          if (c.name == pitConf.pitVersion) {
            c.copy(belongsToPK = false, fk = None)
          } else {
            c.copy(fk = fkFromHisto(c))
          }
        }
    val viewQuery = generateViewQuery(srcHistoTable, pitConf.isLast, cols, extraWhere)
    Schema(
      name = t.name,
      columns = cols,
      description = generateDescription(srcHistoTable.schema.name),
      kind = Schema.View(viewQuery),
      subdomain = l.subdomain,
      subdomainMainTable = l.subdomainMainTable
    )
  }

  private def generateDescription(histoTableName: String): Option[TableDescription] = {
    Some(
      TableDescription(
        description = Some(s"Latest view of $histoTableName"),
        granularity = Some(s"Same as $histoTableName, considering only the latest version")
      )
    )
  }

  override def validate(c: TablesConfig, t: TableConfig): Unit = {
    // TODO
  }
}
