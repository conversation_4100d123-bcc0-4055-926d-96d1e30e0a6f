package com.amadeus.airbi.json2star.common.addons.stackable.proration

import com.amadeus.airbi.json2star.common.addons.base.mapping.Table.KeyValueRow
import com.amadeus.airbi.json2star.common.addons.stackable.Proration
import com.amadeus.airbi.json2star.common.addons.stackable.proration.ProrationAddon.NON_VALID_SEQUENCE_NUMBER
import com.amadeus.airbi.json2star.common.addons.stackable.proration.ProrationColumns._
import com.amadeus.airbi.json2star.common.addons.stackable.proration.ProrationInput.CouponKey
import com.amadeus.airbi.json2star.common.extdata.{ExtData, PorsExtData, PorsExtDataType}
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.scala.DefaultScalaModule
import com.jayway.jsonpath.DocumentContext

import java.time.LocalDate
import scala.util.Try

/** Ticket level input for proration library
  * @param pors point of reference
  * @param fareCalcLine fare calculation line
  * @param issueDate ticket issue date
  * @param paymentCurrency payment currency
  * @param paymentTotal payment total
  * @param paymentTotalTaxes payment total taxes
  * @param itinerary ticket itinerary, expressed as a sequence of CouponKey
  */
@JsonIgnoreProperties(Array("pors"))
case class ProrationInput(
  pors: PorsExtData,
  fareCalcLine: String,
  issueDate: Option[LocalDate],
  paymentCurrency: String,
  paymentTotal: String,
  paymentTotalTaxes: String,
  itinerary: Seq[CouponKey]
)

object ProrationInput {

  private val EMPTY_STRING = ""

  /** Coupon key
    * @param sequenceNumber coupon sequence number
    * @param departureAirport sold departure airport
    * @param arrivalAirport sold arrival airport
    */
  case class CouponKey(sequenceNumber: Int, departureAirport: String, arrivalAirport: String)

  def build(
    rows: Seq[KeyValueRow],
    config: Proration,
    jsonRoot: DocumentContext,
    extData: ExtData
  ): ProrationInput = {
    ProrationInput(
      pors = extData.get[PorsExtData](PorsExtDataType),
      fareCalcLine = Option(jsonRoot.read[String](config.jsonPathFareCalc)).getOrElse(EMPTY_STRING),
      issueDate = getIssueDate(rows),
      paymentCurrency = Option(jsonRoot.read[String](config.jsonPathPriceCurrencyPayment)).getOrElse(EMPTY_STRING),
      paymentTotal = Option(jsonRoot.read[String](config.jsonPathPriceTotalPayment)).getOrElse(EMPTY_STRING),
      paymentTotalTaxes = Option(jsonRoot.read[String](config.jsonPathPriceTotalTaxesPayment)).getOrElse(EMPTY_STRING),
      itinerary = buildItinerary(rows)
    )
  }

  def buildItinerary(rows: Seq[KeyValueRow]): Seq[CouponKey] = {
    val couponKeys = rows.map { row => buildCouponKey(row) }

    if (couponKeys.contains(None)) { Seq() }
    else { couponKeys.flatten }
  }

  /** Build a CouponKey from a row.
    * If the sequence number is not convertible to integer, it will be set to NON_VALID_SEQUENCE_NUMBER.
    * If the SOLD_DEPARTURE_AIRPORT or the SOLD_ARRIVAL_AIRPORT is not retrievable, we return None.
    */
  def buildCouponKey(row: KeyValueRow): Option[CouponKey] = {
    val sequenceNumber = Try { row(SEQUENCE_NUMBER).toInt }.toOption.getOrElse(NON_VALID_SEQUENCE_NUMBER)

    for {
      departureAirport <- row.get(SOLD_DEPARTURE_AIRPORT)
      arrivalAirport <- row.get(SOLD_ARRIVAL_AIRPORT)
    } yield CouponKey(sequenceNumber, departureAirport, arrivalAirport)
  }

  /** Check that the column DOCUMENT_CREATION_DATE has the same value for all rows (coupons), and extract that value.
    * If the column has different values, or it is a non parsable date, return None.
    */
  def getIssueDate(rows: Seq[KeyValueRow]): Option[LocalDate] = {
    val issueDates = rows.map { row => row(DOCUMENT_CREATION_DATE) }
    if (issueDates.distinct.size == 1) {
      Try { LocalDate.parse(issueDates.head) }.toOption
    } else {
      None
    }
  }

  def asJsonDebugString(input: ProrationInput): String = {
    val mapper = new ObjectMapper()
    mapper.registerModule(DefaultScalaModule)
    mapper.registerModule(new JavaTimeModule());
    mapper.writeValueAsString(input)
  }

}
