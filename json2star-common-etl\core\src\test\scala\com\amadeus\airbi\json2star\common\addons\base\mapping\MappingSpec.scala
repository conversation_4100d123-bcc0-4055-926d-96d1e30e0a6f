package com.amadeus.airbi.json2star.common.addons.mapping

import com.amadeus.airbi.common.utils.CommonSpec
import com.amadeus.airbi.json2star.common.addons.base.mapping.Mapping
import com.amadeus.airbi.json2star.common.app.Json2StarApp.readMappingConfig
import com.amadeus.airbi.json2star.common.{Schema, TablesDef}
import com.amadeus.airbi.rawvault.common.application.config.TableDescription
import com.amadeus.airbi.rawvault.common.config.ColumnType._
import com.amadeus.airbi.rawvault.common.testfwk.SpecHelper
import com.amadeus.airbi.rawvault.common.testfwk.ImplicitConversions.OriginConverters

import scala.List

class MappingSpec extends CommonSpec with SpecHelper {

  Mapping.getClass.getName should "generate columns DDL" in {
    val mp = readMappingConfig("views/sample_4/mapping.conf")
    val schemas = TablesDef.consolidate(mp).tables.map(_.schema)
    schemas should ===(
      List(
        Schema(
          name = "FACT_RESERVATION_HISTO",
          columns = List(
            columnDef("RESERVATION_ID", strColumn, isMandatory = true, belongsToPK = true,
              origins = Seq("$.mainResource.current.image.id", "$.mainResource.current.image.dummy.id").map(_.toOrigin("hashM")), preExpr = Some("hashM({0})")),
            columnDef("VERSION", intColumn, isMandatory = true, belongsToPK = true, origins = Seq("$.mainResource.current.image.version", "$.mainResource.current.image.dummy.version").map(_.toOrigin)),
            columnDef("PNR_CREATION_DATE", timestampColumn, isMandatory = false, belongsToPK = false,
              origins = Seq("$.mainResource.current.image.creation.dateTime", "$.mainResource.current.image.dummy.creation.dateTime").map(_.toOrigin)),
            columnDef("DATE_BEGIN", timestampColumn, isMandatory = false, belongsToPK = false,
              origins = Seq("$.mainResource.current.image.lastModification.dateTime", "$.mainResource.current.image.dummy.lastModification.dateTime").map(_.toOrigin)),
            columnDef("DATE_END", timestampColumn, isMandatory = false, belongsToPK = false),
            columnDef("IS_LAST_VERSION", booleanColumn, isMandatory = false, belongsToPK = false),
            columnDef("LOAD_DATE", timestampColumn, isMandatory = false, belongsToPK = false)
          ),
          description = Some(TableDescription(
            description = Some("It contains information related to the booking on PNR-level."),
            granularity = Some("1 PNR, version")
          )),
          partitionColumn = Some("IS_LAST_VERSION"),
          kind = Schema.Materialized
        ),
        Schema(
          name = "DIM_POINT_OF_SALE",
          columns = List(
            columnDef("COMMON_COLUMN", strColumn, isMandatory = false, belongsToPK = false,
              origins = Seq(
                "$.mainResource.current.image.owner.office.id",
                "$.mainResource.current.image.creation.pointOfSale.office.id",
                "$.mainResource.current.image.lastModification.pointOfSale.office.id"
              ).map(_.toOrigin)),
            columnDef("POINT_OF_SALE_ID", strColumn, isMandatory = true, belongsToPK = true,
              origins = Seq(
                "$.mainResource.current.image.owner.office.id - $.mainResource.current.image.owner.login.cityCode",
                "$.mainResource.current.image.creation.pointOfSale.office.id - $.mainResource.current.image.creation.pointOfSale.login.cityCode",
                "$.mainResource.current.image.lastModification.pointOfSale.office.id - $.mainResource.current.image.lastModification.pointOfSale.login.iataNumber"
              ).map(_.toOrigin("hashM")), preExpr = Some("hashM({0})")),
            columnDef("COL_PRESENT_ONLY_IN_A_ROOT", strColumn, isMandatory = false, belongsToPK = false,
              origins = Seq("$.mainResource.current.image.owner.office.id").map(_.toOrigin)),
            columnDef("LOAD_DATE", timestampColumn, isMandatory = false, belongsToPK = false)
          ),
          description = None,
          kind = Schema.Materialized
        ),
        Schema(
          name = "DIM_CARRIER",
          columns = List(
            columnDef("CARRIER_ID", longColumn, isMandatory = true, belongsToPK = true,
              origins = Seq(
                "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.marketing.flightDesignator.carrierCode.value",
                "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.operating.flightDesignator.carrierCode.value",
                "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].flightSegment.marketing.flightDesignator.carrierCode.value"
              ).map(_.toOrigin("hashXS")), preExpr = Some("hashXS({0})")),
            columnDef("CARRIER", strColumn, isMandatory = false, belongsToPK = false,
              origins = Seq(
                "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.marketing.flightDesignator.carrierCode.value",
                "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.operating.flightDesignator.carrierCode.value",
                "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].flightSegment.marketing.flightDesignator.carrierCode.value"
              ).map(_.toOrigin)),
            columnDef("LOAD_DATE", timestampColumn, isMandatory = false, belongsToPK = false)
          ),
          description = None,
          kind = Schema.Materialized
        )
      )
    )
  }

  Mapping.getClass.getName should "fail when trying to generate columns be cause of cyclic definitiions" in {
    val mp = readMappingConfig("views/sample_4/failing_mapping_with_variables.conf")
    val e = intercept[IllegalAccessException]{
      TablesDef.consolidate(mp).tables.toList
    }
    e.getMessage should include("is caught in a cyclic dependency")
  }

  Mapping.getClass.getName should "fail when trying to generate columns DDL because of wrong source type" in {
    val mp = readMappingConfig("views/sample_4/failing_mapping_with_variables_literal.conf")
    val e = intercept[IllegalArgumentException]{
      TablesDef.consolidate(mp).tables.toList
    }
    e.getMessage should include("Only BlockSource supported when variables are configured")
  }
  Mapping.getClass.getName should "fail when trying to generate columns DDL because of unknown column" in {
    val mp = readMappingConfig("views/sample_4/failing_mapping_with_variables_unknown.conf")
    val e = intercept[NoSuchFieldException]{
      TablesDef.consolidate(mp).tables.toList
    }
    e.getMessage should include("is not defined in mapping configuration")
  }

  Mapping.getClass.getName should "not fail when trying to generate columns DDL" in {
    val mp = readMappingConfig("views/sample_4/not_failing_mapping_with_variables.conf")
    val schemas = TablesDef.consolidate(mp).tables.map(_.schema)
    schemas should not be ('empty)

  }
}
