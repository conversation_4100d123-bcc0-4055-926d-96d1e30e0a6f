package com.amadeus.airbi.rawvault.common.io

import java.time.format.DateTimeFormatter

/** From https://docs.microsoft.com/en-us/azure/event-hubs/event-hubs-capture-overview
  * {
  * "type":"record",
  * "name":"EventData",
  * "namespace":"Microsoft.ServiceBus.Messaging",
  * "fields":[
  * {"name":"SequenceNumber","type":"long"},
  * {"name":"Offset","type":"string"},
  * {"name":"EnqueuedTimeUtc","type":"string"},
  * {"name":"SystemProperties","type":{"type":"map","values":["long","double","string","bytes"]}},
  * {"name":"Properties","type":{"type":"map","values":["long","double","string","bytes"]}},
  * {"name":"Body","type":["null","bytes"]}
  * ]
  * }
  */

object AvroFile {

  val SystemEnqueuedTimePropertyName = "x-opt-enqueued-time"

  object FieldsName extends Enumeration {
    type FieldsName = Value
    val SequenceNumber, Offset, EnqueuedTimeUtc, SystemProperties, Properties, Body = Value
  }

  val AzureCaptureDateTimeFormat =
    DateTimeFormatter
      .ofPattern("M/d/yyyy h:mm:ss a") // 4/19/2021 9:43:03 AM
      .withZone(java.time.ZoneOffset.UTC)
}
