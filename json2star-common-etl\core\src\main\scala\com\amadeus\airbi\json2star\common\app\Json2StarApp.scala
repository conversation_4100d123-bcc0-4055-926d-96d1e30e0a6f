package com.amadeus.airbi.json2star.common.app

import com.amadeus.airbi.json2star.common.Schema.Materialized
import com.amadeus.airbi.json2star.common.TablesDef
import com.amadeus.airbi.json2star.common.addons.base.correlation.{
  Correlation,
  CorrelationPipeline,
  SourceCorrelation,
  SourceCorrelationPipeline
}
import com.amadeus.airbi.json2star.common.addons.base.mapping.Mapping
import com.amadeus.airbi.json2star.common.addons.base.{AddonTable, mapping}
import com.amadeus.airbi.json2star.common.eventgrid.{DefaultAccessTokenProvider, EventPublisher}
import com.amadeus.airbi.json2star.common.metadata._
import com.amadeus.airbi.json2star.common.optimize.Optimize
import com.amadeus.airbi.json2star.common.resize.{DatabricksUtils, DefaultDatabricksUtils}
import com.amadeus.airbi.rawvault.common.RootConfig
import com.amadeus.airbi.rawvault.common.application.config.{MappingConfig, ModelConfigLoader, TablesConfig}
import com.amadeus.airbi.rawvault.common.vault.hashers.StarSchemaHasher
import com.amadeus.airbi.rawvault.common.vault.spark.EventsListener
import com.typesafe.scalalogging.Logger
import org.apache.spark.sql.SparkSession
import org.json4s.DefaultFormats
import org.json4s.jackson.Serialization.{write => asJson}
import org.slf4j.LoggerFactory

import scala.concurrent.duration.Duration
import scala.concurrent.{Await, Future}
import scala.util.{Failure, Success, Try}

object Json2StarApp extends Json2StarFwk {
  def main(args: Array[String]): Unit = {
    runSpark(args)
  }
}

trait Json2StarFwk {
  @transient
  lazy val logger: Logger = Logger(LoggerFactory.getLogger(getClass.getName))

  def runSpark(args: Array[String]): Unit = {
    val rootConfig = RootConfig.fromArgs(args)
    val model =
      readMappingConfig(rootConfig.modelConfFilePath, rootConfig.tablesSelectors, rootConfig.disabledStackableAddons)

    logger.info(s"### ROOT CONFIG: ${asJson(rootConfig)(DefaultFormats)}")
    logger.info(s"### MODEL: ${asJson(model)(DefaultFormats)}")

    implicit val spark: SparkSession = SparkSession
      .builder()
      .getOrCreate()

    val eventsListener = new EventsListener(rootConfig.processingParams)
    spark.sparkContext.addSparkListener(eventsListener)
    run(spark, rootConfig, model, Some(Metrics.register(spark)), DefaultDatabricksUtils)
    eventsListener.report()
  }

  def run(
    spark: SparkSession,
    conf: RootConfig,
    tablesConf: TablesConfig,
    metricsOpt: Option[Metrics] = None,
    dbx: DatabricksUtils = DefaultDatabricksUtils
  ): Unit = {
    // ensure we have the metrics (for tests)
    val metrics = metricsOpt.getOrElse(Metrics.register(spark))

    logger.info("Starting ingestion pipeline...")
    logger.info("Estimated number of cores: " + spark.sparkContext.defaultParallelism)

    // Create processing context
    val context = ProcessingContext(conf.processingParams)

    // Register custom UDFs before doing anything
    StarSchemaHasher.registerUdfs(spark, context.processingParams.hashDebug)

    // METADATA
    val metadataEnabled = context.processingParams.enableMetadata
    val metadata = new JobRunMetadata(
      spark,
      conf.etl.common.outputDatabase,
      conf.etl.common.domain,
      conf.jobInfo.jobId,
      conf.jobInfo.jobRunId,
      metadataEnabled
    )
    metadata.logStart()

    val pipelineStatus = Try {
      // Initialize the common pipeline context
      val pipelineContext = new PipelineContext(conf, spark, dbx, context)

      // Consolidate table definitions
      val tablesDef = TablesDef.consolidate(tablesConf)

      val etlOrPartialEtlIsEnabled = conf.processingSelectors.exists(s => s == RootConfig.EtlMode || s == RootConfig.PartialEtlMode)

      // MAPPING
      val mappingQualityMetrics = if (etlOrPartialEtlIsEnabled) {
        checkPartialReprocessingConf(tablesDef, conf)
        val mappingPipeline = mapping.MappingPipeline(pipelineContext, tablesConf.mappingRecordFilter, metrics)
        val mappingTables = AddonTable.fromTablesDef(Mapping)(tablesDef).filter(t => shouldRunMappingAddonOnTable(conf, t))
        if (mappingTables.nonEmpty) {
          mappingPipeline.run(mappingTables)
          val metrics = mappingPipeline.metrics
          logger.info(s"Number of successfully transformed json messages: ${metrics.transformed.value}")
          logger.info(s"Number of dropped json messages (not eligible): ${metrics.dropped.value}")
          logger.info(s"Number of failed json message transformations: ${metrics.transformErrors.value}")
          logger.info(s"Number of rows extracted per table: \n${metrics.tableRowCounts.value.mkString("\n")}")
          Display.display(s"Total number of processed records: ${metrics.totProcessed}", conf, logger)
          Some(MappingQualityMetrics.fromMetrics(metrics))
        } else {
          None
        }
      } else {
        logger.info("Mapping is disabled")
        None
      }

      // CORRELATION
      val correlationQualityMetrics = if (etlOrPartialEtlIsEnabled) {
        import pipelineContext.xc
        val corrTablesDef = AddonTable.fromTablesDef(Correlation)(tablesDef)
        val corrTables = corrTablesDef.map { t =>
          val cp = new CorrelationPipeline(spark, conf, t.table, t.addonConfig)
          cp.runStream()
        }
        val srcCrrTablesDef = AddonTable.fromTablesDef(SourceCorrelation)(tablesDef)
        val srcCorrTables = srcCrrTablesDef.map { t =>
          SourceCorrelationPipeline.runAndAwaitStream(spark, conf, t.table, t.addonConfig)
        }
        Await.result(Future.sequence(corrTables ++ srcCorrTables), Duration.Inf)

        None // no correlation metrics for the moment
      } else {
        logger.info("Correlation is disabled")
        None
      }

      // OPTIMIZE
      if (conf.processingSelectors.contains(RootConfig.OptimizeMode)) {
        val optimize = new Optimize(pipelineContext)
        optimize.optimize(tablesDef)
      } else {
        logger.info("Optimize is disabled")
      }

      // Shutdown pipeline execution context
      pipelineContext.shutdown()

      // Expose quality metadata
      val qualityMetrics = JobRunMetadataDetails()
        .withMetric(MappingQualityMetrics.name, mappingQualityMetrics)
        .withMetric(CorrelationQualityMetrics.name, correlationQualityMetrics)
      qualityMetrics
    }

    pipelineStatus match {
      case Failure(exception) =>
        metadata.logFailure(JobRunMetadataDetails.fromThrowable(exception))
        throw exception
      case Success(m) =>
        metadata.logSuccess(m)
        EventPublisher.run(conf, spark, dbx, DefaultAccessTokenProvider)
    }

  }

  /** Read and validate the model config from a file on fs or from a resource file
    */
  // TODO no more mapping, it is called model now
  def readMappingConfig(
    confFile: String,
    selector: Set[String] = Set.empty[String],
    disabledStackableAddons: Set[String] = Set.empty[String]
  ): TablesConfig = {
    ModelConfigLoader.defaultLoad(confFile, selector, disabledStackableAddons)
  }

  /** Check the partial reprocessing configuration:
    *  - Check that the partial reprocessing section is present in the config if the partial etl processing selector is present
    *  - Check that the tables to reprocess are in the model
    *  - Check that only materialized tables are in the list of tables to reprocess
    *
    * @param tablesDef a TablesDef object containing the model
    * @param conf the RootConfig object containing the configuration
    */
  private def checkPartialReprocessingConf(
    tablesDef: TablesDef,
    conf: RootConfig
  ): Unit = {
    if (conf.processingSelectors.contains(RootConfig.PartialEtlMode)) {
      conf.partialReprocessingParams match {
        case Some(prParams) =>
          val tablesToReprocess = prParams.tablesToReprocess.map(_.trim.toUpperCase())
          val tablesInModel = tablesDef.tables.map(_.table.name)
          val missingTables = tablesToReprocess.filterNot(tablesInModel.contains)
          if (missingTables.nonEmpty) {
            throw new RuntimeException(
              s"Partial reprocessing is enabled on the following tables which are not in the model: ${missingTables.mkString(", ")}"
            )
          }
          val nonMaterializedTables = tablesToReprocess.filterNot(tableName => {
            val table = tablesDef.tables.find(_.table.name == tableName).get
            table.schema.kind == Materialized
          })
          if (nonMaterializedTables.nonEmpty) {
            throw new RuntimeException(
              s"Partial reprocessing is enabled on the following tables which are non materialized: ${nonMaterializedTables
                .mkString(", ")}"
            )
          }
        case None =>
          throw new RuntimeException(
            s"Partial reprocessing is not configured. Set 'partial-reprocessing-params' in the app config file"
          )
      }
    }
  }

  /** Determines if the mapping addon should be run on the specified table, depending on whether the partial
    * reprocessing ETL mode is enabled and on the partial reprocessing parameters
    *
    * @param conf the RootConfig
    * @param addonTable the AddonTable to check
    * @return true if the mapping addon should be run on the table, false otherwise
    */
  private def shouldRunMappingAddonOnTable(conf: RootConfig, addonTable: AddonTable[MappingConfig]): Boolean = {
    if (conf.processingSelectors.contains(RootConfig.PartialEtlMode)) {
      // We only reprocess tables that are in the list of tables to reprocess + the master PIT table
      val tableConfig = addonTable.table
      conf.partialReprocessingParams.get.tablesToReprocess
        .contains(tableConfig.name) || tableConfig.mapping.get.masterPit.isDefined
    } else {
      true // We process all tables in full ETL mode
    }
  }
}
