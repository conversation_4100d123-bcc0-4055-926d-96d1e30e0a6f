package com.amadeus.airbi.rawvault.common

import com.amadeus.airbi.json2star.common.config.AppConfig.Secret
import com.amadeus.airbi.json2star.common.config.{AppConfig, EtlConfig, EventGridParams, ProcessingParams, PartialReprocessingParams}
import com.typesafe.scalalogging.Logger
import org.slf4j.LoggerFactory

case class RootConfig private (
  etl: EtlConfig,
  jobInfo: JobInfo,
  modelConfFilePath: String,
  inputDatabases: Map[String, String],
  processingParams: ProcessingParams,
  tablesSelectors: Set[String],
  disabledStackableAddons: Set[String],
  eventGridParams: Option[EventGridParams],
  workspaceSecrets: Option[Map[String, Secret]] = None,
  isLight: Boolean = false,
  processingSelectors: Set[String] = Set.empty[String],
  partialReprocessingParams: Option[PartialReprocessingParams] = None
)

case class JobInfo(jobId: String, jobRunId: String)

object RootConfig {
  lazy val logger: Logger = Logger(LoggerFactory.getLogger(getClass.getName))

  // Processing selectors
  val EtlMode = "etl"
  val PartialEtlMode = "partial-etl"
  val OptimizeMode = "optimize"

  // TODO use named parameters like scallop
  def fromArgs(args: Array[String]): RootConfig = {
    // Expected arguments:
    //   0 -> Job ID
    //   1 -> Job Run ID
    //   2 -> App conf file
    //   3 -> Processing selectors (comma-separated)
    val jobInfo = JobInfo(jobId = args(0), jobRunId = args(1))
    val processingSelectors = args(3).split(",").map(_.trim.toLowerCase).toSet
    RootConfig(confFile = args(2), jobInfo, processingSelectors)
  }

  def apply(confFile: String, jobInfo: JobInfo, processingSelectors: Set[String]): RootConfig = {
    val appConfig = AppConfig.apply(confFile)
    RootConfig(
      jobInfo = jobInfo,
      etl = EtlConfig(appConfig.common, appConfig.stream),
      modelConfFilePath = appConfig.modelConfFile,
      inputDatabases = appConfig.inputDatabases.getOrElse(Map.empty),
      processingParams = appConfig.processingParams.getOrElse(ProcessingParams()),
      tablesSelectors = appConfig.tablesSelectors,
      disabledStackableAddons = appConfig.disabledStackableAddons,
      eventGridParams = appConfig.eventGridParams,
      workspaceSecrets = appConfig.workspaceSecrets,
      processingSelectors = processingSelectors,
      partialReprocessingParams = appConfig.partialReprocessingParams
    )
  }
}
