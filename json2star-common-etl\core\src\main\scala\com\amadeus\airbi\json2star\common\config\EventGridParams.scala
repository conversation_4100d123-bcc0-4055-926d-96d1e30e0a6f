package com.amadeus.airbi.json2star.common.config

import com.amadeus.airbi.json2star.common.resize.DatabricksUtils

import scala.util.Try

/** Event Grid Parameters
  *
  * @param topicUrl Event Grid Namespace Topic URL (e.g. https://<EVENT-GRID-NAMESPACE>.eventgrid.azure.net/topics/<EVENT-GRID-TOPIC>:publish?api-version=<API-VERSION>
  * @param topicName Event Grid topic name. This value is used to fill the source field in the JSON CloudEvent message to send
  * @param serviceProviderTenantId tenant ID of the Service Provider
  * @param serviceProviderAppId application ID of the Service Provider
  * @param dbxSecretScope databricks secret scope for the Service Provider Secret
  * @param dbxSecretKey  databricks secret key for the Service Provider Secret
  * @param managedAppResourceId resource ID of the managed application (e.g. /subscriptions/<subscription-id>/resourceGroups/<resource-group-name>/providers/Microsoft.Solutions/applications/<managed-app-name>)
  * @param isBlocking optional flag to block the execution in case of error
  * @param httpLogDetailLevel optional http log level for the http client (e.g. default is BASIC, other options are HEADERS_ONLY, BODY_AND_HEADERS, NONE
  *                     refer to https://learn.microsoft.com/en-us/java/api/com.azure.core.http.policy.httplogdetaillevel?view=azure-java-stable#fields
  */
case class EventGridParams(
  topicUrl: String,
  topicName: String,
  serviceProviderTenantId: String,
  serviceProviderAppId: String,
  dbxSecretScope: String,
  dbxSecretKey: String,
  managedAppResourceId: String,
  isBlocking: Boolean = true,
  httpLogDetailLevel: Option[String] = None
) {

  def serviceProviderAppSecretValue(dbx: DatabricksUtils): Either[String, String] = {
    Try(dbx.getSecret(dbxSecretScope, dbxSecretKey)).fold(
      error =>
        Left(
          s"[event-grid] ERROR: missing secret in databricks. Check scope '$dbxSecretScope' and key '$dbxSecretKey' in databricks secrets"
        ),
      secret => Right(secret)
    )
  }

}
