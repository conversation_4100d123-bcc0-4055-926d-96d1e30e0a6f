package com.amadeus.airbi.json2star.common.optimize

import io.delta.tables.DeltaTable
import org.apache.spark.sql.{Column, SparkSession}
import org.apache.spark.sql.functions.{coalesce, col, lit, max}
import org.apache.spark.sql.types.StructType

import java.sql.Timestamp
import java.time.{Instant, ZoneId}

/** Internal Optimize metadata table
  *
  * This class manages the INTERNAL_OPTIMIZE_METADATA table.
  * This delta table is used to keep track of the last time we run an optimize on the configured tables.
  */
case class InternalOptimizeMetadata(
  spark: SparkSession
) {
  import InternalOptimizeMetadata._
  import InternalOptimizeMetadataTable._
  import spark.implicits._

  val DstAlias = "dst"
  val FalseCondition = "1 == 2"
  val TheEpoch: Column = lit("1970-01-01 00:00:00").cast("timestamp")

  def logOptimize(): Unit = {
    createMetadataTableIfNotExists()
    val metadataTable = DeltaTable.forName(InternalOptimizeMetadataTable.Name)
    val rowDf = Seq(InternalOptimizeMetadataTable.Row(getCurrentTimestamp)).toDF()
    metadataTable
      .as(DstAlias)
      .merge(rowDf, FalseCondition)
      .whenNotMatched()
      .insertAll()
      .execute()
  }

  def getLastOptimizeTimestamp: Timestamp = {
    createMetadataTableIfNotExists()
    val metadataTable = DeltaTable.forName(InternalOptimizeMetadataTable.Name)
    val timestamp = metadataTable.toDF
      .agg(max(s"${Columns.OPTIMIZE_TIMESTAMP}").alias("_last"))
      .withColumn("_last_optimize", coalesce(col("_last"), TheEpoch))
      .first
      .getAs[Timestamp]("_last_optimize")
    timestamp
  }

  private def createMetadataTableIfNotExists(): Unit = {
    createTableIfNotExists(
      spark,
      InternalOptimizeMetadataTable.Name,
      InternalOptimizeMetadataTable.Schema
    )
  }

}

object InternalOptimizeMetadata {

  /** Get the current timestamp in UTC
    */
  def getCurrentTimestamp: Timestamp = {
    Timestamp.from(Instant.now().atZone(ZoneId.of("UTC")).toInstant)
  }

  /** Create a table if it does not exist
    */
  def createTableIfNotExists(spark: SparkSession, tableName: String, schema: StructType): Unit = {
    DeltaTable
      .createIfNotExists(spark)
      .property("delta.autoOptimize.optimizeWrite", "true")
      .property("delta.autoOptimize.autoCompact", "true")
      .tableName(tableName)
      .addColumns(schema)
      .execute()
  }

}

/** Object providing constants for the INTERNAL_OPTIMIZE_METADATA table (name, column names, schema, etc..)
  */
object InternalOptimizeMetadataTable {
  val Name: String = "INTERNAL_OPTIMIZE_METADATA"

  object Columns {
    val OPTIMIZE_TIMESTAMP = "OPTIMIZE_TIMESTAMP"
  }

  case class Row(
    OPTIMIZE_TIMESTAMP: Timestamp
  )

  val Ddl: String =
    s"""
      |  ${Columns.OPTIMIZE_TIMESTAMP} TIMESTAMP NOT NULL
      |""".stripMargin

  val Schema: StructType = StructType.fromDDL(Ddl)

}
