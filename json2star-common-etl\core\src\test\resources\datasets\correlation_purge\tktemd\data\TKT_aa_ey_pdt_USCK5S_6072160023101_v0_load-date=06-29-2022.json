{"correlatedResourcesCurrent": {"TKT-PNR": {"correlations": [{"corrTktPnr": {"items": [{"pnrAirSegmentId": "USCK5S-2022-06-29-ST-17", "pnrTicketingReferenceId": "USCK5S-2022-06-29-OT-384", "pnrTravelerId": "USCK5S-2022-06-29-PT-3-INF", "ticketCouponId": "6072160023101-2022-06-29-1"}, {"pnrAirSegmentId": "USCK5S-2022-06-29-ST-18", "pnrTicketingReferenceId": "USCK5S-2022-06-29-OT-384", "pnrTravelerId": "USCK5S-2022-06-29-PT-3-INF", "ticketCouponId": "6072160023101-2022-06-29-2"}, {"pnrAirSegmentId": "USCK5S-2022-06-29-ST-19", "pnrTicketingReferenceId": "USCK5S-2022-06-29-OT-384", "pnrTravelerId": "USCK5S-2022-06-29-PT-3-INF", "ticketCouponId": "6072160023101-2022-06-29-3"}, {"pnrAirSegmentId": "USCK5S-2022-06-29-ST-25", "pnrTicketingReferenceId": "USCK5S-2022-06-29-OT-384", "pnrTravelerId": "USCK5S-2022-06-29-PT-3-INF", "ticketCouponId": "6072160023101-2022-06-29-4"}, {"pnrAirSegmentId": "USCK5S-2022-06-29-ST-23", "pnrTicketingReferenceId": "USCK5S-2022-06-29-OT-384", "pnrTravelerId": "USCK5S-2022-06-29-PT-3-INF", "ticketCouponId": "6072160023101-2022-06-29-5"}, {"pnrAirSegmentId": "USCK5S-2022-06-29-ST-24", "pnrTicketingReferenceId": "USCK5S-2022-06-29-OT-384", "pnrTravelerId": "USCK5S-2022-06-29-PT-3-INF", "ticketCouponId": "6072160023101-2022-06-29-6"}]}, "fromVersion": "0", "toId": "USCK5S-2022-06-29", "toVersion": "7"}], "fromDomain": "TKT", "fromFullVersion": "0", "id": "6072160023101-2022-06-29", "isFullUpdate": true, "toDomain": "PNR", "version": "PNR-1"}}, "mainResource": {"current": {"correlations": [{"name": "TKT-PNR", "relation": {"rel": "related"}}], "image": {"@type": "type.googleapis.com/com.amadeus.pulse.message.AirTravelDocument", "associatedPnrs": [{"creation": {"pointOfSale": {"office": {"systemCode": "EY"}}}, "reference": "USCK5S"}, {"creation": {"pointOfSale": {"office": {"systemCode": "1A"}}}, "reference": "USCK5S"}], "conjunctiveDocumentNumbers": ["6072160023101", "6072160023102"], "coupons": [{"baggageAllowance": {"weight": {"amount": "10", "unit": "KILOGRAMS"}}, "currentSegment": {"arrival": {"iataCode": "AUH", "localDateTime": "2022-07-01T23:55:00"}, "bookingClass": {"code": "Q"}, "carrierCode": "EY", "departure": {"iataCode": "SYD", "localDate": "2022-07-01", "localDateTime": "2022-07-01T15:15:00"}, "number": "451"}, "documentNumber": "6072160023101", "fareBasis": {"fareBasisCode": "QHWC2AUIN"}, "id": "6072160023101-2022-06-29-1", "isCodeshare": false, "isFromConnection": true, "isNonExchangeable": false, "isNonRefundable": false, "number": 1, "reservationStatus": "NO_SEAT", "sequenceNumber": 1, "soldSegment": {"arrival": {"iataCode": "AUH", "localDateTime": "2022-07-01T23:55:00"}, "bookingClass": {"code": "Q"}, "carrierCode": "EY", "departure": {"iataCode": "SYD", "localDateTime": "2022-07-01T15:15:00"}, "number": "451"}, "status": "OPEN_FOR_USE", "validityDates": {"notValidAfterDate": "2022-07-01", "notValidBeforeDate": "2022-07-01"}}, {"baggageAllowance": {"weight": {"amount": "10", "unit": "KILOGRAMS"}}, "currentSegment": {"arrival": {"iataCode": "MUC", "localDateTime": "2022-07-02T06:55:00"}, "bookingClass": {"code": "Q"}, "carrierCode": "EY", "departure": {"iataCode": "AUH", "localDate": "2022-07-02", "localDateTime": "2022-07-02T02:30:00"}, "number": "5"}, "documentNumber": "6072160023101", "fareBasis": {"fareBasisCode": "QHWC2AUIN"}, "id": "6072160023101-2022-06-29-2", "isCodeshare": false, "isFromConnection": false, "isNonExchangeable": false, "isNonRefundable": false, "number": 2, "reservationStatus": "NO_SEAT", "sequenceNumber": 2, "soldSegment": {"arrival": {"iataCode": "MUC", "localDateTime": "2022-07-02T06:55:00"}, "bookingClass": {"code": "Q"}, "carrierCode": "EY", "departure": {"iataCode": "AUH", "localDateTime": "2022-07-02T02:30:00"}, "number": "5"}, "status": "OPEN_FOR_USE", "validityDates": {"notValidAfterDate": "2022-07-02", "notValidBeforeDate": "2022-07-02"}}, {"baggageAllowance": {"weight": {"amount": "10", "unit": "KILOGRAMS"}}, "currentSegment": {"arrival": {"iataCode": "QYG", "localDateTime": "2022-07-02T09:00:00"}, "bookingClass": {"code": "Y"}, "carrierCode": "W2", "departure": {"iataCode": "MUC", "localDate": "2022-07-02", "localDateTime": "2022-07-02T08:00:00"}, "number": "6261"}, "documentNumber": "6072160023101", "fareBasis": {"fareBasisCode": "QHWC2AUIN"}, "id": "6072160023101-2022-06-29-3", "isCodeshare": false, "isFromConnection": false, "isNonExchangeable": false, "isNonRefundable": false, "number": 3, "reservationStatus": "NO_SEAT", "sequenceNumber": 3, "soldSegment": {"arrival": {"iataCode": "QYG", "localDateTime": "2022-07-02T09:00:00"}, "bookingClass": {"code": "Y"}, "carrierCode": "W2", "departure": {"iataCode": "MUC", "localDateTime": "2022-07-02T08:00:00"}, "number": "6261"}, "status": "OPEN_FOR_USE", "validityDates": {"notValidAfterDate": "2022-07-02", "notValidBeforeDate": "2022-07-02"}}, {"baggageAllowance": {"weight": {"amount": "10", "unit": "KILOGRAMS"}}, "currentSegment": {"arrival": {"iataCode": "MUC", "localDateTime": "2022-07-12T06:00:00"}, "bookingClass": {"code": "Y"}, "carrierCode": "W2", "departure": {"iataCode": "QYG", "localDate": "2022-07-12", "localDateTime": "2022-07-12T05:00:00"}, "number": "6240"}, "documentNumber": "6072160023101", "fareBasis": {"fareBasisCode": "MKXC2AUIN"}, "id": "6072160023101-2022-06-29-4", "isCodeshare": false, "isFromConnection": true, "isNonExchangeable": false, "isNonRefundable": false, "number": 4, "reservationStatus": "NO_SEAT", "sequenceNumber": 4, "soldSegment": {"arrival": {"iataCode": "MUC", "localDateTime": "2022-07-12T06:00:00"}, "bookingClass": {"code": "Y"}, "carrierCode": "W2", "departure": {"iataCode": "QYG", "localDateTime": "2022-07-12T05:00:00"}, "number": "6240"}, "status": "OPEN_FOR_USE", "validityDates": {"notValidAfterDate": "2022-07-12", "notValidBeforeDate": "2022-07-12"}}, {"baggageAllowance": {"weight": {"amount": "10", "unit": "KILOGRAMS"}}, "currentSegment": {"arrival": {"iataCode": "AUH", "localDateTime": "2022-07-12T19:55:00"}, "bookingClass": {"code": "M"}, "carrierCode": "EY", "departure": {"iataCode": "MUC", "localDate": "2022-07-12", "localDateTime": "2022-07-12T12:10:00"}, "number": "6"}, "documentNumber": "6072160023102", "fareBasis": {"fareBasisCode": "MKXC2AUIN"}, "id": "6072160023101-2022-06-29-5", "isCodeshare": false, "isFromConnection": false, "isNonExchangeable": false, "isNonRefundable": false, "number": 1, "reservationStatus": "NO_SEAT", "sequenceNumber": 5, "soldSegment": {"arrival": {"iataCode": "AUH", "localDateTime": "2022-07-12T19:55:00"}, "bookingClass": {"code": "M"}, "carrierCode": "EY", "departure": {"iataCode": "MUC", "localDateTime": "2022-07-12T12:10:00"}, "number": "6"}, "status": "OPEN_FOR_USE", "validityDates": {"notValidAfterDate": "2022-07-12", "notValidBeforeDate": "2022-07-12"}}, {"baggageAllowance": {"weight": {"amount": "10", "unit": "KILOGRAMS"}}, "currentSegment": {"arrival": {"iataCode": "MEL", "localDateTime": "2022-07-13T17:05:00"}, "bookingClass": {"code": "M"}, "carrierCode": "EY", "departure": {"iataCode": "AUH", "localDate": "2022-07-12", "localDateTime": "2022-07-12T21:50:00"}, "number": "460"}, "documentNumber": "6072160023102", "fareBasis": {"fareBasisCode": "MKXC2AUIN"}, "id": "6072160023101-2022-06-29-6", "isCodeshare": false, "isFromConnection": false, "isNonExchangeable": false, "isNonRefundable": false, "number": 2, "reservationStatus": "NO_SEAT", "sequenceNumber": 6, "soldSegment": {"arrival": {"iataCode": "MEL", "localDateTime": "2022-07-13T17:05:00"}, "bookingClass": {"code": "M"}, "carrierCode": "EY", "departure": {"iataCode": "AUH", "localDateTime": "2022-07-12T21:50:00"}, "number": "460"}, "status": "OPEN_FOR_USE", "validityDates": {"notValidAfterDate": "2022-07-12", "notValidBeforeDate": "2022-07-12"}}], "creation": {"dateTime": "2022-06-29", "pointOfSale": {"login": {"cityCode": "XMY", "countryCode": "AU", "currencyCode": "AUD", "dutyCode": "GS", "initials": "PH", "numericSign": "0202"}, "office": {"agentType": "AIRLINE_AGENT", "iataNumber": "02090082", "id": "EY0", "inHouseIdentification": "369192"}}}, "destinationCityIataCode": "MEL", "documentType": "TICKET", "endorsementFreeFlow": "NON ENDO/ REF", "formsOfPayment": [{"authorization": {"approvalCode": "APS1OK", "sourceOfApprovalCode": "S"}, "code": "CC", "displayedAmount": {"amount": "1.00", "currency": "AUD"}, "fopIndicator": "NEW", "freeText": "CCCAXXXXXXXXXXXX0008/0139", "paymentCard": {"expiryDate": "0139", "holderName": "gst", "maskedCardNumber": "XXXXXXXXXXXX0008", "vendorCode": "CA"}}, {"code": "CC", "displayedAmount": {"currency": "AUD"}, "fopIndicator": "ORIGINAL", "freeText": "CCCA", "paymentCard": {"vendorCode": "CA"}}], "id": "6072160023101-2022-06-29", "issuanceType": "REISSUE", "latestEvent": {"dateTime": "2022-06-29T08:16:05.683159000", "id": "0", "pointOfSale": {"login": {"cityCode": "XMY", "countryCode": "AU", "currencyCode": "AUD", "dutyCode": "GS", "initials": "PH", "numericSign": "0202"}, "office": {"agentType": "AIRLINE_AGENT", "iataNumber": "02090082", "id": "XMYEY02AU", "inHouseIdentification": "369192", "systemCode": "1A"}}, "triggerEventName": "134"}, "numberOfBooklets": 2, "originCityIataCode": "SYD", "originalDocuments": [{"creation": {"dateTime": "2022-06-29"}, "documentNumber": "6072160023095", "documentType": "TICKET"}], "originalIssueFreeFlow": "6072160023095XMY29JUN2202090082", "price": {"currency": "AUD", "detailedPrices": [{"amount": "1.00A", "currency": "AUD", "elementaryPriceType": "TotalFare"}, {"amount": "357.00", "currency": "AUD", "elementaryPriceType": "BaseFare"}, {"amount": "1.00", "currency": "AUD", "elementaryPriceType": "AdditionalCollectionFare"}], "total": "1.00A", "totalTaxes": "0.00"}, "pricingConditions": {"fareCalculation": {"pricingIndicator": "0", "text": "SYD EY X/AUH Q50.00EY X/MUC W2 QYG105.66W2 X/MUC EY X/AUH EY MEL94.52Q SYDMEL3.00NUC253.18END ROE1.409137"}, "isInternationalSale": true, "isNonEndorsable": true, "isNonExchangeable": false, "isNonRefundable": false, "isPenaltyRestriction": false}, "primaryDocumentNumber": "6072160023101", "traveler": {"contact": {"phone": {"number": "61123456789"}}, "name": {"firstName": "PAT MISS", "lastName": "GST"}, "passengerTypeCode": "INF"}, "validatingCarrierCode": "EY", "validatingCarrierPnr": {"reference": "USCK5S"}, "version": "0"}}, "id": "6072160023101-2022-06-29", "type": "com.amadeus.pulse.message.AirTravelDocument"}}