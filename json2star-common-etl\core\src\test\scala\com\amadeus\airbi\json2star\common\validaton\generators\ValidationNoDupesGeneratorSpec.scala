package com.amadeus.airbi.json2star.common.validaton.generators

import com.amadeus.airbi.common.utils.CommonSpec
import com.amadeus.airbi.json2star.common.TablesDef
import com.amadeus.airbi.json2star.common.app.Json2StarApp.readMappingConfig
import com.amadeus.airbi.json2star.common.validation.config.ValidationConfig
import com.amadeus.airbi.json2star.common.validation.generators.ValidationNoDupesGenerator
import com.amadeus.airbi.rawvault.common.testfwk.ImplicitConversions.Converters

import java.time.OffsetDateTime
class ValidationNoDupesGeneratorSpec extends CommonSpec {

  "ValidationSchemaGenerator.toCreateValidationRequest" should "generate validation SQL queries - default case" in {
    val test = "validation/sample_1"
    val config = readMappingConfig(s"${test}/mapping.conf".toPath, Set("latest_selector"))
    val tables = TablesDef.consolidate(config)
    val validParams = ValidationConfig(
      validationDatabase = "tata",
      validationTablename = "toto",
      domain = "PNR",
      domainVersion = "31_2_3",
      customer = "CRO",
      phase = "UNIT_TEST",
      currentTimestamp = OffsetDateTime.now().toString,
      daysBack = 1
    )
    val results = ValidationNoDupesGenerator.toCreateValidationRequest(
      tables,
      database = "MY_DB",
      validParams,
      checkOnlyLatest = false
    )
    val sql = results.map(_._3)

    val expectedSQLResult = List(
      s"""WITH total_records AS (select count(distinct PASSENGER_ID,VERSION) AS nb_total_records from MY_DB.FACT_PASSENGER_HISTO),
         | failed_records AS  ( select PASSENGER_ID,VERSION,count(*) as a_count
         |   from MY_DB.FACT_PASSENGER_HISTO group by PASSENGER_ID,VERSION having a_count > 1
         |   ),
         | counter_failed_records AS  (select count(*)  AS nb_failed_records from failed_records),
         | sample_failed_records AS(select concat_ws(',',collect_list(distinct failed)) AS fsample
         |   from (select concat_ws('-',PASSENGER_ID,VERSION) as failed from failed_records limit 5)
         |   )
         | select nb_total_records, nb_failed_records, fsample
         | from counter_failed_records, total_records, sample_failed_records""".stripMargin,
      s"""WITH total_records AS (select count(distinct FACT_SECONDARY_ID,VERSION) AS nb_total_records from MY_DB.FACT_SECONDARY_HISTO),
         | failed_records AS  ( select FACT_SECONDARY_ID,VERSION,count(*) as a_count
         |   from MY_DB.FACT_SECONDARY_HISTO group by FACT_SECONDARY_ID,VERSION having a_count > 1
         |   ),
         | counter_failed_records AS  (select count(*)  AS nb_failed_records from failed_records),
         | sample_failed_records AS(select concat_ws(',',collect_list(distinct failed)) AS fsample
         |   from (select concat_ws('-',FACT_SECONDARY_ID,VERSION) as failed from failed_records limit 5)
         |   )
         | select nb_total_records, nb_failed_records, fsample
         | from counter_failed_records, total_records, sample_failed_records""".stripMargin
    )

    sql should equal(expectedSQLResult)
  }

  it should "generate validation SQL queries - only latest case" in {
    val test = "validation/sample_1"
    val config = readMappingConfig(s"${test}/mapping.conf".toPath, Set("latest_selector"))
    val tables = TablesDef.consolidate(config)
    val validParams = ValidationConfig(
      validationDatabase = "tata",
      validationTablename = "toto",
      domain = "PNR",
      domainVersion = "31_2_3",
      customer = "CRO",
      phase = "UNIT_TEST",
      currentTimestamp = OffsetDateTime.now().toString,
      daysBack = 1
    )
    val results = ValidationNoDupesGenerator.toCreateValidationRequest(
      tables,
      database = "MY_DB",
      validParams,
      checkOnlyLatest = true
    )
    val sql = results.map(_._3)

    val expectedSQLResult = List(
      s"""|WITH total_records AS (select count(distinct PASSENGER_ID) AS nb_total_records from MY_DB.FACT_PASSENGER),
         | failed_records AS  ( select PASSENGER_ID,count(*) as a_count
         |   from MY_DB.FACT_PASSENGER group by PASSENGER_ID having a_count > 1
         |   ),
         | counter_failed_records AS  (select count(*)  AS nb_failed_records from failed_records),
         | sample_failed_records AS(select concat_ws(',',collect_list(distinct failed)) AS fsample
         |   from (select concat_ws('-',PASSENGER_ID) as failed from failed_records limit 5)
         |   )
         | select nb_total_records, nb_failed_records, fsample
         | from counter_failed_records, total_records, sample_failed_records""".stripMargin
    )
    sql should equal(expectedSQLResult)
  }
}
