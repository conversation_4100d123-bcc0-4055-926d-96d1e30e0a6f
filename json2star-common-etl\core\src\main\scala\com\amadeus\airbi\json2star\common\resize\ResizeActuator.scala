package com.amadeus.airbi.json2star.common.resize

import com.amadeus.airbi.json2star.common.app.Display.display
import com.amadeus.airbi.json2star.common.config.AppConfig
import com.amadeus.airbi.rawvault.common.RootConfig
import com.databricks.sdk.mixin.ClustersExt
import com.databricks.sdk.service.compute.ResizeCluster
import com.typesafe.scalalogging.Logger
import org.apache.spark.sql.SparkSession
import org.slf4j.LoggerFactory

import scala.util.{Failure, Success, Try};

/** Implement the actual cluster resize operation.
  *
  * @param config resize actuator configuration
  * @param dbx databricks utils handle
  */
class ResizeActuator(val config: ResizeActuatorConfig, dbx: DatabricksUtils) {

  lazy val logger: Logger = Logger(LoggerFactory.getLogger(getClass.getName))

  private val clustersAPI = createClustersAPI(config.workspaceUrl, config.workspaceToken)

  def createClustersAPI(workspaceUrl: String, workspaceToken: String): ClustersExt = {
    dbx.getWorkspaceClient(workspaceUrl, workspaceToken).clusters()
  }

  def resize(spark: SparkSession, targetNumWorkers: Int): Unit = {
    val clusterId = spark.conf.get("spark.databricks.clusterUsageTags.clusterId")
    resize(clusterId, targetNumWorkers)
  }

  def resize(clusterId: String, targetNumWorkers: Int): Unit = {
    val currentNumWorkers = clustersAPI.get(clusterId).getNumWorkers
    Try {
      clustersAPI.resize(new ResizeCluster().setClusterId(clusterId).setNumWorkers(targetNumWorkers))
    } match {
      case Failure(exception) =>
        display(
          s"[resize] Resize request failed because: ${exception.getMessage}",
          config.displayEvents,
          logger
        )
      case Success(_) =>
        // Note: for the response there is no callback, to check with background polling if necessary or onProgress method
        display(
          s"[resize] Started request for cluster ID ${clusterId}: from ${currentNumWorkers} to ${targetNumWorkers} workers",
          config.displayEvents,
          logger
        )
    }
  }

}

object ResizeActuator {
  def fromRootConfig(rootConfig: RootConfig, dbx: DatabricksUtils): Option[ResizeActuator] = {
    ResizeActuatorConfig.fromRootConfig(rootConfig, dbx).map(c => new ResizeActuator(c, dbx))
  }

  def fromAppConfig(appConfig: AppConfig, dbx: DatabricksUtils): Option[ResizeActuator] = {
    ResizeActuatorConfig.fromAppConfig(appConfig, dbx).map(c => new ResizeActuator(c, dbx))
  }

  def resize(spark: SparkSession, rootConfig: RootConfig, dbx: DatabricksUtils, targetNumWorkers: Int): Unit = {
    fromRootConfig(rootConfig, dbx).foreach(a => a.resize(spark, targetNumWorkers))
  }
}
