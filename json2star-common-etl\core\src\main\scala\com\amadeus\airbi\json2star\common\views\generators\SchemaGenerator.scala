package com.amadeus.airbi.json2star.common.views.generators

import com.amadeus.airbi.json2star.common.views.generators.SchemaGenerator.Statements
import com.amadeus.airbi.json2star.common.{ColumnDef, Schema, TableDef, TablesDef}
import com.amadeus.airbi.rawvault.common.application.config.TablesConfig
import com.amadeus.airbi.rawvault.common.config.ColumnType.ColumnType

trait SchemaGenerator {
  private[views] def toTableType(columnType: ColumnType): String

  private[views] def toColumnDDL(col: ColumnDef): Seq[String]

  private[views] def toDDL(cols: Seq[ColumnDef]): Seq[Seq[String]] = cols.map(toColumnDDL)

  def formatDDL(cols: Seq[ColumnDef]): String = {
    toDDL(cols).map(cs => s"\t${cs.mkString(" ")}").mkString(",\n")
  }
  private[views] def createInternalTable: <PERSON><PERSON><PERSON>

  def toCreateTableSql(conf: TableDef, database: String, options: Map[String, String]): Option[Statements]

  /** Generate the SQL statements to initialize all Tables from the configuration
    *
    * @param config   the tables definitions
    * @param database database name
    * @param options  configurable parameters for table
    * @return a seq of string with DDL SQL statement
    */
  def toInitSql(
    config: TablesDef,
    database: String,
    options: Map[String, String]
  ): Seq[Statements] = {
    val tables = if (createInternalTable) config.tables else config.tables.filter(!_.schema.isInternal)
    val (materializedTables, rest) = tables.partition(_.schema.kind == Schema.Materialized)
    // must first create materialized tables, then views and the rest
    (materializedTables ++ rest).flatMap(c => toCreateTableSql(c, database, options))
  }

  /** Get the list of tables from the config
    *
    * @param config a MappingConfigBean
    * @return a seq of table names
    */
  def getTables(config: TablesConfig): List[String] = {
    config.tables.map(_.name)
  }

}

object SchemaGenerator {
  /**
    * A case class to represent a collection of SQL statements to be executed sequentially
    * @param statements the different statements, guaranteed to be executed sequentially
    */
  case class Statements(statements: Seq[String])
  object Statements {
    def apply(s: String): Statements = Statements(Seq(s)) // for convenience
  }
}