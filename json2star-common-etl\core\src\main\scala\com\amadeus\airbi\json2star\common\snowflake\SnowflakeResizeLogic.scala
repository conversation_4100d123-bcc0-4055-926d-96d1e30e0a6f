package com.amadeus.airbi.json2star.common.snowflake

import com.amadeus.airbi.json2star.common.config.ResizeParams
import com.amadeus.airbi.json2star.common.resize.ResizeLogic
import com.amadeus.airbi.json2star.common.resize.ResizeLogic.DoResize

object SnowflakeResizeLogic extends ResizeLogic {
  override def shouldResize(resizeParams: ResizeParams): ResizeLogic.Resize = {
    DoResize(resizeParams.numWorkersSfPush, "Running Snowflake Push")
  }
}
