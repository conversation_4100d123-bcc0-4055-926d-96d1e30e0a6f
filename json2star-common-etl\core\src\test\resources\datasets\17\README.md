# Readme

## test multiple root_source for a fact table

The file in  data/ is a simplier version of a dcspax.DCSPassenger message

This data has been extracted from EY UAT data.

goal is to extracts services from 2 part of this message : 
the seg_delivery/leg_delivery/seating part and the service part.
ids are constructing using a different definition per root-source
some fields are only available for the seg_delivery/leg_delivery/ root-source