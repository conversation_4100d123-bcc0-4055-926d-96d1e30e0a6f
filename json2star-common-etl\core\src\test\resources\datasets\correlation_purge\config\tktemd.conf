//Version Field cast to bigint
versionExprVal: "bigint({0})"
//Version Field type
versionTypeVal: "longColumn"
  "tables": [
    {
        "name": "FACT_TRAVEL_DOCUMENT_HISTO",
        "mapping": {
          "merge": {
            "key-columns": ["TRAVEL_DOCUMENT_ID", "VERSION"],
            "if-dupe-take-higher": ["LOAD_DATE"]
          },
          "root-sources": [{ "blocks": [{"base": "$.mainResource.current.image"}]}],
          "columns": [
            {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})" },
            {"name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
            {"name": "VERSION", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.version"}]}},
            {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]}},
            {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
            {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
          ],
          "pit": {
            "type": "master-pit-table",
            "master" : {
              "pit-key": "TRAVEL_DOCUMENT_ID",
              "pit-version": "VERSION",
              "valid-from": "DATE_BEGIN",
              "valid-to": "DATE_END",
              "is-last": "IS_LAST_VERSION"
            }
          },
          "description": {
            "description": "",
            "granularity": ""
          }
        }
    },
    {
        "name": "TKT_PNR_PARTIAL_CORR_PIT",
        "mapping": {
          "merge": {
            "key-columns": ["RESERVATION_ID", "TRAVEL_DOCUMENT_ID", "AIR_SEGMENT_PAX_ID", "COUPON_ID", "VERSION"],
            "if-dupe-take-higher": ["LOAD_DATE"]
          },
          "root-sources": [{ "blocks": [
            {"corr": "$.correlatedResourcesCurrent.TKT-PNR.correlations[*]"},
            {"coupon": "$.corrTktPnr.items[*]"}
          ]}],
          "columns": [
            {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
            {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"root": "$.mainResource.current.image.id"}]}, "expr": "hashM({0})"},
            {"name": "AIR_SEGMENT_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "false", "sources": {"blocks": [{"coupon": "$.pnrAirSegmentId"}, {"coupon": "$.pnrTravelerId"}]}, "expr": "hashM({0})"},
            {"name": "COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "false", "sources": {"blocks": [{"coupon": "$.ticketCouponId"}]}, "expr": "hashM({0})"},
            {"name": "VERSION", "column-type": "intColumn", "sources": {"blocks": [{"root": "$.mainResource.current.image.version"}]}},
            {"name": "REFERENCE_KEY_PNR", "column-type": "strColumn", "is-mandatory": "false", "sources": {"blocks": [{"corr": "$.toId"}]}},
            {"name": "REFERENCE_KEY_TRAVEL_DOC", "column-type": "strColumn", "is-mandatory": "false", "sources": {"blocks": [{"root": "$.mainResource.current.image.id"}]}},
            {"name": "REFERENCE_KEY_AIR_SEGMENT_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "false", "sources": {"blocks": [{"coupon": "$.pnrAirSegmentId"}, {"coupon": "$.pnrTravelerId"}]}},
            {"name": "REFERENCE_KEY_COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "false", "sources": {"blocks": [{"coupon": "$.ticketCouponId"}]}},
            {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"root": "$.mainResource.current.image.latestEvent.dateTime"}]}},
            {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
            {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
          ],
          "pit": {
            "type": "secondary-pit-table"
          },
          "description": {
            "description": "It contains PNR-TKT correlation information as seen by TKT.",
            "granularity": "1 PAX-SEG"
          }
        }
    }
  ]
