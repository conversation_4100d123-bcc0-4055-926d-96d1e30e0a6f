package com.amadeus.airbi.json2star.common.addons.stackable.weight

import com.amadeus.airbi.json2star.common.addons.stackable.StackableAddonValidationException
import com.amadeus.airbi.json2star.common.app.{FatalJson2StarAppException, Json2StarApp}
import com.amadeus.airbi.json2star.common.app.Json2StarApp.readMappingConfig
import com.amadeus.airbi.rawvault.common.testfwk.Json2StarSpec
import org.apache.spark.sql.streaming.StreamingQueryException

import java.io.File
import scala.reflect.io.Directory

class IntegrationWeightConversionSpec extends Json2StarSpec {

  val mappingFile = "datasets/weight_conversion/simplified_dcsbag.conf"

  val TableNames: Seq[String] = getTableNames(mappingFile)

  override def beforeAll: Unit = {
    super.beforeAll
    createTables(mappingFile)
  }

  override def beforeEach: Unit = {
    cleanTables(TableNames)
  }

  "IntegrationWeightConversionSpec" should "convert weight in FACT_BAG_HISTO" taggedAs(SlowTestTag) in {
    val dataDir = "datasets/weight_conversion/data/"
    val pathExpectedResults = "src/test/resources/" + dataDir + "expected_results/"
    val rc = rootConfig(dataDir, inputDatabase, isLight = DefaultIsLight, homeWeightUnit = Some("KILOGRAMS"))

    val directory = new Directory(new File(rc.etl.stream.sink.checkpointLocation))
    directory.deleteRecursively()
    val mapping = readMappingConfig(mappingFile)
    Json2StarApp.run(spark, rc, mapping)
    checkTablesContent(TableNames, pathExpectedResults, model = Some(mapping))
    directory.deleteRecursively()
  }

  "IntegrationWeightConversionSpec" should "make the app fail with a fatal exception if home weight unit not defined" in {
    val dataDir = "datasets/weight_conversion/data/"
    val rc = rootConfigWithoutWeightUnit(dataDir)
    val directory = new Directory(new File(rc.etl.stream.sink.checkpointLocation))
    directory.deleteRecursively()
    val mapping = readMappingConfig(mappingFile)
    assertThrows[StreamingQueryException](
      Json2StarApp.run(spark, rc, mapping)
    )
    directory.deleteRecursively()
  }

  private def rootConfigWithoutWeightUnit(dataDir: String) = {
    val rc = rootConfig(dataDir, inputDatabase, isLight = DefaultIsLight)
    rc.copy(etl = rc.etl.copy(common = rc.etl.common.copy(homeWeightUnit = None)))
  }
}
