reservationIdVal: {
  "name": "RESERVATION_ID",
  "column-type": "binaryStrColumn",
  "is-mandatory": "true",
  "sources": {"blocks": [{"base": "$.id"}]},
  "expr": "hashM({0})"
}


   "defaultComment" : "A coment here",
   "partition-spec" : {
          "key" : "PNR_CREATION_DATE",
          "column-name": "PART_PNR_CREATION_MONTH",
          "expr": "date_format(PNR_CREATION_DATE, \"yyyy-MM\")"
    },
  "tables": [
    {
        "name": "FACT_RESERVATION_HISTO",
        "mapping": {
          "merge": {
            "key-columns": ["RESERVATION_ID", "VERSION"],
            "if-dupe-take-higher": ["LOAD_DATE"]
          },
          "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}]}],
          "columns": [
            ${reservationIdVal},
            // Added a fake column POINT_OF_SALE_ID == POINT_OF_SALE_OWNER_ID to test the foreign key behavior in the doc generator
            {"name": "POINT_OF_SALE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.owner.office.id"}, {"base": "$.owner.office.iataNumber"}, {"base": "$.owner.office.systemCode"}, {"base": "$.owner.office.agentType"}, {"base": "$.owner.login.cityCode"}, {"base": "$.owner.login.countryCode"}, {"base": "$.owner.login.numericSign"}, {"base": "$.owner.login.initials"}, {"base": "$.owner.login.dutyCode"}]},"expr": "hashM({0})"},

            // Explicit fk
            {"name": "POINT_OF_SALE_OWNER_ID", "fk": [{"table":"DIM_POINT_OF_SALE", "column":"POINT_OF_SALE_ID"}], "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.owner.office.id"}, {"base": "$.owner.office.iataNumber"}, {"base": "$.owner.office.systemCode"}, {"base": "$.owner.office.agentType"}, {"base": "$.owner.login.cityCode"}, {"base": "$.owner.login.countryCode"}, {"base": "$.owner.login.numericSign"}, {"base": "$.owner.login.initials"}, {"base": "$.owner.login.dutyCode"}]},"expr": "hashM({0})"},
            {"name": "POINT_OF_SALE_CREATION_ID", "fk": [{"table":"DIM_POINT_OF_SALE", "column":"POINT_OF_SALE_ID"}], "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.creation.pointOfSale.office.id"}, {"base": "$.creation.pointOfSale.office.iataNumber"}, {"base": "$.creation.pointOfSale.office.systemCode"}, {"base": "$.creation.pointOfSale.office.agentType"}, {"base": "$.creation.pointOfSale.login.cityCode"}, {"base": "$.creation.pointOfSale.login.countryCode"}, {"base": "$.creation.pointOfSale.login.numericSign"}, {"base": "$.creation.pointOfSale.login.initials"}, {"base": "$.creation.pointOfSale.login.dutyCode"}]},"expr": "hashM({0})"},
            {"name": "POINT_OF_SALE_LAST_UPDATE_ID", "fk": [{"table":"DIM_POINT_OF_SALE", "column":"POINT_OF_SALE_ID"}, {"table":"DUMMY", "column":"DUMMY"}], "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.lastModification.pointOfSale.office.id"}, {"base": "$.lastModification.pointOfSale.office.iataNumber"}, {"base": "$.lastModification.pointOfSale.office.systemCode"}, {"base": "$.lastModification.pointOfSale.office.agentType"}, {"base": "$.lastModification.pointOfSale.login.cityCode"}, {"base": "$.lastModification.pointOfSale.login.countryCode"}, {"base": "$.lastModification.pointOfSale.login.numericSign"}, {"base": "$.lastModification.pointOfSale.login.initials"}, {"base": "$.lastModification.pointOfSale.login.dutyCode"}]},"expr": "hashM({0})"},

            // DIM generated FK
            { "name": "MY_TABLE_CODE", "column-type": "strColumn","sources": {"blocks": [{"base": "$.table.id"}]},"create-fk" : {"column-type" : "longColumn" ,"expr" :"hashXS({0})", "fk":[{"schema": "PNR", "table":"DIM_TABLE", "column":"TABLE_ID"}]},
            "meta": {"gdpr-zone": "red"}},
            { "name": "TABLE", "column-type": "strColumn","sources": {"blocks": [{"base": "$.table.id"}]}, "create-fk" : {"column-type" : "longColumn" ,"expr" :"hashXS({0})"} },
            // generated FK with explicit name
            { "name": "COLUMN_NAME_A", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.table.id"}]}, "create-fk" : {"name": "COLUMN_ID_A", "column-type" : "binaryStrColumn" ,"expr" :"hashXS({0})"} },

            // column belonging to 2 FKs (FACT_A and FACT_B)
            {"name": "VERSION", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.version"}]}},
            {"name": "FACT_A_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.ida2"}]}, "expr": "hashM({0})"},
            {"name": "FACT_B_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.idb2"}]}, "expr": "hashM({0})"},

            {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}]}},
            {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
            {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
            {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
          ],
          "pit": {
            "type": "master-pit-table",
            "master" : {
              "pit-key": "RESERVATION_ID",
              "pit-version": "ENVELOP_NB",
              "valid-from": "DATE_BEGIN",
              "valid-to": "DATE_END",
              "is-last": "IS_LAST_VERSION"
            }
          },
          "description": {
            "description": "It contains information related to the booking on PNR-level.",
            "granularity": "1 PNR, version"
          },
        },
        "table-snowflake": {
            "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
        }
    },
    {
      "name": "FACT_RESERVATION",
      "latest": {
        "histo-table-name": "FACT_RESERVATION_HISTO"
      }
    },
    {
      "name": "DIM_POINT_OF_SALE",
      "mapping": {
        "merge": {
          "key-columns": ["POINT_OF_SALE_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [{"blocks": [{"base": "$.dummy"}]}],
        "columns": [
          { "name": "POINT_OF_SALE_ID", "column-type": "binaryStrColumn","sources": {"blocks": [{"base": "$.id"}]} },
          { "name": "OFFICE_IATA_NUMBER","column-type": "strColumn", "sources":  {"blocks": [{"base": "$.iataNumber"}]}},
          { "name": "OFFICE_SYSTEM_CODE", "column-type": "strColumn", "sources":  {"blocks": [{"base": "$.systemCode"}]}},
          { "name": "OFFICE_AGENT_TYPE","column-type": "strColumn","sources":  {"blocks": [{"base": "$.agentType"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "FACT_A",
      "latest": {
        "histo-table-name": "FACT_A_HISTO"
      }
    },
    {
      "name": "FACT_A_HISTO",
      "mapping": {
        "merge": {
          "key-columns": ["FACT_A_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [{"blocks": [{"base": "$.dummy"}]}],
        "columns": [
          { "name": "VERSION", "column-type": "strColumn","sources": {"blocks": [{"base": "$.version"}]} },
          { "name": "FACT_A_ID", "column-type": "binaryStrColumn","sources": {"blocks": [{"base": "$.id2"}]} },
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
        ],
        "pit": {
          "type": "secondary-pit-table"
        }
      }
    },
    {
      "name": "FACT_B",
      "latest": {
        "histo-table-name": "FACT_B_HISTO"
      }
    },
    {
      "name": "FACT_B_HISTO",
      "mapping": {
        "merge": {
          "key-columns": ["FACT_B_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [{"blocks": [{"base": "$.dummy"}]}],
        "columns": [
          { "name": "VERSION", "column-type": "strColumn","sources": {"blocks": [{"base": "$.version"}]} },
          { "name": "FACT_B_ID", "column-type": "binaryStrColumn","sources": {"blocks": [{"base": "$.id2"}]} },
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
        ],
        "pit": {
          "type": "secondary-pit-table"
        }
      }
    },
    {
      "name": "DIM_TABLE",
      "mapping": {
        "merge": {
          "key-columns": ["TABLE_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [{"blocks": [{"base": "$.dummy"}]}],
        "columns": [
          { "name": "TABLE_ID", "column-type": "longColumn","sources": {"blocks": [{"base": "$.table.id"}]} ,"expr" :"hashXS({0})" },
          { "name": "TABLE_CODE", "column-type": "strColumn","sources": {"blocks": [{"base": "$.table.id"}]} },
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    }


]

