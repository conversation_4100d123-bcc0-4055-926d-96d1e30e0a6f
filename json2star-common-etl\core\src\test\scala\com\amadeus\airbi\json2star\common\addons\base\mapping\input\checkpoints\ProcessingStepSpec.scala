package com.amadeus.airbi.json2star.common.addons.base.mapping.input.checkpoints

import com.amadeus.airbi.common.utils.CommonSpec

import scala.collection.Seq

class ProcessingStepSpec extends CommonSpec {
  import java.time.LocalDate

  "StaticStartStepPath" should "resolve paths given a date" in {
    val sp = StaticStartStepPath("/base", Seq(0, 1))
    sp.pathsForDate(LocalDate.of(2020, 1, 2)) should ===(Seq("/base/0/2020/01/02/*/*", "/base/1/2020/01/02/*/*"))
  }

  "StaticStartStep" should "resolve a batch with one simple input" in {
    val paths = Seq(
      StaticStartStepPath("/b", Seq(0, 1), pathSuffix = "")
    )
    val c = StaticStartStep(2, paths, LocalDate.of(2020, 1, 1), LocalDate.of(2020, 1, 4))
    c.batches should ===(
      Seq(
        StaticStartStep.Batch(
          LocalDate.of(2020, 1, 1),
          LocalDate.of(2020, 1, 2),
          Seq( /* day 1 */ "/b/0/2020/01/01/", "/b/1/2020/01/01/", /* day 2 */ "/b/0/2020/01/02/", "/b/1/2020/01/02/")
        ),
        StaticStartStep.Batch(
          LocalDate.of(2020, 1, 3),
          LocalDate.of(2020, 1, 4),
          Seq( /* day 3 */ "/b/0/2020/01/03/", "/b/1/2020/01/03/", /* day 4 */ "/b/0/2020/01/04/", "/b/1/2020/01/04/")
        )
      )
    )
  }

  "StaticStartStep" should "resolve a batch with multiple inputs" in {
    val paths = Seq(
      StaticStartStepPath("/a", Seq(0), pathSuffix = ""),
      StaticStartStepPath("/b", Seq(0), pathSuffix = "")
    )
    val c = StaticStartStep(2, paths, LocalDate.of(2020, 1, 1), LocalDate.of(2020, 1, 4))
    c.batches should ===(
      Seq(
        StaticStartStep.Batch(
          LocalDate.of(2020, 1, 1),
          LocalDate.of(2020, 1, 2),
          Seq( /* day 1 */ "/a/0/2020/01/01/", "/b/0/2020/01/01/", /* day 2 */ "/a/0/2020/01/02/", "/b/0/2020/01/02/")
        ),
        StaticStartStep.Batch(
          LocalDate.of(2020, 1, 3),
          LocalDate.of(2020, 1, 4),
          Seq( /* day 3 */ "/a/0/2020/01/03/", "/b/0/2020/01/03/", /* day 4 */ "/a/0/2020/01/04/", "/b/0/2020/01/04/")
        )
      )
    )
  }

}
