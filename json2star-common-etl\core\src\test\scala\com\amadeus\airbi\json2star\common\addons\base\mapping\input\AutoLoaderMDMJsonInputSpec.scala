package com.amadeus.airbi.json2star.common.addons.base.mapping.input

import com.amadeus.airbi.datalake.common.spark.SparkSqlSpecification
import com.amadeus.airbi.rawvault.common.testfwk.DataFrameComparison.assertSmallDataFrameEquality
import org.apache.spark.sql.Row

class AutoLoaderMDMJsonInputSpec extends SparkSqlSpecification {

  case class ParsedData(
    BODY: String,
    LOAD_DATE: java.sql.Timestamp,
    RECORD_ID: String
  )

  "AutoLoaderMDMJsonInputSpec" should "parse input data with LOAD_DATE from filename" in {
    // create a dataframe

    val inputPath =
      "abfss://<EMAIL>/group=Currency/table=CurrencyRate"
    val inputPartitions = "year=2025/month=03/day=01/hour=05/minute=30/second=45/"
    val inputFileName = "RFD_RFD_refdatacurrency_Currency_CurrencyRate_VERSION=1_2025_03_01_05_30_45.json"

    val data = Seq(
      // path, modificationTime, length, content
      Row(
        s"${inputPath}/${inputPartitions}/${inputFileName}",
        java.sql.Timestamp.valueOf("2025-01-01 00:00:00"),
        0L,
        "[{'key1': 'value1'}]".getBytes
      )
    )

    val input = spark.createDataFrame(
      spark.sparkContext.parallelize(data),
      AutoLoaderMDMJsonInput.inputSchema
    )

    val parsedDf = AutoLoaderMDMJsonInput.parse(input)
    // compare the content of the dataframe with the expected content
    val expectedDf = spark
      .createDataFrame(
        ParsedData(
          BODY = "[{'key1': 'value1'}]",
          LOAD_DATE = java.sql.Timestamp.valueOf("2025-03-01 05:30:45"),
          RECORD_ID = "2025-03-01 05:30:45"
        ) :: Nil
      )
      .toDF("BODY", "LOAD_DATE", "RECORD_ID")

    // run the assert comparison
    assertSmallDataFrameEquality(parsedDf, expectedDf)
  }

}
