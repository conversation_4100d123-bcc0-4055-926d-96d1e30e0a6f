package com.amadeus.airbi.rawvault.common.config

import com.amadeus.airbi.rawvault.common.config.Blocks.Block

/**
  * Represents a non-empty-list of [[Block]] that are to be used to build a data structure [[PreRow]], to be able to query
  * a json at multiple parent-levels.
  *
  * The [[headBlock]] contains the top level query to perform on the JSON.
  * The [[finerBlocks]] are the sequence of block queries to be performed on the previous result.
  *
  * @param headBlock
  * @param finerBlocks
  */
case class Blocks(
  headBlock: Block,
  finerBlocks: Option[Blocks] = None
) {
  override def toString: String = {
    headBlock + finerBlocks.map(f => s"=>$f").mkString
  }
  def aliases: Seq[BlockJsonAlias] = headBlock.aliases ++ finerBlocks.toSeq.flatMap(_.aliases)
  def blocks: Seq[Block] = Seq(headBlock) ++ finerBlocks.toSeq.flatMap(_.blocks)
}

object Blocks {

  def from(b: List[Block]): Blocks = b match {
    case Nil => throw new IllegalArgumentException(s"Can't create ${this.getClass.getName} from empty list")
    case head :: Nil => Blocks(head)
    case head :: tail => Blocks(head, Some(Blocks.from(tail)))
  }

  /**
    * Represents a query within a parenting level context
    */
  sealed trait Block {
    def aliases: Seq[BlockJsonAlias]
  }

  /**
    * Represents a query to be performed on a JSON
    * @param alias the name to give to the result of the query once resolved
    * @param path the JSON path for the query
    */
  case class JsonpathBlock(
    alias: BlockJsonAlias,
    path: BlockJsonPath
  ) extends Block {
    override def aliases: Seq[BlockJsonAlias] = Seq(alias)
  }

  /**
    * Represents a block with sibling queries that will compute a cartesian product between their results
    * @param cartesian all children blocks on which the cartesian product will be computed
    */
  case class CartesianBlock(
    cartesian: List[Blocks]
  ) extends Block {
    override def aliases: Seq[BlockJsonAlias] = cartesian.flatMap(_.aliases)
  }

}
