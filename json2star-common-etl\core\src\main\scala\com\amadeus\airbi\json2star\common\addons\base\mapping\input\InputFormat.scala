package com.amadeus.airbi.json2star.common.addons.base.mapping.input

import com.amadeus.airbi.json2star.common.addons.base.mapping.Table
import com.amadeus.airbi.json2star.common.addons.base.mapping.input.InputFormat.InputDataFrame
import org.apache.spark.sql.{DataFrame, Dataset}
import org.apache.spark.sql.streaming.{DataStreamReader, OutputMode, Trigger}

/** Adaptation layer from various input sources and execution environments (test, local, cluster) to internal data structure
  */
trait InputFormat extends Serializable {

  /** Input format entry point for consumer code.
    *
    * If not overridden, this method always calls `processBatchesStreaming`.
    * See `StreamExecutorInputFormat` for a more complex implementation.
    */
  def processBatches(
    streamReader: DataStreamReader,
    paths: Seq[String],
    checkpointLocation: String,
    trigger: Trigger,
    generateTables: InputDataFrame => Dataset[Table],
    processBatchFunction: (Dataset[Table], String) => Unit
  ): Unit =
    processBatchesStreaming(streamReader, paths, checkpointLocation, trigger, generateTables, processBatchFunction)

  /** This method implements the default behavior of the input format:
    * - use the streamReader to stream the data from the paths
    * - extract tables from the data
    * - process the tables in batches with the provided function
    */
  def processBatchesStreaming(
    streamReader: DataStreamReader,
    paths: Seq[String],
    checkpointLocation: String,
    trigger: Trigger,
    generateTables: InputDataFrame => Dataset[Table],
    processBatchFunction: (Dataset[Table], String) => Unit
  ): Unit = {
    val tables = generateTables(streamingDataFrameFrom(streamReader, paths))
    tables.writeStream
      .option("checkpointLocation", checkpointLocation + "/FROM_RAW")
      .foreachBatch { (ds: Dataset[Table], id: Long) => processBatchFunction(ds, s"S${id}") } // S as in Stream
      .outputMode(OutputMode.Update)
      .trigger(trigger)
      .start()
      .awaitTermination()
  }

  def streamingDataFrameFrom(streamReader: DataStreamReader, paths: Seq[String]): InputDataFrame

}

object InputFormat {
  type InputDataFrame =
    DataFrame // must contain LOAD_DATE, BODY and RECORD_ID columns (as defined in the companion object)
  val LOAD_DATE = "LOAD_DATE" // TIMESTAMP, the moment at which the record is considered to be generated
  val BODY = "BODY" // STRING, content in JSON format
  val RECORD_ID = "RECORD_ID" // STRING, an index to identify the record in the source
}
