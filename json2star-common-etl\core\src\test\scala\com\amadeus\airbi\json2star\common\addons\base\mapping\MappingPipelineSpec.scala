package com.amadeus.airbi.json2star.common.addons.base.mapping

import com.amadeus.airbi.datalake.common.spark.SparkSqlSpecification
import com.amadeus.airbi.json2star.common.TablesDef
import com.amadeus.airbi.json2star.common.addons.base.AddonTable
import com.amadeus.airbi.json2star.common.app.Json2StarApp.readMappingConfig
import com.amadeus.airbi.json2star.common.app.{Metrics, PipelineContext, ProcessingContext}
import com.amadeus.airbi.json2star.common.resize.DefaultDatabricksUtils
import com.amadeus.airbi.rawvault.common.RootConfig
import com.amadeus.airbi.rawvault.common.application.config.TablesConfig
import com.amadeus.airbi.rawvault.common.testfwk.ConfigContext
import org.apache.spark.sql.{Dataset, Encoders}
import scala.util.{Failure, Success, Try}

class MappingPipelineSpec extends SparkSqlSpecification with ConfigContext {

  val dataDir = ""
  val modelFile = "datasets/data_types/mapping.conf"
  val batchId = "S0"

  // BDS-29819
  it should "skip the batch processing when there is no input rows" in {
    // Scenario: the processBatch method is launched with minimal configuration and empty input.
    // The execution passes without error only if the batch processing is skipped, else it should fail
    // with an exception as the configuration is not valid (no input data for instance).

    val rootConf: RootConfig = rootConfig(dataDir, inputDatabase, isLight = DefaultIsLight)
    val dbx = DefaultDatabricksUtils
    val context: ProcessingContext= ProcessingContext(rootConf.processingParams)
    val pipelineContext : PipelineContext = new PipelineContext(rootConf, spark, dbx, context)
    val metrics : Metrics = Metrics.register(spark)
    val model: TablesConfig = readMappingConfig(modelFile)
    val tablesDef = TablesDef.consolidate(model)
    val mappingTables = AddonTable.fromTablesDef(Mapping)(tablesDef)
    mappingTables should not be empty

    val dsRaw : Dataset[Table] = spark.emptyDataset[Table](Encoders.product[Table])
    val pipeline = MappingPipeline(pipelineContext, None, metrics)

    Try(
      pipeline.processBatch(rootConf, mappingTables)(dsRaw, batchId)
    ) match {
      case Success(_) => succeed
      case Failure(e) => fail("Processing should not fail", e)
    }
  }
}