package com.amadeus.airbi.json2star.common.purge

import com.amadeus.airbi.json2star.common.app.Json2StarApp.readMappingConfig
import com.amadeus.airbi.json2star.common.app.{Json2StarApp, PurgeApp}
import com.amadeus.airbi.rawvault.common.testfwk.Json2StarSpec

import java.io.File
import scala.reflect.io.Directory

class PurgeIntegrationSpec extends Json2StarSpec {
  val testBaseDir = "src/test/resources/datasets/purge/"
  val mappingFile: String = testBaseDir + "pnr.conf"
  def pathExpectedResults(index: Int): String = testBaseDir + s"data/step${index}/expected_results/"
  val dataDir = "datasets/purge/data/step1/"

  val SourceDatabase: String = inputDatabase.toLowerCase

  override def isLight: Boolean = false

  override def beforeAll: Unit = {
    super.beforeAll
    // Create the Source database
    createTables(mappingFile)
  }

  override def beforeEach: Unit = {
    cleanTables(mappingFile)
  }

  it should "purge old data based on retention policy" in {

    //step 1 - generate the star schema
    val model = readMappingConfig(mappingFile)
    val rc = rootConfig(dataDir, inputDatabase, displayMainEventsParam = false, isLight = isLight)
    val directory = new Directory(new File(rc.etl.stream.sink.checkpointLocation))
    directory.deleteRecursively()
    Json2StarApp.run(spark, rc, model)
    checkTablesContent(getTableNames(mappingFile), pathExpectedResults(1), model = Some(model))

    // PurgeApp MUST NOT rely on the database set the catalog, it must use full table names
    spark.catalog.setCurrentDatabase("default")

    //purge in dry mode - no data will be purged
    val purgeThresholdMinutes = 3 * 365 * 24 * 60 // Retention period for the purge
    val purgeParams = PurgeParams(
      displayEvents = true,
      purgeThresholdMinutes = purgeThresholdMinutes
    )
    PurgeApp.run(spark, inputDatabase, purgeParams, model, dryRun = true, refDate = Some("2025-07-01"))
    checkTablesContent(getTableNames(mappingFile), pathExpectedResults(1), model = Some(model))

    //step 2 - real purge
    PurgeApp.run(spark, inputDatabase, purgeParams, model, dryRun = false, refDate = Some("2025-07-01"))
    checkTablesContent(getTableNames(mappingFile), pathExpectedResults(2), model = Some(model))

    directory.deleteRecursively()
  }
}
