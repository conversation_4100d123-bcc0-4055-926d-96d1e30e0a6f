model-conf-file = /some/file

cloud-files-conf {
  use-notifications = true
  backfill-interval = 1 day
  include-existing-files = false
  max-files-per-trigger = 4
}

stream {
  sink {
    checkpoint-location = /a/path
    trigger = once
    delta-options = {}
  }
}

common {
    domain = "DOMAIN"
    output-database = "DB_TEST"
    domain-version = "1_0_0"
    output-path = "/tmp/json2star/output/path"
    shard = "6X"
}

input-databases {
  "PNR" = "DATABASE_PNR"
  "TKT" = "DATABASE_TKT"
}

workspace-secrets {
  workspace-url = { scope: "az-db-dummy", key: "dummy-url"}
  workspace-token = { scope: "az-db-dummy", key: "dummy-token"}
}