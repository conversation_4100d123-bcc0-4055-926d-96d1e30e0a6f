package com.amadeus.airbi.json2star.common.addons.base.correlation

import com.amadeus.airbi.json2star.common.addons.base.correlation.CorrelationIdsTable.Columns
import com.amadeus.airbi.json2star.common.addons.base.correlation.CorrelationPipeline._
import io.delta.tables.DeltaTable
import org.apache.spark.sql.functions.{col, lit}
import org.apache.spark.sql.streaming.Trigger
import org.apache.spark.sql.types.StructType
import org.apache.spark.sql.{DataFrame, SparkSession}

import java.sql.Timestamp
import java.time.{Instant, ZoneId}

class CorrelationIdsTable(db: String, corrTable: String) {
  val TableName: String = s"INTERNAL_CORR_IDS_${corrTable}"

  val FullTableName: String = s"${db}.${TableName}"

  case class Row(
    DOMAIN_A_ID: String,
    DOMAIN_B_ID: String
  )

  val Ddl: String =
    s"""
       |  ${Columns.DOMAIN_A_ID} STRING,
       |  ${Columns.DOMAIN_B_ID} STRING
       |""".stripMargin

  val Schema: StructType = StructType.fromDDL(Ddl)

  /** Create a table if it does not exist
    */
  def createTableIfNotExists(spark: SparkSession): Unit = {
    DeltaTable
      .createIfNotExists(spark)
      .property("delta.autoOptimize.optimizeWrite", "true")
      // disable auto compaction to avoid rewriting more files than necessary
      // otherwise consumer will receive more IDs than necessary and recompute additional IDs
      .property("delta.autoOptimize.autoCompact", "false")
      .tableName(FullTableName)
      .addColumns(Schema)
      .execute()
  }

  private def filterWithCdf(df: DataFrame): DataFrame = {
    // filter out update_preimage
    df.filter(col("_change_type").isin("insert", "update_postimage", "delete"))
  }

  def insertCorrIds(
    spark: SparkSession,
    srcTable: String,
    srcIdCol: String,
    domain: SourceDomain,
    checkpointLocation: String
  ): Unit = {
    val (idCol, emptyIdCol) = domain match {
      case DomainA => (Columns.DOMAIN_A_ID, Columns.DOMAIN_B_ID)
      case DomainB => (Columns.DOMAIN_B_ID, Columns.DOMAIN_A_ID)
    }
    val queryName = s"TYPE=CORRCMP-IDS TABLE=$corrTable STREAMING=$srcTable TO=$TableName"

    spark.readStream
      .option("readChangeFeed", "true")
      .table(srcTable)
      .writeStream
      .queryName(queryName)
      .foreachBatch((srcBatch: DataFrame, batchId: Long) => {
        spark.sparkContext.setJobGroup(
          s"BATCH=$batchId",
          s"TYPE=CORRCMP-IDS TABLE=$corrTable STREAMING=$srcTable TO=$TableName"
        )
        // discard update_preimage
        val srcbatchFiltered = filterWithCdf(srcBatch)
        val srcIdsBatch = srcbatchFiltered
          .select(srcIdCol)
          // Using CDF it receives only rows that are changed and not unchanged rows for file rewrite
          // Filter rows
          // -> insert/update_postimage as generated by the current J2S processing logic
          // --> delete can happen only for manual patch operation or purge
          // ---> track as well to recompute its correlation and propagate changes in corr db
          .distinct()
          .withColumnRenamed(srcIdCol, idCol)
          .withColumn(emptyIdCol, lit(null))
          .select(Columns.DOMAIN_A_ID, Columns.DOMAIN_B_ID)

        // Use an append-only approach to avoid an additional read using an update approach
        // and it avoids rewrites files
        srcIdsBatch.write
          .format("delta")
          .mode("append")
          .saveAsTable(FullTableName)
      })
      .trigger(Trigger.AvailableNow())
      .options(
        Map[String, String](
          ("checkpointLocation", checkpointLocation + '/' + srcTable + '_' + TableName)
        )
      )
      .start()
      .awaitTermination()
  }

  def streamCorrIds(
    spark: SparkSession,
    checkpointLocation: String,
    process: (DataFrame, Long) => Unit
  ): Unit = {
    val queryName = s"TYPE=CORRCMP TABLE=$corrTable STREAMING=$TableName"
    spark.readStream
      .option("ignoreChanges", "true")
      .table(FullTableName)
      .writeStream
      .queryName(queryName)
      .foreachBatch((srcBatch: DataFrame, id: Long) => process(srcBatch, id))
      .trigger(Trigger.AvailableNow())
      .options(
        Map[String, String](
          ("checkpointLocation", checkpointLocation + '/' + TableName)
        )
      )
      .start()
      .awaitTermination()
  }

}

object CorrelationIdsTable {
  object Columns {
    val DOMAIN_A_ID = "DOMAIN_A_ID"
    val DOMAIN_B_ID = "DOMAIN_B_ID"
  }
}
