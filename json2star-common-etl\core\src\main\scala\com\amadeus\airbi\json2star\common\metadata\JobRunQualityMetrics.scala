package com.amadeus.airbi.json2star.common.metadata

import com.amadeus.airbi.json2star.common.app.Metrics

import scala.collection.immutable.ListMap

/** This file contains the case classes and corresponding objects for the different sections of the json object stored
  * into the DETAILS column of the METADATA_JOB_RUN table.
  *
  * There is one case class/object for each base addon (or group of related base addons like
  * correlation/source-correlation/latest).
  *
  * The case class provides the data model of the information we want to expose in the json for that section (e.g. for
  * the "mapping" section, the data model is [[MappingQualityMetrics]]).
  *
  * The object provides the name of the section and the necessary logic to pass from the Spark [[Metrics]] to the above
  * data model.
  */

/** Data quality metadata for Mapping base addon
  */
case class MappingQualityMetrics(
  totProcessed: Long,
  transformed: Long,
  dropped: Long,
  transformErrors: Long,
  tableRowCounts: ListMap[String, Int]
)

object MappingQualityMetrics {
  val name: String = "mapping"

  def fromMetrics(metrics: Metrics): MappingQualityMetrics = MappingQualityMetrics(
    totProcessed = metrics.totProcessed,
    transformed = metrics.transformed.value,
    dropped = metrics.dropped.value,
    transformErrors = metrics.transformErrors.value,
    tableRowCounts = ListMap(metrics.tableRowCounts.value.toSeq.sortBy(_._1): _*)
  )
}

/** Data quality metadata for Correlation base addon
  */
case class CorrelationQualityMetrics(
  // only a placeholder for the moment
  val test: Long
)

object CorrelationQualityMetrics {
  val name: String = "correlation"
}
