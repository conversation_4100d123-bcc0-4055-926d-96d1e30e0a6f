package com.amadeus.airbi.json2star.common.addons.base.mapping.input.checkpoints

import com.amadeus.airbi.json2star.common.addons.base.mapping.MappingAddonParams.{
  AutoloaderStart,
  StartConfig,
  StaticStart
}
import io.delta.tables.DeltaTable
import org.apache.spark.sql.functions.col
import org.apache.spark.sql.{DataFrame, SparkSession}
import org.json4s.jackson.Serialization
import org.json4s.{CustomSerializer, DefaultFormats, Extraction, Formats, JField, JObject, JString}

import java.sql.{Date, Timestamp}
import java.time.LocalDate
import java.time.format.DateTimeFormatter

/** Delta table providing the storage layer of the custom checkpointing mechanism introduced with
  * static-start feature (BDS-25132).
  *
  * For the table schema, see the companion object (cf. `StartCheckpointsTable.Schema`).
  *
  * The table stores the necessary information to detect the state of the stream processing.
  * For a detailed description of the processing logic and steps, please refer to the `StreamPlanner`.
  *
  * The table also stores an immutable view of the start configuration (static-start or autoloader-start).
  * Any change to this immutable configuration will result in a failure of the custom streaming mechanism.
  * See `ImmutableStartConfig` in the companion object for more details.
  *
  * @param spark spark session
  * @param location path to the delta table
  */
class StartCheckpointsTable(spark: SparkSession, location: String) {

  import StartCheckpointsTable._
  import ImmutableStartConfigJsonSerialization.formats
  import org.json4s.jackson.Serialization.{read, write}
  import spark.implicits._

  private val FullTablePath = s"$location/${StartCheckpointsTable.Name}"

  def readStoredConfig(): Option[(Timestamp, ImmutableStartConfig)] = {
    createTableIfNotExists()
    val df = DeltaTable.forPath(spark, FullTablePath).toDF
    val configRows = df
      .filter(col(Columns.ROW_TYPE) === RowType.CONFIG_ROW)
      .as[Row]
      .collect()
    assert(configRows.length <= 1, s"There should be at most one row with ${Columns.ROW_TYPE} = ${RowType.CONFIG_ROW}")
    configRows.headOption.map(i => (i.START_TIMESTAMP, read[ImmutableStartConfig](i.START_CONFIG)))
  }

  def storeConfig(startMode: ImmutableStartConfig, ts: Timestamp): Unit = {
    createTableIfNotExists()
    val configRow = StartCheckpointsTable.Row(
      ROW_TYPE = RowType.CONFIG_ROW,
      START_TIMESTAMP = ts,
      START_CONFIG = write(startMode)
    )
    val rowDf = toDF(configRow)
    rowDf.write
      .format("delta")
      .mode("append")
      .save(FullTablePath)
  }

  /** Create a DataFrame from a single Row enforcing the schema preserving the nullability information
    */
  private def toDF(configRow: Row) = {
    spark.createDataFrame(Seq(configRow).toDF().rdd, Schema)
  }

  /** Logs a row corresponding to a static-start batch.
    * This kind of rows must always be logged after a static batch has been successfully processed.
    */
  def logBatch(
    firstProcessedDate: LocalDate,
    lastProcessedDate: LocalDate,
    inputDates: String,
    startTimestamp: Timestamp,
    endTimestamp: Timestamp,
    staticStartFinished: Boolean,
    config: ImmutableStartConfig
  ): Unit = {
    val row = StartCheckpointsTable.Row(
      ROW_TYPE = StartCheckpointsTable.RowType.BATCH_ROW,
      FIRST_PROCESSED_DATE = Date.valueOf(firstProcessedDate),
      LAST_PROCESSED_DATE = Date.valueOf(lastProcessedDate),
      INPUT_DATES = inputDates,
      START_TIMESTAMP = startTimestamp,
      END_TIMESTAMP = endTimestamp,
      STATIC_START_FINISHED = staticStartFinished,
      USED_AUTOLOADER_INPUT = null,
      START_CONFIG = Serialization.write(config)
    )
    createTableIfNotExists()
    assert(config.isInstanceOf[ImmutableStaticStart], "Batch rows must have a StaticStart config")
    toDF(row).write
      .format("delta")
      .mode("append")
      .save(FullTablePath)
  }

  /** Logs a row corresponding to an autoloader month step.
    * This kind of rows must always be logged after an entire month has been entirely processed.
    */
  def logAutoloader(
    lastProcessedDate: LocalDate,
    endTimestamp: Timestamp,
    usedAutoloaderInput: Seq[String],
    config: ImmutableStartConfig
  ): Unit = {
    val row = StartCheckpointsTable.Row(
      ROW_TYPE = StartCheckpointsTable.RowType.AUTOLOADER_ROW,
      FIRST_PROCESSED_DATE = null,
      LAST_PROCESSED_DATE = Date.valueOf(lastProcessedDate),
      INPUT_DATES = null,
      START_TIMESTAMP = null,
      END_TIMESTAMP = endTimestamp,
      STATIC_START_FINISHED = true, // static start is finished when logging an AUTOLOADER_ROW
      USED_AUTOLOADER_INPUT = usedAutoloaderInput,
      START_CONFIG = Serialization.write(config)
    )
    createTableIfNotExists()
    assert(config.isInstanceOf[ImmutableStaticStart], "Autoloader rows must have a StaticStart config")
    assert(usedAutoloaderInput.nonEmpty, "USED_AUTOLOADER_INPUT must not be empty when logging an AUTOLOADER_ROW")
    toDF(row).write
      .format("delta")
      .mode("append")
      .save(FullTablePath)
  }

  /** Reads the static-start offset, defined as the last processed date in static batch mode and the static start
    * finished flag.
    */
  def readStaticStartOffset(): Option[(LocalDate, Boolean)] = {
    createTableIfNotExists()
    val df = DeltaTable.forPath(spark, FullTablePath).toDF
    val lastBatchRow = df
      .filter(col(Columns.ROW_TYPE) === RowType.BATCH_ROW)
      .orderBy(col(Columns.END_TIMESTAMP).desc)
      .limit(1)
      .select(col(Columns.LAST_PROCESSED_DATE), col(Columns.STATIC_START_FINISHED))
      .as[(Date, Boolean)]
      .collect()
      .headOption
    lastBatchRow.map { case (d, l) => ((d.toLocalDate, l)) }
  }

  /** Reads the autoloader offset, defined as the last processed date in autoloader mode.
    * This date m
    */
  def readAutoloaderOffset(): Option[LocalDate] = {
    createTableIfNotExists()
    val df = DeltaTable.forPath(spark, FullTablePath).toDF
    val lastBatchRow = df
      .filter(col(Columns.ROW_TYPE) === RowType.AUTOLOADER_ROW)
      .orderBy(col(Columns.END_TIMESTAMP).desc)
      .limit(1)
      .select(col(Columns.LAST_PROCESSED_DATE))
      .as[Date]
      .collect()
      .headOption
    lastBatchRow.map { case d => d.toLocalDate }
  }

  def runQuery(where: Option[String] = None): DataFrame = {
    createTableIfNotExists()
    val df = DeltaTable.forPath(spark, FullTablePath).toDF
    where.fold(df)(df.filter)
  }

  def createTableIfNotExists(): Unit = {
    DeltaTable
      .createIfNotExists(spark)
      .property("delta.autoOptimize.optimizeWrite", "true")
      .property("delta.autoOptimize.autoCompact", "true")
      .location(FullTablePath)
      .addColumns(StartCheckpointsTable.Schema)
      .partitionedBy(StartCheckpointsTable.Columns.STATIC_START_FINISHED)
      .execute()
  }

  def delete(): Unit = {
    DeltaTable.forPath(spark, FullTablePath).delete()
  }

}

object StartCheckpointsTable {

  val Name: String = "START_CHECKPOINTS"

  object RowType {
    val AUTOLOADER_ROW = "AUTOLOADER_ROW"
    val BATCH_ROW = "BATCH_ROW"
    val CONFIG_ROW = "CONFIG_ROW"
  }

  object Columns {
    val ROW_TYPE = "ROW_TYPE"
    val FIRST_PROCESSED_DATE = "FIRST_PROCESSED_DATE"
    val LAST_PROCESSED_DATE = "LAST_PROCESSED_DATE"
    val INPUT_DATES = "INPUT_DATES"
    val START_TIMESTAMP = "START_TIMESTAMP"
    val END_TIMESTAMP = "END_TIMESTAMP"
    val STATIC_START_FINISHED = "STATIC_START_FINISHED"
    val USED_AUTOLOADER_INPUT = "USED_AUTOLOADER_INPUT"
    val START_CONFIG = "START_CONFIG"
  }

  case class Row(
    ROW_TYPE: String,
    FIRST_PROCESSED_DATE: Date = null,
    LAST_PROCESSED_DATE: Date = null,
    INPUT_DATES: String = null,
    START_TIMESTAMP: Timestamp,
    END_TIMESTAMP: Timestamp = null,
    STATIC_START_FINISHED: Boolean = false,
    USED_AUTOLOADER_INPUT: Seq[String] = null,
    START_CONFIG: String
  )

  import org.apache.spark.sql.types._

  val Schema: StructType = StructType(
    Seq(
      StructField(Columns.ROW_TYPE, StringType, nullable = false),
      StructField(Columns.FIRST_PROCESSED_DATE, DateType, nullable = true),
      StructField(Columns.LAST_PROCESSED_DATE, DateType, nullable = true),
      StructField(Columns.INPUT_DATES, StringType, nullable = true),
      StructField(Columns.START_TIMESTAMP, TimestampType, nullable = true),
      StructField(Columns.END_TIMESTAMP, TimestampType, nullable = true),
      StructField(Columns.STATIC_START_FINISHED, BooleanType, nullable = false),
      StructField(Columns.USED_AUTOLOADER_INPUT, ArrayType(StringType, containsNull = false), nullable = true),
      StructField(Columns.START_CONFIG, StringType, nullable = false)
    )
  )

  /** Immutable view of the start configuration.
    */
  sealed trait ImmutableStartConfig
  case class ImmutableStaticStart(
    inputPathRegexp: String,
    startDate: Option[LocalDate],
    endDate: Option[LocalDate],
    inputPaths: Seq[String]
  ) extends ImmutableStartConfig
  case class ImmutableAutoloaderStart(inputPaths: Seq[String]) extends ImmutableStartConfig

  object ImmutableStartConfig {
    def from(startConfig: StartConfig, inputPaths: Seq[String]): ImmutableStartConfig = startConfig match {
      case StaticStart(regex, _, startDate, endDate) =>
        ImmutableStaticStart(regex, startDate, endDate, inputPaths)
      case AutoloaderStart() =>
        ImmutableAutoloaderStart(inputPaths)
    }
  }

  /** Custom Json serializer for ImmutableStartConfig ADT */
  object ImmutableStartConfigJsonSerialization {

    object LocalDateSerializer
        extends CustomSerializer[LocalDate](format =>
          (
            { case JString(date) =>
              LocalDate.parse(date, DateTimeFormatter.ISO_LOCAL_DATE)
            },
            { case date: LocalDate =>
              JString(date.format(DateTimeFormatter.ISO_LOCAL_DATE))
            }
          )
        )

    object ImmutableStartConfigSerializer
        extends CustomSerializer[ImmutableStartConfig](format =>
          (
            { case jsonObj: JObject =>
              (jsonObj \ "type").extract[String] match {
                case "immutable-static-start" => jsonObj.extract[ImmutableStaticStart]
                case "immutable-autoloader-start" => jsonObj.extract[ImmutableAutoloaderStart]
              }
            },
            {
              case staticStart: ImmutableStaticStart =>
                JObject(
                  JField("type", JString("immutable-static-start")) :: Extraction
                    .decompose(staticStart)(DefaultFormats + LocalDateSerializer)
                    .asInstanceOf[JObject]
                    .obj
                )
              case autoloaderStart: ImmutableAutoloaderStart =>
                JObject(
                  JField("type", JString("immutable-autoloader-start")) :: Extraction
                    .decompose(autoloaderStart)(DefaultFormats + LocalDateSerializer)
                    .asInstanceOf[JObject]
                    .obj
                )
            }
          )
        )
    implicit val formats: Formats = DefaultFormats + ImmutableStartConfigSerializer + LocalDateSerializer
  }
}
