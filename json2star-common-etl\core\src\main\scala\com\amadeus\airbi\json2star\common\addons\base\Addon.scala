package com.amadeus.airbi.json2star.common.addons.base

import com.amadeus.airbi.json2star.common.{Schema, TableDef}
import com.amadeus.airbi.rawvault.common.application.config.{TableConfig, TablesConfig}

/**
  * Addon (data added value)
  *
  * The addon is a feature that allows to generate a new table (from raw data, other table/s, ...).
  * An addon represents a kind of table with added value respect to its source.
  *
  * Examples of addons are:
  * - mapping (a table generated from a mapping)
  * - correlation (a table generated from the correlation logic, from multiple mapping tables)
  * - latest (a table generated from a mapping table)
  *
  * @tparam T the type of the configuration used to apply the addon
  */
trait Addon[T <: AddonConfig] {

  /**
    * Retrieve the configuration for the addon
    * @param t the table raw configuration
    * @return the configuration, or none if this addon is not applicable to the table
    */
  def getConfig(t: TableConfig): Option[T]

  /**
    * Enriches the schema with the modifications expected by this addon
    *
    * @param c the general raw configuration
    * @param t the table raw configuration
    * @param a the addon configuration
    * @param td the schema to enrich
    * @return the enriched schema
    */
  def enrichSchema(c: TablesConfig, t: TableConfig, a: T, td: Schema): Schema

  def isApplicable(t: TableConfig): Boolean = getConfig(t).isDefined
  def isApplicable(t: TableDef): Boolean = isApplicable(t.table)

  /**
    * Validates the settings provided in this table for this addon
    *
    * TODO: will evolve, for now issues are meant to be raised via exceptions
    *
    * @param c the general raw configuration
    * @param t the table raw configuration
    */
  def validate(c: TablesConfig, t: TableConfig): Unit
}
