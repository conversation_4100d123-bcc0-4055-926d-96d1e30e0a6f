model-conf-file = /some/domain.conf

stream {
  sink {
    checkpoint-location = "target/test/resources/checkpoints/"
    trigger = "once"
    delta-options = {}
  }
}

spark-conf {
  "spark.app.name" = Foo bar
  "spark.driver.memory" = 2g
}

common {
  domain = "DOMAIN"
  output-database = "DB_DOMAIN_1_0_1"
  domain-version = "1_0_1"
  output-path = "abfss://container-name@storage-account/tmp/json2star/output/path"
  shard = "6X"
}

cloud-files-conf {
  use-notifications = true
  backfill-interval = 1 day
  include-existing-files = false
}

snowflake-params {

  spark-conf {
    "spark.app.name" = Foo bar
    "spark.driver.memory" = 2g
  }

  stream {
    sink {
      checkpoint-location = "target/test/resources/checkpoints/snowflake"
      trigger = "availablenow"
      delta-options = {}
    }
  }

  stream-options {
    "maxFilesPerTrigger" = "40000"
  }

  azure-conf {
    secret-scope = keyvault
  }

  snowflake-conf {
    url = {type = clear, value = snowflakecomputing.com}
    user = {type = clear, value = ROBOTIC_DEV}
    pem_private_key = {type = secret, secret-name = my-value}
    role = {type = clear, value = APP_OWNER}
    database = {type = clear, value = database_name}
    schema = {type = clear, value = schema_name}
    warehouse = {type = clear, value = WAREHOUSE_DIHDLK_XS}
  }


}