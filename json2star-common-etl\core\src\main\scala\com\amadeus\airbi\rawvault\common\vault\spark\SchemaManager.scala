package com.amadeus.airbi.rawvault.common.vault.spark

import com.amadeus.airbi.rawvault.common.application.config.{MappingConfig, TableConfig}
import com.amadeus.airbi.rawvault.common.config.ColumnType.ColumnType
import com.amadeus.airbi.rawvault.common.config.{ColumnConfig, ColumnType}
import com.amadeus.airbi.rawvault.common.vault.generators.RowGenerator.{LOAD_DATE_COLUMN_NAME, RECORD_SOURCE_COLUMN_NAME}
import org.apache.spark.sql.types.{DataType, DataTypes, StructType}

object SchemaManager {

  /** Compute the Spark schema of a table based on its config
    *
    * @param cfg
    * @return
    */
  def computeTableSchema(cfg: MappingConfig): StructType =
    cfg.allOrderedColumnsWithLoadDate.foldLeft(new StructType())(
      (s: StructType, c: ColumnConfig) => addColumnType(s, c)
    )

  /** Add a column type to a StructType
    *
    * @param schema       : StructType to be updated
    * @param columnConfig : Column to be added
    * @return a new StructType
    */
  def addColumnType(schema: StructType, columnConfig: ColumnConfig): StructType = {
    addColumnType(schema, columnConfig.name, columnConfig.columnType)
  }

  /** Add a column type to a StructType
    *
    * @param schema : StructType to be updated
    * @param name
    * @param columnType
    * @return a new StructType
    */
  def addColumnType(schema: StructType, name: String, columnType: ColumnType.Value): StructType = {
    schema.add(name, toDataType(columnType))
  }

  def toDataType(columnType: ColumnType): DataType = {
    columnType match {
      case ColumnType.intColumn => DataTypes.IntegerType
      case ColumnType.strColumn => DataTypes.StringType
      case ColumnType.dateColumn => DataTypes.DateType
      case ColumnType.timestampColumn => DataTypes.TimestampType
      case ColumnType.binaryColumn => DataTypes.BinaryType
      case ColumnType.binaryStrColumn => DataTypes.StringType
      case ColumnType.booleanColumn => DataTypes.BooleanType
      case ColumnType.longColumn => DataTypes.LongType
      case ColumnType.floatColumn => DataTypes.FloatType
      case _ => DataTypes.StringType
    }
  }

}
