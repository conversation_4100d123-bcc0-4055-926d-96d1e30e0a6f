package com.amadeus.airbi.rawvault.common.vault.generators

import com.amadeus.airbi.json2star.common.addons.base.mapping.MappingPipeline.TableMapping
import com.amadeus.airbi.json2star.common.addons.base.mapping.Table
import com.amadeus.airbi.json2star.common.addons.base.mapping.Table._
import com.amadeus.airbi.json2star.common.addons.stackable.StackableAddons.stackableAddonLookup
import com.amadeus.airbi.json2star.common.extdata.ExtData
import com.amadeus.airbi.rawvault.common.config.ColSources.BlocksSource
import com.amadeus.airbi.rawvault.common.config.RootSource.rootSourceAsBlocks
import com.amadeus.airbi.rawvault.common.config.{ColumnConfig, RootSource}
import com.amadeus.airbi.rawvault.common.processors.BlockResults.{BlockResult, RecordsSeq}
import com.amadeus.airbi.rawvault.common.processors.JsonProcessor
import com.amadeus.airbi.rawvault.common.vault.generators.RowGenerator._
import com.jayway.jsonpath.DocumentContext
import org.apache.spark.sql.types.{StructField, StructType}

import java.util.UUID
import java.util.regex.{Matcher, Pattern}
import scala.annotation.tailrec

object TableGenerator {

  private val MAX_RECURSIVITY_LEVEL = 5

  private def computeTableRow(
    tableConfig: TableMapping,
    rootSource: RootSource,
    loadDate: String,
    jsonDoc: PreRow
  ): KeyValueRow = {
    val allColumnsForRootSource = tableConfig.conf.allColumns(rootSource)

    val intConfig = allColumnsForRootSource.filter(p => !p.hasVariable)
    val intCols = generateRow(intConfig, jsonDoc, loadDate)

    val columnsWithVariables = allColumnsForRootSource.filter(p => p.hasVariable)
    val resolvedColValues =
      resolveColumnsVariable(
        MAX_RECURSIVITY_LEVEL,
        tableConfig.tableName,
        columnsWithVariables,
        intCols,
        jsonDoc
      )

    // LOAD_DATE column is added by default in all table
    resolvedColValues + (LOAD_DATE_COLUMN_NAME -> loadDate)

  }

  /** purpose is to resolve the case when you are in this case:
    *   column {A : sources : 'source using {B}'}
    *   column {B : sources : 'source using {C}'}
    *   column {C : sources : 'normal-source'} .
    * We call this method recursively so it is able to use resolved column C (in resolvedColValues) to resolve column B
    * and in a 2nd iteration column A.
    *
    * @param maxRecursivityLevel  is here to ensure we don't go in an infinite loop :
    *                            *   {A : sources : 'source using {B}'}
    *                            *   {B : sources : 'source using {C}'}
    *                            *   {C : sources : 'source using {A}'}
    *           We put 5 as value so we limit the depth of this recursive call to 5 level of embdedded column/variables
    *          !!!! LIMITATION TO 5 LEVEL of EMBEDDED VARIABLES !!!!
    * @param tableName current table name
    * @param colsToBeResolved list of columns having a variable in their sources
    * @param resolvedColValues columns for which we were able to found a value in jsonDoc already
    * @param jsonDoc jsonDoc to be used to resolve the variables
    * @return
    */
  @tailrec
  private def resolveColumnsVariable(
    maxRecursivityLevel: Int,
    tableName: String,
    colsToBeResolved: Seq[ColumnConfig],
    resolvedColValues: KeyValueRow,
    jsonDoc: PreRow
  ): KeyValueRow = {
    if (maxRecursivityLevel > 0) { // to avoid inifinite recursivity loop : F1 = {F2}, F2 = {F3}, F3 = {F1}
      colsToBeResolved match {
        case Nil => resolvedColValues
        case _ => {
          val extConfig = colsToBeResolved
            .map(c =>
              c.copy(sources = c.sources match {
                case BlocksSource(l) => BlocksSource(l.map(replaceVariables(_, colsToBeResolved, resolvedColValues)))
                case _ => throw new IllegalArgumentException(s"Only BlockSource supported (table: ${tableName})")
              })
            )
          val extCols = generateRow(extConfig, jsonDoc, loadDate = "")
          resolveColumnsVariable(
            maxRecursivityLevel - 1,
            tableName,
            extConfig.filter(c => detectColumnHasVariable(tableName, c)),
            resolvedColValues ++ extCols,
            jsonDoc
          )
        }
      }
    } else {
      resolvedColValues
    }

  }

  private def sanitizeRow(
    tableConfig: TableMapping,
    recordId: String,
    row: KeyValueRow
  ): Either[TableRowDropReason, KeyValueRow] = {
    val addonConf = tableConfig.conf
    val pkCols = addonConf.merge.keyColumns
    val pkColsValues = pkCols.map(row.get(_))

    val mandatoryCols = addonConf.allOrderedColumnsWithLoadDate.collect { case c if c.isMandatory => c.name }
    val mandatoryColsValues = mandatoryCols.map(c => (c, row.get(c)))

    val allPkValuesAreMissing = pkColsValues.forall(_.isEmpty)
    val mandatoryValueMissingCols = mandatoryColsValues.collect { case (k, v) if v.isEmpty => k }
    val coord = RowDropCoordinates(recordId, tableConfig.tableName)
    if (allPkValuesAreMissing) {
      Left(AllPrimaryKeysNull(coord, s"All PK columns are null"))
    } else if (mandatoryValueMissingCols.nonEmpty) {
      Left(MandatoryColMissing(coord, s"Mandatory column/s ${mandatoryValueMissingCols.mkString(",")} are null"))
    } else {
      Right(row)
    }
  }

  def computeTableRows(
    tableConfig: TableMapping,
    recordId: String,
    rootSource: RootSource,
    tableDocs: Seq[PreRow],
    loadDate: String
  ): Seq[Either[TableRowDropReason, KeyValueRow]] = {
    tableDocs.map { i =>
      val row = computeTableRow(tableConfig, rootSource, loadDate, i)
      val sanitizedRow = sanitizeRow(tableConfig, recordId, row)
      sanitizedRow
    }
  }

  private def detectColumnHasVariable(tableName: String, c: ColumnConfig): Boolean = {
    c.sources match {
      case BlocksSource(l) => l.exists(s => detectVariables(s).nonEmpty)
      case _ => throw new IllegalArgumentException(s"Only BlockSource supported (table: ${tableName})")
    }
  }

  private def detectVariables(source: PreRowQuery): Seq[String] = {
    class VariableIterator(m: Matcher) extends Iterator[String] {
      override def hasNext: Boolean = m.find()
      override def next(): String = m.group(1)
    }
    val regex = Pattern.compile("\\{(.*?)\\}")
    val regexMatcher = regex.matcher(source.path)
    val variables = new VariableIterator(regexMatcher).toList
    variables
  }

  /** replace the variables by their already resolved values
    * @param source : where to replace the variable
    * @param tobeResolvedCols : columns that are still to be resolved (still contain variables)
    * @param resolvedCols : resolved values to be used
    * @return
    */
  private def replaceVariables(
    source: PreRowQuery,
    tobeResolvedCols: Seq[ColumnConfig],
    resolvedCols: KeyValueRow
  ): PreRowQuery = {
    val variables = detectVariables(source)
    variables.foldLeft(source)((s, v) =>
      s.copy(path =
        s.path.replace(
          s"{$v}",
          // if it s not part of th column to still be resolved then
          try {
            resolvedCols(v)
          } catch {
            //in case the variable is not found in the resolvedCols,
            case e: NoSuchElementException =>
              //if the variable is a column that is also part of the tobeResolvedCols
              if (tobeResolvedCols.exists(c => c.name == v)) {
                //don t resolve it
                s"{$v}"
              } else {
                // we replace it with a random UUID to be sure we won't match in the jsonDoc after
                s"COL_NOT_FOUND_${UUID.randomUUID().toString}"
              }
          }
        )
      )
    )
  }

  /** Generate a table out of a config and a Json document
    */
  def createTable(
    recordId: String,
    loadDate: String,
    tableConfig: TableMapping,
    jsonDocument: DocumentContext,
    extData: ExtData
  ): Table = {
    val tablesFromAllPossibleRootSources =
      tableConfig.conf.rootSources.map(rs => rsToRows(recordId, loadDate, tableConfig, jsonDocument, rs))

    val rows = tablesFromAllPossibleRootSources.flatten
    val rowsAndStats = rows.map {
      case Right(row) =>
        (Some(row), Stats.Zero)
      // Monitoring metrics reporting to be addressed properly in https://rndwww.nce.amadeus.net/agile/browse/BDS-841
      case Left(err: MandatoryColMissing) =>
        logger.trace(err.toDebugString)
        (None, Stats.Zero.copy(rowDiscardedCosMandatoryCols = 1))
      case Left(err: AllPrimaryKeysNull) =>
        logger.trace(err.toDebugString)
        (None, Stats.Zero.copy(rowDiscardedCosPrimaryKeyNull = 1))
    }
    val stats = rowsAndStats.map(_._2).foldLeft(Stats.Zero)(_.aggregate(_))
    val rowsAsKV = rowsAndStats.flatMap(_._1)
    val enrichedRowsAsKV =
      tableConfig.stackableAddons.foldLeft(rowsAsKV)((r, c) =>
        stackableAddonLookup(c).enrichTableRows(r, c, jsonDocument, tableConfig.rootConfig, extData)
      )
    new Table(tableConfig.tableName, tableRowsString(tableConfig.sparkSchema, enrichedRowsAsKV), stats)
  }

  private[generators] def rsToRows(
    recordId: String,
    loadDate: String,
    tableConfig: TableMapping,
    jsonDocument: DocumentContext,
    rs: RootSource
  ): Seq[Either[TableRowDropReason, KeyValueRow]] = {
    // step 1: build the tree with block results (no cartesian product resolution)
    val s1: BlockResult = JsonProcessor.toBlockResultTree(rootSourceAsBlocks(rs), jsonDocument)
    // step 2: explode cartesian product elements
    val s2: RecordsSeq[Attempt[PreRow]] = PreRowGenerator.generatePreRow(s1)
    s2.collect { case Left(e) => logger.trace(s"Table issue '${tableConfig.tableName}': ${e}") }
    val s2ok: Seq[PreRow] = s2.collect { case Right(v) => v }
    // step 3: generate rows
    val s3 = computeTableRows(tableConfig, recordId, rs, s2ok, loadDate)
    s3
  }

  private def tableRowsString(schema: StructType, rowsAsKV: Seq[KeyValueRow]): Seq[TableRowType] = {
    def getFieldValue(values: KeyValueRow, f: StructField): String = {
      if (f.nullable) {
        values.get(f.name).orNull
      } else {
        values(f.name)
      }
    }

    rowsAsKV.map { row =>
      // order fields according to the schema so we don't need column names
      schema.map {
        getFieldValue(row, _)
      }
    }
  }

}
