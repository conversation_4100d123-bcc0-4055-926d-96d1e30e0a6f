package com.amadeus.airbi.json2star.common.addons.stackable.proration

import com.amadeus.airbi.json2star.common.app.Json2StarApp
import com.amadeus.airbi.json2star.common.app.Json2StarApp.readMappingConfig
import com.amadeus.airbi.rawvault.common.testfwk.Json2StarSpec
import org.apache.spark.sql.streaming.StreamingQueryException
import org.scalatest.Ignore

import java.io.File
import scala.reflect.io.Directory

class IntegrationProrationSpec extends Json2StarSpec {

  val mappingFile = "datasets/proration/simplified_tktemd.conf"

  val TableNames: Seq[String] = getTableNames(mappingFile)

  override def beforeAll: Unit = {
    super.beforeAll
    createTables(mappingFile)
  }

  override def beforeEach: Unit = {
    cleanTables(TableNames)
  }

  "ProrationAddon" should "prorate coupons in FACT_COUPON_HISTO" taggedAs (SlowTestTag) in {
    val dataDir = "datasets/proration/data/"
    val optdPath = s"src/test/resources/datasets/proration/extdata/input_optd_por_public_sample.csv"
    val pathExpectedResults = "src/test/resources/" + dataDir + "expected_results/"
    val rc = rootConfig(dataDir, inputDatabase, isLight = DefaultIsLight)
    val rcWithOptd = rc.copy(
      etl = rc.etl.copy(
        common = rc.etl.common.copy(
          prorationEnableMetadataColumn = true,
          refDataOptdLocation = Some(optdPath)
        )
      )
    )

    val directory = new Directory(new File(rcWithOptd.etl.stream.sink.checkpointLocation))
    directory.deleteRecursively()
    val mapping = readMappingConfig(mappingFile)
    Json2StarApp.run(spark, rcWithOptd, mapping)
    checkTablesContent(TableNames, pathExpectedResults, model = Some(mapping), createExpected = false)
    directory.deleteRecursively()
  }

}
