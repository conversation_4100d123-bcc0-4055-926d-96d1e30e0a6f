package com.amadeus.airbi.json2star.common.views.executors

import com.amadeus.airbi.json2star.common.config.AppConfig
import com.amadeus.airbi.json2star.common.views.generators.JDFStatus.JDFStatusValue
import com.amadeus.airbi.json2star.common.views.generators.{DocGenerator, JDFMetadataGenerator, JDFStatus}
import org.rogach.scallop.{ScallopConf, ScallopOption}
import org.slf4j.{Logger, LoggerFactory}

import java.io.{BufferedWriter, File, FileWriter}

case class DataDictExecutorConfig(arguments: Seq[String]) extends ScallopConf(arguments) {
  val appConfigFilePath: ScallopOption[String] = opt[String](required = true)

  val filterHistoColumns: ScallopOption[Boolean] = opt[Boolean](required = false)
  val filterColumnsByRegex: ScallopOption[String] = opt[String](required = false)
  val originOldPrefix: ScallopOption[String] = opt[String](required = false)
  val originNewPrefix: ScallopOption[String] = opt[String](required = false)
  val outputDocFile: ScallopOption[String] = opt[String](required = false, default = None)
  dependsOnAll(outputDocFile, List(appConfigFilePath, originOldPrefix, originNewPrefix, filterHistoColumns))

  val jdfStatus: ScallopOption[JDFStatusValue] =
    opt[JDFStatusValue](required = false, default = None)(JDFStatus.converter)
  val outputJdfFile: ScallopOption[String] = opt[String](required = false, default = None)
  dependsOnAll(outputJdfFile, List(appConfigFilePath, jdfStatus))

  val outputJdfViewsFile: ScallopOption[String] = opt[String](required = false, default = None)
  dependsOnAll(outputJdfViewsFile, List(outputJdfFile))

  verify()
}

object DataDictExecutor {

  implicit lazy val logger: Logger = LoggerFactory.getLogger(getClass.getName)
  def main(args: Array[String]): Unit = {

    val executorConfig = DataDictExecutorConfig(args)
    val appConfig = AppConfig(executorConfig.appConfigFilePath())

    executorConfig.outputDocFile.toOption.foreach { output =>
      val docConfig = DocGenerator.Config(
        modelConfFile = appConfig.modelConfFile,
        tablesSelectors = appConfig.tablesSelectors,
        disabledStackableAddons = appConfig.disabledStackableAddons,
        yamlFilePath = appConfig.validYamlFile,
        filterHistoColumns = executorConfig.filterHistoColumns(),
        filterColumnsByRegex = executorConfig.filterColumnsByRegex.toOption,
        originOldPrefix = executorConfig.originOldPrefix(),
        originNewPrefix = executorConfig.originNewPrefix()
      )
      val docStr = DocGenerator.from(docConfig)
      writeFile(output, docStr)
    }

    executorConfig.outputJdfFile.toOption.foreach { jsonOutput =>
      val jdfConfig = JDFMetadataGenerator.Config(
        modelConfFile = appConfig.modelConfFile,
        tablesSelectors = appConfig.tablesSelectors,
        disabledStackableAddons = appConfig.disabledStackableAddons,
        common = appConfig.common,
        yamlFilePath = appConfig.validYamlFile,
        inputDatabases = appConfig.inputDatabases.getOrElse(Map.empty),
        status = executorConfig.jdfStatus.toOption
      )
      val jsonStr = JDFMetadataGenerator.jsonFrom(jdfConfig)
      writeFile(jsonOutput, jsonStr)

      executorConfig.outputJdfViewsFile.toOption.foreach { sqlOutput =>
        val sqlStr = JDFMetadataGenerator.viewsFrom(jdfConfig)
        writeFile(sqlOutput, sqlStr)
      }
    }
  }

  def writeFile(outFile: String, content: String): Unit = {
    if (outFile == "DEBUG") {
      logger.debug(content)
    } else {
      // Overwrite the file
      val appendFlag = false
      val bw = new BufferedWriter(new FileWriter(new File(outFile), appendFlag))
      bw.write(content)
      bw.close()
    }
  }

}
