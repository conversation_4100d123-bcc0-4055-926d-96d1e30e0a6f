versionExprVal: "bigint({0})"
versionTypeVal: "longColumn"
hashMIdCheckEndNotNull: "if(element_at(split({0},'-'),array_size(split({0},'-'))) == '_', NULL,hashM({0}) )"
hashSIdCheckEndNotNull: "if(element_at(split({0},'-'),array_size(split({0},'-'))) == '_', NULL,hashS({0}) )"

"tables": [
  {
    "name": "FACT_TRAVEL_DOCUMENT_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "description": {"description": "This is the main fact table of the Ticketing star schema. Contains general information of travel documents (tickets, EMDs) such as document numbers (primary, conjunctive, original), overall monetary information for payment (total price/taxes) and refund (total refund/taxes/fees), related points of sale and basic passenger information.", "granularity": "1 travel document"},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Functional key: primDocNum-issuanceDate", "rule": "replace"}, "example": {"value": "1234567890123-2020-01-31", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "PRIMARY_DOCUMENT_NUMBER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.primaryDocumentNumber"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "DOCUMENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.documentType"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "ISSUANCE_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.issuanceType"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "ORIGIN_CITY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.originCityIataCode"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "DESTINATION_CITY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.destinationCityIataCode"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "TRAVELER_FIRST_NAME", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.traveler.name.firstName"}]},
          "meta": {"description": {"value": "First name of the travel document's passenger", "rule": "replace"}, "gdpr-zone": "red"}},
        {"name": "TRAVELER_LAST_NAME", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.traveler.name.lastName"}]},
          "meta": {"description": {"value": "Last name of the travel document's passenger", "rule": "replace"}, "gdpr-zone": "red"}},
        {"name": "TRAVELER_PAX_TYPE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.traveler.passengerTypeCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "TRAVELER_PHONE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.traveler.contact.phone.number"}]}, "meta": {"gdpr-zone": "red"}},
        {"name": "TRAVELER_EMAIL", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.traveler.contact.email.address"}]}, "meta": {"gdpr-zone": "red"}},
        {"name": "ENDORSEMENT_FREETEXT", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.endorsementFreeFlow"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "NUMBER_OF_BOOKLETS", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.numberOfBooklets"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CONJUNCTIVE_DOCUMENT_1", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.conjunctiveDocumentNumbers[1]"}]},
          "meta": {"description": {"value": "Document number of first conjunctive document, if any", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "CONJUNCTIVE_DOCUMENT_2", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.conjunctiveDocumentNumbers[2]"}]},
          "meta": {"description": {"value": "Document number of second conjunctive document, if any", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "CONJUNCTIVE_DOCUMENT_3", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.conjunctiveDocumentNumbers[3]"}]},
          "meta": {"description": {"value": "Document number of third conjunctive document, if any", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "ORIGINAL_DOCUMENT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.originalDocuments[0].documentNumber"}]},
          "meta": {"description": {"value": "Primary document number of the original travel document further to an exchange", "rule": "replace"}, "example": {"value": "1234567890123", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "ORIGINAL_DOCUMENT_CREATION_DATE", "column-type": "dateColumn", "sources": {"blocks": [{"base": "$.originalDocuments[0].creation.dateTime"}]},
          "meta": {"description": {"value": "Date/time UTC of the original document issuance", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "ORIGINAL_ISSUE_FREETEXT", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.originalIssueFreeFlow"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "VOID_DATETIME_UTC", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.void.dateTime"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "VALIDATING_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.validatingCarrierCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "VALIDATING_CARRIER_RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.validatingCarrierPnr.reference"}]}, "meta": {"gdpr-zone": "orange"}},
        {"name": "PRICE_TOTAL", "column-type": "floatColumn", "sources": {},
          "meta": {"description": {"value": "Calculated field: converted price_payment into airline's home currency", "rule": "replace"}, "example": {"value": "514.13", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "PRICE_TAXES", "column-type": "floatColumn", "sources": {},
          "meta": {"description": {"value": "Calculated field: converted total_taxes_payment into airline's home currency", "rule": "replace"}, "example": {"value": "78.53", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "PRICE_CURRENCY", "column-type": "strColumn", "sources": {},
          "meta": {"description": {"value": "Static field: airline's home currency", "rule": "replace"}, "example": {"value": "EUR", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "PRICE_TOTAL_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.price.total"}]},
          "meta": {"example": {"value": "758.5", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "PRICE_TAXES_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.price.totalTaxes"}]},
          "meta": {"description": {"value": "The total amount of taxes contained in price_total_payment, in payment currency", "rule": "replace"}, "example": {"value": "128.12", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "PRICE_CURRENCY_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.price.currency"}]},
          "meta": {"example": {"value": "CAD", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "PRICE_EXCHANGE_RATE_DATE_NEEDED", "column-type": "dateColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"description": {"value": "The date for which the home currency conversion rate was needed", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "PRICE_EXCHANGE_RATE_DATE_TAKEN", "column-type": "dateColumn", "sources": {},
          "meta": {"description": {"value": "The date for which the home currency conversion rate was found and taken", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFUND_TOTAL", "column-type": "floatColumn", "sources": {},
          "meta": {"description": {"value": "Calculated field: converted total_taxes_payment into airline's home currency", "rule": "replace"}, "example": {"value": "514.13", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFUND_CURRENCY", "column-type": "strColumn", "sources": {},
          "meta": {"description": {"value": "Static field: airline's home currency", "rule": "replace"}, "example": {"value": "EUR", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFUND_FARE_PAID", "column-type": "floatColumn", "sources": {}, "meta": {"gdpr-zone": "green"}},
        {"name": "REFUND_FARE_USED", "column-type": "floatColumn", "sources": {}, "meta": {"gdpr-zone": "green"}},
        {"name": "REFUND_FARE_REFUND", "column-type": "floatColumn", "sources": {}, "meta": {"gdpr-zone": "green"}},
        {"name": "REFUND_TAXES", "column-type": "floatColumn", "sources": {}, "meta": {"gdpr-zone": "green"}},
        {"name": "REFUND_CANCELLATION_PENALTY", "column-type": "floatColumn", "sources": {}, "meta": {"gdpr-zone": "green"}},
        {"name": "REFUND_NO_SHOW_FEE", "column-type": "floatColumn", "sources": {}, "meta": {"gdpr-zone": "green"}},
        {"name": "REFUND_OB_FEES", "column-type": "floatColumn", "sources": {}, "meta": {"gdpr-zone": "green"}},
        {"name": "REFUND_TOTAL_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.documentRefund.refundTotal.amount"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REFUND_CURRENCY_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.documentRefund.refundTotal.currency"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REFUND_FARE_PAID_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.documentRefund.farePaid.amount"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REFUND_FARE_USED_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.documentRefund.fareUsed.amount"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REFUND_FARE_REFUND_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.documentRefund.fareRefund.amount"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REFUND_TAXES_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.documentRefund.totalTax.amount"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REFUND_CANCELLATION_PENALTY_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.documentRefund.cancellationPenalty.amount"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REFUND_NO_SHOW_FEE_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.documentRefund.noShowFee.amount"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REFUND_OB_FEES_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.documentRefund.totalObFee.amount"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REFUND_EXCHANGE_RATE_DATE_NEEDED", "column-type": "dateColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},  // TODO : refund date should be DATE_BEGIN when the refund has happened (arg... so the min() value of the first TKT version which has REFUND filled)
          "meta": {"description": {"value": "The date for which the home currency conversion rate was needed", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFUND_EXCHANGE_RATE_DATE_TAKEN", "column-type": "dateColumn", "sources": {},
          "meta": {"description": {"value": "The date for which the home currency conversion rate was found and taken", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "ORIGIN_CITY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.originCityIataCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_CITY", "column": "CITY_ID"}]},
        {"name": "DESTINATION_CITY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.destinationCityIataCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_CITY", "column": "CITY_ID"}]},
        {"name": "VALIDATING_CARRIER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.validatingCarrierCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_AIRLINE", "column": "AIRLINE_ID"}]},
        {"name": "CREATION_POINT_OF_SALE_ID", "column-type": "binaryStrColumn",
          "sources": {"blocks": [{"base": "$.creation.pointOfSale.office.id"}, {"base": "$.creation.pointOfSale.office.iataNumber"}, {"base": "$.creation.pointOfSale.office.inHouseIdentification"}, {"base": "$.creation.pointOfSale.office.systemCode"}, {"base": "$.creation.pointOfSale.office.agentType"}, {"base": "$.creation.pointOfSale.login.cityCode"}, {"base": "$.creation.pointOfSale.login.countryCode"}, {"base": "$.creation.pointOfSale.login.numericSign"}, {"base": "$.creation.pointOfSale.login.initials"}, {"base": "$.creation.pointOfSale.login.dutyCode"}]}, "expr": "hashM({0})",
          "fk": [{"table": "DIM_POINT_OF_SALE", "column":"POINT_OF_SALE_ID"}]},
        {"name": "LAST_UPDATE_POINT_OF_SALE_ID", "column-type": "binaryStrColumn",
          "sources": {"blocks": [{"base": "$.lastestEvent.pointOfSale.office.id"}, {"base": "$.lastestEvent.pointOfSale.office.iataNumber"}, {"base": "$.lastestEvent.pointOfSale.office.inHouseIdentification"}, {"base": "$.lastestEvent.pointOfSale.office.systemCode"}, {"base": "$.lastestEvent.pointOfSale.office.agentType"}, {"base": "$.lastestEvent.pointOfSale.login.cityCode"}, {"base": "$.lastestEvent.pointOfSale.login.countryCode"}, {"base": "$.lastestEvent.pointOfSale.login.numericSign"}, {"base": "$.lastestEvent.pointOfSale.login.initials"}, {"base": "$.lastestEvent.pointOfSale.login.dutyCode"}]}, "expr": "hashM({0})",
          "fk": [{"table": "DIM_POINT_OF_SALE", "column":"POINT_OF_SALE_ID"}]},
        {"name": "ORIGINAL_DOCUMENT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.originalDocuments[0].documentNumber"},{"base": "$.originalDocuments[0].creation.dateTime"}]}, "expr": ${hashMIdCheckEndNotNull},
          "meta": {"description": {"value": "Hashed foreign key (representing the original travel document further to an exchange)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DOCUMENT_CREATION_DATE", "column-type": "dateColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"description": {"value": "Date/time UTC of the document issuance", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]},
          "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "master-pit-table",
        "master": {
          "pit-key": "INTERNAL_ZORDER",
          "pit-version": "VERSION",
          "valid-from": "DATE_BEGIN",
          "valid-to": "DATE_END",
          "is-last": "IS_LAST_VERSION"
        }
      }
    }
  },
  // validation ok
  {
    "name": "FACT_COUPON_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "description": {"description": "Contains detailed information on coupons of travel documents, such as related flight segments (sold, used, current - with marketing and operating flight information), coupon status, estimated prorated fare, fare basis/family and further indicators on fare conditions (exchangeable, refundable) and baggage allowance.", "granularity": "1 coupon for 1 travel document"},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "COUPON_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [
          {"base": "$.mainResource.current.image"},
          {"cpn": "$.coupons[*]"}
        ]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"cpn": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"cpn": "$.id"}]},
          "meta": {"description": {"value": "Functional key: docId-cpnSeqNum", "rule": "replace"}, "example": {"value": "1234567890123-2020-01-31-1", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "COUPON_DOCUMENT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.documentNumber"}]},
          "meta": {"example": {"value": "1234567890123", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "COUPON_NUMBER", "column-type": "intColumn", "sources": {"blocks": [{"cpn": "$.number"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "SEQUENCE_NUMBER", "column-type": "intColumn", "sources": {"blocks": [{"cpn": "$.sequenceNumber"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "RESERVATION_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.reservationStatus"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "COUPON_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.status"}]}, "meta": {"gdpr-zone": "green"}},
        //CURRENT SEGMENT
        {"name": "CURRENT_DEPARTURE_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.departure.iataCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_ARRIVAL_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.arrival.iataCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_DEPARTURE_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.departure.localDateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_ARRIVAL_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.arrival.localDateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_MKT_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.carrierCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_MKT_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.number"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_MKT_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.suffix"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_MKT_BOOKING_CLASS", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.bookingClass.code"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_OPE_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.operating.flightDesignator.carrierCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_OPE_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.operating.flightDesignator.flightNumber"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_OPE_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.operating.flightDesignator.operationalSuffix"}]}, "meta": {"gdpr-zone": "green"}},
        //SOLD SEGMENT
        {"name": "SOLD_DEPARTURE_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.departure.iataCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_ARRIVAL_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.arrival.iataCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_DEPARTURE_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.departure.localDateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_ARRIVAL_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.arrival.localDateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_MKT_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.carrierCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_MKT_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.number"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_MKT_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.suffix"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_MKT_BOOKING_CLASS", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.bookingClass.code"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_OPE_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.operating.flightDesignator.carrierCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_OPE_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.operating.flightDesignator.flightNumber"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_OPE_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.operating.flightDesignator.operationalSuffix"}]}, "meta": {"gdpr-zone": "green"}},
        //USED SEGMENT
        {"name": "USED_DEPARTURE_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.departure.iataCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_ARRIVAL_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.arrival.iataCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_DEPARTURE_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.departure.localDateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "USED_ARRIVAL_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.arrival.localDateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "USED_MKT_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.carrierCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_MKT_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.number"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_MKT_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.suffix"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_MKT_BOOKING_CLASS", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.bookingClass.code"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_OPE_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.operating.flightDesignator.carrierCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_OPE_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.operating.flightDesignator.flightNumber"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_OPE_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.operating.flightDesignator.operationalSuffix"}]}, "meta": {"gdpr-zone": "green"}},

        {"name": "FARE_BASIS_CODE", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.fareBasis.fareBasisCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "FARE_FAMILY_CODE", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.fareFamily.code"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "FARE_FAMILY_OWNER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.fareFamily.owner"}]}, "meta": {"gdpr-zone": "green"}},
        //PRORATION
        {"name": "ESTIMATED_PRORATED_FARE", "column-type": "floatColumn", "sources": {},
          "meta": {"example": {"value": "68.91", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "ESTIMATED_PRORATED_FARE_CURRENCY", "column-type": "strColumn", "sources": {},
          "meta": {"example": {"value": "EUR", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "ESTIMATED_PRORATED_FARE_ORIGINAL", "column-type": "strColumn", "sources": {},
          "meta": {"description": {"value": "The original prorated amount extracted from the fare calc line or by distance", "rule": "replace"}, "example": {"value": "722.55", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "ESTIMATED_PRORATED_FARE_CURRENCY_ORIGINAL", "column-type": "strColumn", "sources": {},
          "meta": {"description": {"value": "The original currency of proration amount: for FARE-CALC it is the extracted fare calculation currency (can be NUC), and for DISTANCE it is the payment currency", "rule": "replace"}, "example": {"value": "USD", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "ESTIMATED_PRORATED_FARE_ALGORITHM", "column-type": "strColumn", "sources": {},
          "meta": {"description": {"value": "Indicates which algorithm was used to estimate the prorated fare: FARE-CALC, DISTANCE or NONE in case of error", "rule": "replace"}, "example": {"value": "FARE-CALC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "PRORATION_EXCHANGE_RATE_DATE_NEEDED", "column-type": "dateColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"description": {"value": "The date for which the home currency conversion rate was needed", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "PRORATION_EXCHANGE_RATE_DATE_TAKEN", "column-type": "dateColumn", "sources": {},
          "meta": {"description": {"value": "The date for which the home currency conversion rate was found and taken", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "INTERNAL_PRORATION_METADATA", "column-type": "strColumn", "sources": {},
          "meta": {"description": {"value": "Technical field containing details on the used proration estimation algorithm", "rule": "replace"}, "gdpr-zone": "green"}},

        {"name": "REASON_FOR_ISSUANCE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.reasonForIssuance.code"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REASON_FOR_ISSUANCE_SUB_CODE", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.reasonForIssuance.subCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REASON_FOR_ISSUANCE_DESCRIPTION", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.reasonForIssuance.description"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SERVICE_REMARK", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.serviceRemark"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "IS_CODESHARE", "column-type": "booleanColumn", "sources": {"blocks": [{"cpn": "$.isCodeshare"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "IS_NON_EXCHANGEABLE", "column-type": "booleanColumn", "sources": {"blocks": [{"base": "$.isNonExchangeable"}]},
          "meta": {"description": {"value": "Indicates if the coupon is non-exchangeable (true), or if it can be exchanged (false)", "rule": "replace"}, "example": {"value": "TRUE", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_NON_REFUNDABLE", "column-type": "booleanColumn", "sources": {"blocks": [{"base": "$.isNonRefundable"}]},
          "meta": {"description": {"value": "Indicates if the coupon is non-refundable (true), or if it can be refunded (false)", "rule": "replace"}, "example": {"value": "TRUE", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_FROM_CONNECTION", "column-type": "booleanColumn", "sources": {"blocks": [{"cpn": "$.isFromConnection"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_ALLOWANCE_WEIGHT_VALUE", "column-type": "floatColumn", "sources": {},
          "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_ALLOWANCE_WEIGHT_UNIT", "column-type": "strColumn", "sources": {},
          "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_ALLOWANCE_WEIGHT_VALUE_ORIGINAL", "column-type": "floatColumn", "sources": {"blocks": [{"cpn": "$.baggageAllowance.weight.amount"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_ALLOWANCE_WEIGHT_UNIT_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.baggageAllowance.weight.unit"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_ALLOWANCE_QUANTITY", "column-type": "intColumn", "sources": {"blocks": [{"cpn": "$.baggageAllowance.quantity"}]},
          "meta": {"example": {"value": "2", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "BAGGAGE_EXCESS_RATE", "column-type": "floatColumn", "sources": {}, "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_EXCESS_RATE_CURRENCY", "column-type": "strColumn", "sources": {}, "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_EXCESS_RATE_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.baggageAllowance.excessRate.amount"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_EXCESS_RATE_CURRENCY_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.baggageAllowance.excessRate.currency"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_EXCHANGE_RATE_DATE_NEEDED", "column-type": "dateColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"description": {"value": "The date for which the home currency conversion rate was needed", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "BAGGAGE_EXCHANGE_RATE_DATE_TAKEN", "column-type": "dateColumn", "sources": {},
          "meta": {"description": {"value": "The date for which the home currency conversion rate was found and taken", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REVALIDATION_DATETIME_UTC", "column-type": "timestampColumn", "sources": {"blocks": [{"cpn": "$.revalidation.dateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "REVALIDATION_OFFICE_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.revalidation.pointOfSale.office.id"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REVALIDATION_OFFICE_IATA_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.revalidation.pointOfSale.office.iataNumber"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REVALIDATION_OFFICE_IN_HOUSE_IDENTIFICATION", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.revalidation.pointOfSale.office.inHouseIdentification"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REVALIDATION_OFFICE_SYSTEM_CODE", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.revalidation.pointOfSale.office.systemCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REVALIDATION_OFFICE_AGENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.revalidation.pointOfSale.office.agentType"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "NOT_VALID_BEFORE_DATE_UTC", "column-type": "dateColumn", "sources": {"blocks": [{"cpn": "$.validityDates.notValidBeforeDate"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "NOT_VALID_AFTER_DATE_UTC", "column-type": "dateColumn", "sources": {"blocks": [{"cpn": "$.validityDates.notValidAfterDate"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "VOLUNTARY_INDICATOR", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.voluntaryIndicator"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "SETTLEMENT_AUTHORIZATION_CODE", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.settlementAuthorizationCode"}]}, "meta": {"gdpr-zone": "green"}},
        //IDs
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})", "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}]},
        {"name": "CURRENT_DEPARTURE_AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.departure.iataCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}]},
        {"name": "CURRENT_ARRIVAL_AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.arrival.iataCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}]},
        {"name": "SOLD_DEPARTURE_AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.departure.iataCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}]},
        {"name": "SOLD_ARRIVAL_AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.arrival.iataCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}]},
        {"name": "USED_DEPARTURE_AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.departure.iataCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}]},
        {"name": "USED_ARRIVAL_AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.arrival.iataCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}]},

        {"name": "REASON_FOR_ISSUANCE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.reasonForIssuance.code"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_REASON_FOR_ISSUANCE", "column": "REASON_FOR_ISSUANCE_ID"}]},
        {"name": "MEMBERSHIP_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"cpn": "$.frequentFlyer.applicableAirlineCode"}, {"cpn": "$.frequentFlyer.frequentFlyerNumber"}]}, "expr": ${hashSIdCheckEndNotNull}, "fk": [{"table": "FACT_MEMBERSHIP_HISTO", "column": "MEMBERSHIP_ID"}]},
        {"name": "REVALIDATION_POINT_OF_SALE_ID", "column-type": "binaryStrColumn",
          "sources": {"blocks": [{"cpn": "$.revalidation.pointOfSale.office.id"}, {"cpn": "$.revalidation.pointOfSale.office.iataNumber"}, {"cpn": "$.revalidation.pointOfSale.office.inHouseIdentification"}, {"cpn": "$.revalidation.pointOfSale.office.systemCode"}, {"cpn": "$.revalidation.pointOfSale.office.agentType"}, {"cpn": "$.revalidation.pointOfSale.login.cityCode"}, {"cpn": "$.revalidation.pointOfSale.login.countryCode"}, {"cpn": "$.revalidation.pointOfSale.login.numericSign"}, {"cpn": "$.revalidation.pointOfSale.login.initials"}, {"cpn": "$.revalidation.pointOfSale.login.dutyCode"}]}, "expr": "hashM({0})",
          "fk": [{"table": "DIM_POINT_OF_SALE", "column": "POINT_OF_SALE_ID"}]},
        {"name": "VOLUNTARY_INDICATOR_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.voluntaryIndicator"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_COUPON_VOLUNTARY_INDICATOR", "column": "COUPON_VOLUNTARY_INDICATOR_ID"}]},

        //final columns
        {"name": "DOCUMENT_NUMBER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.primaryDocumentNumber"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "DOCUMENT_CREATION_DATE", "column-type": "dateColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"description": {"value": "Date/time UTC of the document issuance", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]},
          "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "stackable-addons": [
      {
        "type": "proration",
        "json-path-fare-calc": "$.mainResource.current.image.pricingConditions.fareCalculation.text",
        "json-path-price-currency-payment": "$.mainResource.current.image.price.currency",
        "json-path-price-total-payment": "$.mainResource.current.image.price.total",
        "json-path-price-total-taxes-payment": "$.mainResource.current.image.price.totalTaxes"
      }
    ]
  },
  // validation fails because of missing mandatory columns
  {
    "name": "FACT_FAIL_COLS_HISTO",
    "table-selectors": ["fail"],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "description": {"description": "Contains detailed information on coupons of travel documents, such as related flight segments (sold, used, current - with marketing and operating flight information), coupon status, estimated prorated fare, fare basis/family and further indicators on fare conditions (exchangeable, refundable) and baggage allowance.", "granularity": "1 coupon for 1 travel document"},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "COUPON_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [
          {"base": "$.mainResource.current.image"},
          {"cpn": "$.coupons[*]"}
        ]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"cpn": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"cpn": "$.id"}]},
          "meta": {"description": {"value": "Functional key: docId-cpnSeqNum", "rule": "replace"}, "example": {"value": "1234567890123-2020-01-31-1", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "COUPON_DOCUMENT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.documentNumber"}]},
          "meta": {"example": {"value": "1234567890123", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "COUPON_NUMBER", "column-type": "intColumn", "sources": {"blocks": [{"cpn": "$.number"}]},
          "meta": {"gdpr-zone": "green"}},
        // MISSING MANDATORY COL
//        {"name": "SEQUENCE_NUMBER", "column-type": "intColumn", "sources": {"blocks": [{"cpn": "$.sequenceNumber"}]},
//          "meta": {"gdpr-zone": "green"}},
        {"name": "RESERVATION_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.reservationStatus"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "COUPON_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.status"}]}, "meta": {"gdpr-zone": "green"}},
        //CURRENT SEGMENT
        {"name": "CURRENT_DEPARTURE_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.departure.iataCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_ARRIVAL_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.arrival.iataCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_DEPARTURE_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.departure.localDateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_ARRIVAL_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.arrival.localDateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_MKT_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.carrierCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_MKT_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.number"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_MKT_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.suffix"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_MKT_BOOKING_CLASS", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.bookingClass.code"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_OPE_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.operating.flightDesignator.carrierCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_OPE_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.operating.flightDesignator.flightNumber"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_OPE_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.operating.flightDesignator.operationalSuffix"}]}, "meta": {"gdpr-zone": "green"}},
        //SOLD SEGMENT
        // MISSING MANDATORY COL
//        {"name": "SOLD_DEPARTURE_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.departure.iataCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_ARRIVAL_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.arrival.iataCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_DEPARTURE_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.departure.localDateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_ARRIVAL_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.arrival.localDateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_MKT_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.carrierCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_MKT_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.number"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_MKT_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.suffix"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_MKT_BOOKING_CLASS", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.bookingClass.code"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_OPE_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.operating.flightDesignator.carrierCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_OPE_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.operating.flightDesignator.flightNumber"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_OPE_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.operating.flightDesignator.operationalSuffix"}]}, "meta": {"gdpr-zone": "green"}},
        //USED SEGMENT
        // MISSING NON-MANDATORY COL
//        {"name": "USED_DEPARTURE_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.departure.iataCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_ARRIVAL_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.arrival.iataCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_DEPARTURE_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.departure.localDateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "USED_ARRIVAL_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.arrival.localDateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "USED_MKT_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.carrierCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_MKT_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.number"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_MKT_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.suffix"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_MKT_BOOKING_CLASS", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.bookingClass.code"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_OPE_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.operating.flightDesignator.carrierCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_OPE_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.operating.flightDesignator.flightNumber"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_OPE_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.operating.flightDesignator.operationalSuffix"}]}, "meta": {"gdpr-zone": "green"}},

        {"name": "FARE_BASIS_CODE", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.fareBasis.fareBasisCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "FARE_FAMILY_CODE", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.fareFamily.code"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "FARE_FAMILY_OWNER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.fareFamily.owner"}]}, "meta": {"gdpr-zone": "green"}},
        //PRORATION
        {"name": "ESTIMATED_PRORATED_FARE", "column-type": "floatColumn", "sources": {},
          "meta": {"example": {"value": "68.91", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "ESTIMATED_PRORATED_FARE_CURRENCY", "column-type": "strColumn", "sources": {},
          "meta": {"example": {"value": "EUR", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "ESTIMATED_PRORATED_FARE_ORIGINAL", "column-type": "strColumn", "sources": {},
          "meta": {"description": {"value": "The original prorated amount extracted from the fare calc line or by distance", "rule": "replace"}, "example": {"value": "722.55", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "ESTIMATED_PRORATED_FARE_CURRENCY_ORIGINAL", "column-type": "strColumn", "sources": {},
          "meta": {"description": {"value": "The original currency of proration amount: for FARE-CALC it is the extracted fare calculation currency (can be NUC), and for DISTANCE it is the payment currency", "rule": "replace"}, "example": {"value": "USD", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "ESTIMATED_PRORATED_FARE_ALGORITHM", "column-type": "strColumn", "sources": {},
          "meta": {"description": {"value": "Indicates which algorithm was used to estimate the prorated fare: FARE-CALC, DISTANCE or NONE in case of error", "rule": "replace"}, "example": {"value": "FARE-CALC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "PRORATION_EXCHANGE_RATE_DATE_NEEDED", "column-type": "dateColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"description": {"value": "The date for which the home currency conversion rate was needed", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "PRORATION_EXCHANGE_RATE_DATE_TAKEN", "column-type": "dateColumn", "sources": {},
          "meta": {"description": {"value": "The date for which the home currency conversion rate was found and taken", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "INTERNAL_PRORATION_METADATA", "column-type": "strColumn", "sources": {},
          "meta": {"description": {"value": "Technical field containing details on the used proration estimation algorithm", "rule": "replace"}, "gdpr-zone": "green"}},

        {"name": "REASON_FOR_ISSUANCE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.reasonForIssuance.code"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REASON_FOR_ISSUANCE_SUB_CODE", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.reasonForIssuance.subCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REASON_FOR_ISSUANCE_DESCRIPTION", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.reasonForIssuance.description"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SERVICE_REMARK", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.serviceRemark"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "IS_CODESHARE", "column-type": "booleanColumn", "sources": {"blocks": [{"cpn": "$.isCodeshare"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "IS_NON_EXCHANGEABLE", "column-type": "booleanColumn", "sources": {"blocks": [{"base": "$.isNonExchangeable"}]},
          "meta": {"description": {"value": "Indicates if the coupon is non-exchangeable (true), or if it can be exchanged (false)", "rule": "replace"}, "example": {"value": "TRUE", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_NON_REFUNDABLE", "column-type": "booleanColumn", "sources": {"blocks": [{"base": "$.isNonRefundable"}]},
          "meta": {"description": {"value": "Indicates if the coupon is non-refundable (true), or if it can be refunded (false)", "rule": "replace"}, "example": {"value": "TRUE", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_FROM_CONNECTION", "column-type": "booleanColumn", "sources": {"blocks": [{"cpn": "$.isFromConnection"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_ALLOWANCE_WEIGHT_VALUE", "column-type": "floatColumn", "sources": {},
          "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_ALLOWANCE_WEIGHT_UNIT", "column-type": "strColumn", "sources": {},
          "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_ALLOWANCE_WEIGHT_VALUE_ORIGINAL", "column-type": "floatColumn", "sources": {"blocks": [{"cpn": "$.baggageAllowance.weight.amount"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_ALLOWANCE_WEIGHT_UNIT_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.baggageAllowance.weight.unit"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_ALLOWANCE_QUANTITY", "column-type": "intColumn", "sources": {"blocks": [{"cpn": "$.baggageAllowance.quantity"}]},
          "meta": {"example": {"value": "2", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "BAGGAGE_EXCESS_RATE", "column-type": "floatColumn", "sources": {}, "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_EXCESS_RATE_CURRENCY", "column-type": "strColumn", "sources": {}, "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_EXCESS_RATE_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.baggageAllowance.excessRate.amount"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_EXCESS_RATE_CURRENCY_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.baggageAllowance.excessRate.currency"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_EXCHANGE_RATE_DATE_NEEDED", "column-type": "dateColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"description": {"value": "The date for which the home currency conversion rate was needed", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "BAGGAGE_EXCHANGE_RATE_DATE_TAKEN", "column-type": "dateColumn", "sources": {},
          "meta": {"description": {"value": "The date for which the home currency conversion rate was found and taken", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REVALIDATION_DATETIME_UTC", "column-type": "timestampColumn", "sources": {"blocks": [{"cpn": "$.revalidation.dateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "REVALIDATION_OFFICE_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.revalidation.pointOfSale.office.id"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REVALIDATION_OFFICE_IATA_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.revalidation.pointOfSale.office.iataNumber"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REVALIDATION_OFFICE_IN_HOUSE_IDENTIFICATION", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.revalidation.pointOfSale.office.inHouseIdentification"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REVALIDATION_OFFICE_SYSTEM_CODE", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.revalidation.pointOfSale.office.systemCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REVALIDATION_OFFICE_AGENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.revalidation.pointOfSale.office.agentType"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "NOT_VALID_BEFORE_DATE_UTC", "column-type": "dateColumn", "sources": {"blocks": [{"cpn": "$.validityDates.notValidBeforeDate"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "NOT_VALID_AFTER_DATE_UTC", "column-type": "dateColumn", "sources": {"blocks": [{"cpn": "$.validityDates.notValidAfterDate"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "VOLUNTARY_INDICATOR", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.voluntaryIndicator"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "SETTLEMENT_AUTHORIZATION_CODE", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.settlementAuthorizationCode"}]}, "meta": {"gdpr-zone": "green"}},
        //IDs
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})", "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}]},
        {"name": "CURRENT_DEPARTURE_AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.departure.iataCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}]},
        {"name": "CURRENT_ARRIVAL_AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.arrival.iataCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}]},
        {"name": "SOLD_DEPARTURE_AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.departure.iataCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}]},
        {"name": "SOLD_ARRIVAL_AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.arrival.iataCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}]},
        {"name": "USED_DEPARTURE_AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.departure.iataCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}]},
        {"name": "USED_ARRIVAL_AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.arrival.iataCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}]},

        {"name": "REASON_FOR_ISSUANCE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.reasonForIssuance.code"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_REASON_FOR_ISSUANCE", "column": "REASON_FOR_ISSUANCE_ID"}]},
        {"name": "MEMBERSHIP_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"cpn": "$.frequentFlyer.applicableAirlineCode"}, {"cpn": "$.frequentFlyer.frequentFlyerNumber"}]}, "expr": ${hashSIdCheckEndNotNull}, "fk": [{"table": "FACT_MEMBERSHIP_HISTO", "column": "MEMBERSHIP_ID"}]},
        {"name": "REVALIDATION_POINT_OF_SALE_ID", "column-type": "binaryStrColumn",
          "sources": {"blocks": [{"cpn": "$.revalidation.pointOfSale.office.id"}, {"cpn": "$.revalidation.pointOfSale.office.iataNumber"}, {"cpn": "$.revalidation.pointOfSale.office.inHouseIdentification"}, {"cpn": "$.revalidation.pointOfSale.office.systemCode"}, {"cpn": "$.revalidation.pointOfSale.office.agentType"}, {"cpn": "$.revalidation.pointOfSale.login.cityCode"}, {"cpn": "$.revalidation.pointOfSale.login.countryCode"}, {"cpn": "$.revalidation.pointOfSale.login.numericSign"}, {"cpn": "$.revalidation.pointOfSale.login.initials"}, {"cpn": "$.revalidation.pointOfSale.login.dutyCode"}]}, "expr": "hashM({0})",
          "fk": [{"table": "DIM_POINT_OF_SALE", "column": "POINT_OF_SALE_ID"}]},
        {"name": "VOLUNTARY_INDICATOR_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.voluntaryIndicator"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_COUPON_VOLUNTARY_INDICATOR", "column": "COUPON_VOLUNTARY_INDICATOR_ID"}]},

        //final columns
        {"name": "DOCUMENT_NUMBER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.primaryDocumentNumber"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "DOCUMENT_CREATION_DATE", "column-type": "dateColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"description": {"value": "Date/time UTC of the document issuance", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]},
          "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "stackable-addons": [
      {
        "type": "proration",
        "json-path-fare-calc": "$.mainResource.current.image.pricingConditions.fareCalculation.text",
        "json-path-price-currency-payment": "$.mainResource.current.image.price.currency",
        "json-path-price-total-payment": "$.mainResource.current.image.price.total",
        "json-path-price-total-taxes-payment": "$.mainResource.current.image.price.totalTaxes"
      }
    ]
  },
  // validation fails because of missing json paths
  {
    "name": "FACT_FAIL_PATHS_HISTO",
    "table-selectors": ["fail"],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "description": {"description": "Contains detailed information on coupons of travel documents, such as related flight segments (sold, used, current - with marketing and operating flight information), coupon status, estimated prorated fare, fare basis/family and further indicators on fare conditions (exchangeable, refundable) and baggage allowance.", "granularity": "1 coupon for 1 travel document"},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "COUPON_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [
          {"base": "$.mainResource.current.image"},
          {"cpn": "$.coupons[*]"}
        ]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"cpn": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"cpn": "$.id"}]},
          "meta": {"description": {"value": "Functional key: docId-cpnSeqNum", "rule": "replace"}, "example": {"value": "1234567890123-2020-01-31-1", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "COUPON_DOCUMENT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.documentNumber"}]},
          "meta": {"example": {"value": "1234567890123", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "COUPON_NUMBER", "column-type": "intColumn", "sources": {"blocks": [{"cpn": "$.number"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "SEQUENCE_NUMBER", "column-type": "intColumn", "sources": {"blocks": [{"cpn": "$.sequenceNumber"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "RESERVATION_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.reservationStatus"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "COUPON_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.status"}]}, "meta": {"gdpr-zone": "green"}},
        //CURRENT SEGMENT
        {"name": "CURRENT_DEPARTURE_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.departure.iataCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_ARRIVAL_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.arrival.iataCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_DEPARTURE_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.departure.localDateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_ARRIVAL_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.arrival.localDateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_MKT_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.carrierCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_MKT_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.number"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_MKT_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.suffix"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_MKT_BOOKING_CLASS", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.bookingClass.code"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_OPE_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.operating.flightDesignator.carrierCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_OPE_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.operating.flightDesignator.flightNumber"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_OPE_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.operating.flightDesignator.operationalSuffix"}]}, "meta": {"gdpr-zone": "green"}},
        //SOLD SEGMENT
        {"name": "SOLD_DEPARTURE_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.departure.iataCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_ARRIVAL_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.arrival.iataCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_DEPARTURE_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.departure.localDateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_ARRIVAL_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.arrival.localDateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_MKT_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.carrierCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_MKT_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.number"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_MKT_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.suffix"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_MKT_BOOKING_CLASS", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.bookingClass.code"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_OPE_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.operating.flightDesignator.carrierCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_OPE_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.operating.flightDesignator.flightNumber"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_OPE_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.operating.flightDesignator.operationalSuffix"}]}, "meta": {"gdpr-zone": "green"}},
        //USED SEGMENT
        {"name": "USED_DEPARTURE_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.departure.iataCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_ARRIVAL_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.arrival.iataCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_DEPARTURE_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.departure.localDateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "USED_ARRIVAL_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.arrival.localDateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "USED_MKT_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.carrierCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_MKT_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.number"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_MKT_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.suffix"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_MKT_BOOKING_CLASS", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.bookingClass.code"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_OPE_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.operating.flightDesignator.carrierCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_OPE_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.operating.flightDesignator.flightNumber"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_OPE_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.operating.flightDesignator.operationalSuffix"}]}, "meta": {"gdpr-zone": "green"}},

        {"name": "FARE_BASIS_CODE", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.fareBasis.fareBasisCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "FARE_FAMILY_CODE", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.fareFamily.code"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "FARE_FAMILY_OWNER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.fareFamily.owner"}]}, "meta": {"gdpr-zone": "green"}},
        //PRORATION
        {"name": "ESTIMATED_PRORATED_FARE", "column-type": "floatColumn", "sources": {},
          "meta": {"example": {"value": "68.91", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "ESTIMATED_PRORATED_FARE_CURRENCY", "column-type": "strColumn", "sources": {},
          "meta": {"example": {"value": "EUR", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "ESTIMATED_PRORATED_FARE_ORIGINAL", "column-type": "strColumn", "sources": {},
          "meta": {"description": {"value": "The original prorated amount extracted from the fare calc line or by distance", "rule": "replace"}, "example": {"value": "722.55", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "ESTIMATED_PRORATED_FARE_CURRENCY_ORIGINAL", "column-type": "strColumn", "sources": {},
          "meta": {"description": {"value": "The original currency of proration amount: for FARE-CALC it is the extracted fare calculation currency (can be NUC), and for DISTANCE it is the payment currency", "rule": "replace"}, "example": {"value": "USD", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "ESTIMATED_PRORATED_FARE_ALGORITHM", "column-type": "strColumn", "sources": {},
          "meta": {"description": {"value": "Indicates which algorithm was used to estimate the prorated fare: FARE-CALC, DISTANCE or NONE in case of error", "rule": "replace"}, "example": {"value": "FARE-CALC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "PRORATION_EXCHANGE_RATE_DATE_NEEDED", "column-type": "dateColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"description": {"value": "The date for which the home currency conversion rate was needed", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "PRORATION_EXCHANGE_RATE_DATE_TAKEN", "column-type": "dateColumn", "sources": {},
          "meta": {"description": {"value": "The date for which the home currency conversion rate was found and taken", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "INTERNAL_PRORATION_METADATA", "column-type": "strColumn", "sources": {},
          "meta": {"description": {"value": "Technical field containing details on the used proration estimation algorithm", "rule": "replace"}, "gdpr-zone": "green"}},

        {"name": "REASON_FOR_ISSUANCE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.reasonForIssuance.code"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REASON_FOR_ISSUANCE_SUB_CODE", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.reasonForIssuance.subCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REASON_FOR_ISSUANCE_DESCRIPTION", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.reasonForIssuance.description"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SERVICE_REMARK", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.serviceRemark"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "IS_CODESHARE", "column-type": "booleanColumn", "sources": {"blocks": [{"cpn": "$.isCodeshare"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "IS_NON_EXCHANGEABLE", "column-type": "booleanColumn", "sources": {"blocks": [{"base": "$.isNonExchangeable"}]},
          "meta": {"description": {"value": "Indicates if the coupon is non-exchangeable (true), or if it can be exchanged (false)", "rule": "replace"}, "example": {"value": "TRUE", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_NON_REFUNDABLE", "column-type": "booleanColumn", "sources": {"blocks": [{"base": "$.isNonRefundable"}]},
          "meta": {"description": {"value": "Indicates if the coupon is non-refundable (true), or if it can be refunded (false)", "rule": "replace"}, "example": {"value": "TRUE", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_FROM_CONNECTION", "column-type": "booleanColumn", "sources": {"blocks": [{"cpn": "$.isFromConnection"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_ALLOWANCE_WEIGHT_VALUE", "column-type": "floatColumn", "sources": {},
          "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_ALLOWANCE_WEIGHT_UNIT", "column-type": "strColumn", "sources": {},
          "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_ALLOWANCE_WEIGHT_VALUE_ORIGINAL", "column-type": "floatColumn", "sources": {"blocks": [{"cpn": "$.baggageAllowance.weight.amount"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_ALLOWANCE_WEIGHT_UNIT_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.baggageAllowance.weight.unit"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_ALLOWANCE_QUANTITY", "column-type": "intColumn", "sources": {"blocks": [{"cpn": "$.baggageAllowance.quantity"}]},
          "meta": {"example": {"value": "2", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "BAGGAGE_EXCESS_RATE", "column-type": "floatColumn", "sources": {}, "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_EXCESS_RATE_CURRENCY", "column-type": "strColumn", "sources": {}, "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_EXCESS_RATE_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.baggageAllowance.excessRate.amount"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_EXCESS_RATE_CURRENCY_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.baggageAllowance.excessRate.currency"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_EXCHANGE_RATE_DATE_NEEDED", "column-type": "dateColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"description": {"value": "The date for which the home currency conversion rate was needed", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "BAGGAGE_EXCHANGE_RATE_DATE_TAKEN", "column-type": "dateColumn", "sources": {},
          "meta": {"description": {"value": "The date for which the home currency conversion rate was found and taken", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REVALIDATION_DATETIME_UTC", "column-type": "timestampColumn", "sources": {"blocks": [{"cpn": "$.revalidation.dateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "REVALIDATION_OFFICE_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.revalidation.pointOfSale.office.id"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REVALIDATION_OFFICE_IATA_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.revalidation.pointOfSale.office.iataNumber"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REVALIDATION_OFFICE_IN_HOUSE_IDENTIFICATION", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.revalidation.pointOfSale.office.inHouseIdentification"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REVALIDATION_OFFICE_SYSTEM_CODE", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.revalidation.pointOfSale.office.systemCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REVALIDATION_OFFICE_AGENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.revalidation.pointOfSale.office.agentType"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "NOT_VALID_BEFORE_DATE_UTC", "column-type": "dateColumn", "sources": {"blocks": [{"cpn": "$.validityDates.notValidBeforeDate"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "NOT_VALID_AFTER_DATE_UTC", "column-type": "dateColumn", "sources": {"blocks": [{"cpn": "$.validityDates.notValidAfterDate"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "VOLUNTARY_INDICATOR", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.voluntaryIndicator"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "SETTLEMENT_AUTHORIZATION_CODE", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.settlementAuthorizationCode"}]}, "meta": {"gdpr-zone": "green"}},
        //IDs
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})", "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}]},
        {"name": "CURRENT_DEPARTURE_AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.departure.iataCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}]},
        {"name": "CURRENT_ARRIVAL_AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.arrival.iataCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}]},
        {"name": "SOLD_DEPARTURE_AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.departure.iataCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}]},
        {"name": "SOLD_ARRIVAL_AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.arrival.iataCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}]},
        {"name": "USED_DEPARTURE_AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.departure.iataCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}]},
        {"name": "USED_ARRIVAL_AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.arrival.iataCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}]},

        {"name": "REASON_FOR_ISSUANCE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.reasonForIssuance.code"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_REASON_FOR_ISSUANCE", "column": "REASON_FOR_ISSUANCE_ID"}]},
        {"name": "MEMBERSHIP_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"cpn": "$.frequentFlyer.applicableAirlineCode"}, {"cpn": "$.frequentFlyer.frequentFlyerNumber"}]}, "expr": ${hashSIdCheckEndNotNull}, "fk": [{"table": "FACT_MEMBERSHIP_HISTO", "column": "MEMBERSHIP_ID"}]},
        {"name": "REVALIDATION_POINT_OF_SALE_ID", "column-type": "binaryStrColumn",
          "sources": {"blocks": [{"cpn": "$.revalidation.pointOfSale.office.id"}, {"cpn": "$.revalidation.pointOfSale.office.iataNumber"}, {"cpn": "$.revalidation.pointOfSale.office.inHouseIdentification"}, {"cpn": "$.revalidation.pointOfSale.office.systemCode"}, {"cpn": "$.revalidation.pointOfSale.office.agentType"}, {"cpn": "$.revalidation.pointOfSale.login.cityCode"}, {"cpn": "$.revalidation.pointOfSale.login.countryCode"}, {"cpn": "$.revalidation.pointOfSale.login.numericSign"}, {"cpn": "$.revalidation.pointOfSale.login.initials"}, {"cpn": "$.revalidation.pointOfSale.login.dutyCode"}]}, "expr": "hashM({0})",
          "fk": [{"table": "DIM_POINT_OF_SALE", "column": "POINT_OF_SALE_ID"}]},
        {"name": "VOLUNTARY_INDICATOR_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.voluntaryIndicator"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_COUPON_VOLUNTARY_INDICATOR", "column": "COUPON_VOLUNTARY_INDICATOR_ID"}]},

        //final columns
        {"name": "DOCUMENT_NUMBER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.primaryDocumentNumber"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "DOCUMENT_CREATION_DATE", "column-type": "dateColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"description": {"value": "Date/time UTC of the document issuance", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]},
          "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "stackable-addons": [
      {
        "type": "proration",
        "json-path-fare-calc": "",
        "json-path-price-currency-payment": "$.mainResource.current.image.price.currency",
        "json-path-price-total-payment": "$.mainResource.current.image.price.total",
        "json-path-price-total-taxes-payment": "$.mainResource.current.image.price.totalTaxes"
      }
    ]
  },
  // validation fails because of json paths not starting with $
  {
    "name": "FACT_FAIL_PATHS_$_HISTO",
    "table-selectors": ["fail"],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "description": {"description": "Contains detailed information on coupons of travel documents, such as related flight segments (sold, used, current - with marketing and operating flight information), coupon status, estimated prorated fare, fare basis/family and further indicators on fare conditions (exchangeable, refundable) and baggage allowance.", "granularity": "1 coupon for 1 travel document"},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "COUPON_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [
          {"base": "$.mainResource.current.image"},
          {"cpn": "$.coupons[*]"}
        ]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"cpn": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"cpn": "$.id"}]},
          "meta": {"description": {"value": "Functional key: docId-cpnSeqNum", "rule": "replace"}, "example": {"value": "1234567890123-2020-01-31-1", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "COUPON_DOCUMENT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.documentNumber"}]},
          "meta": {"example": {"value": "1234567890123", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "COUPON_NUMBER", "column-type": "intColumn", "sources": {"blocks": [{"cpn": "$.number"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "SEQUENCE_NUMBER", "column-type": "intColumn", "sources": {"blocks": [{"cpn": "$.sequenceNumber"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "RESERVATION_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.reservationStatus"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "COUPON_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.status"}]}, "meta": {"gdpr-zone": "green"}},
        //CURRENT SEGMENT
        {"name": "CURRENT_DEPARTURE_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.departure.iataCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_ARRIVAL_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.arrival.iataCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_DEPARTURE_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.departure.localDateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_ARRIVAL_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.arrival.localDateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_MKT_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.carrierCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_MKT_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.number"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_MKT_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.suffix"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_MKT_BOOKING_CLASS", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.bookingClass.code"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_OPE_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.operating.flightDesignator.carrierCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_OPE_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.operating.flightDesignator.flightNumber"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_OPE_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.operating.flightDesignator.operationalSuffix"}]}, "meta": {"gdpr-zone": "green"}},
        //SOLD SEGMENT
        {"name": "SOLD_DEPARTURE_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.departure.iataCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_ARRIVAL_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.arrival.iataCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_DEPARTURE_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.departure.localDateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_ARRIVAL_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.arrival.localDateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_MKT_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.carrierCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_MKT_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.number"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_MKT_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.suffix"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_MKT_BOOKING_CLASS", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.bookingClass.code"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_OPE_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.operating.flightDesignator.carrierCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_OPE_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.operating.flightDesignator.flightNumber"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_OPE_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.operating.flightDesignator.operationalSuffix"}]}, "meta": {"gdpr-zone": "green"}},
        //USED SEGMENT
        {"name": "USED_DEPARTURE_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.departure.iataCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_ARRIVAL_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.arrival.iataCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_DEPARTURE_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.departure.localDateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "USED_ARRIVAL_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.arrival.localDateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "USED_MKT_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.carrierCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_MKT_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.number"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_MKT_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.suffix"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_MKT_BOOKING_CLASS", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.bookingClass.code"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_OPE_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.operating.flightDesignator.carrierCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_OPE_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.operating.flightDesignator.flightNumber"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_OPE_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.operating.flightDesignator.operationalSuffix"}]}, "meta": {"gdpr-zone": "green"}},

        {"name": "FARE_BASIS_CODE", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.fareBasis.fareBasisCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "FARE_FAMILY_CODE", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.fareFamily.code"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "FARE_FAMILY_OWNER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.fareFamily.owner"}]}, "meta": {"gdpr-zone": "green"}},
        //PRORATION
        {"name": "ESTIMATED_PRORATED_FARE", "column-type": "floatColumn", "sources": {},
          "meta": {"example": {"value": "68.91", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "ESTIMATED_PRORATED_FARE_CURRENCY", "column-type": "strColumn", "sources": {},
          "meta": {"example": {"value": "EUR", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "ESTIMATED_PRORATED_FARE_ORIGINAL", "column-type": "strColumn", "sources": {},
          "meta": {"description": {"value": "The original prorated amount extracted from the fare calc line or by distance", "rule": "replace"}, "example": {"value": "722.55", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "ESTIMATED_PRORATED_FARE_CURRENCY_ORIGINAL", "column-type": "strColumn", "sources": {},
          "meta": {"description": {"value": "The original currency of proration amount: for FARE-CALC it is the extracted fare calculation currency (can be NUC), and for DISTANCE it is the payment currency", "rule": "replace"}, "example": {"value": "USD", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "ESTIMATED_PRORATED_FARE_ALGORITHM", "column-type": "strColumn", "sources": {},
          "meta": {"description": {"value": "Indicates which algorithm was used to estimate the prorated fare: FARE-CALC, DISTANCE or NONE in case of error", "rule": "replace"}, "example": {"value": "FARE-CALC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "PRORATION_EXCHANGE_RATE_DATE_NEEDED", "column-type": "dateColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"description": {"value": "The date for which the home currency conversion rate was needed", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "PRORATION_EXCHANGE_RATE_DATE_TAKEN", "column-type": "dateColumn", "sources": {},
          "meta": {"description": {"value": "The date for which the home currency conversion rate was found and taken", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "INTERNAL_PRORATION_METADATA", "column-type": "strColumn", "sources": {},
          "meta": {"description": {"value": "Technical field containing details on the used proration estimation algorithm", "rule": "replace"}, "gdpr-zone": "green"}},

        {"name": "REASON_FOR_ISSUANCE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.reasonForIssuance.code"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REASON_FOR_ISSUANCE_SUB_CODE", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.reasonForIssuance.subCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REASON_FOR_ISSUANCE_DESCRIPTION", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.reasonForIssuance.description"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SERVICE_REMARK", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.serviceRemark"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "IS_CODESHARE", "column-type": "booleanColumn", "sources": {"blocks": [{"cpn": "$.isCodeshare"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "IS_NON_EXCHANGEABLE", "column-type": "booleanColumn", "sources": {"blocks": [{"base": "$.isNonExchangeable"}]},
          "meta": {"description": {"value": "Indicates if the coupon is non-exchangeable (true), or if it can be exchanged (false)", "rule": "replace"}, "example": {"value": "TRUE", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_NON_REFUNDABLE", "column-type": "booleanColumn", "sources": {"blocks": [{"base": "$.isNonRefundable"}]},
          "meta": {"description": {"value": "Indicates if the coupon is non-refundable (true), or if it can be refunded (false)", "rule": "replace"}, "example": {"value": "TRUE", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_FROM_CONNECTION", "column-type": "booleanColumn", "sources": {"blocks": [{"cpn": "$.isFromConnection"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_ALLOWANCE_WEIGHT_VALUE", "column-type": "floatColumn", "sources": {},
          "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_ALLOWANCE_WEIGHT_UNIT", "column-type": "strColumn", "sources": {},
          "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_ALLOWANCE_WEIGHT_VALUE_ORIGINAL", "column-type": "floatColumn", "sources": {"blocks": [{"cpn": "$.baggageAllowance.weight.amount"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_ALLOWANCE_WEIGHT_UNIT_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.baggageAllowance.weight.unit"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_ALLOWANCE_QUANTITY", "column-type": "intColumn", "sources": {"blocks": [{"cpn": "$.baggageAllowance.quantity"}]},
          "meta": {"example": {"value": "2", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "BAGGAGE_EXCESS_RATE", "column-type": "floatColumn", "sources": {}, "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_EXCESS_RATE_CURRENCY", "column-type": "strColumn", "sources": {}, "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_EXCESS_RATE_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.baggageAllowance.excessRate.amount"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_EXCESS_RATE_CURRENCY_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.baggageAllowance.excessRate.currency"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_EXCHANGE_RATE_DATE_NEEDED", "column-type": "dateColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"description": {"value": "The date for which the home currency conversion rate was needed", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "BAGGAGE_EXCHANGE_RATE_DATE_TAKEN", "column-type": "dateColumn", "sources": {},
          "meta": {"description": {"value": "The date for which the home currency conversion rate was found and taken", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REVALIDATION_DATETIME_UTC", "column-type": "timestampColumn", "sources": {"blocks": [{"cpn": "$.revalidation.dateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "REVALIDATION_OFFICE_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.revalidation.pointOfSale.office.id"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REVALIDATION_OFFICE_IATA_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.revalidation.pointOfSale.office.iataNumber"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REVALIDATION_OFFICE_IN_HOUSE_IDENTIFICATION", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.revalidation.pointOfSale.office.inHouseIdentification"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REVALIDATION_OFFICE_SYSTEM_CODE", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.revalidation.pointOfSale.office.systemCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REVALIDATION_OFFICE_AGENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.revalidation.pointOfSale.office.agentType"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "NOT_VALID_BEFORE_DATE_UTC", "column-type": "dateColumn", "sources": {"blocks": [{"cpn": "$.validityDates.notValidBeforeDate"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "NOT_VALID_AFTER_DATE_UTC", "column-type": "dateColumn", "sources": {"blocks": [{"cpn": "$.validityDates.notValidAfterDate"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "VOLUNTARY_INDICATOR", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.voluntaryIndicator"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "SETTLEMENT_AUTHORIZATION_CODE", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.settlementAuthorizationCode"}]}, "meta": {"gdpr-zone": "green"}},
        //IDs
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})", "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}]},
        {"name": "CURRENT_DEPARTURE_AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.departure.iataCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}]},
        {"name": "CURRENT_ARRIVAL_AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.arrival.iataCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}]},
        {"name": "SOLD_DEPARTURE_AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.departure.iataCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}]},
        {"name": "SOLD_ARRIVAL_AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.arrival.iataCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}]},
        {"name": "USED_DEPARTURE_AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.departure.iataCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}]},
        {"name": "USED_ARRIVAL_AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.arrival.iataCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}]},

        {"name": "REASON_FOR_ISSUANCE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.reasonForIssuance.code"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_REASON_FOR_ISSUANCE", "column": "REASON_FOR_ISSUANCE_ID"}]},
        {"name": "MEMBERSHIP_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"cpn": "$.frequentFlyer.applicableAirlineCode"}, {"cpn": "$.frequentFlyer.frequentFlyerNumber"}]}, "expr": ${hashSIdCheckEndNotNull}, "fk": [{"table": "FACT_MEMBERSHIP_HISTO", "column": "MEMBERSHIP_ID"}]},
        {"name": "REVALIDATION_POINT_OF_SALE_ID", "column-type": "binaryStrColumn",
          "sources": {"blocks": [{"cpn": "$.revalidation.pointOfSale.office.id"}, {"cpn": "$.revalidation.pointOfSale.office.iataNumber"}, {"cpn": "$.revalidation.pointOfSale.office.inHouseIdentification"}, {"cpn": "$.revalidation.pointOfSale.office.systemCode"}, {"cpn": "$.revalidation.pointOfSale.office.agentType"}, {"cpn": "$.revalidation.pointOfSale.login.cityCode"}, {"cpn": "$.revalidation.pointOfSale.login.countryCode"}, {"cpn": "$.revalidation.pointOfSale.login.numericSign"}, {"cpn": "$.revalidation.pointOfSale.login.initials"}, {"cpn": "$.revalidation.pointOfSale.login.dutyCode"}]}, "expr": "hashM({0})",
          "fk": [{"table": "DIM_POINT_OF_SALE", "column": "POINT_OF_SALE_ID"}]},
        {"name": "VOLUNTARY_INDICATOR_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.voluntaryIndicator"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_COUPON_VOLUNTARY_INDICATOR", "column": "COUPON_VOLUNTARY_INDICATOR_ID"}]},

        //final columns
        {"name": "DOCUMENT_NUMBER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.primaryDocumentNumber"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "DOCUMENT_CREATION_DATE", "column-type": "dateColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"description": {"value": "Date/time UTC of the document issuance", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]},
          "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "stackable-addons": [
      {
        "type": "proration",
        "json-path-fare-calc": "this.is.not.good",
        "json-path-price-currency-payment": "$.mainResource.current.image.price.currency",
        "json-path-price-total-payment": "$.mainResource.current.image.price.total",
        "json-path-price-total-taxes-payment": "$.mainResource.current.image.price.totalTaxes"
      }
    ]
  },
  // validation fail because latest addon not supported
  {
    "name": "FACT_FAIL",
    "table-selectors": ["fail"],
    "latest": {
      "histo-table-name": "FACT_COUPON_HISTO"
    }
    "stackable-addons": [
      {
        "type": "proration",
        "json-path-fare-calc": "$.mainResource.current.image.pricingConditions.fareCalculation.text",
        "json-path-price-currency-payment": "$.mainResource.current.image.price.currency",
        "json-path-price-total-payment": "$.mainResource.current.image.price.total",
        "json-path-price-total-taxes-payment": "$.mainResource.current.image.price.totalTaxes"
      }
    ]
  }
]
