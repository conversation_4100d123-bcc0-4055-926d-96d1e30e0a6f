package com.amadeus.airbi.json2star.common.addons.base.mapping.input

import com.amadeus.airbi.rawvault.common.processors.JsonProcessor
import InputFormat._
import com.amadeus.airbi.json2star.common.addons.base.mapping.MappingAddonParams
import org.apache.spark.sql.DataFrame
import org.apache.spark.sql.functions._
import org.apache.spark.sql.streaming.DataStreamReader
import org.apache.spark.sql.types._
import org.slf4j.{Logger, LoggerFactory}

import java.sql.Timestamp
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import scala.util.{Failure, Success, Try}

/** JSON local input format based on json files.
  * Each files corresponds to one record. Naming convention of the file matters:
  * - <description>_load-date=<load-date-MM-dd-YYYY>.json
  */
case object JsonFileInput extends InputFormat {
  private val logger: Logger = LoggerFactory.getLogger(getClass.getName)
  val DefaultRecordId = "0"

  // Add new field at top level of the json for test purposes
  // This field will be used to filter the data to identify rows for a specific test
  // The value of the field is extracted from the path src/test/resources/datasets/<test-value>/data/
  val TestKeyField = "json2starTestKey"

  val dateParsing = (LocalDate
    .parse(
      _: CharSequence,
      DateTimeFormatter
        .ofPattern("MM-dd-yyyy") // 4/19/2021 9:43:03 AM
        .withZone(java.time.ZoneOffset.UTC)
    )
    .atStartOfDay())
    .andThen(Timestamp.valueOf(_))

  val udfParse = udf(dateParsing)
  val udfInjectTestKey = udf((json: String, testKeyValue: String) => {
    val withTestKey = Try {
      val jsonProcessor = JsonProcessor.parseJsonString(
        json,
        JsonProcessor.SINGLE_VALUE_CONFIGURATION
      )
      // add new field to the json at root level (avoid to change the main content)
      val withTestKey = jsonProcessor.put("$", TestKeyField, testKeyValue)
      withTestKey.jsonString()
    }
    withTestKey match {
      case Success(jsonWithTestKey) => jsonWithTestKey
      case Failure(exception) =>
        // in case of wrong json file in the local tests, log error and keep the original json
        logger.error(s"Wrong json format ${json} for test key ${testKeyValue}", exception)
        json
    }
  })

  def streamingDataFrameFrom(streamReader: DataStreamReader, paths: Seq[String]): DataFrame = {
    val onlyFilename = regexp_replace(col("path"), ".*/", "")
    val onlyMetadata = regexp_replace(onlyFilename, ".*_", "")
    val noDotJson = regexp_replace(onlyMetadata, "\\.json", "")
    val noLoadDate = regexp_replace(noDotJson, "load-date=", "")
    // extract the test value from the path - the convention /datasets/<test-value>/
    val testKeyValue = regexp_extract(col("path"), "/([^/]+)/data/", 1)

    val reader = streamReader
      .schema(
        StructType(
          Array(
            StructField("path", StringType, nullable = true),
            StructField("modificationTime", TimestampType, nullable = true),
            StructField("length", LongType, nullable = true),
            StructField("content", BinaryType, nullable = true)
          )
        )
      )
      .format("binaryFile")
      .option("pathGlobFilter", "*.json") // read all json files within the directory

    val input = paths
      .map { p =>
        val parsed = reader
          .load(p)
          // scalastyle:off null
          .withColumn("contentStr", col("content").cast(StringType))
          .select(
            when(testKeyValue.isNull, col("contentStr"))
              .otherwise(udfInjectTestKey(col("contentStr"), testKeyValue)) as BODY,
            udfParse(noLoadDate) as LOAD_DATE,
            lit(DefaultRecordId) as RECORD_ID
          )
        parsed
      }
      .reduce(_ union _)
    input
    // scalastyle:off on
  }
}
