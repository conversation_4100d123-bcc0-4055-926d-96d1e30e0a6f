package com.amadeus.airbi.json2star.common.addons.base.mapping.input

import InputFormat._
import com.amadeus.airbi.json2star.common.addons.base.mapping.MappingAddonParams
import com.amadeus.airbi.json2star.common.addons.base.mapping.input.DeltaInput.DeltaInputFormatConfig
import org.apache.spark.sql.DataFrame
import org.apache.spark.sql.streaming.DataStreamReader

case class DeltaInput(
  conf: DeltaInputFormatConfig = DeltaInputFormatConfig()
) extends InputFormat {
  def streamingDataFrameFrom(streamReader: DataStreamReader, paths: Seq[String]): InputDataFrame = {
    val path = paths.toList match {
      case h :: Nil => h
      case _ => throw new IllegalArgumentException(s"Delta input format expects one and only one path: ${paths}")
    }
    streamReader
      .options(conf.options)
      .format("delta")
      .load(path)
      .selectExpr(conf.selectExpr: _*)
  }
}

object DeltaInput {
  val DefaultOptions: Map[String, String] = Map(
    "ignoreChanges" -> "true"
  )
  // Schema: timestamp:bigint,payload:binary,keys:map<string,string>,replayContext:map<string,string>
  val DefaultSelectExpr: List[String] = List(
    s"CAST(timestamp/1000 AS TIMESTAMP) as $LOAD_DATE",
    s"CAST(timestamp AS STRING) as $RECORD_ID",
    s"CAST(payload AS STRING) as $BODY"
  )

  case class DeltaInputFormatConfig(
    inputPaths: Seq[String] = Seq.empty,
    selectExpr: List[String] = DefaultSelectExpr,
    options: Map[String, String] = DefaultOptions
  )

}
