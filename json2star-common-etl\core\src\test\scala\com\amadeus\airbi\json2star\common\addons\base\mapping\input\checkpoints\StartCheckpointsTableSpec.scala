package com.amadeus.airbi.json2star.common.addons.base.mapping.input.checkpoints

import com.amadeus.airbi.datalake.common.spark.SparkSqlSpecification
import com.amadeus.airbi.json2star.common.addons.base.mapping.input.checkpoints.StartCheckpointsTable._
import com.amadeus.airbi.rawvault.common.testfwk.TmpDir
import org.scalatest.BeforeAndAfterEach

import java.sql.Timestamp
import java.time.{Instant, LocalDate}
import java.util.TimeZone

class StartCheckpointsTableSpec extends SparkSqlSpecification with BeforeAndAfterEach with TmpDir {

  TimeZone.setDefault(TimeZone.getTimeZone("UTC"))
  val Regex = "*"
  val Today: LocalDate = LocalDate.of(2024, 10, 29)
  val TodayTimestamp: Timestamp = Timestamp.valueOf(Today.atStartOfDay())

  override def beforeAll: Unit = {
    super.beforeAll
    TimeZone.setDefault(TimeZone.getTimeZone("UTC"))
    spark.conf.set("spark.databricks.delta.allowArbitraryProperties.enabled", "true")
  }

  "StartCheckpointsTable" should "store and read the config" in withTmpDir { tmpDir =>
    val table = new StartCheckpointsTable(spark, tmpDir.toString)

    // write and read a static start config
    val staticStart: ImmutableStartConfig =
      ImmutableStaticStart(Regex, Some(Today.minusDays(1)), Some(Today), Seq("/path"))
    table.storeConfig(staticStart, TodayTimestamp)

    table.readStoredConfig() shouldBe Some((TodayTimestamp, staticStart))
    table.runQuery(Some(s"${Columns.ROW_TYPE} = '${RowType.CONFIG_ROW}'")).count() shouldBe 1

    table.delete()

    // write and read an autoloader start config
    val autoloaderStart: ImmutableStartConfig = ImmutableAutoloaderStart(Seq("path1", "path2"))
    table.storeConfig(autoloaderStart, TodayTimestamp)

    table.runQuery(Some(s"${Columns.ROW_TYPE} = '${RowType.CONFIG_ROW}'")).count() shouldBe 1
    table.readStoredConfig() shouldBe Some((TodayTimestamp, autoloaderStart))

    table.delete()

    // write and fail to read multiple start config entries
    table.storeConfig(autoloaderStart, TodayTimestamp)
    table.storeConfig(staticStart, TodayTimestamp)
    table.runQuery(Some(s"${Columns.ROW_TYPE} = '${RowType.CONFIG_ROW}'")).count() shouldBe 2

    val e = intercept[AssertionError](table.readStoredConfig())
    e.getMessage should include("at most one row with ROW_TYPE = CONFIG_ROW")
  }

  it should "store rows for batches" in withTmpDir { tmpDir =>
    val table = new StartCheckpointsTable(spark, tmpDir.toString)

    val startDate = LocalDate.of(2024, 9, 10)
    val staticStart: ImmutableStartConfig = ImmutableStaticStart(Regex, Some(startDate), None, Seq("/path"))
    table.storeConfig(staticStart, TodayTimestamp)

    val numBatches = 5
    storeBatches(table, numBatches, 1, startDate, staticStart)
    table.runQuery().count() shouldBe (numBatches + 1)
    table.runQuery(Some(s"${Columns.ROW_TYPE} = '${RowType.BATCH_ROW}'")).count() shouldBe numBatches
  }

  it should "store rows for autoloader" in withTmpDir { tmpDir =>
    val table = new StartCheckpointsTable(spark, tmpDir.toString)

    val startDate = LocalDate.of(2024, 9, 10)
    val staticStart: ImmutableStartConfig = ImmutableStaticStart(Regex, Some(startDate), None, Seq("/path"))
    table.storeConfig(staticStart, TodayTimestamp)

    storeBatch(table, startDate, finished = true, staticStart)
    table.runQuery().count() shouldBe (2)
    table.runQuery(Some(s"${Columns.ROW_TYPE} = '${RowType.BATCH_ROW}'")).count() shouldBe 1

    table.logAutoloader(LocalDate.of(2024, 9, 30), TodayTimestamp, Seq("/path/2024/09/30/"), staticStart)
    table.logAutoloader(LocalDate.of(2024, 10, 31), TodayTimestamp, Seq("/path/2024/10/31/"), staticStart)

    table.runQuery().count() shouldBe (4)
    table.runQuery(Some(s"${Columns.ROW_TYPE} = '${RowType.AUTOLOADER_ROW}'")).count() shouldBe 2
  }

  it should "raise an error when logging an autoloader row with autoloader start config" in withTmpDir { tmpDir =>
    val table = new StartCheckpointsTable(spark, tmpDir.toString)

    val autoloaderStart: ImmutableStartConfig = ImmutableAutoloaderStart(Seq("path"))
    table.storeConfig(autoloaderStart, TodayTimestamp)

    intercept[AssertionError] {
      table.logAutoloader(LocalDate.of(2024, 9, 30), TodayTimestamp, Seq("/path/2024/09/30/"), autoloaderStart)
    }.getMessage should include("Autoloader rows must have a StaticStart config")
  }

  it should "raise an error when logging an autoloader row empty autoloader path" in withTmpDir { tmpDir =>
    val table = new StartCheckpointsTable(spark, tmpDir.toString)

    val startDate = LocalDate.of(2024, 9, 10)
    val staticStart: ImmutableStartConfig = ImmutableStaticStart(Regex, Some(startDate), None, Seq("/path"))
    table.storeConfig(staticStart, TodayTimestamp)

    intercept[AssertionError] {
      table.logAutoloader(LocalDate.of(2024, 9, 30), TodayTimestamp, Seq.empty, staticStart)
    }.getMessage should include("USED_AUTOLOADER_INPUT must not be empty when logging an AUTOLOADER_ROW")
  }

  it should "raise an error when logging a batch with autoloader start config" in withTmpDir { tmpDir =>
    val table = new StartCheckpointsTable(spark, tmpDir.toString)

    val startDate = LocalDate.of(2024, 9, 10)
    val autoloaderStart: ImmutableStartConfig = ImmutableAutoloaderStart(Seq("path1", "path2"))
    table.storeConfig(autoloaderStart, TodayTimestamp)

    intercept[AssertionError] {
      storeBatches(table, 1, 1, startDate, autoloaderStart)
    }.getMessage should include("Batch rows must have a StaticStart config")
  }

  it should "read the last batch end date" in withTmpDir { tmpDir =>
    val table = new StartCheckpointsTable(spark, tmpDir.toString)

    val startDate = LocalDate.of(2024, 9, 10)
    val staticStart: ImmutableStartConfig = ImmutableStaticStart(Regex, Some(startDate), None, Seq("/path"))
    table.storeConfig(staticStart, TodayTimestamp)

    table.readStaticStartOffset() shouldBe None

    val numBatches = 3
    storeBatches(table, numBatches, 2, startDate, staticStart)

    table.readStaticStartOffset() shouldBe Some(LocalDate.of(2024, 9, 15), true)
  }

  it should "raise an error if there is more than one row of type config" in withTmpDir { tmpDir =>
    val table = new StartCheckpointsTable(spark, tmpDir.toString)

    val startDate = LocalDate.of(2024, 9, 10)
    val staticStart: ImmutableStartConfig = ImmutableStaticStart(Regex, Some(startDate), None, Seq("/path"))
    table.storeConfig(staticStart, TodayTimestamp)
    table.storeConfig(staticStart, TodayTimestamp)

    intercept[AssertionError] {
      table.readStoredConfig()
    }.getMessage should include("at most one row with ROW_TYPE = CONFIG_ROW")
  }

  /* Utils */

  private def storeBatch(
    table: StartCheckpointsTable,
    startDate: LocalDate,
    finished: Boolean,
    config: ImmutableStartConfig
  ): Unit = {
    table.logBatch(
      firstProcessedDate = startDate,
      lastProcessedDate = startDate.plusDays(1),
      inputDates = s"$startDate,${startDate.plusDays(1)}",
      startTimestamp = TodayTimestamp,
      endTimestamp = TodayTimestamp,
      staticStartFinished = finished,
      config = config
    )
  }

  private def storeBatches(
    table: StartCheckpointsTable,
    numBatches: Int,
    daysPerBatch: Int,
    startDate: LocalDate,
    config: ImmutableStartConfig,
    autoloaderInput: Seq[String] = Seq("/some/path")
  ): Unit = {
    (0 until numBatches * daysPerBatch by daysPerBatch).foreach(batch => {
      val lastBatch = numBatches * daysPerBatch - daysPerBatch
      val start = startDate.plusDays(batch)
      val end = startDate.plusDays(batch + daysPerBatch - 1)
      table.logBatch(
        firstProcessedDate = start,
        lastProcessedDate = end,
        inputDates = s"$start,$end",
        startTimestamp = Timestamp.from(Instant.now().plusSeconds(batch * 60)),
        endTimestamp = Timestamp.from(Instant.now().plusSeconds((batch + 1) * 60)),
        staticStartFinished = (batch == lastBatch),
        config = config
      )
    })
  }

}
