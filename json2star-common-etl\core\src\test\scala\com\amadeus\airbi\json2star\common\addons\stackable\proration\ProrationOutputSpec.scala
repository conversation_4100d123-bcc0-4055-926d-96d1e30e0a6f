package com.amadeus.airbi.json2star.common.addons.stackable.proration

import com.amadeus.airbi.json2star.common.addons.stackable.proration.ProrationInput.CouponKey
import com.amadeus.airbi.json2star.common.extdata.PorsExtData
import com.amadeus.airbi.json2star.common.testfwk.SimpleSpec
import com.amadeus.ti.models.cdb.NumericAmount
import com.amadeus.ti.models.currency.Currency
import com.amadeus.ti.models.currency.Currency.USD
import com.amadeus.ti.parsers.por.optd.PorParser
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.scala.DefaultScalaModule

import java.time.LocalDate
import scala.io.Source

// scalastyle:ignore magic.number
class ProrationOutputSpec extends SimpleSpec {
  val DocumentDate = "2023-01-25"

  lazy val porsRef = {
    val optdPath = s"src/test/resources/datasets/proration/extdata/input_optd_por_public_sample.csv"
    val header :: lines = Source.fromFile(optdPath, "UTF-8").getLines.toList
    PorParser.toPorsRef(header, lines)
  }

  val pors: PorsExtData = PorsExtData(data = porsRef)

  val actualItinerary: Seq[CouponKey] = Seq(
    CouponKey(1, "EWR", "BOS"),
    CouponKey(2, "BOS", "ACK"),
    CouponKey(3, "ACK", "BOS"),
    CouponKey(4, "BOS", "DCA")
  )

  describe("ProrationOutput - build") {

    it("should generate the prorated coupons - case all by fare calc") {
      val actualInput = ProrationInput(
        pors = pors,
        fareCalcLine =
          "EWR B6 BOS64.19UL07AE2C 9K ACK147.81MIPX 9K BOS157.12MIP B6 DCA Q8.79 219.89VH03AE6U USD597.80END ZP EWR4.00BOS0.00ACK0.00BOS4.00XF EWR4.5BOS4.5XT8.00ZP9.00XF",
        paymentCurrency = "USD",
        paymentTotal = "647.80",
        paymentTotalTaxes = "50.00",
        issueDate = Some(LocalDate.parse(DocumentDate)),
        itinerary = actualItinerary
      )

      val expectedOutput = ProrationOutput(
        input = actualInput,
        coupons = Map(
          CouponKey(1, "EWR", "BOS") -> FclProratedCouponSuccess(NumericAmount.fromLong(6419, USD)),
          CouponKey(2, "BOS", "ACK") -> FclProratedCouponSuccess(NumericAmount.fromLong(14781, USD)),
          CouponKey(3, "ACK", "BOS") -> FclProratedCouponSuccess(NumericAmount.fromLong(15712, USD)),
          CouponKey(4, "BOS", "DCA") -> FclProratedCouponSuccess(NumericAmount.fromLong(22868, USD))
        ),
        inputError = None,
        tktStatus = "FARE CALC-ALL"
      )

      val actualOutput = ProrationOutput.build(pors.data, actualInput)

      actualOutput shouldBe expectedOutput
    }

    it("should generate the prorated coupons - case all by distance") {
      val actualInput = ProrationInput(
        pors = pors,
        fareCalcLine = "",
        paymentCurrency = "USD",
        paymentTotal = "647.80",
        paymentTotalTaxes = "50.00",
        issueDate = Some(LocalDate.parse(DocumentDate)),
        itinerary = actualItinerary
      )

      val nfc = FclProratedCouponFailure(StepStatus("FARE_CALC-input-empty", "ERROR: fare calc is empty"))
      val expectedOutput = ProrationOutput(
        input = actualInput,
        coupons = Map(
          CouponKey(1, "EWR", "BOS") -> DistProratedCouponSuccess(
            NumericAmount.fromLong(15334, USD),
            StepStatus(
              "DISTANCE-proration-success",
              "SUCCESS: check details|ratio = 0.26|remainingAmount = 597.80|cpnDistance = 319.01|remainingDistanceTot = 1243.65"
            ),
            nfc
          ),
          CouponKey(2, "BOS", "ACK") -> DistProratedCouponSuccess(
            NumericAmount.fromLong(6902, USD),
            StepStatus(
              "DISTANCE-proration-success",
              "SUCCESS: check details|ratio = 0.12|remainingAmount = 597.80|cpnDistance = 143.60|remainingDistanceTot = 1243.65"
            ),
            nfc
          ),
          CouponKey(3, "ACK", "BOS") -> DistProratedCouponSuccess(
            NumericAmount.fromLong(6902, USD),
            StepStatus(
              "DISTANCE-proration-success",
              "SUCCESS: check details|ratio = 0.12|remainingAmount = 597.80|cpnDistance = 143.60|remainingDistanceTot = 1243.65"
            ),
            nfc
          ),
          CouponKey(4, "BOS", "DCA") -> DistProratedCouponSuccess(
            NumericAmount.fromLong(30641, USD),
            StepStatus(
              "DISTANCE-proration-success",
              "SUCCESS: check details|ratio = 0.51|remainingAmount = 597.80|cpnDistance = 637.45|remainingDistanceTot = 1243.65"
            ),
            nfc
          )
        ),
        inputError = None,
        tktStatus = "DISTANCE-ALL"
      )

      val actualOutput = ProrationOutput.build(pors.data, actualInput)

      actualOutput shouldBe expectedOutput
    }

    it("should generate the prorated coupons - case partial - similar currencies - USD NUC") {
      // Adapted from MH
      val DocumentDate = "2023-07-24"
      val actualInput = ProrationInput(
        pors = pors,
        fareCalcLine = "MNL MH KUL//SIN111.00MH X/KUL MH MNL111.00NUC222.00END ROE1.000000",
        paymentCurrency = "USD",
        paymentTotal = "279.10",
        paymentTotalTaxes = "57.10",
        issueDate = Some(LocalDate.parse(DocumentDate)),
        itinerary = Seq(
          CouponKey(1, "MNL", "KUL"),
          CouponKey(2, "SIN", "KUL"),
          CouponKey(3, "KUL", "MNL")
        )
      )

      val expectedOutput = ProrationOutput(
        input = actualInput,
        coupons = Map(
          CouponKey(1, "MNL", "KUL") -> DistProratedCouponSuccess(
            NumericAmount.fromLong(11100, USD),
            StepStatus(
              "DISTANCE-proration-success",
              "SUCCESS: check details|ratio = 1.00|remainingAmount = 111.00|cpnDistance = 2488.24|remainingDistanceTot = 2488.24"
            ),
            FclProratedCouponFailure(StepStatus("FARE_CALC-cpn-not-found", "ERROR: (1,MNL,KUL)"))
          ),
          CouponKey(2, "SIN", "KUL") -> FclProratedCouponSuccess(NumericAmount.fromLong(1185, USD)),
          CouponKey(3, "KUL", "MNL") -> FclProratedCouponSuccess(NumericAmount.fromLong(9915, USD))
        ),
        inputError = None,
        tktStatus = "PARTIAL"
      )

      val actualOutput = ProrationOutput.build(pors.data, actualInput)
      // check if all keys are present
      actualOutput.coupons.keys should contain theSameElementsAs (actualInput.itinerary)
      actualOutput shouldBe expectedOutput
    }

    it("should generate the prorated coupons - case partial - same currency") {
      // Adapted from 4Z - 41e9937676504162e9bddff564e2ca9a - version 3
      val DocumentDate = "2023-08-02"
      val actualInput = ProrationInput(
        pors = pors,
        fareCalcLine = "NBO KQ JNB//CPT255.50 4Z X/JNB KQ NBO275.00USD530.50END",
        paymentCurrency = "USD",
        paymentTotal = "1016.60",
        paymentTotalTaxes = "314.60",
        issueDate = Some(LocalDate.parse(DocumentDate)),
        itinerary = Seq(
          CouponKey(1, "NBO", "JNB"),
          CouponKey(2, "CPT", "JNB"),
          CouponKey(3, "JNB", "NBO")
        )
      )

      val expectedOutput = ProrationOutput(
        input = actualInput,
        coupons = Map(
          CouponKey(1, "NBO", "JNB") -> DistProratedCouponSuccess(
            NumericAmount.fromLong(42700, USD),
            StepStatus(
              "DISTANCE-proration-success",
              "SUCCESS: check details|ratio = 1.00|remainingAmount = 427.00|cpnDistance = 2911.76|remainingDistanceTot = 2911.76"
            ),
            FclProratedCouponFailure(StepStatus("FARE_CALC-cpn-not-found", "ERROR: (1,NBO,JNB)"))
          ),
          CouponKey(2, "CPT", "JNB") -> FclProratedCouponSuccess(NumericAmount.fromLong(8356, USD)),
          CouponKey(3, "JNB", "NBO") -> FclProratedCouponSuccess(NumericAmount.fromLong(19144, USD))
        ),
        inputError = None,
        tktStatus = "PARTIAL"
      )

      val actualOutput = ProrationOutput.build(pors.data, actualInput)
      // check if all keys are present
      actualOutput.coupons.keys should contain theSameElementsAs (actualInput.itinerary)
      actualOutput shouldBe expectedOutput
    }

    it("should generate the prorated coupons - case distance all - fallback for different currency") {
      // Extract from 4Z - 41e9937676504162e9bddff564e2ca9a - version 3
      val DocumentDate = "2023-08-02"
      val actualInput = ProrationInput(
        pors = pors,
        fareCalcLine = "NBO KQ JNB//CPT255.50 4Z X/JNB KQ NBO275.00USD530.50END", // currency is USD
        paymentCurrency = "CAD", // currency is CAD
        paymentTotal = "1016.60",
        paymentTotalTaxes = "314.60",
        issueDate = Some(LocalDate.parse(DocumentDate)),
        itinerary = Seq(
          CouponKey(1, "NBO", "JNB"),
          CouponKey(2, "CPT", "JNB"),
          CouponKey(3, "JNB", "NBO")
        )
      )

      val actualOutput = ProrationOutput.build(pors.data, actualInput)

      actualOutput.inputError shouldBe None
      actualOutput.tktStatus shouldBe "DISTANCE-ALL"
      actualOutput.coupons(CouponKey(1, "NBO", "JNB")) should be(
        DistProratedCouponSuccess(
          NumericAmount.fromLong(28812, Currency.CAD),
          StepStatus(
            "DISTANCE-proration-success",
            "SUCCESS: check details|ratio = 0.41|remainingAmount = 702.00|cpnDistance = 2911.76|remainingDistanceTot = 7094.34"
          ),
          FclProratedCouponFailure(
            StepStatus(
              "FARE_CALC-cpn-not-found",
              "ERROR: (1,NBO,JNB)"
            )
          )
        )
      )

      actualOutput.coupons(CouponKey(2, "CPT", "JNB")) should be(
        DistProratedCouponSuccess(
          NumericAmount.fromLong(12575, Currency.CAD),
          StepStatus(
            "DISTANCE-proration-success",
            "SUCCESS: check details|ratio = 0.18|remainingAmount = 702.00|cpnDistance = 1270.83|remainingDistanceTot = 7094.34"
          ),
          FclProratedCouponFailure(
            StepStatus(
              "DISTANCE-currency-different",
              "ERROR: check fcl and payment currency|payment_amount = 702.00|payment_currency = CAD|fcl_amount = 83.56|fcl_currency = USD"
            )
          )
        )
      )

      actualOutput.coupons(CouponKey(3, "JNB", "NBO")) should be(
        DistProratedCouponSuccess(
          NumericAmount.fromLong(28812, Currency.CAD),
          StepStatus(
            "DISTANCE-proration-success",
            "SUCCESS: check details|ratio = 0.41|remainingAmount = 702.00|cpnDistance = 2911.76|remainingDistanceTot = 7094.34"
          ),
          FclProratedCouponFailure(
            StepStatus(
              "DISTANCE-currency-different",
              "ERROR: check fcl and payment currency|payment_amount = 702.00|payment_currency = CAD|fcl_amount = 191.44|fcl_currency = USD"
            )
          )
        )
      )
    }
  }

  describe("ProrationOutput - buildColumns") {
    val actualInput = ProrationInput(
      pors = pors,
      fareCalcLine = "DUMMY FARE CALC LINE",
      paymentCurrency = "USD",
      paymentTotal = "647.80",
      paymentTotalTaxes = "50.00",
      issueDate = Some(LocalDate.parse(DocumentDate)),
      itinerary = actualItinerary
    )

    val actualOutput = ProrationOutput(
      input = actualInput,
      coupons = Map(
        CouponKey(1, "EWR", "BOS") -> FclProratedCouponSuccess(NumericAmount.fromLong(6419, USD)),
        CouponKey(2, "BOS", "ACK") -> DistProratedCouponSuccess(
          NumericAmount.fromLong(14781, USD),
          distStep = StepStatus("Step Dist", "details"),
          fclFailure = FclProratedCouponFailure(StepStatus("Step Fcl", "error for fcl - case 2"))
        ),
        CouponKey(3, "ACK", "BOS") -> DistProratedCouponFailure(
          StepStatus("Step Dist", "errors dst for coupon 3"),
          fclFailure = FclProratedCouponFailure(StepStatus("Step Fcl", "errors fcl for coupon 3"))
        ),
        CouponKey(4, "BOS", "DCA") -> FclProratedCouponSuccess(NumericAmount.fromLong(22868, USD))
      ),
      inputError = Some(StepStatus("Step Input", "error for input")),
      tktStatus = "TKT STATUS"
    )

    it("should fill the output columns for a coupon prorated by Fare Calc") {
      val actualCols = actualOutput.buildColumns(Some(CouponKey(1, "EWR", "BOS")), enableMeta = true)
      actualCols shouldBe Map(
        "ESTIMATED_PRORATED_FARE_ORIGINAL" -> "64.19",
        "ESTIMATED_PRORATED_FARE_CURRENCY_ORIGINAL" -> "USD",
        "ESTIMATED_PRORATED_FARE_ALGORITHM" -> "FARE CALC",
        "INTERNAL_PRORATION_METADATA" -> oneLiner("""|{
             |  "input" : {
             |    "fareCalcLine" : "DUMMY FARE CALC LINE",
             |    "issueDate" : [ 2023, 1, 25 ],
             |    "paymentCurrency" : "USD",
             |    "paymentTotal" : "647.80",
             |    "paymentTotalTaxes" : "50.00",
             |    "itinerary" : [ {
             |      "sequenceNumber" : 1,
             |      "departureAirport" : "EWR",
             |      "arrivalAirport" : "BOS"
             |    }, {
             |      "sequenceNumber" : 2,
             |      "departureAirport" : "BOS",
             |      "arrivalAirport" : "ACK"
             |    }, {
             |      "sequenceNumber" : 3,
             |      "departureAirport" : "ACK",
             |      "arrivalAirport" : "BOS"
             |    }, {
             |      "sequenceNumber" : 4,
             |      "departureAirport" : "BOS",
             |      "arrivalAirport" : "DCA"
             |    } ]
             |  },
             |  "tktStatus" : "TKT STATUS",
             |  "exceptionMsg" : null,
             |  "inputStep" : {"value":"Step Input","details":"error for input"},
             |  "fclStep" : null,
             |  "dstStep" : null
             |}""".stripMargin)
      )
    }

    it("should fill the output columns for a coupon prorated by Distance - error case") {
      val actualCols = actualOutput.buildColumns(Some(CouponKey(3, "ACK", "BOS")), enableMeta = true)
      actualCols shouldBe Map(
        "ESTIMATED_PRORATED_FARE_ORIGINAL" -> null,
        "ESTIMATED_PRORATED_FARE_CURRENCY_ORIGINAL" -> null,
        "ESTIMATED_PRORATED_FARE_ALGORITHM" -> "NONE",
        "INTERNAL_PRORATION_METADATA" -> oneLiner("""|{
               |  "input" : {
               |    "fareCalcLine" : "DUMMY FARE CALC LINE",
               |    "issueDate" : [ 2023, 1, 25 ],
               |    "paymentCurrency" : "USD",
               |    "paymentTotal" : "647.80",
               |    "paymentTotalTaxes" : "50.00",
               |    "itinerary" : [ {
               |      "sequenceNumber" : 1,
               |      "departureAirport" : "EWR",
               |      "arrivalAirport" : "BOS"
               |    }, {
               |      "sequenceNumber" : 2,
               |      "departureAirport" : "BOS",
               |      "arrivalAirport" : "ACK"
               |    }, {
               |      "sequenceNumber" : 3,
               |      "departureAirport" : "ACK",
               |      "arrivalAirport" : "BOS"
               |    }, {
               |      "sequenceNumber" : 4,
               |      "departureAirport" : "BOS",
               |      "arrivalAirport" : "DCA"
               |    } ]
               |  },
               |  "tktStatus" : "TKT STATUS",
               |  "exceptionMsg": null,
               |  "inputStep" : {"value":"Step Input","details":"error for input"},
               |  "fclStep" : {"value":"Step Fcl","details":"errors fcl for coupon 3"},
               |  "dstStep" : {"value":"Step Dist","details":"errors dst for coupon 3"}
               |}""".stripMargin)
      )
    }
  }

  def oneLiner(pretty: String): String = {
    val mapper = new ObjectMapper()
    mapper.registerModule(DefaultScalaModule)
    val jsonNode = mapper.readTree(pretty)
    val oneLine = mapper.writeValueAsString(jsonNode)
    oneLine
  }

}
