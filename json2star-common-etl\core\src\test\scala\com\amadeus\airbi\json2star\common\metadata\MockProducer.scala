package com.amadeus.airbi.json2star.common.metadata

import com.amadeus.airbi.json2star.common.app.Metrics
import io.delta.tables.DeltaTable
import org.apache.spark.sql.types.StructType
import org.apache.spark.sql.{Encoders, SparkSession}

import scala.util.{Failure, Success, Try}

case class PIVOT(
  ID: String,
  FIELD_A: String,
  FIELD_B: String
)

case class OTHER(
  ID: String,
  NAME: String
)

object MockProducer {
  val PIVOT_TABLE_NAME: String = "PIVOT"
  val OTHER_TABLE_NAME: String = "OTHER"
  val DOMAIN = "PNR"
}

/** Mock producer entry point
  *
  * @param database   output database name
  * @param runId      used to build the JOB_RUN_ID of the metadata table
  * @param checkpoint used in the values written in the tables to indicate that they come from different checkpoints
  *                   using the same values in different runs of `run(...)` allows to simulate several steps of the
  *                   same run provided you're using the same runId (first write one table, then another)
  * @param fail       simulate a global failure
  * @param writePivot write the pivot table
  * @param writeOther write the other table
  * @param logStart log start time in metadata
  * @param logEnd log end time in metadata
  */
case class MockProducerConfig(
  database: String,
  runId: String,
  checkpoint: Int,
  fail: Boolean = false,
  writePivot: Boolean = true,
  writeOther: Boolean = true,
  logStart: Boolean = true,
  logEnd: Boolean = true
)

class MockProducer(spark: SparkSession) {

  import MockProducer._

  def run(config: MockProducerConfig): Unit = {
    val id = config.runId
    val checkpoint = config.checkpoint
    val metadata = new JobRunMetadata(spark, config.database, DOMAIN, "jobid", s"jobrunid-$id")
    if (config.logStart) {
      metadata.logStart()
    }
    val pipelineStatus = Try {
      if (config.fail) {
        throw new Exception("oops")
      }
      import spark.implicits._
      if (config.writePivot) {
        createTableIfNotExists(PIVOT_TABLE_NAME, Encoders.product[PIVOT].schema)
        Seq(PIVOT(s"checkpoint-$checkpoint", s"a$checkpoint", s"b$checkpoint")).toDF
          .createOrReplaceTempView("p")
        merge(PIVOT_TABLE_NAME, "p")
      }
      if (config.writeOther) {
        createTableIfNotExists(OTHER_TABLE_NAME, Encoders.product[OTHER].schema)
        Seq(OTHER(s"checkpoint-$checkpoint", s"name$checkpoint")).toDF
          .createOrReplaceTempView("o")
        merge(OTHER_TABLE_NAME, "o")
      }
      MockQualityMetrics.mappingMetrics
    }

    if (config.logEnd) {
      pipelineStatus match {
        case Failure(exception) => metadata.logFailure(JobRunMetadataDetails.fromThrowable(exception))
        case Success(metrics) => metadata.logSuccess(metrics)
      }
    }
  }

  private def merge(table: String, input: String): Unit = {
    spark.sql(s"""
              MERGE INTO $table e USING $input s
              ON e.ID = s.ID
              WHEN MATCHED THEN UPDATE SET *
              WHEN NOT MATCHED THEN INSERT * 
              """)
  }

  private def createTableIfNotExists(tableName: String, schema: StructType): Unit = {
    DeltaTable
      .createIfNotExists(spark)
      .property("delta.autoOptimize.optimizeWrite", "true")
      .property("delta.autoOptimize.autoCompact", "true")
      .property("delta.enableChangeDataFeed", "true")
      .tableName(tableName)
      .addColumns(schema)
      .execute()
  }
}
