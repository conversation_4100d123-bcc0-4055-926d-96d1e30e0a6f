EXCESS_<PERSON><PERSON><PERSON><PERSON>_ITEM_ID,R<PERSON><PERSON><PERSON><PERSON>_KEY,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>ENTIFIER,<PERSON>EM_IDENTIFIER,WEIGHT_VALUE,WEI<PERSON><PERSON>_<PERSON><PERSON>,WEIGHT_VALUE_ORIGINAL,WEIG<PERSON>_<PERSON>IT_ORIGINAL,<PERSON><PERSON><PERSON>_AMOUNT,<PERSON>AR<PERSON>_CURRENCY,<PERSON>AR<PERSON>_EXCHANGE_RATE_DATE_TAKEN,<PERSON>AR<PERSON>_AMOUNT_ORIGINAL,CH<PERSON>GE_CURRENCY_ORIGIN<PERSON>,CHAR<PERSON>_EXCHANGE_RATE_DATE_NEEDED,RATE_AMOUNT,RATE_CURRENCY,RATE_EXCHANGE_RATE_DATE_TAKEN,RATE_AMOUNT_ORIGINAL,RATE_AMOUNT_EXTRA_PART,RATE_CURRENCY_ORIGINAL,RATE_EXCHANGE_RATE_DATE_NEEDED,<PERSON><PERSON>_<PERSON>,EXCESS_BAG<PERSON><PERSON>_CHARGE_ID,<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>EG<PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON>_LAST_VERSION,<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>
hashM(BAGDA3Y49025-10000000011AA11A-10A0110001AA1AA1-1000000001A12345),BAGDA3Y49025-10000000011AA11A-10A0110001AA1AA1-1000000001A12345,10A0110001AA1AA1,1000000001A12345,56.7,KILOGRAMS,125.0,POUNDS,81.57407,USD,1970-01-01,75,EUR,2018-10-23,81.57407,USD,1970-01-01,75,,EUR,2018-10-23,hashM(BAGDA3Y49025-2002BAG00036004D),hashM(BAGDA3Y49025-10000000011AA11A),hashM(BAGDA3Y49025),1612410241801,2021-02-04T03:44:01.789000Z,,true,2018-02-01T00:00:00.000000Z
