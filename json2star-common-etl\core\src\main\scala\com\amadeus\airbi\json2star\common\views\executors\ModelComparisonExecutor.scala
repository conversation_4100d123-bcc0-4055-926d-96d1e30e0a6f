package com.amadeus.airbi.json2star.common.views.executors

import com.amadeus.airbi.json2star.common.config.AppConfig
import com.amadeus.airbi.json2star.common.views.generators.PlantUmlGenerator
import com.amadeus.airbi.rawvault.common.application.config.{ModelConfigLoader, TablesConfig}
import com.amadeus.airbi.rawvault.common.config.{ColumnConfig, ColumnMetadata}
import org.rogach.scallop.{ScallopConf, ScallopOption}
import org.slf4j.{Logger, LoggerFactory}

import java.io.{BufferedWriter, File, FileWriter}

case class ModelComparisonExecutorConfig(arguments: Seq[String]) extends ScallopConf(arguments) {
  val oldMappingFilePath: ScallopOption[String] = opt[String](required = true)
  val newMappingFilePath: ScallopOption[String] = opt[String](required = true)
  val outputDirectory: ScallopOption[String] = opt[String](required = false)

  verify()
}

/**
 * @param tableName: Name of the table
 * @param addedColumns: List of columns that were added
 * @param removedColumns: List of columns that were removed
 * @param changedColumns: List of tuples (columnName, List of fields that changed)
 */
case class ColumnChange(
  tableName: String,
  addedColumns: List[String],
  removedColumns: List[String],
  changedColumns: List[(String, List[String])]
)

/**
 * @param addedTables: List of tables that were added
 * @param removedTables: List of tables that were removed
 * @param columnChanges: List of ColumnChange
 */
case class ModelChanges(
  addedTables: List[String],
  removedTables: List[String],
  columnChanges: List[ColumnChange]
)

/** [Runnable with SBT]
  * This executor is used to compare the model of two different versions of the same domain.
  * The output is a list of tables that needs to be reprocessed and the reason why.
  * The different reasons are:
  *  - A column changed (added, removed, changed type, changed source path)
  *  - A table changed (added, removed)
  *  The outPut is written in the output directory if provided, otherwise in stdout.
  *       --old-mapping-file-path some/path
  *       --new-mapping-file-path some/path
  *       --output-directory  .   [optional]
  */
object ModelComparisonExecutor {

  implicit lazy val logger: Logger = LoggerFactory.getLogger(getClass.getName)
  def main(args: Array[String]): Unit = {

    val metaExclusionKeys = List("description")

    val executorConfig = ModelComparisonExecutorConfig(args)

    val oldTablesConfig: TablesConfig =
      ModelConfigLoader.fromResourcesOrFile(executorConfig.oldMappingFilePath())
    val newTablesConfig: TablesConfig =
      ModelConfigLoader.fromResourcesOrFile(executorConfig.newMappingFilePath())

    val changes = computeChanges(oldTablesConfig, newTablesConfig, metaExclusionKeys)

    writeDiff(changes, executorConfig.outputDirectory.toOption)

  }

  def computeChanges(
    oldTablesConfig: TablesConfig,
    newTablesConfig: TablesConfig,
    metaExclusionKeys: List[String]
  ): ModelChanges = {
    val oldTables = oldTablesConfig.tables.map(t => t.name -> t).toMap
    val newTables = newTablesConfig.tables.map(t => t.name -> t).toMap

    // Identify added and removed tables
    val addedTables: List[String] = newTables.keySet.diff(oldTables.keySet).toList
    val removedTables: List[String] = oldTables.keySet.diff(newTables.keySet).toList

    val commonTables = oldTables.keySet.intersect(newTables.keySet)
    // Identify column changes in common tables
    val columnChanges = commonTables.toList.flatMap { tableName =>
      val oldTable = oldTables(tableName)
      val newTable = newTables(tableName)

      val oldColumns = oldTable.mapping
        .map(_.columns.map(column => column.name -> column).toMap)
        .getOrElse(Map.empty)

      val newColumns = newTable.mapping
        .map(_.columns.map(column => column.name -> column).toMap)
        .getOrElse(Map.empty)

      val addedColumns: List[String] = newColumns.keySet.diff(oldColumns.keySet).toList
      val removedColumns: List[String] = oldColumns.keySet.diff(newColumns.keySet).toList

      // Compute (columnName, reasons)
      val changedColumns = oldColumns.keySet
        .intersect(newColumns.keySet)
        .toList
        .map { column =>
          val oldColumn = oldColumns(column)
          val newColumn = newColumns(column)
          (column, computeColumnDiff(oldColumn, newColumn, metaExclusionKeys))
        }
        .filter(_._2.nonEmpty)

      if (addedColumns.nonEmpty || removedColumns.nonEmpty || changedColumns.nonEmpty) {
        Some(ColumnChange(tableName, addedColumns, removedColumns, changedColumns))
      } else {
        None
      }
    }
    // Combine results
    ModelChanges(
      addedTables,
      removedTables,
      columnChanges
    )

  }

  /** Compares ColumnConfig without the meta part
    * @param col1: ColumnConfig
    * @param col2: ColumnConfig
    * @return List[String]. The list of fields that are different between the two ColumnConfig
    */
  def computeColumnDiff(col1: ColumnConfig, col2: ColumnConfig, metaExclusionKeys: List[String]): List[String] = {

    def compareFields[T](
      obj1: T,
      obj2: T,
      prefix: String = "",
      exclude: List[String] = List.empty[String]
    ): List[String] = {
      val fieldNames = obj1.getClass.getDeclaredFields.map(_.getName)
      fieldNames.flatMap {
        case fieldName if exclude.contains(fieldName) => None
        case fieldName =>
          val field = obj1.getClass.getDeclaredField(fieldName)
          field.setAccessible(true)
          val value1 = field.get(obj1)
          val value2 = field.get(obj2)
          if (value1 != value2) Some(s"$prefix$fieldName") else None
      }.toList
    }

    val metaDiff = compareFields(
      col1.meta.getOrElse(ColumnMetadata()),
      col2.meta.getOrElse(ColumnMetadata()),
      prefix = "meta.",
      exclude = metaExclusionKeys
    )
    val columnDiff = compareFields(col1, col2, exclude = List("meta"))

    columnDiff ++ metaDiff
  }

  def writeDiff(diffs: ModelChanges, outPutDir: Option[String] = None): Unit = {
    val addedTables = diffs.addedTables
    val removedTables = diffs.removedTables
    val columnChanges = diffs.columnChanges

    val outputBuilder = new StringBuilder

    addedTables.foreach { table =>
      outputBuilder.append(s"TABLE $table: added\n")
    }
    removedTables.foreach { table =>
      outputBuilder.append(s"TABLE $table: removed\n")
    }
    columnChanges.foreach { case ColumnChange(tableName, addedColumns, removedColumns, changedColumns) =>
      outputBuilder.append(s"TABLE $tableName: changed\n")
      addedColumns.foreach { column =>
        outputBuilder.append(s"       column $column: added\n")
      }
      removedColumns.foreach { column =>
        outputBuilder.append(s"       column $column: removed\n")
      }
      changedColumns.foreach { case (columnName, fields) =>
        outputBuilder.append(s"       column $columnName: field [${fields.mkString(", ")}] have changed\n")
      }
    }

    val content = outputBuilder.toString() match {
      case "" => "Mappings are identical"
      case str => str
    }

    outPutDir match {
      case Some(path) =>
        val file = new File(path, "model_comparison").toString
        val bw = new BufferedWriter(new FileWriter(file))
        bw.write(content)
        bw.close()

      case None => println(content) // scalastyle:ignore
    }

  }

}
