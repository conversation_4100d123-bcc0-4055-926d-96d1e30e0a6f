{
  "defaultComment" : "A coment here"
  "tables": [
    {
        "name": "FACT_TABLE_1",
        "mapping" : {
          "merge": {
            "key-columns": ["RESERVATION_ID", "VERSION"],
            "if-dupe-take-higher": ["LOAD_DATE"]
          },
          "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}]}],
          "columns": [
            {"name": "RESERVATION_ID", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "anotherOne"},
            {"name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
            {"name": "NIP", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.nip"}]}},
            {"name": "NIPO", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.nip"}]}},
            {"name": "DATE_BEGIN", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.dateTime"}]}},
            {"name": "DATE_END", "column-type": "booleanColumn", "sources": {}},
            {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
          ],
          "pit": {
            "type": "master-pit-table",
            "master" : {
              "pit-key": "RESERVATION_ID",
              "pit-version": "VERSION",
              "valid-from": "DATE_BEGIN",
              "valid-to": "DATE_END",
              "is-last": "IS_LAST_VERSION"
            }
          }
        }
    },
    {
      "name": "FACT_TABLE_2",
      "mapping" : {
        "merge": {
          "key-columns": ["AIR_SEGMENT_PAX_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [{ "blocks": [
          {"all": "$"},
          {"prod": "$.mainResource.current.image.products[?(@.subType == 'AIR')]"},
          {"tr": "$.travelers[*]"}
        ]}],
        "columns": [
          {"name": "AIR_SEGMENT_PAX_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"prod": "$.id"}, {"tr": "$.id"}]}, "expr":  "hashM({0})"},
          {"name": "NEW_COLUMN", "column-type": "strColumn", "sources": {"blocks": [{"prod": "$.id"}, {"tr": "$.id"}]}},
          {"name": "ANOTHER_ONE", "column-type": "strColumn", "sources": {"blocks": [{"prod": "$.id"}, {"tr": "$.id"}]}},
          {"name": "PRODUCT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"prod": "$.id"}]}}, // ADDED one for filtering the delivery
          {"name": "DATE_BEGIN", "column-type": "intColumn", "sources": {"blocks": [{"root" : "$.current.image.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "booleanColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
        ],
        "pit": {
          "type": "secondary-pit-table"
        }
      }
    },
    {
      "name": "FACT_TABLE_6",
      "mapping" : {
        "merge": {
          "key-columns": ["AIR_SEGMENT_PAX_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [{ "blocks": [
          {"all": "$"},
          {"prod": "$.mainResource.current.image.products[?(@.subType == 'AIR')]"},
          {"tr": "$.travelers[*]"}
        ]}],
        "columns": [
          {"name": "AIR_SEGMENT_PAX_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"prod": "$.id"}, {"tr": "$.id"}]}, "expr":  "hashM({0})"},
          {"name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [{"prod": "$.id"}, {"tr": "$.id"}]}},
          {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "sources":{"blocks": [{"root": "$.mainResource.current.image.id"}]}, "expr": "hashM({0})"},
          {"name": "ANOTHER_NEW_ONE", "column-type": "binaryStrColumn", "sources":{"blocks": [{"root": "$.mainResource.current.image.id"}]}, "expr": "hashM({0})"},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
        ],
        "pit": {
          "type": "secondary-pit-table"
        }
      }
    }
  ]
}