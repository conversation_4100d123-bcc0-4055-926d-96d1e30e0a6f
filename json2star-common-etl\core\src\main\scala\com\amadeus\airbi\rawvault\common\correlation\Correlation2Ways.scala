package com.amadeus.airbi.rawvault.common.correlation

import com.amadeus.airbi.rawvault.common.application.config.Correlation
import com.typesafe.scalalogging.Logger
import io.delta.tables.DeltaTable
import org.apache.spark.sql.catalyst.encoders.RowEncoder
import org.apache.spark.sql.functions.col
import org.apache.spark.sql.types._
import org.apache.spark.sql.{DataFrame, SparkSession}
import org.slf4j.LoggerFactory

object Correlation2Ways {

  sealed trait CorrelationWay
  case object DomainAtoDomainB extends CorrelationWay
  case object DomainBtoDomainA extends CorrelationWay

  lazy val logger: Logger = Logger(LoggerFactory.getLogger(getClass.getName))

  def toCorrelationLibrary(a: Correlation.AssoTable, way: CorrelationWay): CorrelationLibrary = { // TODO: reuse in below method and keep in unit tests
    // Domain A: partial table, Domain B: PIT table
    val (partial, pit) = way match {
      case DomainAtoDomainB => (a.domainA.partial, a.domainB.pit)
      case DomainBtoDomainA => (a.domainB.partial, a.domainA.pit)
    }

    CorrelationLibrary(
      END_DATE = partial.endDate,
      START_DATE = partial.startDate, //"DATE_BEGIN",
      PARTIAL_CORR_VERSION = partial.partialCorrVersion, // = "VERSION",
      PIT_VERSION = pit.pitVersion, // "VERSION",
      PARTIAL_CORR_KEY = partial.partialCorrKey, // "COUPON_ID",
      PIT_CORR_KEY = pit.pitCorrKey, // "TRAVEL_DOCUMENT_ID",
      CORRELATION_FIELD = way match {
        case DomainAtoDomainB => a.correlationFieldAToB
        case DomainBtoDomainA => a.correlationFieldBToA
      }, // common to partial & pit
      PARTIAL_CORR_SECONDARY_KEY = partial.partialCorrSecondaryKey, // "AIR_SEGMENT_PAX_ID"
      IS_LAST_PARTIAL = partial.isLast,
      IS_LAST_PIT = pit.isLast
    )
  }

  /** ONLY for testing purposes
    *
    * @param spark
    * @param correlation
    * @return
    */
  def applyStaticStaticForTests(
    spark: SparkSession,
    correlation: Correlation.AssoTable
  ): (DataFrame, DataFrame) = {
    (
      Correlation2Ways.intermediateDataframe(
        spark,
        correlation,
        DeltaTable.forName(correlation.domainA.partial.table).toDF,
        DeltaTable.forName(correlation.domainB.pit.table).toDF,
        way = DomainAtoDomainB
      ),
      Correlation2Ways.intermediateDataframe(
        spark,
        correlation,
        DeltaTable.forName(correlation.domainB.partial.table).toDF,
        DeltaTable.forName(correlation.domainA.pit.table).toDF,
        way = DomainBtoDomainA
      )
    )

  }

  def intermediateDataframe(
    spark: SparkSession,
    correlation: Correlation.AssoTable,
    partialCorr: DataFrame,
    masterPit: DataFrame,
    way: CorrelationWay = DomainAtoDomainB
  ): DataFrame = {
    // Step 1 - compute the correlation validity period joining partial with the PIT table using the correlation key
    val c = Correlation2Ways.toCorrelationLibrary(correlation, way)

    // Get the fields to select from the PIT table and from the PARTIAL table
    val (pitColNames, attrPartialColNames) = way match {
      // A.PARTIAL-B.PIT --> retrieve fields only in B.PIT and assoAttributes from A.partial
      case DomainAtoDomainB =>
        val domainAKeySrc = correlation.target.domainAKey.srcColPitA.getOrElse(correlation.target.domainAKey.name)
        val partialAFields = domainAKeySrc +: correlation.target.assoAttributesSeq.filter(_.srcColPitB.isEmpty).map(_.name)
        (correlation.pitBFields, partialAFields)
      // B.PARTIAL-A.PIT --> retrieve fields only in A.PIT and assoAttributes from B.partial
      case DomainBtoDomainA =>
        val domainBKeySrc = correlation.target.domainBKey.srcColPitB.getOrElse(correlation.target.domainBKey.name)
        val partialBFields = domainBKeySrc +: correlation.target.assoAttributesSeq.filter(_.srcColPitA.isEmpty).map(_.name)
        (correlation.pitAFields, partialBFields)
    }

    val internalCorrDf = c.processCorrelations(
      partialAssoDF = partialCorr,
      pitDF = masterPit,
      assoAttributeFieldsFromPartial = attrPartialColNames,
      assoAttributeFieldsFromPit = pitColNames
    )(
      spark,
      RowEncoder(
        StructType(
          Array(
            StructField("PARTIAL_CORR_KEY", StringType),
            StructField("PARTIAL_CORR_SECONDARY_KEY", StringType),
            StructField("PARTIAL_CORR_VERSION", LongType),
            StructField("PIT_VERSION", LongType),
            StructField("CORR_START_DATE", TimestampType),
            StructField("CORR_END_DATE", TimestampType),
            StructField("CORR_IS_LAST", BooleanType)
          ) ++ attrPartialColNames.map(name => StructField(name, StringType, nullable = true))
            ++ pitColNames.map(name => StructField(name, StringType, nullable = true))
        )
      )
    )

    // Step 2 - Format output according to the way (AB, BA) and daas/dih correlation type
    // Once all fields are retrieved from the correlation join, insert fields in the right destination columns

    // Rules
    // type               | domainAKey.name                     | domainBKey.name
    // dih-corr-AtoB      | domainA.PARTIAL_CORR_KEY            | domainA.PARTIAL_CORR_SECONDARY_KEY
    // dih-corr-BtoA      | domainB.PARTIAL_CORR_SECONDARY_KEY  | domainB.PARTIAL_CORR_KEY
    // daas-corr-AtoB     | domainA.PARTIAL_CORR_KEY            | domainB.PIT_KEY
    // daas-corr-BtoA     | domainA.PIT_KEY                     | domainB.PARTIAL_CORR_KEY
    val (domainAKey, domainBKey, domainAVersion, domainBVersion, assoAttrs) = way match {
      case DomainAtoDomainB =>
        // A.PARTIAL-B.PIT
        val aKey = correlation.target.domainAKey.srcColPitA.getOrElse("PARTIAL_CORR_KEY")
        val bKey = correlation.target.domainBKey.srcColPitB.getOrElse("PARTIAL_CORR_SECONDARY_KEY")
        val aVersion = "PARTIAL_CORR_VERSION"
        val bVersion = "PIT_VERSION"
        // Get asso attributes: default by partial or by pit if specified
        val assoAttr = correlation.target.assoAttributesSeq.map(a => a.name -> a.srcColPitB.getOrElse(a.name))
        (aKey, bKey, aVersion, bVersion, assoAttr)
      case DomainBtoDomainA =>
        // B.PARTIAL-A.PIT
        val aKey = correlation.target.domainAKey.srcColPitA.getOrElse("PARTIAL_CORR_SECONDARY_KEY")
        val bKey = correlation.target.domainBKey.srcColPitB.getOrElse("PARTIAL_CORR_KEY")
        val aVersion = "PIT_VERSION"
        val bVersion = "PARTIAL_CORR_VERSION"
        // Get asso attributes: default by partial or by pit if specified
        val assoAttr = correlation.target.assoAttributesSeq.map(a => a.name -> a.srcColPitA.getOrElse(a.name))
        (aKey, bKey, aVersion, bVersion, assoAttr)
    }

    //  Destination columns with its source column
    val assoColsWithSource = Seq(
      correlation.target.domainAKey.name -> domainAKey,
      correlation.target.domainBKey.name -> domainBKey,
      correlation.target.domainAVersion.name -> domainAVersion,
      correlation.target.domainBVersion.name -> domainBVersion,
      correlation.target.startDate -> "CORR_START_DATE",
      correlation.target.endDate -> "CORR_END_DATE",
      correlation.target.isLast -> "CORR_IS_LAST"
    ) ++ assoAttrs

    // Select the output columns with renaming based on the target column name
    val assoDf = internalCorrDf.select(assoColsWithSource.map { case (dst, src) => col(src).as(dst) }: _*)
    assoDf
  }

}
