package com.amadeus.airbi.json2star.common.addons.stackable.proration

import com.amadeus.airbi.json2star.common.testfwk.SimpleSpec
import com.amadeus.ti.models.cdb.NumericAmount
import com.amadeus.ti.models.currency.Currency

class DistanceProrationSpec extends SimpleSpec {

  describe("DistanceProration - computePaymentAmount") {

    it("should compute a valid payment amount") {
      val na = DistanceProration.computePaymentAmount("647.80", "50.00", "USD")
      na shouldBe Right(NumericAmount.fromLong(59780, Currency.USD))
    }

    it("should compute a valid payment amount - NUC") {
      val na = DistanceProration.computePaymentAmount("647.80", "50.00", "NUC")
      na shouldBe Right(NumericAmount.fromLong(59780, Currency.NUC))
    }

    it("should invalidate a negative payment amount") {
      val na = DistanceProration.computePaymentAmount("50.00", "647.80", "USD")
      na shouldBe Left(StepStatus("DISTANCE-payment-amount-invalid", "ERROR: check -597.80"))
    }

    it("should invalidate a zero payment amount") {
      val na = DistanceProration.computePaymentAmount("647.80", "647.80", "USD")
      na shouldBe Left(StepStatus("DISTANCE-payment-amount-invalid", "ERROR: check 0.00"))
    }

    it("should invalidate an unknown currency") {
      val na = DistanceProration.computePaymentAmount("647.80", "50.00", "DUMMY")
      na shouldBe Left(
        StepStatus(
          "DISTANCE-payment-amount-not-parsed",
          "ERROR: check paymentTotal = 647.80, paymentTotalTaxes = 50.00, currencyCode = DUMMY"
        )
      )
    }

    it("should invalidate a payment total with additional charge") {
      val na = DistanceProration.computePaymentAmount("647.80A", "50.00", "USD")
      na shouldBe Left(
        StepStatus(
          "DISTANCE-payment-amount-not-parsed",
          "ERROR: check paymentTotal = 647.80A, paymentTotalTaxes = 50.00, currencyCode = USD"
        )
      )
    }

    it("should invalidate a wrong payment total taxes") {
      val na = DistanceProration.computePaymentAmount("647.80", "A", "USD")
      na shouldBe Left(
        StepStatus(
          "DISTANCE-payment-amount-not-parsed",
          "ERROR: check paymentTotal = 647.80, paymentTotalTaxes = A, currencyCode = USD"
        )
      )
    }
  }
}
