
Goal: integration test to verify generators using the DIH lookup and custom values from the configuration file 

The input is composed by
- mapping.conf file that contains the configuration for 1 table with multiple root-sources
- dcspax.yaml file that contains a light version of the DIH model

The expected output is the generated documentation in Markdown format with values from the DIH YAML and configuration.
Note:
The generated information are
- Description
- Example
- PII Type


It tests DIM Tables behaviour with the following cases: 
- with prefiller
- without prefiller