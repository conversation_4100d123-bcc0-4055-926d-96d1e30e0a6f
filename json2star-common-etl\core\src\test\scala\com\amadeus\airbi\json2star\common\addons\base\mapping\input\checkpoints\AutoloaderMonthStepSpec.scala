package com.amadeus.airbi.json2star.common.addons.base.mapping.input.checkpoints

import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers

import java.time.{LocalDate, YearMonth}

class AutoloaderMonthStepSpec extends AnyFlatSpec with Matchers {

  "AutoloaderStep.from" should "generate correct paths for given input dates" in {
    val staticStartStep = StaticStartStep(
      numberOfDaysPerBatch = 3,
      paths = Seq(StaticStartStepPath("/base/path", Seq(1, 2))),
      startDate = LocalDate.of(2024, 11, 1),
      endDate = LocalDate.of(2024, 11, 27)
    )

    // autoloader will need to catch up remaining days of 2024/11 (one step)
    // and then continue with full 2024/12 (another step)
    val lastAutoloaderDate = Some(LocalDate.of(2024, 11, 28)) // last date autoloader committed
    val currentDate = LocalDate.of(2024, 12, 7) // today

    val result = AutoloaderMonthStep.from(staticStartStep.endDate, staticStartStep.paths, lastAutoloaderDate, currentDate)
    result should contain theSameElementsAs Seq(
      AutoloaderMonthStep(Seq("/base/path/1/2024/11/{29,30}", "/base/path/2/2024/11/{29,30}"), YearMonth.of(2024, 11)),
      AutoloaderMonthStep(Seq("/base/path/1/2024/12/", "/base/path/2/2024/12/"), YearMonth.of(2024, 12))
    )
  }

  it should "generate correct paths in nominal case (there is a last autoloader date that completed the static start end month)" in {
    val staticStartStep = StaticStartStep(
      numberOfDaysPerBatch = 3,
      paths = Seq(StaticStartStepPath("/base/path", Seq(1))),
      startDate = LocalDate.of(2024, 11, 1),
      endDate = LocalDate.of(2024, 11, 27)
    )

    // autoloader will need to start since the moment the autoloader last finished
    val lastAutoloaderDate = Some(LocalDate.of(2024, 11, 30))
    val currentDate = LocalDate.of(2024, 12, 7)

    val result = AutoloaderMonthStep.from(staticStartStep, lastAutoloaderDate, currentDate)

    result should contain theSameElementsAs Seq(
      AutoloaderMonthStep(Seq("/base/path/1/2024/12/"), YearMonth.of(2024, 12))
    )
  }


  it should "generate correct paths when there is no last autoloader date" in {
    val staticStartStep = StaticStartStep(
      numberOfDaysPerBatch = 3,
      paths = Seq(StaticStartStepPath("/base/path", Seq(1))),
      startDate = LocalDate.of(2024, 11, 1),
      endDate = LocalDate.of(2024, 11, 27)
    )

    // autoloader will need to start since the moment the static start step ends
    val lastAutoloaderDate = None
    val currentDate = LocalDate.of(2024, 12, 7)

    val result = AutoloaderMonthStep.from(staticStartStep, lastAutoloaderDate, currentDate)

    result should contain theSameElementsAs Seq(
      AutoloaderMonthStep(Seq("/base/path/1/2024/11/{28,29,30}"), YearMonth.of(2024, 11)), // remaining part of the month completed
      AutoloaderMonthStep(Seq("/base/path/1/2024/12/"), YearMonth.of(2024, 12)) // another step for the full current month
    )
  }

  it should "generate correct paths when there is a last autoloader date (the month of static start)" in {
    val staticStartStep = StaticStartStep(
      numberOfDaysPerBatch = 3,
      paths = Seq(StaticStartStepPath("/base/path", Seq(1))),
      startDate = LocalDate.of(2024, 11, 1),
      endDate = LocalDate.of(2024, 11, 27)
    )

    // autoloader will need to start since the moment the autoloader last finished, but not the whole month
    val lastAutoloaderDate = Some(LocalDate.of(2024, 11, 28))
    val currentDate = LocalDate.of(2024, 11, 29)

    val result = AutoloaderMonthStep.from(staticStartStep, lastAutoloaderDate, currentDate)

    result should contain theSameElementsAs Seq(
      AutoloaderMonthStep(Seq("/base/path/1/2024/11/{29,30}"), YearMonth.of(2024, 11))
    )
  }

  it should "generate correct paths when there is a last autoloader date (the month after static start)" in {
    val staticStartStep = StaticStartStep(
      numberOfDaysPerBatch = 3,
      paths = Seq(StaticStartStepPath("/base/path", Seq(1))),
      startDate = LocalDate.of(2024, 11, 1),
      endDate = LocalDate.of(2024, 11, 29)
    )

    val lastAutoloaderDate = Some(LocalDate.of(2024, 12, 27))
    val currentDate = LocalDate.of(2024, 12, 29)

    val result = AutoloaderMonthStep.from(staticStartStep, lastAutoloaderDate, currentDate)

    result should contain theSameElementsAs Seq(
      AutoloaderMonthStep(Seq("/base/path/1/2024/12/"), YearMonth.of(2024, 12))
    )
  }

  it should "generate correct paths when there is a last autoloader date (the month after +2 static start)" in {
    val staticStartStep = StaticStartStep(
      numberOfDaysPerBatch = 3,
      paths = Seq(StaticStartStepPath("/base/path", Seq(1))),
      startDate = LocalDate.of(2024, 11, 1),
      endDate = LocalDate.of(2024, 11, 29)
    )

    val lastAutoloaderDate = Some(LocalDate.of(2024, 12, 27))
    val currentDate = LocalDate.of(2025, 1, 10)

    val result = AutoloaderMonthStep.from(staticStartStep, lastAutoloaderDate, currentDate)

    result should contain theSameElementsAs Seq(
      AutoloaderMonthStep(Seq("/base/path/1/2024/12/"), YearMonth.of(2024, 12)),
      AutoloaderMonthStep(Seq("/base/path/1/2025/01/"), YearMonth.of(2025, 1))
    )
  }

  it should "generate correct paths when the current date is within the same month" in {
    val staticStartStep = StaticStartStep(
      numberOfDaysPerBatch = 3,
      paths = Seq(StaticStartStepPath("/base/path", Seq(1))),
      startDate = LocalDate.of(2024, 11, 1),
      endDate = LocalDate.of(2024, 11, 27)
    )
    val lastAutoloaderDate = Some(LocalDate.of(2024, 11, 27))
    val currentDate = LocalDate.of(2024, 11, 30)

    val result = AutoloaderMonthStep.from(staticStartStep, lastAutoloaderDate, currentDate)

    result should contain theSameElementsAs Seq(
      AutoloaderMonthStep(Seq("/base/path/1/2024/11/{28,29,30}"), YearMonth.of(2024, 11))
    )
  }

  it should "generate correct paths when there are multiple years" in {
    val staticStartStep = StaticStartStep(
      numberOfDaysPerBatch = 3,
      paths = Seq(StaticStartStepPath("/base/path", Seq(1))),
      startDate = LocalDate.of(2024, 11, 1),
      endDate = LocalDate.of(2024, 11, 27)
    )
    val lastAutoloaderDate = Some(LocalDate.of(2024, 11, 27))
    val currentDate = LocalDate.of(2025, 1, 7)

    val result = AutoloaderMonthStep.from(staticStartStep, lastAutoloaderDate, currentDate)

    result should contain theSameElementsAs Seq(
      AutoloaderMonthStep(Seq("/base/path/1/2024/11/{28,29,30}"), YearMonth.of(2024, 11)),
      AutoloaderMonthStep(Seq("/base/path/1/2024/12/"), YearMonth.of(2024, 12)),
      AutoloaderMonthStep(Seq("/base/path/1/2025/01/"), YearMonth.of(2025, 1))
    )
  }
}