package com.amadeus.airbi.json2star.common.views.executors

import com.amadeus.airbi.json2star.common.config.AppConfig
import com.databricks.dbutils_v1.DBUtilsHolder.dbutils

case class SnowflakeConfig(
  appConfig: AppConfig,
  command: String,
  options: Map[String, String],
  dryMode: Option[String],
  previousDbSchema: Option[String] = None,
  tablesToReprocess: Option[Seq[String]] = None
) {
  val dbSchema = s"${options("sfDatabase")}.${options("sfSchema")}"
}

object SnowflakeConfig {

  /** Generate a config for SnowflakeSchemaExecutor from Command-Line Arguments
    * For app deployment:
    * Usage: APP --app-config-file application.init.conf --command DROP --dry-mode DEBUG
    *
    * @param args the args array
    * @return a config
    */
  def apply(args: Array[String]): SnowflakeConfig = {
    val argsConf = SnowflakeSchemaExecutorScallopConf(args)
    val appConfigFile = argsConf.appConfigFile()
    val appConfig = AppConfig(appConfigFile)
    val sfOptions = appConfig.assumeSnowflakeParams(jobId = None, runId = None) getSnowflakeOptions (dbutils)


    val baseConfig = SnowflakeConfig(
      appConfig = appConfig,
      command = argsConf.command(),
      options = sfOptions,
      dryMode = argsConf.dryMode.toOption
    )
    argsConf.command() match {
      case "CREATE" | "DROP" =>
        baseConfig
      case "COPY" =>
        val prParams = appConfig.assumePartialReprocessingParams()
        baseConfig.copy(
          // Computes the previous schema name from the current one by replacing the current version with the previous version in it
          previousDbSchema = Some(baseConfig.dbSchema.replace(appConfig.common.domainVersion, prParams.previousVersion)),
          tablesToReprocess = Some(prParams.tablesToReprocess)
        )
    }
  }

}