package com.amadeus.airbi.json2star.common.addons.base.correlation

import _root_.io.delta.tables.DeltaTable
import com.amadeus.airbi.json2star.common.TablesDef
import com.amadeus.airbi.json2star.common.addons.base.AddonTable
import com.amadeus.airbi.json2star.common.app.Json2StarApp
import com.amadeus.airbi.json2star.common.app.Json2StarApp.readMappingConfig
import com.amadeus.airbi.rawvault.common.application.config.ModelConfigLoader
import com.amadeus.airbi.rawvault.common.correlation.Correlation2Ways
import com.amadeus.airbi.rawvault.common.testfwk.Json2StarSpec
import com.amadeus.airbi.rawvault.common.testfwk.TableNames._
import org.apache.spark.sql.DataFrame
import org.apache.spark.sql.types.StructType

import java.io.File
import scala.concurrent.Await
import scala.concurrent.duration.Duration
import scala.reflect.io.Directory

class IntegrationNoVaultSpecTwoWaysCorrelation extends Json2StarSpec {

  val mappingFilePnr = "config/samples/pnr.conf"
  val mappingFileTkt = "config/samples/tkt.conf"
  val mappingFileCorr = "config/samples/correlation_pnr_tkt.conf"

  // correlation is not supported in light tests
  override def isLight: Boolean = false

  override def beforeAll: Unit = {
    super.beforeAll
    createTables(mappingFilePnr)
    createTables(mappingFileTkt)
    createTables(mappingFileCorr)
    //createTables("correlation_tkt_pnr_with_attr.conf")
  }

  override def beforeEach: Unit = {
    cleanTables(mappingFilePnr)
    cleanTables(mappingFileTkt)
    cleanTables(mappingFileCorr)
  }

  it should "compute correlations PNR->TKT" taggedAs (SlowTestTag) in {
    import spark.implicits._
    val pathExpectedResults = "src/test/resources/datasets/12/expected_result/"

    val dataDirPnr = "datasets/12/data/pnr/"
    val rcPnr = rootConfig(dataDirPnr, inputDatabase, isLight = isLight)
    val directoryPnr = new Directory(new File(rcPnr.etl.stream.sink.checkpointLocation))
    directoryPnr.deleteRecursively()
    val mappingPnr = readMappingConfig(mappingFilePnr)
    Json2StarApp.run(spark, rcPnr, mappingPnr)
    // Tables generated: PNR_TKT_PARTIAL_CORR_PIT_NAME FACT_RESERVATION_HISTO_NAME FACT_AIR_SEGMENT_PAX_HISTO_NAME

    val dataDirTkt = "datasets/12/data/tkt/"
    val rcTkt = rootConfig(dataDirTkt, inputDatabase, isLight = isLight)
    val directoryTkt = new Directory(new File(rcTkt.etl.stream.sink.checkpointLocation))
    directoryTkt.deleteRecursively()
    val mappingTkt = readMappingConfig(mappingFileTkt)
    Json2StarApp.run(spark, rcTkt, mappingTkt)
    // Tables generated: FACT_TRAVEL_DOCUMENT_HISTO_NAME TKT_PNR_PARTIAL_CORR_PIT_NAME

    val c = TablesDef.consolidate(ModelConfigLoader.fromResources(mappingFileCorr))
    val asso = AddonTable.fromTablesDef(Correlation)(c).head.addonConfig

    val (pnrToTktDf, tktToPnrDf) = Correlation2Ways.applyStaticStaticForTests(spark, asso)

    //  tktToPnrDf.show(50, false)

    // pnrToTktDf.show(100, false) // TODO: perform the merge
    val ddlExpecteResult = s"""
       |      AIR_SEGMENT_PAX_ID  String,
       |      COUPON_ID  String,
       |      VERSION_PNR Long,
       |      VERSION_TRAVEL_DOCUMENT Long,
       |      DATE_BEGIN Timestamp,
       |      DATE_END Timestamp,
       |      IS_LAST_VERSION Boolean,
       |      REFERENCE_KEY_TRAVEL_DOC String,
       |      REFERENCE_KEY_PNR String,
       |      REFERENCE_KEY_AIR_SEGMENT_PAX_ID String,
       |      REFERENCE_KEY_COUPON_ID String
       |""".stripMargin

    val expectedDf = spark.read
      .schema(StructType.fromDDL(ddlExpecteResult))
      .option("header", "true")
      .csv(pathExpectedResults + "asso_air_segment_pax_coupon_histo.csv")
      .sort()

    pnrToTktDf.count() shouldBe expectedDf.count()
    // pnrToTktDf.show(false)

    assertSmallDataFrameEquality(
      pnrToTktDf,
      expectedDf,
      orderedComparison = false,
      ignoreNullable = true
    )

    assertSmallDataFrameEquality(
      tktToPnrDf.select(
        $"AIR_SEGMENT_PAX_ID",
        $"COUPON_ID",
        $"VERSION_PNR",
        $"VERSION_TRAVEL_DOCUMENT",
        $"DATE_BEGIN",
        $"DATE_END",
        $"IS_LAST_VERSION",
        $"REFERENCE_KEY_TRAVEL_DOC",
        $"REFERENCE_KEY_PNR",
        $"REFERENCE_KEY_AIR_SEGMENT_PAX_ID",
        $"REFERENCE_KEY_COUPON_ID"
      ), // the select here is just because the comparator does not match on column names but on columns in the same order as the file,
      // as you notice, there is no column renaming or type casting
      expectedDf,
      orderedComparison = false,
      ignoreNullable = true
    )
  }

  it should "compute correlations two ways (only reported on TKT->PNR)" taggedAs (SlowTestTag) in {
    import spark.implicits._
    val pathExpectedResults = "src/test/resources/datasets/16/expected_result/"

    val dataDirPnr = "datasets/16/data/pnr/"
    val rcPnr = rootConfig(dataDirPnr, inputDatabase, isLight = isLight)
    val directoryPnr = new Directory(new File(rcPnr.etl.stream.sink.checkpointLocation))
    directoryPnr.deleteRecursively()
    val mappingPnr = readMappingConfig(mappingFilePnr)
    Json2StarApp.run(spark, rcPnr, mappingPnr)
    // Tables generated: PNR_TKT_PARTIAL_CORR_PIT_NAME FACT_RESERVATION_HISTO_NAME FACT_AIR_SEGMENT_PAX_HISTO_NAME

    debugTable(FACT_RESERVATION_HISTO_NAME)

    val dataDirTkt = "datasets/16/data/tkt/"
    val rcTkt = rootConfig(dataDirTkt, inputDatabase, isLight = isLight)
    val directoryTkt = new Directory(new File(rcTkt.etl.stream.sink.checkpointLocation))
    directoryTkt.deleteRecursively()
    val mappingTkt = readMappingConfig(mappingFileTkt)
    Json2StarApp.run(spark, rcTkt, mappingTkt)
    // Tables generated: FACT_TRAVEL_DOCUMENT_HISTO_NAME TKT_PNR_PARTIAL_CORR_PIT_NAME

    debugTable(TKT_PNR_PARTIAL_CORR_PIT_NAME)

    val c = TablesDef.consolidate(ModelConfigLoader.fromResources(mappingFileCorr))
    val asso = AddonTable.fromTablesDef(Correlation)(c).head.addonConfig

    val ddlExpecteResult = s"""
                              |      AIR_SEGMENT_PAX_ID  String,
                              |      COUPON_ID  String,
                              |      VERSION_PNR Long,
                              |      VERSION_TRAVEL_DOCUMENT Long,
                              |      DATE_BEGIN Timestamp,
                              |      DATE_END Timestamp,
                              |      IS_LAST_VERSION Boolean,
                              |      REFERENCE_KEY_TRAVEL_DOC String,
                              |      REFERENCE_KEY_PNR String,
                              |      REFERENCE_KEY_AIR_SEGMENT_PAX_ID String,
                              |      REFERENCE_KEY_COUPON_ID String
                              |""".stripMargin

    def getExpectedDf(ddl: String, fileName: String): DataFrame = {
      spark.read
        .schema(StructType.fromDDL(ddl))
        .option("header", "true")
        .csv(pathExpectedResults + s"${fileName.toLowerCase}.csv")
        .sort()
    }

    // use the same DB for all feeds in test
    val dbs = Map("PNR" -> inputDatabase, "TKT" -> inputDatabase)
    val rcCorr = rootConfig(dataDirPnr, inputDatabase, isLight = isLight).copy(inputDatabases = dbs)
    import scala.concurrent.ExecutionContext.Implicits.global
    val f = new CorrelationPipeline(spark, rcCorr, c.tables.head.table, asso).runStream()
    Await.result(f, Duration.Inf)

    val INTERNAL_CORR_IDS = "INTERNAL_CORR_IDS_ASSO_AIR_SEGMENT_PAX_COUPON_HISTO"
    val expectedIdsDf = getExpectedDf(
      s"""
         |  DOMAIN_A_ID  String,
         |  DOMAIN_B_ID  String
         |""".stripMargin,
      INTERNAL_CORR_IDS
    )

    val numFiles = DeltaTable.forName(INTERNAL_CORR_IDS).detail().select("numFiles").first().getLong(0)
    // 3 files are generated as each update from input tables is append in INTERNAL_CORR_IDS in append mode:
    // - 1 file for A.PIT       FACT_RESERVATION_HISTO  (4 rows) with the same PNR_ID:  N389UA-2022-05-13
    // - 0 file for A.PARTIAL   PNR_TKT_PARTIAL_CORR_PIT (empty)
    // - 1 file for B.PIT       FACT_TRAVEL_DOCUMENT_HISTO  (4 rows) with the same TKT_ID: 7492400944169-2022-05-13
    // - 1 file for B.PARTIAL   TKT_PNR_PARTIAL_CORR_PIT (5 rows) with the same TKT_ID: 7492400944169-2022-05-13
    numFiles shouldBe 3

    assertSmallDataFrameEquality(
      DeltaTable
        .forName(INTERNAL_CORR_IDS)
        .toDF
        .select(
          $"DOMAIN_A_ID",
          $"DOMAIN_B_ID"
        ),
      // the select here is just because the comparator does not match on column names but on columns in the same order as the file,
      // as you notice, there is no column renaming or type casting
      expectedIdsDf,
      orderedComparison = false,
      ignoreNullable = true
    )

    debugTable(ASSO_AIR_SEGMENT_PAX_COUPON_HISTO_NAME)

    val expectedDf = getExpectedDf(ddlExpecteResult, ASSO_AIR_SEGMENT_PAX_COUPON_HISTO_NAME)
    assertSmallDataFrameEquality(
      DeltaTable
        .forName(ASSO_AIR_SEGMENT_PAX_COUPON_HISTO_NAME)
        .toDF
        .select(
          $"AIR_SEGMENT_PAX_ID",
          $"COUPON_ID",
          $"VERSION_PNR",
          $"VERSION_TRAVEL_DOCUMENT",
          $"DATE_BEGIN",
          $"DATE_END",
          $"IS_LAST_VERSION",
          $"REFERENCE_KEY_TRAVEL_DOC",
          $"REFERENCE_KEY_PNR",
          $"REFERENCE_KEY_AIR_SEGMENT_PAX_ID",
          $"REFERENCE_KEY_COUPON_ID"
        ),
      // the select here is just because the comparator does not match on column names but on columns in the same order as the file,
      // as you notice, there is no column renaming or type casting
      expectedDf,
      orderedComparison = false,
      ignoreNullable = true
    )
  }
}
