package com.amadeus.airbi.json2star.common.addons.stackable.currency

import com.amadeus.airbi.json2star.common.app.Json2StarApp
import com.amadeus.airbi.json2star.common.app.Json2StarApp.readMappingConfig
import com.amadeus.airbi.json2star.common.testfwk.CurrencyTestSpec
import com.amadeus.airbi.rawvault.common.testfwk.Json2StarSpec

import java.io.File
import scala.reflect.io.Directory

class IntegrationCurrencyConversionSpec extends Json2StarSpec with CurrencyTestSpec {

  val mappingFile = "datasets/currency_conversion/simplified_dcsbag.conf"

  val TableNames: Seq[String] = getTableNames(mappingFile)

  override def beforeAll: Unit = {
    super.beforeAll
    createTables(mappingFile)
    createXrtData(
      spark,
      inputDatabase,
      "/datasets/currency_conversion/data/input/exchange_rates/xrt.json",
      "refDataDataBase",
      "exchange_rates"
    )
  }

  override def beforeEach: Unit = {
    // cleanTables(TableNames) no need, only one test
  }

  "IntegrationCurrencyConversionSpec" should "convert Currency in FACT_EXCESS_BAG_HISTO" taggedAs (SlowTestTag) in {
    val dataDir = "datasets/currency_conversion/data/"
    val pathExpectedResults = "src/test/resources/" + dataDir + "/expected_results/"
    val rc = rootConfigWithHomeCurrencyParams(inputDatabase, "USD", isLight = DefaultIsLight)

    val directory = new Directory(new File(rc.etl.stream.sink.checkpointLocation))
    directory.deleteRecursively()
    val mapping = readMappingConfig(mappingFile)
    Json2StarApp.run(spark, rc, mapping)
    checkTablesContent(TableNames, pathExpectedResults, model = Some(mapping))
    directory.deleteRecursively()
  }

}
