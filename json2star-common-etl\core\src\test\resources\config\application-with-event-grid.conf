model-conf-file = /some/domain.conf

stream {
  sink {
    checkpoint-location = "target/test/resources/checkpoints/"
    trigger = "once"
    delta-options = {}
  }
}

spark-conf {
  "spark.app.name" = Foo bar
  "spark.driver.memory" = 2g
}

common {
  domain = "DOMAIN"
  output-database = "DB_DOMAIN_1_0_1"
  domain-version = "1_0_1"
  output-path = "abfss://container-name@storage-account/tmp/json2star/output/path"
  shard = "6X"
}

cloud-files-conf {
  use-notifications = true
  backfill-interval = 1 day
  include-existing-files = false
}

event-grid-params {
  topic-url = "https://evgn-we-tst-anm-AAAAAA.westeurope-1.eventgrid.azure.net/topics/evgnt-we-tst-anm-AAAAAA:publish?api-version=2023-11-01"
  topic-name = "evgnt-we-tst-anm-AAAAAA"
  service-provider-tenant-id = "tenant-1234-1234-1234-************"
  service-provider-app-id = "app-0000-0000-0000-************"
  dbx-secret-scope = "my-dbx-scope"
  dbx-secret-key = "my-secret-key"
  managed-app-resource-id = "/subscriptions/subscription-id/resourceGroups/resource-group-name/providers/Microsoft.Solutions/applications/managed-app-name"
  is-blocking = true
  http-log-detail-level = "BODY"
}
