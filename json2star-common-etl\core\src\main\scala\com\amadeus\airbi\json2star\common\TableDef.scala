package com.amadeus.airbi.json2star.common

import com.amadeus.airbi.json2star.common.TableDef.{AllAddons, areAddonsCompatible}
import com.amadeus.airbi.json2star.common.addons.base.correlation.{Correlation, SourceCorrelation}
import com.amadeus.airbi.json2star.common.addons.base.latest.Latest
import com.amadeus.airbi.json2star.common.addons.base.mapping.Mapping
import com.amadeus.airbi.json2star.common.addons.base.{Addon, AddonConfig, AddonTable}
import com.amadeus.airbi.json2star.common.addons.stackable.StackableAddons
import com.amadeus.airbi.json2star.common.addons.stackable.StackableAddons.{areStackableAddonsCompatibleWith, getCompatibleStackableAddons}
import com.amadeus.airbi.rawvault.common.application.config.{TableConfig, TablesConfig}

/** Table definition
  *
  * Allows to generate the schema (output) given the parameters
  *
  * @param table specific configuration for this table
  * @param general general configuration (including all tables)
  */
class TableDef(
  val table: TableConfig,
  val general: TablesConfig
) {
  def hasAddon[K <: AddonConfig](a: Addon[K]): Option[AddonTable[K]] =
    a.getConfig(table)
      .map(c => new AddonTable[K](this, c, getCompatibleStackableAddons(this, a)))
  def assumeAddon[K <: AddonConfig](a: Addon[K]): AddonTable[K] =
    hasAddon[K](a)
      .getOrElse(
        throw new IllegalArgumentException(s"Assumed ${table.name} had addon ${a.getClass.getName}: not the case")
      )
  def getOtherTableByName(n: String): TableDef = new TableDef(general = general, table = general.tableByName(n))

  private def schemaFrom(addons: List[Addon[_ <: AddonConfig]]): Schema = {
    val emptySchema = Schema(
      table.name,
      List.empty[ColumnDef],
      description = None,
      kind = Schema.Materialized,
      subdomain = table.subdomain,
      subdomainMainTable = table.subdomainMainTable.getOrElse(false))
    addons.foldRight(emptySchema) { case (addon, currSchema) =>
      val addonConf = addon.getConfig(table)
      addonConf.map(c => addon.enrichSchema(general, table, c, currSchema)).getOrElse(currSchema)
    }
  }

  /**
    * The consolidated schema for the current table
    */
  val schema: Schema = {
    if (!areAddonsCompatible(table)) {
      throw new IllegalArgumentException(s"The chosen addons are not compatible for table '${table.name}'")
    }
    val baseSchema = schemaFrom(AllAddons)
    StackableAddons.enrichSchemaMetadata(general, table, baseSchema, table.stackableAddons)
  }
}

object TableDef {
  val AllAddons: List[Addon[_ <: AddonConfig]] = List(Mapping, Latest, Correlation, SourceCorrelation)
  private def areAddonsCompatible(table: TableConfig): Boolean = {
    val mapping = Mapping.getConfig(table)
    val latest = Latest.getConfig(table)
    val correlation = Correlation.getConfig(table)
    val sourceCorrelation = SourceCorrelation.getConfig(table)
    val stackableAddons = table.stackableAddons
    (mapping, latest, correlation, sourceCorrelation) match { // we don't allow stacking of base Addons
      case (Some(_), None, None, None) => areStackableAddonsCompatibleWith(stackableAddons, Mapping)
      case (None, Some(_), None, None) => areStackableAddonsCompatibleWith(stackableAddons, Latest)
      case (None, None, Some(_), None) => areStackableAddonsCompatibleWith(stackableAddons, Correlation)
      case (None, None, None, Some(_)) => areStackableAddonsCompatibleWith(stackableAddons, SourceCorrelation)
      case _ => false
    }
  }

}
