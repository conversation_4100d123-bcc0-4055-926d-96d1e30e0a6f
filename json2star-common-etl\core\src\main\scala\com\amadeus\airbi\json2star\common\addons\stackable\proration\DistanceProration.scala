package com.amadeus.airbi.json2star.common.addons.stackable.proration

import com.amadeus.airbi.json2star.common.addons.stackable.proration.ProrationInput.CouponKey
import com.amadeus.airbi.json2star.common.extdata.PorsExtData
import com.amadeus.ti.models.cdb.NumericAmount
import com.amadeus.ti.models.currency.Currency
import com.amadeus.ti.models.por.PorsRef

import java.time.LocalDate
import scala.util.{Failure, Success, Try}

object DistanceProration {

  val StepPrefix = "DISTANCE"

  /** Compute the prorated coupons of a ticket using the distance algorithm
    * It prorates using the remaining amount considering only the not prorated coupons
    * It returns the map of coupon key and ProratedCoupon with the prorated amount or a status error
    *
    * @param porsRef pors geographical reference data
    * @param issueDate ticket issue date
    * @param inputFclCoupons the map of prorated amounts by coupon key
    * @param inputNoFclCoupons the map of not prorated amounts by coupon key
    * @param inputAmountCurrency payment currency
    * @param inputPaymentTotal original payment total
    * @param inputPaymentTotalTaxes original payment total taxes
    * @return a map of prorated coupons
    */
  def computeProratedCoupons(
    porsRef: PorsRef,
    issueDate: LocalDate,
    inputFclCoupons: Map[CouponKey, FclProratedCouponSuccess],
    inputNoFclCoupons: Map[CouponKey, FclProratedCouponFailure],
    inputAmountCurrency: String,
    inputPaymentTotal: String,
    inputPaymentTotalTaxes: String
  ): Map[CouponKey, ProratedCoupon] = {
    // Step 1 - Compute Payment Amount
    val maybeAmountTotal = computePaymentAmount(inputPaymentTotal, inputPaymentTotalTaxes, inputAmountCurrency)
    // Step 1.1 Check validity of Payment Amount
    val distCpns = maybeAmountTotal match {
      case Left(err) =>
        // when invalid amount --> report error status
        computeNoDstProratedCoupons(inputFclCoupons, inputNoFclCoupons, err)
      case Right(amountTotal) =>
        // Step 2 - Compute remaining amount
        // Validate the fcl coupons to verify that they have all the same currency as the payment currency
        val (noFclCoupons, fclCoupons) = validateCoupons(amountTotal, inputFclCoupons, inputNoFclCoupons)
        val maybeRemainingAmount = computeRemainingAmount(amountTotal, fclCoupons) // use fclCoupons
        // Step 2.1 Check validity of the remaining Payment Amount
        maybeRemainingAmount match {
          case Left(err) =>
            // when invalid remaining amount --> report error status
            computeNoDstProratedCoupons(fclCoupons, noFclCoupons, err)
          case Right(remainingAmount) =>
            // Step 3 - Compute distances for the remaining not prorated coupons
            val maybeDistances = computeRemainingDistances(porsRef, issueDate, noFclCoupons) // use noFclCoupons
            // Step 3.1 Check validity of distances
            maybeDistances match {
              case Left(err) =>
                // when invalid distances --> report error status
                computeNoDstProratedCoupons(fclCoupons, noFclCoupons, err)
              case Right(remainingDistances) =>
                val resultsByDistance = computeDstProratedCoupons(
                  noFclCoupons,
                  remainingDistances,
                  remainingAmount
                )
                fclCoupons ++ resultsByDistance
            }
        }
    }
    distCpns
  }

  private def computeNoDstProratedCoupons(
    fclProratedCoupons: Map[CouponKey, FclProratedCouponSuccess],
    noFclProratedCoupons: Map[CouponKey, FclProratedCouponFailure],
    err: StepStatus
  ): Map[CouponKey, ProratedCoupon] = {
    fclProratedCoupons ++ noFclProratedCoupons.mapValues(n => DistProratedCouponFailure(err, n))
  }

  private def computeRemainingDistances(
    porsRef: PorsRef,
    issueDate: LocalDate,
    noFclProratedCoupons: Map[CouponKey, FclProratedCouponFailure]
  ): Either[StepStatus, Map[CouponKey, Double]] = {
    val maybeDistances = noFclProratedCoupons
      .map { case (cpn, _) =>
        val distance = PorsExtData.getDistance(porsRef, cpn.departureAirport, cpn.arrivalAirport, issueDate)
        (cpn, distance)
      }
    val (missingDistances, distances) = maybeDistances.partition(_._2.isLeft)
    if (missingDistances.nonEmpty) {
      val err = missingDistances
        .collect { case (k, Left(err)) => s"$k has no distance because of $err" }
        .mkString(",")
      Left(StepStatus(s"${StepPrefix}-pors-missing", s"ERROR: $err"))
    } else {
      val validDistances = distances.collect { case (k, Right(v)) => (k, v) }.toMap
      Right(validDistances)
    }
  }

  /** Validate the coupons prorated by fare calc to have all the same currency as the payment currency
    * Note: the coupons prorated by fare calc are of 2 types
    *  - fclCoupons: prorated coupons by fare calc
    *  - noFclCoupons: not prorated coupons by fare calc
    *
    * When fclCoupons have all valid currency, fclCoupons and noFclCoupons are returned as is
    * Otherwise fclCoupons is returned as empty and added to the current noFclCoupons with a specific status error
    *
    * @param total the total amount to be prorated
    * @param fclCoupons the fcl prorated coupons
    * @param noFclCoupons the no fcl prorated coupons
    * @return validated fclCoupons and validated noFclCoupons
    */
  private def validateCoupons(
    total: NumericAmount,
    fclCoupons: Map[CouponKey, FclProratedCouponSuccess],
    noFclCoupons: Map[CouponKey, FclProratedCouponFailure]
  ): (Map[CouponKey, FclProratedCouponFailure], Map[CouponKey, FclProratedCouponSuccess]) = {
    // Check if currencies of fcl prorated amounts are all the same
    val fclCurrencies = fclCoupons.values.map { _.na.currency.code }.toList.distinct
    fclCurrencies match {
      // only one currency in the FCL coupons and it is equal to payment currency
      case fclCurrency :: Nil if fclCurrency == total.currency.code =>
        (noFclCoupons, fclCoupons)
      // only one currency in the FCL coupons and it is similar to payment currency
      case fclCurrency :: Nil if isSimilarCurrency(fclCurrency, total.currency.code) =>
        (noFclCoupons, fclCoupons.mapValues(coupon => coupon.copy(na = coupon.na.withCurrency(total.currency))))
      case _ :: Nil =>
        // FCL currency is different than the payment currency
        // -> remaining amount can't be computed without a currency conversion
        // --> invalidate fcl coupons to fall back to distance proration
        val invalidFclCoupons = fclCoupons.mapValues { cpn =>
          FclProratedCouponFailure(
            StepStatus(
              s"${StepPrefix}-currency-different",
              Seq(
                "ERROR: check fcl and payment currency",
                s"payment_amount = ${total.toDecimal.mkString}",
                s"payment_currency = ${total.currency.code}",
                s"fcl_amount = ${cpn.na.toDecimal.mkString}",
                s"fcl_currency = ${cpn.na.currency.code}"
              ).mkString("|")
            )
          )
        }
        (invalidFclCoupons ++ noFclCoupons, Map.empty)
      case fcs =>
        // more than one currency in the FCL coupons
        // -> total of prorated amounts can't be summed without a currency conversion
        // --> invalidate fcl coupons to fall back to distance proration
        val invalidFclCoupons = fclCoupons.mapValues { cpn =>
          FclProratedCouponFailure(
            StepStatus(
              s"${StepPrefix}-currency-many",
              s"ERROR: check fcl ${fcs.mkString(",")}|fcl_amount = ${cpn.na.mkString}|fcl_currency = ${cpn.na.currency}"
            )
          )
        }
        (invalidFclCoupons ++ noFclCoupons, Map.empty)
    }
  }

  /** Check if fare calc line currency is similar to payment currency:
    * - when the fare calc currency is NUC and the ticket payment currency is USD
    * - when the fare calc currency is USD and the ticket payment currency is NUC
    * with
    * USD: dollar currency by default
    * NUC: neutral unit of currency
    *
    * @param fclCurrency     fare calc currency
    * @param paymentCurrency ticket payment currency
    * @return true if these values have the same currency
    */
  def isSimilarCurrency(fclCurrency: String, paymentCurrency: String): Boolean = {
    (fclCurrency == "NUC" && paymentCurrency == "USD") || (fclCurrency == "USD" && paymentCurrency == "NUC")
  }

  private def computeRemainingAmount(
    total: NumericAmount,
    fclCoupons: Map[CouponKey, FclProratedCouponSuccess]
  ): Either[StepStatus, NumericAmount] = {
    if (fclCoupons.isEmpty) {
      Right(total)
    } else {
      // Sum all fcl prorated amounts (if any) and compute the remaining amount as difference
      val proratedAmountTotal = fclCoupons.values.map { c => c.na }.reduce(_ + _)
      val remainingAmount = total - proratedAmountTotal
      if (remainingAmount.value < 0) {
        Left(
          StepStatus(s"${StepPrefix}-remaining-amount-negative", s"ERROR: check $remainingAmount")
        )
      } else {
        Right(remainingAmount)
      }
    }
  }

  private[proration] def computePaymentAmount(
    paymentTotal: String,
    paymentTotalTaxes: String,
    currencyCode: String
  ): Either[StepStatus, NumericAmount] = {
    val currency = Currency.get(currencyCode)
    val paymentTotalNa = NumericAmount.fromString(paymentTotal, currency)
    val paymentTotalTaxesNa = NumericAmount.fromString(paymentTotalTaxes, currency)

    (paymentTotalNa, paymentTotalTaxesNa) match {
      case (Some(ptd), Some(ptt)) =>
        val paymentAmount = ptd - ptt
        if (paymentAmount.value < 0 || paymentAmount.value == 0) {
          Left(StepStatus(s"${StepPrefix}-payment-amount-invalid", s"ERROR: check ${paymentAmount.toDecimal.mkString}"))
        } else {
          Right(paymentAmount)
        }
      case (ptd, ptt) =>
        Left(
          StepStatus(
            s"${StepPrefix}-payment-amount-not-parsed",
            s"ERROR: check paymentTotal = $paymentTotal, paymentTotalTaxes = $paymentTotalTaxes, currencyCode = $currencyCode"
          )
        )
    }
  }

  private def computeDstProratedCoupons(
    noFclProratedCoupons: Map[CouponKey, FclProratedCouponFailure],
    remainingDistances: Map[CouponKey, Double],
    remainingAmount: NumericAmount
  ): Map[CouponKey, ProratedCoupon] = {
    val remainingDistanceTot = remainingDistances.values.sum
    val out = noFclProratedCoupons.map { case (cpn, nfc) =>
      val values = Try {
        val cpnDistance = remainingDistances(cpn)
        val ratio = cpnDistance / remainingDistanceTot
        (ratio, cpnDistance, remainingAmount * ratio)
      }
      val proratedCpn: ProratedCoupon = values match {
        case Failure(err) =>
          DistProratedCouponFailure(
            StepStatus(s"${StepPrefix}-proration-error", s"ERROR: check ${err.getMessage}}"),
            nfc
          )
        case Success((ratio, cpnDistance, proratedAmount)) =>
          val msg = Seq(
            "SUCCESS: check details",
            f"ratio = ${ratio}%.2f",
            f"remainingAmount = ${remainingAmount.toDecimal.mkString}",
            f"cpnDistance = ${cpnDistance}%.2f",
            f"remainingDistanceTot = ${remainingDistanceTot}%.2f"
          ).mkString("|")
          DistProratedCouponSuccess(
            proratedAmount,
            StepStatus(s"${StepPrefix}-proration-success", msg),
            nfc
          )
      }
      cpn -> proratedCpn
    }
    out
  }
}
