package com.amadeus.airbi.json2star.common.addons.stackable
import com.amadeus.airbi.json2star.common.addons.base.{<PERSON>don, AddonConfig}
import com.amadeus.airbi.json2star.common.addons.stackable.currency.CurrencyConversionAddon
import com.amadeus.airbi.json2star.common.addons.stackable.proration.ProrationAddon
import com.amadeus.airbi.json2star.common.addons.stackable.dummy.DummyAddon
import com.amadeus.airbi.json2star.common.addons.stackable.weight.WeightConversionAddon
import com.amadeus.airbi.json2star.common.{Schema, TableDef}
import com.amadeus.airbi.rawvault.common.application.config.{TableConfig, TablesConfig}

import scala.util.{Failure, Try}

/** Object providing the stackable addons common framework logic
  */
object StackableAddons {

  def integrateMetadataInSchema(enrichedMetadata: EnrichedSchemaMetadata, s: Schema): Schema = {
    s.copy(
      description = enrichedMetadata.description.orElse(s.description),
      columns = s.columns.map { c =>
        enrichedMetadata.columnsMetadata
          .get(c.name)
          .map(m =>
            c.copy(
              origins = m.origins.getOrElse(c.origins),
              meta = m.meta.orElse(c.meta)
            )
          )
          .getOrElse(c)
      }
    )
  }

  /** Enrich the Schema metadata with all the stackable addons defined on the table
    *
    * This method is supposed to be called after addon consolidations (i.e., all the stackable addons are validated)
    *
    * @param tablesConfig all tables config
    * @param tableConfig table config
    * @param baseSchema schema as produced by the base addon
    * @param stackableAddons list of stackable addon configs
    * @return the enriched schema
    */
  def enrichSchemaMetadata(
    tablesConfig: TablesConfig,
    tableConfig: TableConfig,
    baseSchema: Schema,
    stackableAddons: List[StackableAddonConfig]
  ): Schema = {
    stackableAddons.foldLeft(baseSchema) { (s, c) =>
      val enrichedMetadata = stackableAddonLookup(c).enrichSchemaMetadata(tablesConfig, tableConfig, c, s)
      integrateMetadataInSchema(enrichedMetadata, s)
    }
  }

  /** Validate the stackable addons config defined for a given table
    *
    * If for a given table more stackable addons fail, only one failure is reported at the end
    * with all the failure messages
    *
    * @param t table config
    * @throws an exception in case of validation failure
    */
  def validate(tablesConfig: TablesConfig, t: TableConfig): Unit = {
    val validations = t.stackableAddons.map { c =>
      Try(stackableAddonLookup(c).validate(tablesConfig, t, c))
    }
    val failureMessages = validations
      .collect { case Failure(exception) => exception.getMessage }
      .map(msg => s"- $msg")
    if (failureMessages.nonEmpty) {
      throw new StackableAddonValidationException(s"Validations in error:\n${failureMessages.mkString("\n")}")
    }
  }

  /** Given a table definition, returns a  list of stackable addon configs compatible with a given base addon
    *
    * @param tableDef table definition
    * @param addon base addon
    * @tparam T base addon config type
    * @return a list of stackable addons compatible with the given base addon
    */
  def getCompatibleStackableAddons[T <: AddonConfig](
    tableDef: TableDef,
    addon: Addon[T]
  ): List[StackableAddonConfig] = getCompatibleStackableAddons(tableDef.table.stackableAddons, addon)

  /** Given a list of stackable addons defined for a table, keeps only the list of addons compatible with a given base addon
    *
    * @param stackableAddons list of stackable addon configs
    * @param addon base addon
    * @tparam T base addon config type
    * @return a list of stackable addons compatible with the given base addon
    */
  def getCompatibleStackableAddons[T <: AddonConfig](
    stackableAddons: List[StackableAddonConfig],
    addon: Addon[T]
  ): List[StackableAddonConfig] = stackableAddons.filter(c => stackableAddonLookup(c).isCompatibleWith(addon))

  /** Check whether all the passed stackable addons are compatible with a given base addon
    *
    * @param stackableAddons list of stackable addon configs
    * @param addon base addon
    * @tparam T type of the base addon config
    * @return true if all the stackable addons are compatible with the passed base addon, false otherwise
    */
  def areStackableAddonsCompatibleWith[T <: AddonConfig](
    stackableAddons: List[StackableAddonConfig],
    addon: Addon[T]
  ): Boolean = stackableAddons.forall(stackableAddonLookup(_).isCompatibleWith(addon))

  /** Determine the stackable addon name (kebab case) from the config class name (PascalCase),
    * consistently with pureconfig
    * @param s config class name
    * @return stackable addon name
    */
  def stackableAddonName(s: StackableAddonConfig): String = {
    def pascalOrCamelToKebab(input: String): String = {
      val kebab = """[A-Z\d]""".r.replaceAllIn(
        input,
        { m =>
          "-" + m.group(0).toLowerCase()
        }
      )
      if (kebab.startsWith("-")) kebab.drop(1) else kebab
    }
    pascalOrCamelToKebab(s.getClass.getSimpleName)
  }

  /** Given the list of table configurations in input, produces a new list
    * where the disabled stackable addons are removed.
    *
    * @param tablesConfig table configurations
    * @return table configurations without disabled stackable addons
    */
  def filterStackableAddons(tablesConfig: TablesConfig, disabledStackableAddons: Set[String]): TablesConfig = {
    def filtered(
      stackableAddons: List[StackableAddonConfig],
      disabledStackableAddons: Set[String]
    ): List[StackableAddonConfig] = {
      stackableAddons.filterNot(s => disabledStackableAddons.contains(stackableAddonName(s)))
    }

    TablesConfig(
      tablesConfig.tables.map(t => t.copy(stackableAddons = filtered(t.stackableAddons, disabledStackableAddons))),
      tablesConfig.ruleset
    )
  }

  /** Given a stackable addon config, returns the corresponding stackable addon object
    *
    * @param c stackable addon config
    * @return the corresponding stackable addon object
    */
  def stackableAddonLookup(c: StackableAddonConfig): StackableAddon[_] = {
    c match {
      case Dummy(_, _, _) => DummyAddon
      case CurrencyConversion(_) => CurrencyConversionAddon
      case WeightConversion(_) => WeightConversionAddon
      case Proration(_, _, _, _) => ProrationAddon
    }
  }
}
