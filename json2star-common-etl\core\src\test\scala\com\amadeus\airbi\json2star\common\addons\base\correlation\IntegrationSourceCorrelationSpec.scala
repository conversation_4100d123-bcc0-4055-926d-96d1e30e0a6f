package com.amadeus.airbi.json2star.common.addons.base.correlation

import com.amadeus.airbi.json2star.common.app.Json2StarApp
import com.amadeus.airbi.json2star.common.app.Json2StarApp.readMappingConfig
import com.amadeus.airbi.rawvault.common.RootConfig
import com.amadeus.airbi.rawvault.common.application.config.TablesConfig
import com.amadeus.airbi.rawvault.common.testfwk.Json2StarSpec
import io.delta.tables.DeltaTable
import org.apache.spark.sql.DataFrame
import org.apache.spark.sql.functions.{col, countDistinct}
import org.apache.spark.sql.streaming.{OutputMode, Trigger}

import java.io.File
import scala.reflect.io.Directory

// TODO BDS-28462 with SnowflakeSpec ?
class IntegrationSourceCorrelationSpec extends Json2StarSpec {

  val mappingFileDcspax = "datasets/source_correlation/dcspax.conf"
  val mappingFileCorr = "datasets/source_correlation/correlation_dcspax_pnr.conf"

  // correlation is not supported in light tests
  override def isLight: Boolean = false

//  override val RewriteExpectedForFailingTests = true

  override def beforeAll: Unit = {
    super.beforeAll
    createTables(mappingFileDcspax)
    createTables(mappingFileCorr)
  }

  override def beforeEach: Unit = {
    cleanTables(mappingFileDcspax)
    cleanTables(mappingFileCorr)
  }

  def getCorrConfig: RootConfig = {
    // use the same DB for all feeds in test
    val dbs = Map("DCSPAX" -> inputDatabase, "PNR" -> inputDatabase)
    rootConfig("", inputDatabase, isLight = isLight, label = "_corr").copy(inputDatabases = dbs)
  }

  def runBatchFeed(batch: Int, feed: String, dataDir: String, confFile: String): Unit = {
    logger.info(s"batch: $batch, feed: $feed ")
    val mapping = readMappingConfig(confFile)
    val rc = rootConfig(dataDir, inputDatabase, label = s"_$feed", isLight = isLight)
    val checkpointLocation = rc.etl.stream.sink.checkpointLocation
    val directory = new Directory(new File(checkpointLocation))
    directory.deleteRecursively()
    Json2StarApp.run(spark, rc, mapping)
    // Note: for simple feeds (pnr, tkt), we are simulating streaming running batches on different folders
    // so the checkpoint location MUST BE cleaned at each batch
    directory.deleteRecursively()
  }

  private def runPaxFeed(batch: Int, testCase: String, expNumFiles: Int): Unit = {
    runBatchFeed(
      batch = batch,
      feed = "DCSPAX",
      dataDir = s"datasets/source_correlation/$testCase/batch$batch/data",
      confFile = mappingFileDcspax
    )

    checkTablesContent(
      tableNames = Seq("FACT_PASSENGER_HISTO", "INTERNAL_ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO"),
      expectedCsvFilesDir =
        s"src/test/resources/datasets/source_correlation/$testCase/batch$batch/data/expected_results/dcspax/",
      createExpected = false
    )

    // number of files in the table directory should be the same as the batch number
    getNumFiles("INTERNAL_ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO") should equal(expNumFiles)
  }

  private def getNumFiles(table: String) = {
    DeltaTable
      .forName(table)
      .detail()
      .select("numFiles")
      .first()
      .getLong(0)
  }

  it should "compute source correlations DCSPAX -> PNR - (default update)" taggedAs (SlowTestTag) in {

    // prepare correlation run
    val corrMapping = readMappingConfig(mappingFileCorr)
    val corrConfig = getCorrConfig
    val checkpointLocation = corrConfig.etl.stream.sink.checkpointLocation
    val corrCheckpointDir = new Directory(new File(checkpointLocation))
    // Note: as correlation consumes and writes in the same DB all along the test, the checkpoint location MUST NOT be
    // cleaned between different batches (it's cleaned only at the beginning and at the end).
    corrCheckpointDir.deleteRecursively()

    // 1st run : insert correlation data
    runPaxFeed(batch = 1, testCase = "default_update", expNumFiles = 1)
    // Tables generated: FACT_PASSENGER_HISTO INTERNAL_ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO

    // insert data ( 1 pax, 2 correlations record)
    runSourceCorrelation(batch = 1, testCase = "default_update", corrMapping, expNumFiles = 1)

    // 2nd run : insert or update correlation data
    runPaxFeed(batch = 2, testCase = "default_update", expNumFiles = 2)
    // Tables generated: FACT_PASSENGER_HISTO INTERNAL_ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO

    //  insert data ( 1 pax, 2 correlations record)
    runSourceCorrelation(batch = 2, testCase = "default_update", corrMapping, expNumFiles = 2)
  }

  it should "compute source correlations - multiple batch in upstream feed" taggedAs (SlowTestTag) in {
    // This scenario reproduces multiple batches in the upstream feed
    // - Init in DCSPAX     - process json with pax1 v1
    // - Init in CORR       - process star schema dcspax with pax1 v1
    // - Regular in DCSPAX  - process json with pax1 v2
    // - Regular in DCSPAX  - process json with pax1 v3
    // - Regular in CORR    - process star schema dcspax with pax1 v1
    // Note:
    // the consumer (CORR) reads the input as a stream
    // - the 1st time it reads directly the last snapshot of the source delta table
    // - next times it reads the delta table with the checkpoint location
    val corrMapping = readMappingConfig(mappingFileCorr)
    val checkpointLocation = getCorrConfig.etl.stream.sink.checkpointLocation
    val corrCheckpointDir = new Directory(new File(checkpointLocation))
    // reset checkpoint folder for correlation the beginning
    corrCheckpointDir.deleteRecursively()

    // DCSPAX INIT (batch 1)
    runPaxFeed(batch = 1, testCase = "multi_batch", expNumFiles = 1) // V1 added

    // CORR INIT (batch 1)
    inspectFeedChanges(corrMapping, expectedNbMultipleLatestVersion = 0)
    runSourceCorrelation(batch = 1, testCase = "multi_batch", corrMapping, expNumFiles = 1)

    // DCSPAX Regular (batch 2 + batch 3)
    runPaxFeed(batch = 2, testCase = "multi_batch", expNumFiles = 2) // V2 added (V1 -> HISTO)
    runPaxFeed(batch = 3, testCase = "multi_batch", expNumFiles = 3) // V3 added (V2 -> HISTO)

    // CORR Regular (batch 2)
    inspectFeedChanges(corrMapping, expectedNbMultipleLatestVersion = 1)
    runSourceCorrelation(batch = 2, testCase = "multi_batch", corrMapping, expNumFiles = 2)
  }

  private def runSourceCorrelation(
    batch: Int,
    testCase: String,
    corrMapping: TablesConfig,
    expNumFiles: Int
  ): Unit = {
    Json2StarApp.run(spark, getCorrConfig, corrMapping)
    checkTablesContent(
      tableNames = Seq("ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO"),
      expectedCsvFilesDir =
        s"src/test/resources/datasets/source_correlation/$testCase/batch$batch/data/expected_results/corr/",
      createExpected = false
    )

    // number of files in the table directory should be the same as the batch number
    getNumFiles("ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO") should equal(expNumFiles)
  }

  private def inspectFeedChanges(corrMapping: TablesConfig, expectedNbMultipleLatestVersion: Int = 0): Unit = {
    val assoTable = corrMapping.tables.filter(_.name.equals("ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO")).head
    val assoTableSrcCorr = assoTable.sourceCorrelation.getOrElse(fail("No source correlation"))
    val target = assoTableSrcCorr.target

    val countMultipleLatestVersionFunction: DataFrame => Long = (b: DataFrame) => {
      b.select(target.domainAKey, target.domainAVersion, target.isLast)
        .where(col(target.isLast) === true)
        .groupBy(target.domainAKey, target.isLast)
        .agg(countDistinct(target.domainAVersion).as("nbLatestVersion"))
        .where(col("nbLatestVersion") > 1)
        .count
    }

    val processBatch: (DataFrame, Long) => Unit = (b: DataFrame, _: Long) => {
      countMultipleLatestVersionFunction(b) should be(expectedNbMultipleLatestVersion)

      // use the same logic as the one in Source Correlation
      val batchDedup = SourceCorrelationPipeline.dedupLogic(
        b,
        keyColumns = Seq(target.domainAKey, target.domainBKey, target.domainAVersion, target.domainBVersion)
      )

      countMultipleLatestVersionFunction(batchDedup) should be(0)
    }

    spark.readStream
      .option("readChangeFeed", "true")
      .format("delta")
      .table(assoTableSrcCorr.domainA.tableName)
      .withColumnRenamed(assoTableSrcCorr.domainA.versionColumnName, target.domainAVersion)
      .writeStream
      .option(
        "checkpointLocation",
        s"${getCorrConfig.etl.stream.sink.checkpointLocation}/dedup/${assoTableSrcCorr.domainA.tableName}"
      )
      .foreachBatch(processBatch)
      .queryName(s"TYPE=CORRSRC TABLE=${assoTable.name} STREAMING=${assoTableSrcCorr.domainA.tableName}")
      .outputMode(OutputMode.Update)
      .trigger(Trigger.AvailableNow())
      .start()
      .awaitTermination()
  }
}
