package com.amadeus.airbi.json2star.common.addons.base.mapping.input.checkpoints

import com.amadeus.airbi.json2star.common.addons.base.mapping.MappingAddonParams.{
  AutoloaderStart,
  StartConfig,
  StaticStart
}
import com.amadeus.airbi.json2star.common.addons.base.mapping.input.checkpoints.StartCheckpointsTable.{
  ImmutableAutoloaderStart,
  ImmutableStartConfig,
  ImmutableStaticStart
}
import com.amadeus.airbi.json2star.common.resize.DatabricksUtils

import java.sql.Timestamp
import java.time.LocalDate

/** The StreamPlanner is responsible for resolving the processing steps based on the provided input paths, start
  * configuration, and checkpoint table state.
  *
  * The logic is the following:
  * - Validate and store the input configuration, checking that it did not change if previously stored.
  * - If the start config is autoloader-start, then we create an autoloader step (legacy behavior)
  * - Else if the start config is static-start, then resolve the plan according to the algorithm detailed below.
  *
  * Static start processing plan:
  * - compute border date D, defined as the day before the stored CONFIG_ROW timestamp
  * - read static start offset from the checkpoint table: (static-start-end-date, static-start-done)
  * - read autoloader offset from the checkpoint table: (last-autoloader-date)
  *
  * - if none of the offsets is defined (beginning)
  *     - compute a static start step (with potentially many static start batches), including batches of N days,
  *       until the border date D included (the last batch could have less then N days)
  *     - if no end-date is defined in the configuration, compute one or more autoloader month steps to process the
  *       months from the end of static start to the current month
  *
  * - if the static start offset is defined
  *     - if static start is not finished
  *         - compute a static start step (with potentially many static start batches)
  *         - if no end-date is defined in the configuration, compute one or more autoloader month steps to process the
  *           months from the end of static start to the current month
  *     - if static start finished
  *         - if no end-date defined in the configuration, compute one or more autoloader month steps to process the
  *           months from the end of static start (or the last-autoloader-date if defined) to the current month
  */
object StreamPlanner {

  //                                              month
  //                                              border
  //                                            2024-11-30
  //                static              autoloader  |          autoloader
  //                start                 done      |            to do
  //  -----[==========================|xxxxxxxxxxxxx|xxxxxxx|-------------|----------
  //       V                          |                     |             |
  //   static-start                   V                     |             |
  //   start date                 static-start              V             |
  //   (discovered/                 end date               last           V
  //    config)                  (calculated/          autoloader      current
  //                               config)                date         date
  //                             2024-11-19                  (may not exist)

  def resolve(
    table: StartCheckpointsTable,
    inputPaths: Seq[String],
    startMode: StartConfig,
    dbx: DatabricksUtils,
    now: Timestamp
  ): Seq[StreamStep] = {
    // check the input config (start mode and input paths) and store it if needed
    val inputConfigImmutableView = ImmutableStartConfig.from(startMode, inputPaths)
    ensureInputConfigIsValid(inputConfigImmutableView, now)
    ensureConfigExistsAndUnchangedOrStore(table, inputConfigImmutableView, now)

    // input config is ok, continue with it
    val resolvedSteps = startMode match {
      case AutoloaderStart() =>
        Seq(AutoloaderStartStep(inputPaths))
      case s: StaticStart =>
        resolveStaticStartSteps(dbx, table, s, inputPaths, now)
    }
    resolvedSteps
  }

  def ensureInputConfigIsValid(inputConfig: ImmutableStartConfig, now: Timestamp): Unit = {
    inputConfig match {
      case ImmutableStaticStart(regex, startDate, endDate, inputPaths) => {
        checkValidDates(startDate, endDate, now)
        checkPathRegex(regex, inputPaths)
      }
      case ImmutableAutoloaderStart(_) => () // nothing to check here
    }
  }

  private def checkPathRegex(regex: String, inputPaths: Seq[String]): Unit = {
    if (inputPaths.exists(p => !p.matches(regex))) {
      throw new IllegalArgumentException(
        s"Provided paths $inputPaths do not match the regex: ${regex}"
      )
    }
  }

  // dates are valid if: startDate <= endDate and endDate < now
  private def checkValidDates(startDate: Option[LocalDate], endDate: Option[LocalDate], now: Timestamp): Unit = {
    val today = now.toLocalDateTime.toLocalDate
    (startDate, endDate) match {
      case (Some(s), Some(e)) if s.isAfter(e) =>
        throw new IllegalArgumentException(
          s"Invalid dates provided. Start date: $s > End date: $e"
        )
      case (_, Some(e)) if e.isEqual(today) || e.isAfter(today) =>
        throw new IllegalArgumentException(
          s"Invalid dates provided. End date: $e >= Now: $today"
        )
      case (Some(s), _) if s.isEqual(today) || s.isAfter(today) =>
        throw new IllegalArgumentException(
          s"Invalid dates provided. Start date: $s >= Now: $today"
        )
      case _ => () // all good otherwise
    }
  }

  // check that the provided config did not change compared to what is stored
  def ensureConfigExistsAndUnchangedOrStore(
    table: StartCheckpointsTable,
    inputConfig: ImmutableStartConfig,
    ts: Timestamp
  ): Unit = {
    val storedConfig = table.readStoredConfig()
    (storedConfig, inputConfig) match {
      // no stored config, store the immutable input config
      case (None, _) => table.storeConfig(inputConfig, ts)
      // stored config exists, check if it matches the input config
      case (Some((_, sc)), c) => if (sc != c) configChangeException(sc, c)
    }
  }

  private def resolveStaticStartSteps(
    dbx: DatabricksUtils,
    table: StartCheckpointsTable,
    staticStart: StaticStart,
    inputPaths: Seq[String],
    now: Timestamp
  ): Seq[StreamStep] = {
    val currentDate = now.toLocalDateTime.toLocalDate
    val (configStoredTimestamp, _) =
      table.readStoredConfig().getOrElse(throw new IllegalStateException("No stored config"))
    val border =
      configStoredTimestamp.toLocalDateTime.toLocalDate.minusDays(1) // inclusive date until static will process
    val staticStartOffset = table.readStaticStartOffset()
    val autoloaderOffset = table.readAutoloaderOffset()

    (staticStartOffset, autoloaderOffset) match {
      case (None, None) =>
        // static not started, start with static then autoloader
        val staticPaths = inputPaths.map { path =>
          StaticStartStepPath(
            basePath = path,
            partitions = FileSystemDiscovery.discoverPartitions(path, dbx)
          )
        }
        val staticStartStep: StaticStartStep = StaticStartStep(
          staticStart.numberOfDaysPerBatch,
          staticPaths,
          staticStart.startDate.getOrElse(FileSystemDiscovery.discoverStartDate(inputPaths.head, dbx)),
          staticStart.endDate.getOrElse(border)
        )
        buildStepsWhenStaticNotFinished(
          staticStart = staticStart,
          staticStartStep = staticStartStep,
          currentDate = currentDate
        )
      case (Some((lastDate, staticBatchInitDone)), _) if !staticBatchInitDone =>
        // static unfinished, continue with static then autoloader
        val staticPaths = inputPaths.map { path =>
          StaticStartStepPath(
            basePath = path,
            partitions = FileSystemDiscovery.discoverPartitions(path, dbx)
          )
        }
        val staticStartStep = StaticStartStep(
          staticStart.numberOfDaysPerBatch,
          staticPaths,
          lastDate.plusDays(1), // next day
          staticStart.endDate.getOrElse(border)
        )
        buildStepsWhenStaticNotFinished(
          staticStart = staticStart,
          staticStartStep = staticStartStep,
          currentDate = currentDate
        )
      case (Some((lastBatchDate, staticBatchInitDone)), autoloaderDate) if staticBatchInitDone =>
        // static finished, continue with autoloader
        val staticPaths = inputPaths.map { path =>
          StaticStartStepPath(
            basePath = path,
            partitions = FileSystemDiscovery.discoverPartitions(path, dbx)
          )
        }
        buildStepsWhenStaticFinished(
          staticStart = staticStart,
          staticStartPaths = staticPaths,
          staticStartEndDate = lastBatchDate,
          lastAutoloaderDate = autoloaderDate,
          currentDate = currentDate
        )
    }
  }

  private def buildStepsWhenStaticFinished(
    staticStart: StaticStart,
    staticStartPaths: Seq[StaticStartStepPath],
    staticStartEndDate: LocalDate,
    lastAutoloaderDate: Option[LocalDate],
    currentDate: LocalDate
  ): Seq[StreamStep] = {
    if (staticStart.endDate.isEmpty) {
      // create an autoloader step only if an end date was not explicitly provided
      AutoloaderMonthStep.from(
        staticStartEndDate = staticStartEndDate,
        staticStartPaths = staticStartPaths,
        lastAutoloaderDate = lastAutoloaderDate,
        currentDate = currentDate
      )
    } else {
      Seq()
    }
  }

  private def buildStepsWhenStaticNotFinished(
    staticStart: StaticStart,
    staticStartStep: StaticStartStep,
    currentDate: LocalDate
  ): Seq[StreamStep] = {
    if (staticStart.endDate.isEmpty) {
      // create an autoloader step only if an end date was not explicitly provided
      val autoloaderSteps = AutoloaderMonthStep.from(staticStartStep, None, currentDate)
      Seq(staticStartStep) ++ autoloaderSteps
    } else {
      Seq(staticStartStep)
    }
  }

  private def configChangeException(storedConfig: ImmutableStartConfig, inputConfig: ImmutableStartConfig): Nothing = {
    throw new IllegalStateException(
      s"Input config changed in a not authorized way. Stored: ${storedConfig} Input: ${inputConfig}"
    )
  }

}
