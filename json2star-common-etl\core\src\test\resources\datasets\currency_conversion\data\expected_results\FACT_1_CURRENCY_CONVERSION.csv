EXCESS_<PERSON><PERSON><PERSON><PERSON>_ITEM_ID,R<PERSON><PERSON><PERSON><PERSON>_KEY,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_IDENTIFIER,<PERSON>EM_IDENTIFIER,WEIGHT_VALUE,WEI<PERSON><PERSON>_<PERSON>IT,WEIGHT_VALUE_ORIGINAL,WEIG<PERSON>_<PERSON>IT_ORIGINAL,<PERSON><PERSON><PERSON>_AMOUNT,<PERSON>AR<PERSON>_CURRENCY,<PERSON><PERSON><PERSON>_AMOUNT_ORIGINAL,<PERSON>AR<PERSON>_CURRENCY_ORIGINAL,RATE_AMOUNT,RATE_CURRENCY,<PERSON><PERSON><PERSON><PERSON><PERSON>_RATE_DATE_NEEDED,EXCHANGE_RATE_DATE_TAKEN,RATE_AMOUNT_ORIGINAL,RATE_CURRENCY_ORIGINAL,BAG_ID,EXCESS_<PERSON>GGA<PERSON>_CHARGE_ID,<PERSON>GS_<PERSON><PERSON>UP_ID,VER<PERSON><PERSON>,LOAD_DATE
hashM(BAGDA3Y49025-10000000011AA11A-10A0110001AA1AA1-1000000001A12345),BAGDA3Y49025-10000000011AA11A-10A0110001AA1AA1-1000000001A12345,10A0110001AA1AA1,1000000001A12345,56.7,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,125.0,<PERSON>O<PERSON><PERSON>,,,75,<PERSON>R,,,,,75,EUR,hashM(BAGDA3Y49025-2002BAG00036004D),hashM(BAGDA3Y49025-10000000011AA11A),hashM(BAGDA3Y49025),1612410241801,2018-02-01T00:00:00.000000Z
