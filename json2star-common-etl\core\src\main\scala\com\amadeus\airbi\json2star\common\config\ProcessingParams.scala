package com.amadeus.airbi.json2star.common.config

import com.amadeus.airbi.json2star.common.app.ProcessingContext
import pureconfig.ConfigReader

case class ProcessingParams(
  // general settings section
  enableMetadata: Boolean = true,
  parallelism: Int = ProcessingContext.DefaultParallelism,
  displayMainEvents: Boolean = false,
  displaySparkStageLevelEvents: Boolean = false,
  optimizeParams: OptimizeParams = OptimizeParams.Default,
  resizeParams: Option[ResizeParams] = None,
  hashDebug: Boolean = false,
  // addons section (addons should retrieve their configuration from here, using a unique key)
  addons: Map[String, Unparsed] = Map.empty[String, Unparsed]
) {
  def addonParamAs[T: ConfigReader](addonName: String): T = {
    val a = addons.get(addonName).map(_.parseAs[T])
    a match {
      case None =>
        throw new IllegalArgumentException(s"Missing addon name key '${addonName}' in addon params")
      case Some(Left(e)) =>
        throw new IllegalArgumentException(s"Invalid content in key '${addonName}': ${e.prettyPrint()}")
      case Some(Right(v)) => v
    }
  }
}
