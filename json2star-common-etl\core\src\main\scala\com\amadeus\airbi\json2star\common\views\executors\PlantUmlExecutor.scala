package com.amadeus.airbi.json2star.common.views.executors

import com.amadeus.airbi.json2star.common.config.AppConfig
import com.amadeus.airbi.json2star.common.views.generators.PlantUmlGenerator
import org.rogach.scallop.{ScallopConf, ScallopOption}
import org.slf4j.{Logger, LoggerFactory}

import java.io.{BufferedWriter, File, FileWriter}

case class PlantUmlExecutorConfig(arguments: Seq[String]) extends ScallopConf(arguments) {
  val appConfigFilePath: ScallopOption[String] = opt[String](required = true)
  val outputDirectory: ScallopOption[String] = opt[String](required = true)

  val enableGlobalLevel: ScallopOption[Boolean] = opt[Boolean](required = false)
  val enableSubdomainLevel: ScallopOption[Boolean] = opt[Boolean](required = false)
  val enableExternalDomainLevel: ScallopOption[Boolean] = opt[Boolean](required = false)
  validate(enableGlobalLevel, enableSubdomainLevel, enableExternalDomainLevel) { (x, y, z) =>
    if (x || y || z) Right(()) else Left("At least one level must be enabled")
  }
  verify()
}

object PlantUmlExecutor {

  implicit lazy val logger: Logger = LoggerFactory.getLogger(getClass.getName)
  def main(args: Array[String]): Unit = {

    val executorConfig = PlantUmlExecutorConfig(args)
    val appConfig = AppConfig(executorConfig.appConfigFilePath())

    val generatorConfig = PlantUmlGenerator.Config(
      domain = appConfig.common.domain,
      version = appConfig.common.domainVersion,
      modelConfFile = appConfig.modelConfFile,
      tablesSelectors = appConfig.tablesSelectors,
      disabledStackableAddons = appConfig.disabledStackableAddons,
      enableGlobalLevel = executorConfig.enableGlobalLevel(),
      enableSubdomainLevel = executorConfig.enableSubdomainLevel(),
      enableExternalDomainLevel = executorConfig.enableExternalDomainLevel()
    )
    val plantUmlInputMap = PlantUmlGenerator.from(generatorConfig)
    plantUmlInputMap.foreach { case (fileName, content) =>
      writeFile(new File(executorConfig.outputDirectory(), fileName).toString, content)
    }
  }

  def writeFile(outFile: String, content: String): Unit = {
    if (outFile == "DEBUG") {
      println(content) // scalastyle:ignore
    } else {
      // Overwrite the file
      val appendFlag = false
      val bw = new BufferedWriter(new FileWriter(new File(outFile), appendFlag))
      bw.write(content)
      bw.close()
    }
  }
}
