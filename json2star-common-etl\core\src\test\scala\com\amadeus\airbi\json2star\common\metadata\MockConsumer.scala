package com.amadeus.airbi.json2star.common.metadata

import org.apache.spark.sql.{DataFrame, SparkSession}

/** Example of concrete implementation of a database query using metadata.
  */
class ConsumerWithConsistentQuery(sparkSession: SparkSession, checkpointDir: String) extends ConsistentQuery {
  override val spark: SparkSession = sparkSession
  override val checkpointLocation: String = checkpointDir
  override val pivotTableName: String = MockProducer.PIVOT_TABLE_NAME
  override val domain: String = MockProducer.DOMAIN

  override def query(batch: DataFrame, endDate: String): Unit = {
    val otherTableName = MockProducer.OTHER_TABLE_NAME
    val otherTable =
      readLastStableVersionBeforeEnd(otherTableName)(endDate).as("other")
    val join = batch.join(otherTable, Seq("ID"), "left")
    join.createOrReplaceTempView("join")
    Consumer.merge(spark, "join")
  }
}

/** Legacy implementation of a database consumer, without metadata
  */
class ConsumerWithoutConsistentQuery(spark: SparkSession, checkpointLocation: String) {

  def runQuery(): Unit = {
    def f(batch: DataFrame, batchId: Long): Unit = {
      val otherTable = spark.read
        .format("delta")
        .table(MockProducer.OTHER_TABLE_NAME)
      val join = batch.join(otherTable, Seq("ID"), "left")
      // spark.sql("select * from CONSUMER_OUTPUT").show(false) // enable if needed
      join.write.insertInto(Consumer.CONSUMER_TABLE_NAME)
    }

    spark.readStream
      .format("delta")
      .option("ignoreChanges", "true")
      .table("PIVOT")
      .writeStream
      .option("checkpointLocation", checkpointLocation)
      .foreachBatch(f _)
      .trigger(org.apache.spark.sql.streaming.Trigger.AvailableNow)
      .start()
      .awaitTermination()
  }
}

object Consumer {
  val CONSUMER_TABLE_NAME = "CONSUMER_OUTPUT"
  val CONSUMER_TABLE_COLUMNS: Seq[String] = Seq("ID", "FIELD_A", "FIELD_B", "NAME")
  val CONSUMER_TABLE_SCHEMA = "ID STRING, FIELD_A STRING, FIELD_B STRING, NAME STRING"
  val CREATE_TABLE_DDL = s"CREATE TABLE IF NOT EXISTS $CONSUMER_TABLE_NAME($CONSUMER_TABLE_SCHEMA) USING DELTA"

  def merge(spark: SparkSession, input: String): Unit = {
    spark.sql(s"""
              MERGE INTO $CONSUMER_TABLE_NAME e USING $input s
              ON e.ID = s.ID
              WHEN MATCHED THEN UPDATE SET *
              WHEN NOT MATCHED THEN INSERT * 
              """)
  }
}
