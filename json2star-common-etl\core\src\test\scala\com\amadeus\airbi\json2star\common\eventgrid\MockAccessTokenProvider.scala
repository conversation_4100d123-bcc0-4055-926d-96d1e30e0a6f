package com.amadeus.airbi.json2star.common.eventgrid

import com.azure.core.credential.AccessToken
import org.apache.http.client.HttpClient
import org.apache.http.client.methods.HttpUriRequest
import org.apache.http.entity.BasicHttpEntity
import org.apache.http.message.BasicStatusLine
import org.apache.http.{HttpResponse, HttpVersion}
import org.scalamock.scalatest.MockFactory

import java.io.ByteArrayInputStream
import java.nio.charset.StandardCharsets
import java.time.OffsetDateTime
import scala.util.{Success, Try}

object MockAccessTokenProvider extends AccessTokenProvider with MockFactory {
  override def authStep1(
    tenantId: String,
    clientId: String,
    clientSecret: String,
    httpLogDetailLevel: Option[String]
  ): Try[AccessToken] = Success(
    new AccessToken(s"$tenantId-$clientId-${clientSecret}", OffsetDateTime.MAX)
  )

  // mock http response for step 2
  override def httpClient: HttpClient = {
    val mockHttpClient = mock[HttpClient]
    val mockResponse = mock[HttpResponse]
    val statusLine = new BasicStatusLine(HttpVersion.HTTP_1_1, 200, "OK")
    val mockEntity = new BasicHttpEntity()
    val mockContent =
      """{
        |  "value": [{
        |      "access_token": "token-of-step-2",
        |      "expires_on": "3600"
        |    }]
        |}""".stripMargin
    mockEntity.setContent(new ByteArrayInputStream(mockContent.getBytes(StandardCharsets.UTF_8)))

    (mockHttpClient.execute(_: HttpUriRequest)).expects(*).returning(mockResponse)
    (mockResponse.getStatusLine _).expects().returning(statusLine)
    (mockResponse.getEntity _).expects().returning(mockEntity)
    mockHttpClient
  }
}

object MockAccessTokenProviderWithError extends AccessTokenProvider with MockFactory {
  override def authStep1(
    tenantId: String,
    clientId: String,
    clientSecret: String,
    httpLogDetailLevel: Option[String]
  ): Try[AccessToken] = Success(
    new AccessToken(s"$tenantId-$clientId-${clientSecret}", OffsetDateTime.MAX)
  )

  // mock http response for step 2
  override val httpClient: HttpClient = {
    val mockHttpClient = mock[HttpClient]
    val mockResponse = mock[HttpResponse]
    val statusLine = new BasicStatusLine(HttpVersion.HTTP_1_1, 404, "")
    (mockHttpClient.execute(_: HttpUriRequest)).expects(*).returning(mockResponse)
    (mockResponse.getStatusLine _).expects().returning(statusLine)
    mockHttpClient
  }
}
