package com.amadeus.airbi.json2star.common.extdata

import com.amadeus.airbi.json2star.common.addons.stackable.StackableAddons
import com.amadeus.airbi.rawvault.common.RootConfig
import com.amadeus.airbi.json2star.common.addons.base.mapping.MappingPipeline.TableMapping
import org.apache.spark.sql.SparkSession

import scala.reflect.ClassTag

trait ExtDataValue

/** Case class containing all external data
  * @param refs a map of ExtDataType and ExtDataValue
  */
case class ExtData(refs: Map[ExtDataType, ExtDataValue]) {

  /** Given an ExtDataType it will return the corresponding external data
    * If the passed ExtDataType has the wrong runtime type, an exception is thrown
    * If the passed ExtDataType is missing in ExtData, an exception is thrown
    *
    * @param edt the ExtDataType
    * @param tag the implicit ClassTag, needed to avoid type erasure of T in the pattern matching
    * @throws an exception when the data type does not match and when the data is missing in refs
    *
    * @return the ExtData with its specific type
    */
  def get[T](edt: ExtDataType)(implicit tag: ClassTag[T]): T = refs.get(edt) match {
    case Some(valueOfTypeT: T) => valueOfTypeT
    case Some(valueOfOtherType) =>
      throw new IllegalStateException(s"Wrong ExtDataValue type passed to ExtData: ${valueOfOtherType} $tag")
    case None =>
      throw new IllegalStateException(s"No ExtDataType ${edt} in the ExtData. Available are ${refs.keys.mkString(",")}")
  }

}

object ExtData {

  /** Load the ExtData containing all external data
    * The SparkSession is used to read from dbfs/abfss when necessary
    *
    * @param mappingConf mapping configuration containing all tables mapping
    * @param rootConfig root configuration for the app
    * @param sparkSession spark session
    *
    * @return the ExtData with loaded data
    */
  def load(mappingConf: List[TableMapping], rootConfig: RootConfig, sparkSession: SparkSession): ExtData = {
    // Get the list of external datatype required by the Stackable Addons
    val allRequiredExtData = mappingConf.flatMap { m =>
      m.stackableAddons.flatMap { sa =>
        // Each addon defines its external data requirements
        val requiredExtData = StackableAddons.stackableAddonLookup(sa).getRequiredExtData
        requiredExtData
      }
    }.toSet

    // Load the external data according to each type
    val extDataMap: Map[ExtDataType, ExtDataValue] = allRequiredExtData.map { edt =>
      edt -> load(edt, rootConfig, sparkSession)
    }.toMap
    ExtData(extDataMap)
  }

  private def load(edt: ExtDataType, rootConfig: RootConfig, sparkSession: SparkSession): ExtDataValue = {
    edt match {
      case DummyExtDataType => DummyExtData.load()
      case PorsExtDataType => PorsExtData.load(rootConfig, sparkSession)
    }
  }

}
