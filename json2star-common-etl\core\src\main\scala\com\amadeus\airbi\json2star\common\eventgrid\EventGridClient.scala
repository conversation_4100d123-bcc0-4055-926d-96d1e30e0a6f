package com.amadeus.airbi.json2star.common.eventgrid

import com.amadeus.airbi.json2star.common.config.EventGridParams
import com.amadeus.airbi.json2star.common.resize.DatabricksUtils
import com.azure.core.credential.AccessToken
import com.typesafe.scalalogging.Logger
import org.apache.http.HttpHeaders
import org.apache.http.client.entity.EntityBuilder
import org.apache.http.client.methods.{CloseableHttpResponse, RequestBuilder}
import org.apache.http.impl.client.HttpClientBuilder
import org.json4s.DefaultFormats
import org.json4s.jackson.Serialization.{write => asJson}
import org.slf4j.LoggerFactory

import scala.util.Try

case class EventGridClient(
  endpointUrl: String,
  token: AccessToken
) {
  lazy val logger: Logger = Logger(LoggerFactory.getLogger(getClass.getName))

  def send(event: CloudEvent): Try[CloseableHttpResponse] = {
    val content = asJson(event)(DefaultFormats)

    logger.info(s"[event-grid] Send to event grid URL: $endpointUrl")
    logger.info(s"[event-grid] Send JSON message: $content")

    val req = RequestBuilder
      .post(endpointUrl)
      .addHeader(HttpHeaders.CONTENT_TYPE, "application/cloudevents+json; charset=utf-8")
      .addHeader(HttpHeaders.AUTHORIZATION, s"Bearer ${token.getToken}")
      .setEntity(
        EntityBuilder.create.setText(content).build()
      )
      .build()
    Try(HttpClientBuilder.create().build().execute(req))
  }
}

object EventGridClient {
  def apply(evp: EventGridParams, dbx: DatabricksUtils, provider: AccessTokenProvider): EventGridClient = {
    val maybeToken = provider.getAccessToken(evp, dbx)
    maybeToken match {
      case Right(t) => EventGridClient(evp.topicUrl, t)
      case Left(error) => throw new RuntimeException(error)
    }
  }
}
