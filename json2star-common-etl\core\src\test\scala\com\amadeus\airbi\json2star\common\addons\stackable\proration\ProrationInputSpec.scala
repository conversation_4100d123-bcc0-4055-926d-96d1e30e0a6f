package com.amadeus.airbi.json2star.common.addons.stackable.proration

import com.amadeus.airbi.json2star.common.addons.base.mapping.Table.KeyValueRow
import com.amadeus.airbi.json2star.common.addons.stackable.Proration
import com.amadeus.airbi.json2star.common.addons.stackable.proration.ProrationAddon.NON_VALID_SEQUENCE_NUMBER
import com.amadeus.airbi.json2star.common.addons.stackable.proration.ProrationColumns._
import com.amadeus.airbi.json2star.common.addons.stackable.proration.ProrationInput.CouponKey
import com.amadeus.airbi.json2star.common.addons.stackable.proration.ProrationSpecHelper.getJsonDocument
import com.amadeus.airbi.json2star.common.extdata.{ExtData, PorsExtData, PorsExtDataType}
import com.amadeus.airbi.json2star.common.testfwk.SimpleSpec
import com.jayway.jsonpath.DocumentContext

import java.time.LocalDate

class ProrationInputSpec extends SimpleSpec {

  val DocumentDate = "2023-01-25"

  val couponRows: Seq[KeyValueRow] = List[KeyValueRow](
    couponRow(DocumentDate, "1", "EWR", "BOS"),
    couponRow(DocumentDate, "2", "BOS", "ACK"),
    couponRow(DocumentDate, "3", "ACK", "BOS"),
    couponRow(DocumentDate, "4", "BOS", "DCA")
  )

  val documentContext: DocumentContext = getJsonDocument(
    "/datasets/proration/data/MH-PRD_load-date=01-10-2024.json"
  )

  val config: Proration = Proration(
    jsonPathFareCalc = "$.mainResource.current.image.pricingConditions.fareCalculation.text",
    jsonPathPriceCurrencyPayment = "$.mainResource.current.image.price.currency",
    jsonPathPriceTotalPayment = "$.mainResource.current.image.price.total",
    jsonPathPriceTotalTaxesPayment = "$.mainResource.current.image.price.totalTaxes"
  )

  val pors: PorsExtData = PorsExtData(data = null) // scalastyle:ignore null

  val extData: ExtData = ExtData(Map(PorsExtDataType -> pors))

  val expectedItinerary: Seq[CouponKey] = Seq(
    CouponKey(1, "EWR", "BOS"),
    CouponKey(2, "BOS", "ACK"),
    CouponKey(3, "ACK", "BOS"),
    CouponKey(4, "BOS", "DCA") // scalastyle:ignore magic.number
  )

  describe("ProrationInput") {

    it("should build the tkt level input") {
      val actualInput = ProrationInput.build(
        couponRows,
        config,
        documentContext,
        extData
      )
      val expectedInput = ProrationInput(
        pors = pors,
        fareCalcLine =
          "EWR B6 BOS64.19UL07AE2C 9K ACK147.81MIPX 9K BOS157.12MIP B6 DCA Q8.79 219.89VH03AE6U USD597.80END ZP EWR4.00BOS0.00ACK0.00BOS4.00XF EWR4.5BOS4.5XT8.00ZP9.00XF",
        paymentCurrency = "USD",
        paymentTotal = "647.80",
        paymentTotalTaxes = "50.00",
        issueDate = Some(LocalDate.parse(DocumentDate)),
        itinerary = expectedItinerary
      )

      actualInput shouldBe expectedInput
    }

    it("should build a coupon key from a coupon row") {
      val actualKey = ProrationInput.buildCouponKey(couponRow(DocumentDate, "1", "CDG", "LHR"))
      actualKey shouldBe Some(CouponKey(1, "CDG", "LHR"))
    }

    it("should build a non valid coupon key from a coupon row in case of invalid sequence number") {
      val actualKey = ProrationInput.buildCouponKey(couponRow(DocumentDate, "SOMETHING_WRONG", "CDG", "LHR"))
      actualKey shouldBe Some(CouponKey(NON_VALID_SEQUENCE_NUMBER, "CDG", "LHR"))
    }

    it("should get the issue date from the coupon rows") {
      val actualIssueDate = ProrationInput.getIssueDate(couponRows)
      actualIssueDate shouldBe Some(LocalDate.parse(DocumentDate))
    }

    it("should return a None issue date if different coupons of a given ticket have different issue dates") {
      val rows = couponRows :+ couponRow("2020-01-02", "7", "CDG", "LHR")
      ProrationInput.getIssueDate(rows) shouldBe None
    }

    it("should return a None issue date if it does not have a valid date format") {
      val rows = Seq(couponRow("2020-01-WRONG", "7", "CDG", "LHR"))
      ProrationInput.getIssueDate(rows) shouldBe None
    }

    it("should build the ticket itinerary from the coupon rows") {
      val actualItinerary = ProrationInput.buildItinerary(couponRows)
      actualItinerary shouldBe expectedItinerary
    }

    it("should serialize the ProrationInput as a json string") {
      val input = ProrationInput.build(
        couponRows,
        config,
        documentContext,
        extData
      )

      ProrationInput.asJsonDebugString(
        input
      ) shouldBe """{"fareCalcLine":"EWR B6 BOS64.19UL07AE2C 9K ACK147.81MIPX 9K BOS157.12MIP B6 DCA Q8.79 219.89VH03AE6U USD597.80END ZP EWR4.00BOS0.00ACK0.00BOS4.00XF EWR4.5BOS4.5XT8.00ZP9.00XF","issueDate":[2023,1,25],"paymentCurrency":"USD","paymentTotal":"647.80","paymentTotalTaxes":"50.00","itinerary":[{"sequenceNumber":1,"departureAirport":"EWR","arrivalAirport":"BOS"},{"sequenceNumber":2,"departureAirport":"BOS","arrivalAirport":"ACK"},{"sequenceNumber":3,"departureAirport":"ACK","arrivalAirport":"BOS"},{"sequenceNumber":4,"departureAirport":"BOS","arrivalAirport":"DCA"}]}"""
    }

  }

  private def couponRow(
    issueDate: String,
    sequenceNumber: String,
    departureAirport: String,
    arrivalAirport: String
  ): KeyValueRow = {
    Map(
      DOCUMENT_CREATION_DATE -> issueDate,
      SEQUENCE_NUMBER -> sequenceNumber,
      SOLD_DEPARTURE_AIRPORT -> departureAirport,
      SOLD_ARRIVAL_AIRPORT -> arrivalAirport
    )
  }

}
