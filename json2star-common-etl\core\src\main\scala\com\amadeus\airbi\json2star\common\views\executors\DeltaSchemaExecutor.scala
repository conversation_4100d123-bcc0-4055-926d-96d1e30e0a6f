package com.amadeus.airbi.json2star.common.views.executors

import com.amadeus.airbi.json2star.common.TablesDef
import com.amadeus.airbi.json2star.common.config.AppConfig
import com.amadeus.airbi.json2star.common.views.generators.DeltaSchemaGenerator
import com.amadeus.airbi.rawvault.common.application.config.{ModelConfigLoader, TablesConfig}
import com.typesafe.scalalogging.Logger
import org.apache.spark.SparkConf
import org.apache.spark.sql.SparkSession
import org.slf4j.LoggerFactory

object DeltaSchemaExecutor {

  val DefaultRootLocation = "/user/hive/warehouse/TRASH"

  @transient
  lazy val logger: Logger = Logger(LoggerFactory.getLogger(getClass.getName))

  def main(args: Array[String]): Unit = {

    // TODO: verify arguments?
    val appConfig = AppConfig(args(0))
    val database = args(1)
    val rootLocation = args(2)

    implicit val spark: SparkSession = SparkSession
      .builder()
      .config(
        new SparkConf()
          .set("spark.databricks.delta.properties.defaults.autoOptimize.optimizeWrite", "true")
          .set("spark.databricks.delta.properties.defaults.autoOptimize.autoCompact", "true")
      )
      .getOrCreate()

    run(
      spark,
      appConfig.modelConfFile,
      database,
      rootLocation,
      appConfig.tablesSelectors,
      appConfig.disabledStackableAddons,
      appConfig.stream.sink.deltaOptions
    )
  }

  def run(
    spark: SparkSession,
    mappingConfFile: String,
    database: String,
    rootLocation: String = DefaultRootLocation,
    selectors: Set[String] = Set.empty[String],
    disabledStackableAddons: Set[String] = Set.empty[String],
    deltaOptions: Map[String, String] = Map.empty[String, String],
    restrictedTableSet: List[String] = List.empty[String], // needed to optimize test run
    modifier: TablesConfig => TablesConfig = identity
  ) {
    val originalmappingConf = modifier(
      ModelConfigLoader.defaultLoad(mappingConfFile, selectors, disabledStackableAddons)
    )
    val mappingConfToUse = if (restrictedTableSet.isEmpty) {
      originalmappingConf
    } else {
      originalmappingConf.copy(tables = originalmappingConf.tables.filter(t => restrictedTableSet.contains(t.name)))
    }
    val tablesDef = TablesDef.consolidate(mappingConfToUse)
    val queries = DeltaSchemaGenerator.toInitSql(tablesDef, database, deltaOptions)
    if (queries.isEmpty) {
      logger.error(s"Wrong SQL generated from the file ${mappingConfFile}")
      logger.error(s"Wrong SQL content is ${queries.mkString("\n")}")
      throw new Exception("ERROR INIT SQL GENERATOR")
    }

    val createDbQuery = s"CREATE DATABASE IF NOT EXISTS $database LOCATION '$rootLocation/$database.db'"
    logger.info(s"${getClass.getName} - Run Create DB Query: ${createDbQuery}")
    spark.sql(createDbQuery)

    spark.sql(s"use  $database;")
    logger.info(s"${getClass.getName} - Creating ${queries.size} tables...")
    queries.foreach { createSql =>
      logger.info(s"${getClass.getName} - Run Create Query: ${createSql}")
      createSql.statements.foreach(spark.sql)
    }
  }

}
