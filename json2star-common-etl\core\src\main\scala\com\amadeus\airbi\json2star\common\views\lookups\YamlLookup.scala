package com.amadeus.airbi.json2star.common.views.lookups

import com.amadeus.airbi.json2star.common._
import com.amadeus.airbi.json2star.common.views.lookups.YamlLookup.{sanitizePath, sanitizeValue}
import com.typesafe.config.{Config, ConfigValueType}
import com.typesafe.scalalogging.Logger
import org.slf4j.LoggerFactory
import pureconfig.module.yaml.YamlConfigSource

import java.io.File
import scala.collection.JavaConverters._
import scala.collection.convert.ImplicitConversions.`collection AsScalaIterable`
import scala.io.Source
import scala.util.{Success, Try}

object YamlLookup {
  lazy val logger: Logger = Logger(LoggerFactory.getLogger(getClass.getName))

  /** Load yaml file as pureconfig YamlConfigSource
    * Yaml can be read from file or string and a resource
    */
  private def loadYamlConfigSource(yamlFile: String): YamlConfigSource = {
    val newLine = System.lineSeparator()
    val fileExists = new File(yamlFile).exists()
    val resourceContent = Try(Source.fromResource(yamlFile).getLines().mkString(newLine))
    val yaml = (resourceContent, fileExists) match {
      case (Success(rc), _) =>
        YamlConfigSource.string(rc)
      case (_, true) =>
        YamlConfigSource.file(yamlFile)
      case (_, _) =>
        throw new IllegalArgumentException(s"Nor file nor resource found for YAML: ${yamlFile}")
    }
    yaml
  }

  /** Load the YAML file and create a YamlLookup object
    *  The yaml file can be
    *  - asyncapi file 3.0.0 (e.g. Nevio data models)
    *  - swagger file 2.0 (e.g. DIH Altea data models)
    * @param yamlFile a yaml file containing the model definitions (e.g. for DIH swagger file, Nevio asyncapi file, ...)
    * @param jsonToYamlPaths a map to convert the JSON path to the YAML path (e.g. "a.b.c" -> "x.y.z")
    * @return
    */
  def apply(
    yamlFile: String,
    jsonToYamlPaths: Map[String, String]
  ): YamlLookup = {
    val yamlSource = loadYamlConfigSource(yamlFile)
    val maybeAsyncApiVersion = yamlSource.at("asyncapi").value().toOption
    val maybeSwaggerVersion = yamlSource.at("swagger").value().toOption

    val rootNode = (maybeAsyncApiVersion, maybeSwaggerVersion) match {
      case (Some(asyncapi), _) =>
        val version = asyncapi.unwrapped().toString
        if (version != "3.0.0") {
          logger.warn(s"Unsupported asyncapi version: ${version}")
        }
        "components.schemas"
      case (_, Some(swagger)) =>
        val version = swagger.unwrapped().toString
        if (version != "2.0") {
          logger.warn(s"Unsupported swagger version: ${version}")
        }
        "definitions"
      case _ =>
        throw new IllegalArgumentException(
          s"Unsupported YAML file: ${yamlFile}. Expected: asyncapi 3.0.0 or swagger 2.0"
        )
    }
    // Go to the root node section of the YAML file
    // Load it as the Config to have "accessible" paths
    //  the root node in the YAML file pointing to the models (e.g. definitions, components.schemas, ...)
    val rootConfig = yamlSource.at(rootNode).loadOrThrow[Config]
    new YamlLookup(rootConfig, jsonToYamlPaths)
  }

  /** Sanitize Yaml path to be compatible with Config path resolution
    *  - Remove escape character
    *  - Replace dot with quoted dot (Leg\.v1 --> Leg"."v1)
    *  - Replace %20 with space (Leg%20Cabin%20Product --> Leg Cabin Product)
    * @param path yaml path
    * @return quoted yaml path
    */
  def sanitizePath(path: String): String = {
    path
      .replaceAll("""\\""", "")
      .replaceAll("\\.", "\".\"")
      .replaceAll("%20", " ")
  }

  /** Sanitize value by removing characters that are unsupported by markdown converter
    * @param value yaml value
    * @return sanitized value
    */
  def sanitizeValue(value: String): String = {
    // https://docs.oracle.com/javase/7/docs/api/java/util/regex/Pattern.html
    val YamlInfoWhitelistedChars = """[^\w\p{Punct}\s]"""
    // Input sanitization
    // filter only whitelisted characters
    xml.Utility.escape(value.replaceAll(YamlInfoWhitelistedChars, ""))
  }

}

/** YamlLookup is a base class to read a YAML file and access the content using a JSON path
  *
  * The Config root allows to access the YAML as a tree structure
  * The access to the YAML node is done by the JSON path
  * A map jsonToYamlMap is used to convert the JSON path to the YAML path
  *
  * @param rootConfig root node section containing Schema models
  * @param jsonToYamlPaths a map to convert the JSON path to the YAML path (e.g. "a.b.c" -> "x.y.z")
  */
case class YamlLookup(
  rootConfig: Config,
  jsonToYamlPaths: Map[String, String]
) {

  lazy val logger: Logger = Logger(LoggerFactory.getLogger(getClass.getName))

  private def toYamlPath(jsonPath: String, jsonToYamlPaths: Map[String, String]): String = {
    // find the conversion from JSON prefix to YAML prefix
    // remove array expression
    val jsonPathNoArray = jsonPath.replaceAll("""(\[.*?\])""", "")
    // if multiple paths are provided, take the first one
    val singleJsonPath = jsonPathNoArray.split('-').head.trim
    val matchingPrefixes = jsonToYamlPaths.keys.toList.filter(jpPrefix => singleJsonPath.startsWith(jpPrefix))
    val yamlPathConverted = matchingPrefixes match {
      case Nil | List() => singleJsonPath
      case ps =>
        // If there more matching prefix for the json path, take the longest prefix
        val jsonPathPrefix = ps.maxBy(_.length)
        val yamlPath = jsonToYamlPaths(jsonPathPrefix)
        singleJsonPath.replace(jsonPathPrefix, yamlPath)
    }
    // remove trailing suffix ".value" if any
    val yamlPathConvertedNoSuffix = yamlPathConverted.stripSuffix(".value")
    yamlPathConvertedNoSuffix
  }

  /** Retrieve the Metadata Fields from YAML using the JSON path for the lookup
    *
    * @param jsonPath  json path to resolve
    * @return yamlPath and yaml info if any
    */
  def getByJsonPath(jsonPath: Origin): (String, Option[MetadataFields]) = {
    // Convert the path from the json path (config) to the yaml path (yaml)
    val yamlPath = toYamlPath(jsonPath.raw, jsonToYamlPaths)
    yamlPath -> getByYamlPath(yamlPath)
  }

  /** Retrieve the YAML info using the YAML path for the lookup
    *
    * @param yamlPath path to resolve (if this parameter includes an array expression, it is already removed)
    * @return metadata fields if any
    */
  def getByYamlPath(yamlPath: String): Option[MetadataFields] = {
    // Separate yamlPath by each node
    val YamlNodeRegex = """(?<!\\)\.""".r
    val yamlNodes = YamlNodeRegex
      .split(yamlPath)
      .map(sanitizePath)
      .toList

    // To traverse all nodes to get the final leaf node
    // Example:
    // when path is a.b.c
    // it returns c
    // Get all definitions
    val leafNode = yamlNodes match {
      case Nil | List() =>
        logger.warn(s"The YAML path is empty ${yamlPath}")
        None
      case nodes =>
        val leafNode = nodes.foldLeft(Option(rootConfig)) { (currNode, childName) =>
          if (currNode.isEmpty) {
            // it means the node from the current yaml path doesn't exist
            None
          } else {
            getConfigNode(rootConfig, currNode.get, childName)
          }
        }
        leafNode
    }

    // Access the value from a Yaml node
    leafNode match {
      case None =>
        logger.warn(s"The path '${yamlPath}' is not found in YAML")
        None
      case Some(foundNode) =>
        val resolvedNode = foundNode match {
          // if the node is a reference and it hasn't any description
          case RefNode(path) if !foundNode.hasPath("description") => rootConfig.getConfig(path)
          case _ => foundNode
        }
        val YAMLInfo = getYamlProperties(resolvedNode)
        Seq("description", "example", "piiType").foreach(v =>
          if (v.isEmpty) {
            logger.warn(s"For the path ${yamlPath} the ${v} field is not present in YAML")
          }
        )
        Some(YAMLInfo)
    }
  }

  private def getStringValue(c: Config, fieldName: String): Option[String] = {
    val parsed = if (c.hasPath(fieldName)) {
      Some(c.getString(fieldName))
    } else {
      None
    }

    // Input sanitization
    parsed.map(sanitizeValue)
  }

  private def getValues(c: Config, fieldName: String): Seq[String] = {
    val parsed = if (c.hasPath(fieldName)) {
      val v = c.getValue(fieldName)
      v.valueType() match {
        case ConfigValueType.STRING => Seq(c.getString(fieldName))
        case ConfigValueType.LIST => c.getStringList(fieldName).asScala
        case ConfigValueType.BOOLEAN => Seq(c.getBoolean(fieldName).toString)
        case ConfigValueType.NUMBER => Seq(c.getNumber(fieldName).toString)
        case ConfigValueType.NULL => Seq.empty
        case ConfigValueType.OBJECT => Seq.empty
      }
    } else {
      Seq.empty
    }

    // Input sanitization
    parsed.map(sanitizeValue)
  }

  private def getExamples(c: Config): Seq[String] = {
    if (c.hasPath("enum")) {
      c.getStringList("enum").asScala.toList
    } else if (c.hasPath("example")) {
      getValues(c, "example")
    } else if (c.hasPath("examples")) {
      getValues(c, "examples")
    } else {
      Seq.empty
    }
  }

  def getYamlProperties(c: Config): MetadataFields = {
    MetadataFields(
      description = getStringValue(c, "description"),
      examples = getExamples(c),
      piiType = getStringValue(c, "piiType")
    )
  }

  /** Extract a child node from the YAML definitions
    * The YAML definitions is a Tree structure
    *
    * A definition (e.g. DcsPassenger) contains properties
    * A property can be
    * - defined (e.g.id) with the fields (e.g description, example, ...)
    * - reference to a definition (e.g. passenger) - it has $ref
    * - array (e.g segmentDeliveries) - it has items with reference to a definition $ref
    *
    * @param root root node containing all YAML definitions
    * @param curr current config node
    * @param childName name of the child node
    * @return child node
    */
  def getConfigNode(root: Config, curr: Config, childName: String): Option[Config] = {
    val value = curr match {
      case node if node.isEmpty => None

      case PointerNode(_) =>
        logger.warn(s"YAML Lookup for $childName: found PointerNode - not supported")
        None

      case ObjectNode(nodes) =>
        logger.info(s"YAML Lookup for $childName: found ObjectNode with properties")
        val found = nodes
          .map { elem => getConfigNode(root, elem, childName) }
          .collectFirst { case Some(n) => n }
        found

      case ArrayNode(arrayItemNode) =>
        logger.info(s"YAML Lookup for $childName: the node is array a reference --> access to an item")
        getConfigNode(root, arrayItemNode, childName)

      case ComposedNode(nodes) =>
        logger.info(s"YAML Lookup for $childName: the node is composed of more schemas --> look in all children")
        // iterates over all nodes to find the first match
        val found = nodes
          .map { elem => getConfigNode(root, elem, childName) }
          .collectFirst { case Some(n) => n }
        found

      case RefNode(referencedNameNode) =>
        logger.info(s"YAML Lookup for $childName: the node has a reference --> access to ${referencedNameNode}")
        getConfigNode(root, root.getConfig(referencedNameNode), childName)

      case node if node.hasPath(childName) =>
        logger.info(s"YAML Lookup for $childName: found definition node")
        val found = node.getConfig(childName)
        Some(found)

      case _ =>
        logger.warn(s"YAML Lookup for $childName: definition resolution not possible")
        None
    }
    value
  }

}

object PointerNode {

  def unapply(c: Config): Option[String] = {
    if (c.hasPath("format") && c.getString("format") == "json-pointer") {
      Some(c.getString("format"))
    } else {
      None
    }
  }

}

object ObjectNode {

  def unapply(c: Config): Option[List[Config]] = {
    val propertyConfig = if (c.hasPath("properties")) List(c.getConfig("properties")) else List.empty
    val otherConfigs = ComposedNode.unapply(c).getOrElse(List.empty)
    val allConfigs = propertyConfig ++ otherConfigs
    if (allConfigs.nonEmpty) Some(allConfigs) else None
  }

}

object RefNode {

  def unapply(c: Config): Option[String] = {
    if (c.root().containsKey("$ref")) {
      // Note: d.hasPath is not supported with $ref - Access with the key using the ConfigObject
      val ref = c.root().get("$ref").unwrapped().toString
      val referencedModel = ref.split("/").last
      val escapedDotReferencedModel = sanitizePath(referencedModel)

      Some(escapedDotReferencedModel)
    } else {
      None
    }
  }

}

object ArrayNode {

  def unapply(c: Config): Option[Config] = {
    if (c.hasPath("items") && c.hasPath("type") && c.getString("type") == "array") {
      Some(c.getConfig("items"))
    } else {
      None
    }
  }

}

object ComposedNode {

  def unapply(c: Config): Option[List[Config]] = {
    if (c.hasPath("allOf")) {
      Some(c.getConfigList("allOf").toList)
    } else if (c.hasPath("oneOf")) {
      Some(c.getConfigList("oneOf").toList)
    } else if (c.hasPath("anyOf")) {
      Some(c.getConfigList("anyOf").toList)
    } else {
      None
    }
  }

}
