hashMIdCheckEndNotNull: "if(element_at(split({0},'-'),array_size(split({0},'-'))) == '_', NULL,hashM({0}) )"
hashSIdCheckEndNotNull: "if(element_at(split({0},'-'),array_size(split({0},'-'))) == '_', NULL,hashS({0}) )"
hashMIdCheckStartAndEndNotNull: "if(element_at(split({0},'-'),1) == '_', NULL, if(element_at(split({0},'-'),array_size(split({0},'-'))) == '_', NULL,hashM({0}) ))"
minDateFromTimestampArray: "to_date(array_min(regexp_extract_all({0}, '(\\\\d+)-(\\\\d+)-(\\\\d+)T(\\\\d+):(\\\\d+):(\\\\d+)Z',0)))"
// input is either A-_ or _-A, output will be A. In case it is A_B, it will be A
createConsolidatedValue: "if(element_at(split({0},'-'),1) == '_', element_at(split({0},'-'),2),element_at(split({0},'-'),1) )"
// input is either A1-A2-B2 : if A1 is _, we use B2 otherwise we use A2
createConsolidatedValueExternalDependency: "if(element_at(split({0},'-'),1) == '_', if(element_at(split({0},'-'),3) == '_', null, element_at(split({0},'-'),3)), if(element_at(split({0},'-'),2) == '_', null, element_at(split({0},'-'),2)) )"
// input is either 2RT2TQ-2022-12-20-OT-68-1111111111111-_ or 2RT2TQ-2022-12-20-OT-68-_-1111111111111 or 2RT2TQ-2022-12-20-OT-68-1111111111111-9999999999999
// it should return 2RT2TQ-2022-12-20-OT-68-1111111111111
createConsolidatedDocumentNumberReferenceKey: "concat_ws ('-', regexp_extract({0}, '(\\\\w+)-(\\\\d+)-(\\\\d+)-(\\\\d+)-(\\\\w+)-(\\\\d+)',0),regexp_extract({0}, '(\\\\d{9,14})',0))"
// input is either 2RT2TQ-2022-12-20-OT-68-1111111111111-9999999999999-1-9
//              or 2RT2TQ-2022-12-20-OT-68-_-1111111111111-_-1
//              or 2RT2TQ-2022-12-20-OT-68-_-1111111111111-1-9
//              or 2RT2TQ-2022-12-20-OT-68-1111111111111-9999999999999-_-1
// it should return 2RT2TQ-2022-12-20-OT-68-1111111111111-1
createConsolidatedCouponReferenceKey: "concat_ws ('-', regexp_extract({0}, '(\\\\w+)-(\\\\d+)-(\\\\d+)-(\\\\d+)-(\\\\w+)-(\\\\d+)',0),regexp_extract({0}, '(\\\\d{9,14})',0),regexp_extract(substring_index({0}, '-', -2), '(\\\\d{1,2})',0))"
//For SEATING product : check if is_chargeable info is avalaible , if not check if seating info get a CH characteristic : false-N-CH-A => false, true-N-CH-A => true, _-N-CH-A => true, _-N-A => false
isSeatChargeable: "if(startswith({0},'_'),contains({0},'-CH-') or endswith({0},'-CH') ,boolean(split_part({0},'-',1)))"
sanitizeFloatValue: "float({0})"
//sanitization of createConsolidatedValueExternalDependency with float conversion
sanitizeFloatCreateConsolidatedValueExternalDependency: "float(if(element_at(split({0},'-'),1) == '_', if(element_at(split({0},'-'),3) == '_', null, element_at(split({0},'-'),3)), if(element_at(split({0},'-'),2) == '_', null, element_at(split({0},'-'),2)) ))"

"defaultComment" : "PNR star schema, V2 (JSON-To-Star)",

"ruleset": {
  "data-dictionary": {
    "json-to-yaml-paths": {
      "$.mainResource.current.image": "PnrPushFeed.processedPnr"
    }
  }
},

"tables": [
  {
    "name": "FACT_RESERVATION",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_RESERVATION_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_RESERVATION_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Reservation",
    "subdomain-main-table": "true",
    "mapping": {
      "description": {
        "description": "Contains information related to the PNR itself, accompanied by information on group bookings if any.",
        "granularity": "1 PNR"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "VERSION"], // "RESERVATION_ID" unicity contained in "INTERNAL_ZORDER" ? (to check with Martin)
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.reference"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Functional key: recordLocator-pnrCreationDate", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "RECORD_LOCATOR", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.reference"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "PNR_PROPERTIES", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.pnrProperties[*]"}]},
          "meta": {"example": {"value": "GROUP-INTERNATIONAL", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "NUMBER_IN_PARTY", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.nip"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "GROUP_SIZE", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.group.size"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "GROUP_NAME", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.group.name"}]},
          "meta": {"gdpr-zone": "red"}},
        {"name": "GROUP_SIZE_TAKEN", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.group.sizeTaken"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "GROUP_SIZE_REMAINING", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.group.sizeRemaining"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "OWNER_OFFICE_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.owner.office.id"}]},
          "meta": {"description": {"value": "AMID - office id in Amadeus (representing the PNR owner)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CREATION_DATETIME_UTC", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "CREATION_OFFICE_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.pointOfSale.office.id"}]},
          "meta": {"description": {"value": "AMID - office id in Amadeus (representing the PNR creator)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CREATION_COMMENT", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.comment"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "LAST_UPDATE_DATETIME_UTC", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "LAST_UPDATE_OFFICE_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.lastModification.pointOfSale.office.id"}]},
          "meta": {"description": {"value": "AMID - office id in Amadeus (representing the PNR updater)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "LAST_UPDATE_COMMENT", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.lastModification.comment"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "QUEUING_OFFICE_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.queuingOffice.id"}]},
          "meta": {"description": {"value": "AMID - office id in Amadeus (representing the queuing office)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "OWNER_POINT_OF_SALE_ID", "column-type": "binaryStrColumn",
          "sources": {"blocks": [{"base": "$.owner.office.id"}, {"base": "$.owner.office.iataNumber"}, {"base": "$.owner.office.systemCode"}, {"base": "$.owner.office.agentType"}, {"base": "$.owner.login.cityCode"}, {"base": "$.owner.login.countryCode"}, {"base": "$.owner.login.numericSign"}, {"base": "$.owner.login.initials"}, {"base": "$.owner.login.dutyCode"}]}, "expr": "hashM({0})",
          "fk": [{"table": "DIM_POINT_OF_SALE", "column": "POINT_OF_SALE_ID"}],
          "meta": {"description": {"value": "Hashed foreign key (representing the PNR owner)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CREATION_POINT_OF_SALE_ID", "column-type": "binaryStrColumn",
          "sources": {"blocks": [{"base": "$.creation.pointOfSale.office.id"}, {"base": "$.creation.pointOfSale.office.iataNumber"}, {"base": "$.creation.pointOfSale.office.systemCode"}, {"base": "$.creation.pointOfSale.office.agentType"}, {"base": "$.creation.pointOfSale.login.cityCode"}, {"base": "$.creation.pointOfSale.login.countryCode"}, {"base": "$.creation.pointOfSale.login.numericSign"}, {"base": "$.creation.pointOfSale.login.initials"}, {"base": "$.creation.pointOfSale.login.dutyCode"}]}, "expr": "hashM({0})",
          "fk": [{"table": "DIM_POINT_OF_SALE", "column":"POINT_OF_SALE_ID"}],
          "meta": {"description": {"value": "Hashed foreign key (representing the PNR creator)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "LAST_UPDATE_POINT_OF_SALE_ID", "column-type": "binaryStrColumn",
          "sources": {"blocks": [{"base": "$.lastModification.pointOfSale.office.id"}, {"base": "$.lastModification.pointOfSale.office.iataNumber"}, {"base": "$.lastModification.pointOfSale.office.systemCode"}, {"base": "$.lastModification.pointOfSale.office.agentType"}, {"base": "$.lastModification.pointOfSale.login.cityCode"}, {"base": "$.lastModification.pointOfSale.login.countryCode"}, {"base": "$.lastModification.pointOfSale.login.numericSign"}, {"base": "$.lastModification.pointOfSale.login.initials"}, {"base": "$.lastModification.pointOfSale.login.dutyCode"}]}, "expr": "hashM({0})",
          "fk": [{"table": "DIM_POINT_OF_SALE", "column": "POINT_OF_SALE_ID"}],
          "meta": {"description": {"value": "Hashed foreign key (representing the last PNR updater)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "QUEUING_POINT_OF_SALE_ID", "column-type": "binaryStrColumn",
          "sources": {"blocks": [{"base": "$.queuingOffice.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "DIM_POINT_OF_SALE", "column": "POINT_OF_SALE_ID"}],
          "meta": {"description": {"value": "Hashed foreign key (representing the queuing office)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"description": {"value": "Envelope/version of the PNR message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]},
          "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "master-pit-table",
        "master" : {
          "pit-key": "INTERNAL_ZORDER",
          "pit-version": "VERSION",
          "valid-from": "DATE_BEGIN",
          "valid-to": "DATE_END",
          "is-last": "IS_LAST_VERSION"
        }
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_KEYWORD",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "latest": {
      "histo-table-name": "FACT_KEYWORD_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_KEYWORD_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "subdomain": "Reservation",
    "mapping": {
      "description": {
        "description": "Contains information related to PNR keywords, such as special keywords (SK) and Other Service Information (OSI).",
        "granularity": "1 PNR keyword"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "KEYWORD_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [
          {"base": "$.mainResource.current.image"},
          {"kw": "$.keywords[*]"}
        ]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.reference"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "KEYWORD_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"kw": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"kw": "$.id"}]},
          "meta": {"description": {"value": "Functional key: keywordId", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "SERVICE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"kw": "$.code"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "SERVICE_SUB_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"kw": "$.subType"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "SERVICE_PROVIDER", "column-type": "strColumn", "sources": {"blocks": [{"kw": "$.serviceProvider.code"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "TEXT", "column-type": "strColumn", "sources": {"blocks": [{"kw": "$.text"}]},
          "meta": {"gdpr-zone": "red"}},
        {"name": "STATUS", "column-type": "strColumn", "sources": {"blocks": [{"kw": "$.status"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "CREATION_DATETIME_UTC", "column-type": "timestampColumn", "sources": {"blocks": [{"kw": "$.creation.dateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "CREATION_OFFICE_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"kw": "$.creation.pointOfSale.office.id"}]},
          "meta": {"description": {"value": "AMID - office id in Amadeus (representing the creator of the keyword)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CREATION_COMMENT", "column-type": "strColumn", "sources": {"blocks": [{"kw": "$.creation.comment"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_RESERVATION_HISTO", "column": "RESERVATION_ID"}],
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "SERVICE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"kw": "$.code"}]}, "expr": "hashS({0})",
          "fk": [{"table": "DIM_SERVICE", "column": "SERVICE_ID"}]},
        {"name": "SERVICE_PROVIDER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"kw": "$.serviceProvider.code"}]}, "expr": "hashS({0})",
          "fk": [{"table": "DIM_AIRLINE", "column": "AIRLINE_ID"}]},
        {"name": "STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"kw": "$.status"}]}, "expr": "hashS({0})",
          "fk": [{"table": "DIM_BOOKING_STATUS", "column": "BOOKING_STATUS_ID"}]},
        {"name": "CREATION_POINT_OF_SALE_ID", "column-type": "binaryStrColumn",
          "sources": {"blocks": [{"kw": "$.creation.pointOfSale.office.id"}, {"kw": "$.creation.pointOfSale.office.iataNumber"}, {"kw": "$.creation.pointOfSale.office.systemCode"}, {"kw": "$.creation.pointOfSale.office.agentType"}, {"kw": "$.creation.pointOfSale.login.cityCode"}, {"kw": "$.creation.pointOfSale.login.countryCode"}, {"kw": "$.creation.pointOfSale.login.numericSign"}, {"kw": "$.creation.pointOfSale.login.initials"}, {"kw": "$.creation.pointOfSale.login.dutyCode"}]}, "expr": "hashM({0})",
          "fk": [{"table": "DIM_POINT_OF_SALE", "column":"POINT_OF_SALE_ID"}],
          "meta": {"description": {"value": "Hashed foreign key (representing the keyword creator)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_LOCATOR", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.reference"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"description": {"value": "Envelope/version of the PNR message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]},
          "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_REMARK",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "latest": {
      "histo-table-name": "FACT_REMARK_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_REMARK_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "subdomain": "Reservation",
    "mapping": {
      "description": {
        "description": "Contains information related to PNR remarks",
        "granularity": "1 PNR remark"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "REMARK_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [
          {"base": "$.mainResource.current.image"},
          {"rm": "$.remarks[*]"}
        ]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.reference"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "REMARK_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"rm": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"rm": "$.id"}]},
          "meta": {"description": {"value": "Functional key: remarkId", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "CATEGORY", "column-type": "strColumn", "sources": {"blocks": [{"rm": "$.category"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "SUB_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"rm": "$.subType"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "TEXT", "column-type": "strColumn", "sources": {"blocks": [{"rm": "$.content"}]},
          "meta": {"gdpr-zone": "red"}},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_RESERVATION_HISTO", "column": "RESERVATION_ID"}],
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_LOCATOR", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.reference"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"description": {"value": "Envelope/version of the PNR message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]},
          "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_ASSOCIATED_RESERVATION",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "latest": {
      "histo-table-name": "FACT_ASSOCIATED_RESERVATION_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_ASSOCIATED_RESERVATION_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "subdomain": "Reservation",
    "mapping": {
      "description": {
        "description": "Contains information on PNRs related to the current PNR. Can refer to the current PNR or a flight transfer.",
        "granularity": "1 related PNR"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "ASSOCIATED_RESERVATION_ID", "RELATES_TO", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"name": "pnr", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"assPnr": "$.associatedPnrs[*]"}, {"creation": "$.creation"}]}},
        {"name": "transfer", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"trans": "$.flightTransfer"}, {"assPnr": "$.associatedOrder"}, {"creation": "$.creation"}]}}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.reference"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "ASSOCIATED_RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"assPnr": "$.reference"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"assPnr": "$.reference"}]},
          "meta": {"description": {"value": "Functional key: reservationId-assoPnrRloc", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-GHIJKL", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "ASSOCIATION_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"assPnr": "$.associationType"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "ASSOCIATION_DIRECTION", "column-type": "strColumn", "sources": {"blocks": [{"assPnr": "$.direction"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "ASSOCIATED_RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"assPnr": "$.reference"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "ASSOCIATED_CREATION_DATETIME_UTC", "column-type": "timestampColumn", "sources": {"blocks": [{"creation": "$.dateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "ASSOCIATED_CREATION_SYSTEM_CODE", "column-type": "strColumn", "sources": {"blocks": [{"creation": "$.pointOfSale.office.systemCode"}]},
          "meta": {"gdpr-zone": "green"}},
        {
          "name": "RELATES_TO", "column-type": "strColumn",
          "sources": {
            "root-specific": [
              {"rs-name": "pnr", "literal": "RESERVATION"},
              {"rs-name": "transfer", "literal": "FLIGHT_TRANSFER"}
            ]
          },
          "meta": {"description": {"value": "Indicates to which parent fact table the record belongs to.", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_RESERVATION_HISTO", "column": "RESERVATION_ID"}],
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "FLIGHT_TRANSFER_ID", "column-type": "binaryStrColumn", "sources": {
          "root-specific": [{"rs-name": "transfer", "blocks": [{"base": "$.id"}, {"trans": "$.id"}]}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_FLIGHT_TRANSFER_HISTO", "column": "FLIGHT_TRANSFER_ID"}]},
        {"name": "ASSOCIATED_CREATION_POINT_OF_SALE_ID", "column-type": "binaryStrColumn",
          "sources": {"blocks": [{"creation": "$.pointOfSale.office.id"}, {"creation": "$.pointOfSale.office.iataNumber"}, {"creation": "$.pointOfSale.office.systemCode"}, {"creation": "$.pointOfSale.office.agentType"}, {"creation": "$.pointOfSale.login.cityCode"}, {"creation": "$.pointOfSale.login.countryCode"}, {"creation": "$.pointOfSale.login.numericSign"}, {"creation": "$.pointOfSale.login.initials"}, {"creation": "$.pointOfSale.login.dutyCode"}]}, "expr": "hashM({0})",
          "fk": [{"table": "DIM_POINT_OF_SALE", "column":"POINT_OF_SALE_ID"}],
          "meta": {"description": {"value": "Hashed foreign key (representing the associated PNR creator system)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_LOCATOR", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.reference"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"description": {"value": "Envelope/version of the PNR message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]},
          "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_TRAVELER",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_TRAVELER_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_TRAVELER_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Traveler",
    "subdomain-main-table": "true",
    "mapping": {
      "description": {
        "description": "Contains personal information on booked passengers of a PNR, such as name, age or passenger type.",
        "granularity": "1 passenger on 1 PNR"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "TRAVELER_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"tr": "$.travelers[*]"}]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.reference"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "TRAVELER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"tr":"$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"tr":"$.id"}]},
          "meta": {"description": {"value": "Functional key: paxId", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-PT-1", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "UNIQUE_CUSTOMER_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"tr": "$.passenger.uniqueIdentifier"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "TITLE", "column-type": "strColumn", "sources": {"blocks": [{"tr": "$.names[0].title"}]},
          "meta": {"description": {"value": "Title of the passenger", "rule": "replace"}, "example": {"value": "Mr", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "FIRST_NAME", "column-type": "strColumn", "sources": {"blocks": [{"tr": "$.names[0].firstName"}]},
          "meta": {"description": {"value": "First name of the passenger", "rule": "replace"}, "example": {"value": "John", "rule": "replace"}, "gdpr-zone": "red"}},
        {"name": "LAST_NAME", "column-type": "strColumn", "sources": {"blocks": [{"tr": "$.names[0].lastName"}]},
          "meta": {"description": {"value": "Last name of the passenger", "rule": "replace"}, "example": {"value": "Doe", "rule": "replace"}, "gdpr-zone": "red"}},
        {"name": "FULL_NAME", "column-type": "strColumn", "sources": {"blocks": [{"tr": "$.names[0].fullName"}]},
          "meta": {"description": {"value": "Full name of the passenger (only populated for passenger deliveries)", "rule": "replace"}, "example": {"value": "John Doe", "rule": "replace"}, "gdpr-zone": "red"}},
        {"name": "PASSENGER_TYPE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"tr": "$.passengerTypeCode"}]},
          "meta": {"gdpr-zone": "red"}},
        {"name": "GENDER", "column-type": "strColumn", "sources": {"blocks": [{"tr": "$.gender"}]},
          "meta": {"gdpr-zone": "green"}},
        //no JSON sample found in MH/EY PRD - the only nationality info are in identityDocuments[code=DOCS].document.nationality, and products.airSegment.deliveries.activeIdentityDocument.nationality related to that passenger (pax tattoo)
        {"name": "NATIONALITY", "column-type": "strColumn", "sources": {"blocks": [{"tr": "$.nationality"}]},
          "meta": {"example": {"value": "FR", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "BIRTH_DATE", "column-type": "dateColumn", "sources": {"blocks": [{"tr": "$.dateOfBirth"}]},
          "meta": {"description": {"value": "Birth date of the passenger", "rule": "replace"}, "gdpr-zone": "red"}},
        //no JSON sample found in MH/EY PRD - the only birth place info are in products.airSegment.deliveries.activeIdentityDocument.placeOfBirth related to that passenger (pax tattoo)
        {"name": "BIRTH_PLACE", "column-type": "strColumn", "sources": {"blocks": [{"tr": "$.placeOfBirth"}]},
          "meta": {"gdpr-zone": "red"}},
        //no JSON sample found in MH/EY PRD - the only country of residence info are in products.airSegment.deliveries.activeIdentityDocument.countryOfResidence related to that passenger (pax tattoo)
        {"name": "COUNTRY_OF_RESIDENCE", "column-type": "strColumn", "sources": {"blocks": [{"tr": "$.countryOfResidence"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "IDENTIFICATION_CODE", "column-type": "strColumn", "sources": {"blocks": [{"tr": "$.identificationCode"}]}, "meta": {"gdpr-zone": "red"}},
        {"name": "STAFF_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"tr": "$.staffType"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SPECIAL_SEAT", "column-type": "strColumn", "sources": {"blocks": [{"tr": "$.passenger.specialSeat"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_RESERVATION_HISTO", "column": "RESERVATION_ID"}],
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RELATED_INFANT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"tr": "$.infant.id"}]}, "expr": "hashM({0})", "fk": [{"table": "FACT_TRAVELER_HISTO", "column": "TRAVELER_ID"}]},
        {"name": "RELATED_ADULT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"tr": "$.adult.id"}]}, "expr": "hashM({0})", "fk": [{"table": "FACT_TRAVELER_HISTO", "column": "TRAVELER_ID"}]},
        {"name": "RELATED_CBBG_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"tr": "$.passenger.cbbg.id"}]}, "expr": "hashM({0})", "fk": [{"table": "FACT_TRAVELER_HISTO", "column": "TRAVELER_ID"}]},
        {"name": "RELATED_EXST_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"tr": "$.passenger.exst.id"}]}, "expr": "hashM({0})", "fk": [{"table": "FACT_TRAVELER_HISTO", "column": "TRAVELER_ID"}]},
        {"name": "RELATED_OWNER_CBBG_EXST_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"tr": "$.passenger.owner.id"}]}, "expr": "hashM({0})", "fk": [{"table": "FACT_TRAVELER_HISTO", "column": "TRAVELER_ID"}]},
        {"name": "PASSENGER_TYPE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"tr": "$.passengerTypeCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_PASSENGER_TYPE", "column": "PASSENGER_TYPE_ID"}]},
        {"name": "RECORD_LOCATOR", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.reference"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"description": {"value": "Envelope/version of the PNR message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]},
          "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_ADDRESS",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "latest": {
      "histo-table-name": "FACT_ADDRESS_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_ADDRESS_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "subdomain": "Traveler",
    "mapping": {
      "description": {
        "description": "Contains information on postal addresses mentioned as PNR contact, which can apply to one or many passengers",
        "granularity": "1 postal address"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "ADDRESS_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"cont": "$.contacts[*]"}, {"add": "$.address"}]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.reference"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "ADDRESS_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"cont":"$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"cont":"$.id"}]},
          "meta": {"description": {"value": "Functional key: contactId", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-OT-100", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "CATEGORY", "column-type": "strColumn", "sources": {"blocks": [{"add": "$.category"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "LINES", "column-type": "strColumn", "sources": {"blocks": [{"add": "$.lines[*]"}]},
          "meta": {"gdpr-zone": "red"}},
        {"name": "POSTAL_BOX", "column-type": "strColumn", "sources": {"blocks": [{"add": "$.postalBox"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "POSTAL_CODE", "column-type": "strColumn", "sources": {"blocks": [{"add": "$.postalCode"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "CITY_NAME", "column-type": "strColumn", "sources": {"blocks": [{"add": "$.cityName"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "STATE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"add": "$.stateCode"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "COUNTRY_CODE", "column-type": "strColumn", "sources": {"blocks": [{"add": "$.countryCode"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "ADDRESSEE_NAME", "column-type": "strColumn", "sources": {"blocks": [{"cont": "$.addresseeName.fullName"}]},
          "meta": {"description": {"value": "The name of the person/company addressed by these contact details", "rule": "replace"}, "gdpr-zone": "red"}},
        {"name": "PURPOSE", "column-type": "strColumn", "sources": {"blocks": [{"cont": "$.purpose"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "LANGUAGE", "column-type": "strColumn", "sources": {"blocks": [{"cont": "$.language"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "TEXT", "column-type": "strColumn", "sources": {"blocks": [{"cont": "$.freeFlowFormat"}]},
          "meta": {"gdpr-zone": "red"}},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_RESERVATION_HISTO", "column": "RESERVATION_ID"}],
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_LOCATOR", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.reference"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"description": {"value": "Envelope/version of the PNR message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]},
          "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_PHONE",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "latest": {
      "histo-table-name": "FACT_PHONE_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_PHONE_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "subdomain": "Traveler",
    "mapping": {
      "description": {
        "description": "Contains information on phone numbers mentioned as PNR contact, which can apply to one or many passengers",
        "granularity": "1 phone number"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "PHONE_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"cont": "$.contacts[*]"}, {"phone": "$.phone"}]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.reference"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "PHONE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"cont":"$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"cont":"$.id"}]},
          "meta": {"description": {"value": "Functional key: contactId", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-OT-100", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "CATEGORY", "column-type": "strColumn", "sources": {"blocks": [{"phone": "$.category"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"phone": "$.number"}]},
          "meta": {"gdpr-zone": "red"}},
        {"name": "DEVICE_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"phone": "$.deviceType"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "ADDRESSEE_NAME", "column-type": "strColumn", "sources": {"blocks": [{"cont": "$.addresseeName.fullName"}]},
          "meta": {"description": {"value": "The name of the person/company addressed by these contact details", "rule": "replace"}, "gdpr-zone": "red"}},
        {"name": "PURPOSE", "column-type": "strColumn", "sources": {"blocks": [{"cont": "$.purpose"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "LANGUAGE", "column-type": "strColumn", "sources": {"blocks": [{"cont": "$.language"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "TEXT", "column-type": "strColumn", "sources": {"blocks": [{"cont": "$.freeFlowFormat"}]},
          "meta": {"gdpr-zone": "red"}},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_RESERVATION_HISTO", "column": "RESERVATION_ID"}],
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_LOCATOR", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.reference"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"description": {"value": "Envelope/version of the PNR message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]},
          "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_EMAIL",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "latest": {
      "histo-table-name": "FACT_EMAIL_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_EMAIL_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "subdomain": "Traveler",
    "mapping": {
      "description": {
        "description": "Contains information on email addresses mentioned as PNR contact, which can apply to one or many passengers",
        "granularity": "1 email address"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "EMAIL_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"cont": "$.contacts[*]"}, {"email": "$.email"}]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.reference"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "EMAIL_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"cont":"$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"cont":"$.id"}]},
          "meta": {"description": {"value": "Functional key: contactId", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-OT-100", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "EMAIL", "column-type": "strColumn", "sources": {"blocks": [{"email": "$.address"}]},
          "meta": {"gdpr-zone": "red"}},
        {"name": "ADDRESSEE_NAME", "column-type": "strColumn", "sources": {"blocks": [{"cont": "$.addresseeName.fullName"}]},
          "meta": {"description": {"value": "The name of the person/company addressed by these contact details", "rule": "replace"}, "gdpr-zone": "red"}},
        {"name": "PURPOSE", "column-type": "strColumn", "sources": {"blocks": [{"cont": "$.purpose"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "LANGUAGE", "column-type": "strColumn", "sources": {"blocks": [{"cont": "$.language"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "TEXT", "column-type": "strColumn", "sources": {"blocks": [{"cont": "$.freeFlowFormat"}]},
          "meta": {"gdpr-zone": "red"}},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_RESERVATION_HISTO", "column": "RESERVATION_ID"}],
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_LOCATOR", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.reference"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"description": {"value": "Envelope/version of the PNR message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]},
          "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_IDENTITY_DOCUMENT_PAX",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "latest": {
      "histo-table-name": "FACT_IDENTITY_DOCUMENT_PAX_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_IDENTITY_DOCUMENT_PAX_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "subdomain": "Traveler",
    "mapping": {
      "description": {
        "description": "Contains regulatory/identity documents of travelers, such as passports and visa.",
        "granularity": "1 document for 1 passenger"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "IDENTITY_DOCUMENT_PAX_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"traveler": "$.travelers[*]"}, {"doc": "$.identityDocuments[*]"}]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.reference"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "IDENTITY_DOCUMENT_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"doc": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"traveler": "$.id"}, {"doc": "$.id"}]},
          "meta": {"description": {"value": "Functional key: docId", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-OT-80", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "FIRST_NAME", "column-type": "strColumn", "sources": {"blocks": [{"doc": "$.document.name.firstName"}]},
          "meta": {"description": {"value": "First name mentioned on the identity document", "rule": "replace"}, "gdpr-zone": "red"}},
        {"name": "LAST_NAME", "column-type": "strColumn", "sources": {"blocks": [{"doc": "$.document.name.lastName"}]},
          "meta": {"description": {"value": "Last name mentioned on the identity document", "rule": "replace"}, "gdpr-zone": "red"}},
        {"name": "FULL_NAME", "column-type": "strColumn", "sources": {"blocks": [{"doc": "$.document.name.fullName"}]},
          "meta": {"gdpr-zone": "red"}},
        {"name": "BIRTH_DATE", "column-type": "dateColumn", "sources": {"blocks": [{"doc": "$.document.birthDate"}]},
          "meta": {"gdpr-zone": "red"}},
        {"name": "BIRTH_PLACE", "column-type": "strColumn", "sources": {"blocks": [{"doc": "$.document.birthPlace"}]},
          "meta": {"gdpr-zone": "red"}},
        {"name": "NATIONALITY", "column-type": "strColumn", "sources": {"blocks": [{"doc": "$.document.nationality"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "GENDER", "column-type": "strColumn", "sources": {"blocks": [{"doc": "$.document.gender"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "DOCUMENT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"doc": "$.document.number"}]},
          "meta": {"gdpr-zone": "red"}},
        {"name": "DOCUMENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"doc": "$.document.documentType"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "ISSUANCE_DATE", "column-type": "dateColumn", "sources": {"blocks": [{"doc": "$.document.issuanceDate"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "ISSUANCE_LOCATION", "column-type": "strColumn", "sources": {"blocks": [{"doc": "$.document.issuanceLocation"}]},
          "meta": {"example": {"value": "LONDON", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "ISSUANCE_COUNTRY", "column-type": "strColumn", "sources": {"blocks": [{"doc": "$.document.issuanceCountry"}]},
          "meta": {"gdpr-zone": "green"}},
        // String column because expiry date is not standard, stored in 4 digits
        {"name": "EXPIRY_DATE", "column-type": "strColumn", "sources": {"blocks": [{"doc": "$.document.expiryDate"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "SERVICE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"doc": "$.code"}]},
          "meta": {"example": {"value": "DOCS", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "SERVICE_SUB_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"doc": "$.subType"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "SERVICE_PROVIDER", "column-type": "strColumn", "sources": {"blocks": [{"doc": "$.serviceProvider.code"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "SERVICE_TEXT", "column-type": "strColumn", "sources": {"blocks": [{"doc": "$.text"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "SERVICE_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"doc": "$.status"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "CREATION_DATETIME_UTC", "column-type": "timestampColumn", "sources": {"blocks": [{"doc": "$.creation.dateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "CREATION_OFFICE_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"doc": "$.creation.pointOfSale.office.id"}]},
          "meta": {"description": {"value": "AMID - office id in Amadeus (representing the creator of the identity document)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CREATION_COMMENT", "column-type": "strColumn", "sources": {"blocks": [{"doc": "$.creation.comment"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_RESERVATION_HISTO", "column": "RESERVATION_ID"}],
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "TRAVELER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"traveler": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_TRAVELER_HISTO", "column": "TRAVELER_ID"}]},
        {"name": "SERVICE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"doc": "$.code"}]}, "expr": "hashS({0})",
          "fk": [{"table": "DIM_SERVICE", "column": "SERVICE_ID"}]},
        {"name": "SERVICE_PROVIDER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"doc": "$.serviceProvider.code"}]}, "expr": "hashS({0})",
          "fk": [{"table": "DIM_AIRLINE", "column": "AIRLINE_ID"}]},
        {"name": "SERVICE_STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"doc": "$.status"}]}, "expr": "hashS({0})",
          "fk": [{"table": "DIM_BOOKING_STATUS", "column": "BOOKING_STATUS_ID"}]},
        {"name": "CREATION_POINT_OF_SALE_ID", "column-type": "binaryStrColumn",
          "sources": {"blocks": [{"doc": "$.creation.pointOfSale.office.id"}, {"doc": "$.creation.pointOfSale.office.iataNumber"}, {"doc": "$.creation.pointOfSale.office.systemCode"}, {"doc": "$.creation.pointOfSale.office.agentType"}, {"doc": "$.creation.pointOfSale.login.cityCode"}, {"doc": "$.creation.pointOfSale.login.countryCode"}, {"doc": "$.creation.pointOfSale.login.numericSign"}, {"doc": "$.creation.pointOfSale.login.initials"}, {"doc": "$.creation.pointOfSale.login.dutyCode"}]}, "expr": "hashM({0})",
          "fk": [{"table": "DIM_POINT_OF_SALE", "column":"POINT_OF_SALE_ID"}],
          "meta": {"description": {"value": "Hashed foreign key (representing the identity document creator)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_LOCATOR", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.reference"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"description": {"value": "Envelope/version of the PNR message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]},
          "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_AIR_SEGMENT_PAX",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_AIR_SEGMENT_PAX_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_AIR_SEGMENT_PAX_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Segment Booking",
    "subdomain-main-table": "true",
    "mapping": {
      "description": {
        "description": "Contains information on a flight segment booking of a given passenger, such as flight information (marketing, operating), cabin and booking classes, booking status and once the segment is delivered also certain high-level DCS information (PDI = product delivery information).",
        "granularity": "1 flight segment booking for 1 passenger"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "AIR_SEGMENT_PAX_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {
          "blocks": [
            //take travelers directly from PNR instead of from the segments because occasionally -INF travelers are not defined at segment level but are always defined at traveler level
            {"base": "$.mainResource.current.image"},
            {
              "cartesian": [
                [{"seg": "$.products[?(@.subType == 'AIR')]"}],
                [{"tr": "$.travelers[*]"}]
              ]
            }
          ]
        }
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.reference"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "AIR_SEGMENT_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.id"}, {"tr": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.id"}, {"tr": "$.id"}]},
          "meta": {"description": {"value": "Functional key: segmentId-paxId", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-ST-1-ABCDEF-2019-10-05-PT-1", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "DEPARTURE_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.airSegment.departure.iataCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DEPARTURE_TERMINAL", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.airSegment.departure.terminal"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "ARRIVAL_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.airSegment.arrival.iataCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "ARRIVAL_TERMINAL", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.airSegment.arrival.terminal"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DEPARTURE_DATETIME_UTC", "column-type": "timestampColumn", "sources": {"blocks": [{"seg": "$.airSegment.departure.dateTime"}]},
          "meta": {"description": {"value": "GMT date & time", "rule": "replace"}, "example": {"value": "2019-10-05T10:00:00Z", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DEPARTURE_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"seg": "$.airSegment.departure.localDateTime"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "ARRIVAL_DATETIME_UTC", "column-type": "timestampColumn", "sources": {"blocks": [{"seg": "$.airSegment.arrival.dateTime"}]},
          "meta": {"description": {"value": "GMT date & time", "rule": "replace"}, "example": {"value": "2019-10-05T10:00:00Z", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "ARRIVAL_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"seg": "$.airSegment.arrival.localDateTime"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "MKT_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.airSegment.marketing.flightDesignator.carrierCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "MKT_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.airSegment.marketing.flightDesignator.flightNumber"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "MKT_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.airSegment.marketing.flightDesignator.operationalSuffix"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "OPE_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.airSegment.operating.flightDesignator.flightNumber"}, {"seg": "$.airSegment.operating.flightDesignator.carrierCode"}, {"seg": "$.airSegment.marketing.flightDesignator.carrierCode"}]}, "expr": ${createConsolidatedValueExternalDependency},
          "meta": {"description": {"value": "Operating information of the flight. For prime flights, copied from marketing information if not explicitly received.", "rule": "concat"}, "example": {"value": "6X", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "OPE_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.airSegment.operating.flightDesignator.flightNumber"}, {"seg": "$.airSegment.operating.flightDesignator.flightNumber"}, {"seg": "$.airSegment.marketing.flightDesignator.flightNumber"}]}, "expr": ${createConsolidatedValueExternalDependency},
          "meta": {"description": {"value": "Operating information of the flight. For prime flights, copied from marketing information if not explicitly received.", "rule": "concat"}, "example": {"value": "1234", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "OPE_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.airSegment.operating.flightDesignator.flightNumber"}, {"seg": "$.airSegment.operating.flightDesignator.operationalSuffix"}, {"seg": "$.airSegment.marketing.flightDesignator.operationalSuffix"}]}, "expr": ${createConsolidatedValueExternalDependency},
          "meta": {"description": {"value": "Operating information of the flight. For prime flights, copied from marketing information if not explicitly received.", "rule": "concat"}, "example": {"value": "D", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "BOOKING_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.airSegment.bookingStatusCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "MKT_CABIN", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.airSegment.marketing.bookingClass.cabin.code"}]}, "meta": {"example": {"value": "Y", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "MKT_BOOKING_CLASS", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.airSegment.marketing.bookingClass.code"}]}, "meta": {"example": {"value": "T", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "MKT_BOOKING_SUB_CLASS", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.airSegment.marketing.bookingClass.subClass.code"}]},
          "meta": {"example": {"value": "5", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "MKT_LEVEL_OF_SERVICE", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.airSegment.marketing.bookingClass.levelOfService"}]},
          "meta": {"example": {"value": "ECONOMY", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "MKT_OVERBOOK_REASON", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.airSegment.marketing.overBookingReason"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "MKT_CABIN_BID_PRICE_AMOUNT_ORIGINAL", "column-type": "floatColumn", "sources": {"blocks": [{"seg": "$.airSegment.marketing.bookingClass.cabin.bidPrice.amount"}]}, "expr": ${sanitizeFloatValue}, "meta": {"gdpr-zone": "green"}},
        {"name": "MKT_CABIN_BID_PRICE_CURRENCY_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.airSegment.marketing.bookingClass.cabin.bidPrice.currency"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "OPE_CABIN", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.airSegment.operating.flightDesignator.flightNumber"}, {"seg": "$.airSegment.operating.bookingClass.cabin.code"}, {"seg": "$.airSegment.marketing.bookingClass.cabin.code"}]}, "expr": ${createConsolidatedValueExternalDependency},
          "meta": {"description": {"value": "Operating information of the flight. For prime flights, copied from marketing information if not explicitly received.", "rule": "concat"}, "example": {"value": "C", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "OPE_BOOKING_CLASS", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.airSegment.operating.flightDesignator.flightNumber"}, {"seg": "$.airSegment.operating.bookingClass.code"}, {"seg": "$.airSegment.marketing.bookingClass.code"}]}, "expr": ${createConsolidatedValueExternalDependency},
          "meta": {"description": {"value": "Operating information of the flight. For prime flights, copied from marketing information if not explicitly received.", "rule": "concat"}, "example": {"value": "S", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "OPE_BOOKING_SUB_CLASS", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.airSegment.operating.flightDesignator.flightNumber"}, {"seg": "$.airSegment.operating.bookingClass.subClass.code"}, {"seg": "$.airSegment.marketing.bookingClass.subClass.code"}]}, "expr": ${createConsolidatedValueExternalDependency},
          "meta": {"description": {"value": "Operating information of the flight. For prime flights, copied from marketing information if not explicitly received.", "rule": "concat"}, "example": {"value": "5", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "OPE_LEVEL_OF_SERVICE", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.airSegment.operating.flightDesignator.flightNumber"}, {"seg": "$.airSegment.operating.bookingClass.levelOfService"}, {"seg": "$.airSegment.marketing.bookingClass.levelOfService"}]}, "expr": ${createConsolidatedValueExternalDependency},
          "meta": {"description": {"value": "Operating information of the flight. For prime flights, copied from marketing information if not explicitly received.", "rule": "concat"}, "example": {"value": "ECONOMY", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "OPE_OVERBOOK_REASON", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.airSegment.operating.flightDesignator.flightNumber"}, {"seg": "$.airSegment.operating.overBookingReason"}, {"seg": "$.airSegment.marketing.overBookingReason"}]}, "expr": ${createConsolidatedValueExternalDependency},
          "meta": {"description": {"value": "Operating information of the flight. For prime flights, copied from marketing information if not explicitly received.", "rule": "concat"}, "example": {"value": "OVERBOOKING_BY_REACCOMODATION", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "OPE_CODESHARE_AGREEMENT", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.airSegment.operating.codeshareAgreement"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "OPE_CABIN_BID_PRICE_AMOUNT_ORIGINAL", "column-type": "floatColumn", "sources": {"blocks": [{"seg": "$.airSegment.operating.flightDesignator.flightNumber"}, {"seg": "$.airSegment.operating.bookingClass.cabin.bidPrice.amount"}, {"seg": "$.airSegment.marketing.bookingClass.cabin.bidPrice.amount"}]}, "expr": ${sanitizeFloatCreateConsolidatedValueExternalDependency},
          "meta": {"description": {"value": "Operating information of the flight. For prime flights, copied from marketing information if not explicitly received.", "rule": "concat"}, "example": {"value": "98.5", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "OPE_CABIN_BID_PRICE_CURRENCY_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.airSegment.operating.flightDesignator.flightNumber"}, {"seg": "$.airSegment.operating.bookingClass.cabin.bidPrice.currency"}, {"seg": "$.airSegment.marketing.bookingClass.cabin.bidPrice.currency"}]}, "expr": ${createConsolidatedValueExternalDependency},
          "meta": {"description": {"value": "Operating information of the flight. For prime flights, copied from marketing information if not explicitly received.", "rule": "concat"}, "example": {"value": "EUR", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_INFORMATIONAL", "column-type": "booleanColumn", "sources": {"blocks": [{"seg": "$.airSegment.isInformational"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "IS_DOMINANT_IN_MARRIAGE", "column-type": "booleanColumn", "sources": {"blocks": [{"seg": "$.airSegment.isDominantInMarriage"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "IS_OPEN_SEGMENT", "column-type": "booleanColumn", "sources": {"blocks": [{"seg": "$.airSegment.marketing.isOpenNumber"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "AIRCRAFT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.airSegment.aircraft.aircraftType"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CREATION_DATETIME_UTC", "column-type": "timestampColumn", "sources": {"blocks": [{"seg": "$.airSegment.creation.dateTime"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CREATION_OFFICE_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.airSegment.creation.pointOfSale.office.id"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CREATION_COMMENT", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.airSegment.creation.comment"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_RESERVATION_HISTO", "column": "RESERVATION_ID"}],
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "AIR_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the segment id, used as foreign key target from correlation", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "TRAVELER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"tr" : "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_TRAVELER_HISTO", "column": "TRAVELER_ID"}]},
        {"name": "BOOKING_STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"seg": "$.airSegment.bookingStatusCode"}]}, "expr": "hashS({0})",
          "fk": [{"table": "DIM_BOOKING_STATUS", "column": "BOOKING_STATUS_ID"}]},
        {"name": "DEPARTURE_AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"seg": "$.airSegment.departure.iataCode"}]}, "expr": "hashS({0})",
          "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}]},
        {"name": "ARRIVAL_AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"seg": "$.airSegment.arrival.iataCode"}]}, "expr": "hashS({0})",
          "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}]},
        {"name": "MKT_CARRIER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"seg": "$.airSegment.marketing.flightDesignator.carrierCode"}]}, "expr": "hashS({0})",
          "fk": [{"table": "DIM_AIRLINE", "column": "AIRLINE_ID"}]},
        {"name": "OPE_CARRIER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"seg": "$.airSegment.operating.flightDesignator.flightNumber"}, {"seg": "$.airSegment.operating.flightDesignator.carrierCode"}, {"seg": "$.airSegment.marketing.flightDesignator.carrierCode"}]}, "expr": hashS(${createConsolidatedValueExternalDependency}),
          "fk": [{"table": "DIM_AIRLINE", "column": "AIRLINE_ID"}],
          "meta": {"description": {"value": "Operating information of the flight. For prime flights, copied from marketing information if not explicitly received.", "rule": "concat"}, "gdpr-zone": "green"}},
        {
          "name": "CREATION_POINT_OF_SALE_ID", "column-type": "binaryStrColumn",
          "sources": {"blocks": [
            {"seg": "$.airSegment.creation.pointOfSale.office.id"},
            {"seg": "$.airSegment.creation.pointOfSale.office.iataNumber"},
            {"seg": "$.airSegment.creation.pointOfSale.office.systemCode"},
            {"seg": "$.airSegment.creation.pointOfSale.office.agentType"},
            {"seg": "$.airSegment.creation.pointOfSale.login.cityCode"},
            {"seg": "$.airSegment.creation.pointOfSale.login.countryCode"},
            {"seg": "$.airSegment.creation.pointOfSale.login.numericSign"},
            {"seg": "$.airSegment.creation.pointOfSale.login.initials"},
            {"seg": "$.airSegment.creation.pointOfSale.login.dutyCode"}
          ]},
          "expr": "hashM({0})",
          "fk": [{"table": "DIM_POINT_OF_SALE", "column":"POINT_OF_SALE_ID"}]
        },
        {"name": "RECORD_LOCATOR", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.reference"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"description": {"value": "Envelope/version of the PNR message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]},
          "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_FLIGHT_TRANSFER",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "latest": {
      "histo-table-name": "FACT_FLIGHT_TRANSFER_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_FLIGHT_TRANSFER_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "subdomain": "Segment Booking",
    "mapping": {
      "description": {
        "description": "Contains details on flight transfers (disruption) for a booked passenger",
        "granularity": "1 flight transfer"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "FLIGHT_TRANSFER_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [
          {"base": "$.mainResource.current.image"},
          {"ft": "$.flightTransfer"}
        ]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.reference"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "FLIGHT_TRANSFER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"},{"ft": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"},{"ft": "$.id"}]},
          "meta": {"description": {"value": "Functional key: reservationId-transferId", "rule": "replace"}, "example": {"value": "ABCDEF-2023-01-01-32101860", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "TRANSFER_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"ft": "$.id"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "SUB_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"ft": "$.subType"}]},
          "meta": {"description": {"value": "Indicates the kind of reaccommodation", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_RESERVATION_HISTO", "column": "RESERVATION_ID"}],
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_LOCATOR", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.reference"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"description": {"value": "Envelope/version of the PNR message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]},
          "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_ORIGIN_DESTINATION",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "latest": {
      "histo-table-name": "FACT_ORIGIN_DESTINATION_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_ORIGIN_DESTINATION_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "subdomain": "Segment Booking",
    "mapping": {
      "description": {
        "description": "Contains the origin-destinations (O&D) of passenger's trip",
        "granularity": "1 O&D for 1 booked passenger"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "ORIGIN_DESTINATION_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"ond": "$.flightItinerary[*]"}]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.reference"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "ORIGIN_DESTINATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"ond": "$.id"}]}, "expr": "hashM({0})",
            "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"ond": "$.id"}]},
            "meta": {"description": {"value": "Functional key: reservationId-connectionId", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-SELL_AVAILABILITY-ST-1-ST-2", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "ORIGIN", "column-type": "strColumn", "sources": {"blocks": [{"ond": "$.originIataCode"}]},
            "meta": {"description": {"value": "Origin of the OnD, only filled for type SELL_AVAILIBILITY", "rule": "replace"}, "example": {"value": "HKG", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DESTINATION", "column-type": "strColumn", "sources": {"blocks": [{"ond": "$.destinationIataCode"}]},
            "meta": {"description": {"value": "Destination of the OnD, only filled for type SELL_AVAILIBILITY", "rule": "replace"}, "example": {"value": "KUL", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "TYPE", "column-type": "strColumn", "sources": {"blocks": [{"ond": "$.type"}]},
            "meta": {"description": {"value": "Type of OnD : CONNECTION or SELL_AVAILIBILITY", "rule": "replace"}, "example": {"value": "SELL_AVAILIBILITY", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "OND_YIELD_TOTAL_ORIGINAL", "column-type": "floatColumn", "sources": {"blocks": [{"ond": "$.yield.amount"}]},
            "meta": {"description": {"value": "Yield value for this OnD, only filled for type SELL_AVAILIBILITY ", "rule": "replace"}, "example": {"value": "139.94", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "OND_YIELD_CURRENCY_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"ond": "$.yield.currency"}]},
            "meta": {"description": {"value": "Currency of yield value, may not be filled", "rule": "replace"}, "example": {"value": "EUR", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "COMMENCEMENT_IATA_CODE", "column-type": "strColumn", "sources": {"blocks": [{"ond": "$.pointOfCommencement.iataCode"}]},
            "meta": {"description": {"value": "Point of commencement IATA code, may not be filled", "rule": "replace"}, "example": {"value": "CDG", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "COMMENCEMENT_NAME", "column-type": "strColumn", "sources": {"blocks": [{"ond": "$.pointOfCommencement.name"}]},
            "meta": {"description": {"value": "Point of commencement name, may not be filled", "rule": "replace"}, "example": {"value": "Charles de Gaulle", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "COMMENCEMENT_SUB_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"ond": "$.pointOfCommencement.subType"}]},
            "meta": {"description": {"value": "Point of commencement type, may not be filled", "rule": "replace"}, "example": {"value": "airport", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "COMMENCEMENT_COUNTRY", "column-type": "strColumn", "sources": {"blocks": [{"ond": "$.pointOfCommencement.address.countryCode"}]},
            "meta": {"description": {"value": "Point of commencement country, may not be filled", "rule": "replace"}, "example": {"value": "FR", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "COMMENCEMENT_TIME_ZONE", "column-type": "strColumn", "sources": {"blocks": [{"ond": "$.pointOfCommencement.timeZoneName"}]},
            "meta": {"description": {"value": "Point of commencement time zone, may not be filled", "rule": "replace"}, "example": {"value": "Europe/Paris", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_RESERVATION_HISTO", "column": "RESERVATION_ID"}],
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_LOCATOR", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.reference"}]},
            "meta": {"gdpr-zone": "orange"}},
        {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
            "meta": {"gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
            "meta": {"description": {"value": "Envelope/version of the PNR message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]},
            "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
            "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
            "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
          "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
        "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_FLIGHT_BOUND_PAX",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "latest": {
      "histo-table-name": "FACT_FLIGHT_BOUND_PAX_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_FLIGHT_BOUND_PAX_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "subdomain": "Segment Booking",
    "mapping": {
      "description": {
        "description": "Contains the flight connections of a passenger's O&D",
        "granularity": "1 flight connection in 1 O&D"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "FLIGHT_BOUND_PAX_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"name": "connection", "rs" :{"blocks": [
          {"base": "$.mainResource.current.image"},
          {"cartesian": [
            // id and id2 are used to prevent creating lines if ids do not exist
            [{"ond": "$.flightItinerary[*]"}, {"bound": "$.flights[*]"}, {"conn": "$.connectedFlights"}],
            [{"trv": "$.travelers[*]"}, {"id2": "$.id"}]
          ]}
        ]}},
        {"name": "relation", "rs" :{"blocks": [
          {"base": "$.mainResource.current.image"},
          {"cartesian": [
            // id and id2 are used to prevent creating lines if ids do not exist
            [{"ond": "$.flightItinerary[*]"}, {"id": "$.flights[0].flightSegment.id"}],
            [{"trv": "$.travelers[*]"}, {"id2": "$.id"}]
          ]}
        ]}}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.reference"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "FLIGHT_BOUND_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "true",
          "sources": { "root-specific": [{"rs-name": "connection", "blocks": [ {"base": "$.id"}, {"ond": "$.id"}, {"bound": "$.id"}, {"trv": "$.id"} ]},
                                         {"rs-name": "relation", "blocks": [ {"base": "$.id"}, {"ond": "$.id"}, {"trv": "$.id"} ]}]}, "expr": "hashM({0})",
            "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true",
          "sources": { "root-specific": [{"rs-name": "connection", "blocks": [ {"base": "$.id"}, {"ond": "$.id"}, {"bound": "$.id"}, {"trv": "$.id"} ]},
                                         {"rs-name": "relation", "blocks": [ {"base": "$.id"}, {"ond": "$.id"}, {"trv": "$.id"} ]}]},
            "meta": {"description": {"value": "Functional key: reservationId-connectionId-boundId", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-SELL_AVAILABILITY-ST-1-ST-2-ST-1-ST-2", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "CONNECTION_TYPE", "column-type": "strColumn",
          "sources": { "root-specific": [{"rs-name": "connection", "blocks": [ {"bound": "$.connectedFlights.connectionType"} ]}]},
            "meta": {"description": {"value": "Connection type : connection, marriages.. ", "rule": "replace"}, "example": {"value": "INTERACTIVE_MARRIAGE", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CONNECTION_TIME_DURATION", "column-type": "strColumn",
          "sources": { "root-specific": [{"rs-name": "connection", "blocks": [ {"bound": "$.connectedFlights.connectionTimeDuration"} ]}]},
            "meta": {"description": {"value": "Connection duration", "rule": "replace"}, "example": {"value": "2H10M", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_RESERVATION_HISTO", "column": "RESERVATION_ID"}],
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "ORIGIN_DESTINATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"ond": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_ORIGIN_DESTINATION_HISTO", "column": "ORIGIN_DESTINATION_ID"}],
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "INBOUND_AIR_SEGMENT_PAX_ID", "column-type": "binaryStrColumn",
          "sources": { "root-specific": [{"rs-name": "connection", "blocks": [ {"bound": "$.connectedFlights.flightSegments[0].id"}, {"trv": "$.id"} ]},
                                         {"rs-name": "relation", "blocks": [ {"ond": "$.flights[0].flightSegment.id"}, {"trv": "$.id"} ]}]},
          "expr": ${hashMIdCheckStartAndEndNotNull},
          "fk": [{"table": "FACT_AIR_SEGMENT_PAX_HISTO", "column": "AIR_SEGMENT_PAX_ID"}],
          "meta": {"description": {"value": "Foreign key to FACT_AIR_SEGMENT_PAX of the inbound flight, always filled", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "OUTBOUND_AIR_SEGMENT_PAX_ID", "column-type": "binaryStrColumn",
          "sources": { "root-specific": [{"rs-name": "connection", "blocks": [ {"bound": "$.connectedFlights.flightSegments[1].id"}, {"trv": "$.id"} ]},
                                         {"rs-name": "relation", "blocks": [ {"ond": "$.flights[1].flightSegment.id"}, {"trv": "$.id"} ]}]},
          "expr": ${hashMIdCheckStartAndEndNotNull},
          "fk": [{"table": "FACT_AIR_SEGMENT_PAX_HISTO", "column": "AIR_SEGMENT_PAX_ID"}],
          "meta": {"description": {"value": "Foreign key to FACT_AIR_SEGMENT_PAX of the outbound flight, not filled in case of direct flight", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_LOCATOR", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.reference"}]},
            "meta": {"gdpr-zone": "orange"}},
        {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
            "meta": {"gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
            "meta": {"description": {"value": "Envelope/version of the PNR message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]},
            "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
            "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
            "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
          "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
        "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_SEGMENT_YIELD",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "latest": {
      "histo-table-name": "FACT_SEGMENT_YIELD_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_SEGMENT_YIELD_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "subdomain": "Segment Booking",
    "mapping": {
      "description": {
        "description": "Contains the estimated revenue of passengers' trip. Can refer to a marketing or operating scenario.",
        "granularity": "1 for each type of yield for each segment passenger in booking"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "SEGMENT_YIELD_ID", "RELATES_TO", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"name": "ope", "rs" :{"blocks": [{"base": "$.mainResource.current.image"}, {"seg": "$.products[?(@.subType == 'AIR')]"}, {"yield": "$.airSegment.operating.bookingClass.subClass.yields[*]"}]}},
        {"name": "mkt", "rs" :{"blocks": [{"base": "$.mainResource.current.image"}, {"seg": "$.products[?(@.subType == 'AIR')]"}, {"yield": "$.airSegment.marketing.bookingClass.subClass.yields[*]"}]}}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.reference"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "SEGMENT_YIELD_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.id"}, {"yield": "$.elementaryPriceType"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.id"}, {"yield": "$.elementaryPriceType"}]},
          "meta": {"description": {"value": "Functional key: segmentId-elemPriceType", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-ST-1-ADJUSTED_YIELD", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "ELEMENTARY_PRICE_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"yield": "$.elementaryPriceType"}]},
          "meta": {"description": {"value": "Defines the type of yield information : effective, adjusted, ond...", "rule": "replace"}, "example": {"value": "OND_YIELD_HKG_KUL", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "AMOUNT_ORIGINAL", "column-type": "floatColumn", "sources": {"blocks": [{"yield": "$.amount"}]},
          "meta": {"description": {"value": "Yield value", "rule": "replace"}, "example": {"value": "46.2355", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CURRENCY_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"yield": "$.currency"}]},
          "meta": {"description": {"value": "Currency of yield value", "rule": "replace"}, "example": {"value": "EUR", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RELATES_TO", "column-type": "strColumn",
          "sources": {
            "root-specific": [
              {"rs-name": "ope", "literal": "OPERATING"},
              {"rs-name": "mkt", "literal": "MARKETING"}
            ]
          },
          "meta": {"description": {"value": "Indicates to which parent fact table the record belongs to.", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_RESERVATION_HISTO", "column": "RESERVATION_ID"}],
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_LOCATOR", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.reference"}]},
            "meta": {"gdpr-zone": "orange"}},
        {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
            "meta": {"gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
            "meta": {"description": {"value": "Envelope/version of the PNR message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]},
            "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
            "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
            "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
          "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
        "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_SERVICE_PAX",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_SERVICE_PAX_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_SERVICE_PAX_HISTO",//@TODO !! table not validated
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Service Booking",
    "subdomain-main-table": "true",
    "mapping": {
      "description": {
        "description": "Contains information on a service booking (SSR or SVC) of a given passenger, such as service code, booking status and service freetext.",
        "granularity": "1 service booking for 1 passenger"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "SERVICE_PAX_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        // CLID and FQTU services are in FACT_LOYALTY_REQUEST_PAX
        {"blocks": [{"base": "$.mainResource.current.image"}, {"prod": "$.products[?(@.subType == 'SERVICE' && @.service.code != 'CLID' && @.service.code != 'FQTU')]"}, {"trv": "$.travelers[*]"}]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.reference"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "SERVICE_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"prod": "$.id"}, {"trv": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [ {"prod": "$.id"}, {"trv": "$.id"}]},
          "meta": {"description": {"value": "Functional key: serviceId-paxId", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-OT-1-ABCDEF-2019-10-05-PT-1", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "SERVICE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"prod": "$.service.code"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "SERVICE_SUB_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"prod": "$.service.subType"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "SERVICE_PROVIDER", "column-type": "strColumn", "sources": {"blocks": [{"prod": "$.service.serviceProvider.code"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "TEXT", "column-type": "strColumn", "sources": {"blocks": [{"prod": "$.service.text"}]},
          "meta": {"gdpr-zone": "red"}},
        {"name": "STATUS", "column-type": "strColumn", "sources": {"blocks": [{"prod": "$.service.status"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "IS_CHARGEABLE", "column-type": "booleanColumn", "sources": {"blocks": [{"prod": "$.service.isChargeable"}]},
          "meta": {"description": {"value": "Indicates if the service booking is a chargeable service for the customer", "rule": "replace"}, "example": {"value": "true", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REASON_FOR_ISSUANCE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"prod": "$.service.priceCategory.code"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "REASON_FOR_ISSUANCE_SUB_CODE", "column-type": "strColumn", "sources": {"blocks": [{"prod": "$.service.priceCategory.subCode"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "REASON_FOR_ISSUANCE_DESCRIPTION", "column-type": "strColumn", "sources": {"blocks": [{"prod": "$.service.priceCategory.description"}]},
          "meta": {"description": {"value": "The description of the reason for issuance code/subcode", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CREATION_DATETIME_UTC", "column-type": "timestampColumn", "sources": {"blocks": [{"prod": "$.service.creation.dateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "CREATION_OFFICE_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"prod": "$.service.creation.pointOfSale.office.id"}]},
          "meta": {"description": {"value": "AMID - office id in Amadeus (representing the creator of the service booking)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CREATION_COMMENT", "column-type": "strColumn", "sources": {"blocks": [{"prod": "$.service.creation.comment"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_RESERVATION_HISTO", "column": "RESERVATION_ID"}],
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "TRAVELER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"trv": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_TRAVELER_HISTO", "column": "TRAVELER_ID"}]},
        {"name": "SERVICE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"prod": "$.service.code"}]}, "expr": "hashS({0})",
          "fk": [{"table": "DIM_SERVICE", "column": "SERVICE_ID"}]},
        {"name": "SERVICE_PROVIDER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"prod": "$.service.serviceProvider.code"}]}, "expr": "hashS({0})",
          "fk": [{"table": "DIM_AIRLINE", "column": "AIRLINE_ID"}]},
        {"name": "STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"prod": "$.service.status"}]}, "expr": "hashS({0})",
          "fk": [{"table": "DIM_BOOKING_STATUS", "column": "BOOKING_STATUS_ID"}]},
        {"name": "REASON_FOR_ISSUANCE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"prod": "$.service.priceCategory.code"}]}, "expr": "hashS({0})",
          "fk": [{"table": "DIM_REASON_FOR_ISSUANCE", "column": "REASON_FOR_ISSUANCE_ID"}]},
        {"name": "CREATION_POINT_OF_SALE_ID", "column-type": "binaryStrColumn",
          "sources": {"blocks": [{"prod": "$.service.creation.pointOfSale.office.id"}, {"prod": "$.service.creation.pointOfSale.office.iataNumber"}, {"prod": "$.service.creation.pointOfSale.office.systemCode"}, {"prod": "$.service.creation.pointOfSale.office.agentType"}, {"prod": "$.service.creation.pointOfSale.login.cityCode"}, {"prod": "$.service.creation.pointOfSale.login.countryCode"}, {"prod": "$.service.creation.pointOfSale.login.numericSign"}, {"prod": "$.service.creation.pointOfSale.login.initials"}, {"prod": "$.service.creation.pointOfSale.login.dutyCode"}]}, "expr": "hashM({0})",
          "fk": [{"table": "DIM_POINT_OF_SALE", "column":"POINT_OF_SALE_ID"}],
          "meta": {"description": {"value": "Hashed foreign key (representing the service creator)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_LOCATOR", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.reference"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"description": {"value": "Envelope/version of the PNR message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]},
          "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_SEATING_PAX",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "latest": {
      "histo-table-name": "FACT_SEATING_PAX_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_SEATING_PAX_HISTO", //@TODO !! table not validated
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "subdomain": "Service Booking",
    "subdomain-main-table": "true",
    "mapping": {
      "description": {
        "description": "Contains information on a seat booking of a given passenger on a given segment, such as seat number, service code, booking status and freetext.",
        "granularity": "1 seat booking for 1 passenger on 1 segment"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "SEATING_PAX_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"prod": "$.products[?(@.subType == 'SEATING')]"}, {"seat": "$.seating.seats[*]"}, {"trv": "$.traveler"} ]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.reference"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "SEATING_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"prod": "$.id"}, {"trv": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [ {"prod": "$.id"}, {"trv": "$.id"}]},
          "meta": {"description": {"value": "Functional key: seatId-paxId-segId", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-OT-2-ABCDEF-2019-10-05-PT-1-ABCDEF-2019-10-05-ST-1", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "SERVICE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"prod": "$.seating.code"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "SERVICE_SUB_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"prod": "$.seating.subType"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "SERVICE_PROVIDER", "column-type": "strColumn", "sources": {"blocks": [{"prod": "$.seating.serviceProvider.code"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "SEAT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"seat": "$.number"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "SEAT_CHARACTERISTICS", "column-type": "strColumn", "sources": {"blocks": [{"seat": "$.characteristicCodes[*]"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "TEXT", "column-type": "strColumn", "sources": {"blocks": [{"prod": "$.seating.text"}]},
          "meta": {"gdpr-zone": "red"}},
        {"name": "STATUS", "column-type": "strColumn", "sources": {"blocks": [{"prod": "$.seating.status"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "IS_CHARGEABLE", "column-type": "booleanColumn", "sources": {"blocks": [{"prod": "$.seating.isChargeable"},{"seat": "$.characteristicCodes[*]"}]}, "expr": ${isSeatChargeable},
          "meta": {"description": {"value": "Indicates if the seat booking is a chargeable service for the customer. This information comes either from the seating service or the seat characteritics", "rule": "replace"}, "example": {"value": "true", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REASON_FOR_ISSUANCE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"prod": "$.seating.priceCategory.code"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "REASON_FOR_ISSUANCE_SUB_CODE", "column-type": "strColumn", "sources": {"blocks": [{"prod": "$.seating.priceCategory.subCode"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "REASON_FOR_ISSUANCE_DESCRIPTION", "column-type": "strColumn", "sources": {"blocks": [{"prod": "$.seating.priceCategory.description"}]},
          "meta": {"description": {"value": "The description of the reason for issuance code/subcode", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CREATION_DATETIME_UTC", "column-type": "timestampColumn", "sources": {"blocks": [{"prod": "$.seating.creation.dateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "CREATION_OFFICE_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"prod": "$.seating.creation.pointOfSale.office.id"}]},
          "meta": {"description": {"value": "AMID - office id in Amadeus (representing the creator of the seat booking)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CREATION_COMMENT", "column-type": "strColumn", "sources": {"blocks": [{"prod": "$.seating.creation.comment"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_RESERVATION_HISTO", "column": "RESERVATION_ID"}],
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "TRAVELER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"trv": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_TRAVELER_HISTO", "column": "TRAVELER_ID"}]},
        {"name": "SERVICE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"prod": "$.seating.code"}]}, "expr": "hashS({0})",
          "fk": [{"table": "DIM_SERVICE", "column": "SERVICE_ID"}]},
        {"name": "SERVICE_PROVIDER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"prod": "$.seating.serviceProvider.code"}]}, "expr": "hashS({0})",
          "fk": [{"table": "DIM_AIRLINE", "column": "AIRLINE_ID"}]},
        {"name": "STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"prod": "$.seating.status"}]}, "expr": "hashS({0})",
          "fk": [{"table": "DIM_BOOKING_STATUS", "column": "BOOKING_STATUS_ID"}]},
        {"name": "REASON_FOR_ISSUANCE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"prod": "$.seating.priceCategory.code"}]}, "expr": "hashS({0})",
          "fk": [{"table": "DIM_REASON_FOR_ISSUANCE", "column": "REASON_FOR_ISSUANCE_ID"}]},
        {"name": "CREATION_POINT_OF_SALE_ID", "column-type": "binaryStrColumn",
          "sources": {"blocks": [{"prod": "$.seating.creation.pointOfSale.office.id"}, {"prod": "$.seating.creation.pointOfSale.office.iataNumber"}, {"prod": "$.seating.creation.pointOfSale.office.systemCode"}, {"prod": "$.seating.creation.pointOfSale.office.agentType"}, {"prod": "$.seating.creation.pointOfSale.login.cityCode"}, {"prod": "$.seating.creation.pointOfSale.login.countryCode"}, {"prod": "$.seating.creation.pointOfSale.login.numericSign"}, {"prod": "$.seating.creation.pointOfSale.login.initials"}, {"prod": "$.seating.creation.pointOfSale.login.dutyCode"}]}, "expr": "hashM({0})",
          "fk": [{"table": "DIM_POINT_OF_SALE", "column":"POINT_OF_SALE_ID"}],
          "meta": {"description": {"value": "Hashed foreign key (representing the seating request creator)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_LOCATOR", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.reference"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"description": {"value": "Envelope/version of the PNR message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]},
          "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_AUTOMATED_PROCESS",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "latest": {
      "histo-table-name": "FACT_AUTOMATED_PROCESS_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_AUTOMATED_PROCESS_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "subdomain": "Ticketing",
    "mapping": {
      "description": {
        "description": "Contains information related to automated ticketing processes, such as ticketing arrangements or ticketing time limits.",
        "granularity": "1 ticketing process"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "AUTOMATED_PROCESS_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [
          {"base": "$.mainResource.current.image"},
          {"pro": "$.automatedProcesses[*]"},
          {"pax": "$.travelers[*]"}
        ]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.reference"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "AUTOMATED_PROCESS_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"pro": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"pro": "$.id"}]},
          "meta": {"description": {"value": "Functional key: processId", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "CODE", "column-type": "strColumn", "sources": {"blocks": [{"pro": "$.code"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "SOURCE", "column-type": "strColumn", "sources": {"blocks": [{"pro": "$.source"}]},
          "meta": {"description": {"value": "The source of the automated process", "rule": "replace"}, "example": {"value": "AMADEUS_TIME_LIMITS", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "APPLICABLE_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"pro": "$.applicableCarrierCode"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "ACTION_DATETIME_UTC", "column-type": "timestampColumn", "sources": {"blocks": [{"pro": "$.dateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "OFFICE_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"pro": "$.office.id"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "QUEUE_CATEGORY", "column-type": "strColumn", "sources": {"blocks": [{"pro": "$.queue.category"}]},
          "meta": {"description": {"value": "The category of the ticketing queue", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "QUEUE_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"pro": "$.queue.number"}]},
          "meta": {"description": {"value": "The number of the ticketing queue", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_APPLICABLE_TO_INFANTS", "column-type": "booleanColumn", "sources": {"blocks": [{"pro": "$.isApplicableToInfants"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "DOCUMENT_DELIVERY_OPTIONS", "column-type": "strColumn", "sources": {"blocks": [{"pro": "$.documentDeliveryOptions"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "TEXT", "column-type": "strColumn", "sources": {"blocks": [{"pro": "$.text"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "CANCELLATION_RULES_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"pro": "$.cancellationRuleId"}]},
          "meta": {"description": {"value": "The identifier of the applied cancellation rule", "rule": "replace"}, "example": {"value": "12345678", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_RESERVATION_HISTO", "column": "RESERVATION_ID"}],
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "APPLICABLE_CARRIER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"pro": "$.applicableCarrierCode"}]}, "expr": "hashS({0})",
          "fk": [{"table": "DIM_AIRLINE", "column": "AIRLINE_ID"}]},
        {"name": "RECORD_LOCATOR", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.reference"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"description": {"value": "Envelope/version of the PNR message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]},
          "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_TRAVEL_DOCUMENT",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "latest": {
      "histo-table-name": "FACT_TRAVEL_DOCUMENT_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_TRAVEL_DOCUMENT_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "subdomain": "Ticketing",
    "subdomain-main-table": "true",
    "mapping": {
      "description": {
        "description": "Contains high-level information on ticket and EMDs for segment or service bookings of a given passenger, such as document number, document status, and total amount converted to the airline's home currency",
        "granularity": "1 travel document"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "TRAVEL_DOCUMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"ref": "$.ticketingReferences[*]"}, {"doc": "$.documents[*]"}]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.reference"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [ {"ref": "$.id"}, {"doc": "$.primaryDocumentNumber"}, {"doc": "$.documentNumber"}]}, "expr": hashM(${createConsolidatedDocumentNumberReferenceKey}),
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [ {"ref": "$.id"}, {"doc": "$.primaryDocumentNumber"}, {"doc": "$.documentNumber"}]}, "expr": ${createConsolidatedDocumentNumberReferenceKey},
          "meta": {"description": {"value": "Functional key: ticketingRefId-consolDocNum", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-OT-10-1234567890123", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "DOCUMENT_NUMBER_CONSOLIDATED", "column-type": "strColumn", "sources": {"blocks": [{"doc": "$.primaryDocumentNumber"}, {"doc": "$.documentNumber"}]}, "expr": ${createConsolidatedValue},
          "meta": {"description": {"value": "Computed field: filled with either the primary document number (if it exists), else with the document number", "rule": "replace"}, "example": {"value": "1234567890123", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "DOCUMENT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"doc": "$.documentNumber"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "PRIMARY_DOCUMENT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"doc": "$.primaryDocumentNumber"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "DOCUMENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"doc": "$.documentType"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "DOCUMENT_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"doc": "$.status"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "ASSOCIATION_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"doc": "$.associationStatus"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "NUMBER_OF_BOOKLETS", "column-type": "intColumn", "sources": {"blocks": [{"doc": "$.numberOfBooklets"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "TOTAL_AMOUNT", "column-type": "floatColumn", "sources": {}  ,
          "meta": {"description": {"value": "Calculated field: converted total_amount_payment into airline's home currency", "rule": "replace"}, "example": {"value": "514.13", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CURRENCY", "column-type": "strColumn", "sources": {},
          "meta": {"description": {"value": "Static field: airline's home currency", "rule": "replace"}, "example": {"value": "EUR", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "TOTAL_AMOUNT_ORIGINAL", "column-type": "floatColumn", "sources": {"blocks": [{"doc": "$.price.total"}]},
          "meta": {"example": {"value": "199.00", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CURRENCY_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"doc": "$.price.currency"}]},
          "meta": {"example": {"value": "USD", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "EXCHANGE_RATE_DATE_NEEDED", "column-type": "dateColumn", "sources": {"blocks": [{"doc": "$.creation.dateTime"}]}, "expr": "to_date({0})",
          "meta": {"description": {"value": "The date for which the home currency conversion rate was needed: document issuance date", "rule": "replace"}, "example": {"value": "2023-01-01", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "EXCHANGE_RATE_DATE_TAKEN", "column-type": "dateColumn", "sources": {},
          "meta": {"description": {"value": "The date for which the home currency conversion rate was found and taken", "rule": "replace"}, "example": {"value": "2023-01-01", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_INFANT", "column-type": "booleanColumn", "sources": {"blocks": [{"ref": "$.isInfant"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "BLACKLIST_CATEGORY", "column-type": "strColumn", "sources": {"blocks": [{"doc": "$.blacklistCategory"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "TICKETING_REFERENCE_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"ref": "$.referenceTypeCode"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "TICKETING_REFERENCE_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"ref": "$.referenceStatusCode"}]},
          "meta": {"example": {"value": "HK", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "TICKETING_REFERENCE_TEXT", "column-type": "strColumn", "sources": {"blocks": [{"ref": "$.text"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "CREATION_DATETIME_UTC", "column-type": "timestampColumn", "sources": {"blocks": [{"doc": "$.creation.dateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "CREATION_OFFICE_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"doc": "$.creation.pointOfSale.office.id"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "CREATION_OFFICE_IATA_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"doc": "$.creation.pointOfSale.office.iataNumber"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "CREATION_OFFICE_SYSTEM_CODE", "column-type": "strColumn", "sources": {"blocks": [{"doc": "$.creation.pointOfSale.office.systemCode"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_RESERVATION_HISTO", "column": "RESERVATION_ID"}],
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CREATION_POINT_OF_SALE_ID", "column-type": "binaryStrColumn",
          "sources": {"blocks": [{"doc": "$.creation.pointOfSale.office.id"}, {"doc": "$.creation.pointOfSale.office.iataNumber"}, {"doc": "$.creation.pointOfSale.office.systemCode"}, {"doc": "$.creation.pointOfSale.office.agentType"}, {"doc": "$.creation.pointOfSale.login.cityCode"}, {"doc": "$.creation.pointOfSale.login.countryCode"}, {"doc": "$.creation.pointOfSale.login.numericSign"}, {"doc": "$.creation.pointOfSale.login.initials"}, {"doc": "$.creation.pointOfSale.login.dutyCode"}]}, "expr": "hashM({0})",
          "fk": [{"table": "DIM_POINT_OF_SALE", "column":"POINT_OF_SALE_ID"}],
          "meta": {"description": {"value": "Hashed foreign key (representing the creator of the travel document)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "TICKETING_REFERENCE_STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"ref": "$.referenceStatusCode"}]}, "expr": "hashS({0})",
          "fk": [{"table": "DIM_BOOKING_STATUS", "column": "BOOKING_STATUS_ID"}]},
        {"name": "RECORD_LOCATOR", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.reference"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"description": {"value": "Envelope/version of the PNR message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]},
          "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "stackable-addons": [
      {
        "type": "currency-conversion",
        "conversions": [
          {"src-col": "TOTAL_AMOUNT_ORIGINAL", "src-unit-col": "CURRENCY_ORIGINAL", "src-date-col" : "EXCHANGE_RATE_DATE_NEEDED", "dst-col": "TOTAL_AMOUNT", "dst-unit-col": "CURRENCY","dst-date-col" :"EXCHANGE_RATE_DATE_TAKEN"}
        ]
      }
    ],
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_COUPON",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "latest": {
      "histo-table-name": "FACT_COUPON_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_COUPON_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "subdomain": "Ticketing",
    "mapping": {
      "description": {
        "description": "Contains high-level information on coupons, mainly the reason for issuance",
        "granularity": "1 coupon"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "COUPON_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"ref": "$.ticketingReferences[*]"}, {"doc": "$.documents[*]"}, {"cpn": "$.coupons[*]"}]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.reference"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [ {"ref": "$.id"}, {"doc": "$.primaryDocumentNumber"}, {"doc": "$.documentNumber"}, {"cpn": "$.sequenceNumber"}, {"cpn": "$.number"}]}, "expr": hashM(${createConsolidatedCouponReferenceKey}),
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [ {"ref": "$.id"}, {"doc": "$.primaryDocumentNumber"}, {"doc": "$.documentNumber"}, {"cpn": "$.sequenceNumber"}, {"cpn": "$.number"}]}, "expr": ${createConsolidatedCouponReferenceKey},
            "meta": {"description": {"value": "Functional key: ticketingRefId-consolDocNum-consolCpnNum", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-OT-10-1234567890123-2", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "COUPON_NUMBER_CONSOLIDATED", "column-type": "intColumn", "sources": {"blocks": [{"cpn": "$.sequenceNumber"}, {"cpn": "$.number"}]}, "expr": ${createConsolidatedValue},
          "meta": {"description": {"value": "Computed field: filled with either the coupon number on the primary document (if it exists), else with the coupon number on the document", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "COUPON_NUMBER", "column-type": "intColumn", "sources": {"blocks": [{"cpn": "$.number"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "SEQUENCE_NUMBER", "column-type": "intColumn", "sources": {"blocks": [{"cpn": "$.sequenceNumber"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "REASON_FOR_ISSUANCE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.reasonForIssuance.code"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "REASON_FOR_ISSUANCE_SUB_CODE", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.reasonForIssuance.subCode"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "REASON_FOR_ISSUANCE_DESCRIPTION", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.reasonForIssuance.description"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_RESERVATION_HISTO", "column": "RESERVATION_ID"}],
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [ {"ref": "$.id"}, {"doc": "$.primaryDocumentNumber"}, {"doc": "$.documentNumber"}]}, "expr": hashM(${createConsolidatedDocumentNumberReferenceKey}),
          "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}]},
        //TODO: AIR_SEGMENT_PAX_ID  (FK)
        //TODO: SERVICE_PAX_ID  (FK)
        //TODO: SEATING_PAX_ID  (FK)
        {"name": "REASON_FOR_ISSUANCE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.reasonForIssuance.code"}]}, "expr": "hashS({0})",
          "fk": [{"table": "DIM_REASON_FOR_ISSUANCE", "column": "REASON_FOR_ISSUANCE_ID"}]},
        {"name": "RECORD_LOCATOR", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.reference"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"description": {"value": "Envelope/version of the PNR message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]},
          "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "DIM_POINT_OF_SALE",
    "table-selectors": ["PNR_CORE"],
    "mapping": {
      "description": {"description": "Lists the point of sales (office-level and user-level) used as PNR creator/owner/updater, or as creator of segment/service/seat bookings, keywords, loyalty requests, identity documents, ...", "granularity": "1 point of sale"},
      "merge": {
        "key-columns": ["POINT_OF_SALE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        { "name": "owner", "rs": { "blocks": [
          {"pos": "$.mainResource.current.image.owner"},
          {"office" : "$.office"}]}
        },
        { "name": "creation", "rs": { "blocks": [
          {"pos": "$.mainResource.current.image.creation.pointOfSale"},
          {"office" : "$.office"}]}
        },
        { "name": "lastModification", "rs": { "blocks": [
          {"pos": "$.mainResource.current.image.lastModification.pointOfSale"},
          {"office" : "$.office"}]}
        },
        { "name": "queuing", "rs": { "blocks": [
          {"pos": "$.mainResource.current.image"},
          {"office" : "$.queuingOffice"}]}
        },
        { "name": "assPnr", "rs": { "blocks": [
          {"pos": "$.mainResource.current.image.associatedPnrs[*].creation.pointOfSale"},
          {"office" : "$.office"}]}
        },
        { "name": "transfer", "rs": { "blocks": [
          {"pos": "$.mainResource.current.image.flightTransfer.associatedOrder.creation.pointOfSale"},
          {"office" : "$.office"}]}
        },
        { "name": "identityDoc", "rs": { "blocks": [
          {"pos": "$.mainResource.current.image.travelers[*].identityDocuments[*].creation.pointOfSale"},
          {"office" : "$.office"}]}
        },
        { "name": "keyword", "rs": { "blocks": [
          {"pos": "$.mainResource.current.image.keywords[*].creation.pointOfSale"},
          {"office" : "$.office"}]}
        },
        { "name": "quotationCreation", "rs": { "blocks": [
          {"pos": "$.mainResource.current.image.quotations[*].creation.pointOfSale"},
          {"office" : "$.office"}]}
        },
        { "name": "quotationLastUpdater", "rs": { "blocks": [
          {"pos": "$.mainResource.current.image.quotations[*].lastModification.pointOfSale"},
          {"office" : "$.office"}]}
        },
        { "name": "travelDoc", "rs": { "blocks": [
          {"pos": "$.mainResource.current.image.ticketingReferences[*].documents[*].creation.pointOfSale"},
          {"office" : "$.office"}]}
        },
        { "name": "air", "rs": { "blocks": [
          {"pos": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.creation.pointOfSale"},
          {"office" : "$.office"}]}
        },
        { "name": "serv", "rs": { "blocks": [
          // Remove the filter on CLID, FQTU -> anyway, it is in FACT_SERVICE or in FACT_LOYALTY_REQUEST
          {"pos": "$.mainResource.current.image.products[?(@.subType == 'SERVICE')].service.creation.pointOfSale"},
          {"office" : "$.office"}]}
        },
        { "name": "seating", "rs": { "blocks": [
          {"pos": "$.mainResource.current.image.products[?(@.subType == 'SEATING')].seating.creation.pointOfSale"},
          {"office" : "$.office"}]}
        },
        { "name": "loyReq", "rs": { "blocks": [
          {"pos": "$.mainResource.current.image.loyaltyRequests[*].creation.pointOfSale"},
          {"office" : "$.office"}]}
        },
        { "name": "collAg", "rs": { "blocks": [
          {"pos": "$.mainResource.current.image.fareElements.commissions.values[*].collectingAgency"},
          {"office" : "$.office"}]}
        }
        //@TODO: any more blocks from other parts of the model (e.g. Service, seating, loyalty requests)
      ],
      "columns": [
        { "name": "POINT_OF_SALE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": { "root-specific": [
          {"rs-name": "owner", "blocks": [
            {"office": "$.id"},
            {"office": "$.iataNumber"},
            {"office": "$.systemCode"},
            {"office": "$.agentType"},
            {"pos": "$.login.cityCode"},
            {"pos": "$.login.countryCode"},
            {"pos": "$.login.numericSign"},
            {"pos": "$.login.initials"},
            {"pos": "$.login.dutyCode"}
          ]},
          {"rs-name": "creation", "blocks": [
            {"office": "$.id"},
            {"office": "$.iataNumber"},
            {"office": "$.systemCode"},
            {"office": "$.agentType"},
            {"pos": "$.login.cityCode"},
            {"pos": "$.login.countryCode"},
            {"pos": "$.login.numericSign"},
            {"pos": "$.login.initials"},
            {"pos": "$.login.dutyCode"}
          ]},
          {"rs-name": "lastModification", "blocks": [
            {"office": "$.id"},
            {"office": "$.iataNumber"},
            {"office": "$.systemCode"},
            {"office": "$.agentType"},
            {"pos": "$.login.cityCode"},
            {"pos": "$.login.countryCode"},
            {"pos": "$.login.numericSign"},
            {"pos": "$.login.initials"},
            {"pos": "$.login.dutyCode"}
          ]},
          {"rs-name": "quotationCreation", "blocks": [
            {"office": "$.id"},
            {"office": "$.iataNumber"},
            {"office": "$.systemCode"},
            {"office": "$.agentType"},
            {"pos": "$.login.cityCode"},
            {"pos": "$.login.countryCode"},
            {"pos": "$.login.numericSign"},
            {"pos": "$.login.initials"},
            {"pos": "$.login.dutyCode"}
          ]},
          {"rs-name": "quotationLastUpdater", "blocks": [
            {"office": "$.id"},
            {"office": "$.iataNumber"},
            {"office": "$.systemCode"},
            {"office": "$.agentType"},
            {"pos": "$.login.cityCode"},
            {"pos": "$.login.countryCode"},
            {"pos": "$.login.numericSign"},
            {"pos": "$.login.initials"},
            {"pos": "$.login.dutyCode"}
          ]},
          {"rs-name": "queuing", "blocks": [
            {"office": "$.id"}
          ]},
          {"rs-name": "assPnr", "blocks": [
            {"office": "$.id"},
            {"office": "$.iataNumber"},
            {"office": "$.systemCode"},
            {"office": "$.agentType"},
            {"pos": "$.login.cityCode"},
            {"pos": "$.login.countryCode"},
            {"pos": "$.login.numericSign"},
            {"pos": "$.login.initials"},
            {"pos": "$.login.dutyCode"}
          ]},
          {"rs-name": "transfer", "blocks": [
            {"office": "$.id"},
            {"office": "$.iataNumber"},
            {"office": "$.systemCode"},
            {"office": "$.agentType"},
            {"pos": "$.login.cityCode"},
            {"pos": "$.login.countryCode"},
            {"pos": "$.login.numericSign"},
            {"pos": "$.login.initials"},
            {"pos": "$.login.dutyCode"}
          ]},
          {"rs-name": "identityDoc", "blocks": [
            {"office": "$.id"},
            {"office": "$.iataNumber"},
            {"office": "$.systemCode"},
            {"office": "$.agentType"},
            {"pos": "$.login.cityCode"},
            {"pos": "$.login.countryCode"},
            {"pos": "$.login.numericSign"},
            {"pos": "$.login.initials"},
            {"pos": "$.login.dutyCode"}
          ]},
          {"rs-name": "keyword", "blocks": [
            {"office": "$.id"},
            {"office": "$.iataNumber"},
            {"office": "$.systemCode"},
            {"office": "$.agentType"},
            {"pos": "$.login.cityCode"},
            {"pos": "$.login.countryCode"},
            {"pos": "$.login.numericSign"},
            {"pos": "$.login.initials"},
            {"pos": "$.login.dutyCode"}
          ]},
          {"rs-name": "seating", "blocks": [
            {"office": "$.id"},
            {"office": "$.iataNumber"},
            {"office": "$.systemCode"},
            {"office": "$.agentType"},
            {"pos": "$.login.cityCode"},
            {"pos": "$.login.countryCode"},
            {"pos": "$.login.numericSign"},
            {"pos": "$.login.initials"},
            {"pos": "$.login.dutyCode"}
          ]},
          {"rs-name": "travelDoc", "blocks": [
            {"office": "$.id"},
            {"office": "$.iataNumber"},
            {"office": "$.systemCode"},
            {"office": "$.agentType"},
            {"pos": "$.login.cityCode"},
            {"pos": "$.login.countryCode"},
            {"pos": "$.login.numericSign"},
            {"pos": "$.login.initials"},
            {"pos": "$.login.dutyCode"}
          ]},
          {"rs-name": "air", "blocks": [
            {"office": "$.id"},
            {"office": "$.iataNumber"},
            {"office": "$.systemCode"},
            {"office": "$.agentType"},
            {"pos": "$.login.cityCode"},
            {"pos": "$.login.countryCode"},
            {"pos": "$.login.numericSign"},
            {"pos": "$.login.initials"},
            {"pos": "$.login.dutyCode"}
          ]},
          {"rs-name": "serv", "blocks": [
            {"office": "$.id"},
            {"office": "$.iataNumber"},
            {"office": "$.systemCode"},
            {"office": "$.agentType"},
            {"pos": "$.login.cityCode"},
            {"pos": "$.login.countryCode"},
            {"pos": "$.login.numericSign"},
            {"pos": "$.login.initials"},
            {"pos": "$.login.dutyCode"}
          ]},
          {"rs-name": "loyReq", "blocks": [
            {"office": "$.id"},
            {"office": "$.iataNumber"},
            {"office": "$.systemCode"},
            {"office": "$.agentType"},
            {"pos": "$.login.cityCode"},
            {"pos": "$.login.countryCode"},
            {"pos": "$.login.numericSign"},
            {"pos": "$.login.initials"},
            {"pos": "$.login.dutyCode"}
          ]},
          {"rs-name": "collAg", "blocks": [
            {"office": "$.id"},
            {"office": "$.iataNumber"},
            {"office": "$.agentType"}
          ]}
        ]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (officeId-officeIATA-system-agentType-city-country-numSign-initials-duty)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        { "name": "OFFICE_IDENTIFIER", "column-type": "strColumn","sources": { "blocks": [{"office": "$.id"}]}, "meta": {"gdpr-zone": "green"}},
        //@TODO: why do the login-fields have explicit rs-names and the office-fields not => fix all following fields in a consistent way to match all defined root sources
        { "name": "OFFICE_IATA_NUMBER","column-type": "strColumn", "sources":  { "blocks": [{"office": "$.iataNumber"}]}, "meta": {"gdpr-zone": "green"}},
        { "name": "OFFICE_SYSTEM_CODE", "column-type": "strColumn", "sources":  { "blocks": [{"office": "$.systemCode"}]}},
        { "name": "OFFICE_AGENT_TYPE","column-type": "strColumn","sources":  { "blocks": [{"office": "$.agentType"}]}},
        { "name": "LOGIN_CITY_CODE","column-type": "strColumn","sources": { "blocks": [{"pos": "$.login.cityCode"}]}},
        { "name": "LOGIN_COUNTRY_CODE","column-type": "strColumn","sources": { "blocks": [{"pos": "$.login.countryCode"}]}},
        { "name": "LOGIN_NUMERIC_SIGN", "column-type": "strColumn","sources": { "blocks": [{"pos": "$.login.numericSign"}]}, "meta": {"gdpr-zone": "orange"}},
        { "name": "LOGIN_INITIALS", "column-type": "strColumn", "sources": { "blocks": [{"pos": "$.login.initials"}]}, "meta": {"gdpr-zone": "orange"}},
        { "name": "LOGIN_DUTY_CODE", "column-type": "strColumn", "sources": { "blocks": [{"pos": "$.login.dutyCode"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_AIRLINE",
    "table-selectors": ["PNR_CORE"],
    "mapping": {
      "description": {"description": "Lists the airlines used as marketing/operating carriers on flight segments, or service providers, or for loyalty programs. Includes alliances (prefixed by *).", "granularity": "1 airline"},
      "merge": {
        "key-columns": ["AIRLINE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        //PNR V2
        { "blocks": [{"companyCode": "$.mainResource.current.image.keywords[*].serviceProvider.code"}]},
        { "blocks": [{"companyCode": "$.mainResource.current.image.travelers[*].identityDocuments[*].serviceProvider.code"}]},
        { "blocks": [{"companyCode": "$.mainResource.current.image.automatedProcesses[*].applicableCarrierCode"}]},
        { "blocks": [{"companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.marketing.flightDesignator.carrierCode"}]},
        { "blocks": [{"companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.operating.flightDesignator.carrierCode"}]},
        { "blocks": [{"companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].flightSegment.marketing.flightDesignator.carrierCode"}]},
        { "blocks": [{"companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].flightSegment.operating.flightDesignator.carrierCode"}]},
        { "blocks": [{"companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].handlingCarrier"}]},
        { "blocks": [{"companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].acceptance.interAirlineThroughCheckIn.originatorCompany"}]},
        { "blocks": [{"companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].acceptance.interAirlineThroughCheckIn.targetCompany"}]},
        { "blocks": [{"companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].acceptance.interAirlineThroughCheckIn.onwardFlight.marketing.flightDesignator.carrierCode"}]},
        { "blocks": [{"companyCode": "$.mainResource.current.image.products[*].service.serviceProvider.code"}]},
        { "blocks": [{"companyCode": "$.mainResource.current.image.products[*].service.membership.activeTier.companyCode"}]},
        { "blocks": [{"companyCode": "$.mainResource.current.image.products[*].service.membership.allianceTier.companyCode"}]},
        { "blocks": [{"companyCode": "$.mainResource.current.image.loyaltyRequests[*].serviceProvider.code"}]},
        { "blocks": [{"companyCode": "$.mainResource.current.image.loyaltyRequests[*].membership.activeTier.companyCode"}]},
        { "blocks": [{"companyCode": "$.mainResource.current.image.loyaltyRequests[*].membership.allianceTier.companyCode"}]},
        { "blocks": [{"companyCode": "$.mainResource.current.image.paymentMethods[*].formsOfPayment[*].paymentLoyalty.membership.activeTier.companyCode"}]},
        { "blocks": [{"companyCode": "$.mainResource.current.image.paymentMethods[*].formsOfPayment[*].paymentLoyalty.membership.allianceTier.companyCode"}]},
        //@TODO: need more root sources for the other part of the model (seating, ...)

        // below blocks are not used by star schema, only raw vault
        { "blocks": [{"companyCode": "$.mainResource.current.image.travelers[*].passenger.passengerDeliveries[*].bagsGroupDeliveries[*].bagTag.issuingCarrier"}]},
        { "blocks": [{"companyCode": "$.mainResource.current.image.travelers[*].passenger.passengerDeliveries[*].deliveryAirlineCode"}]}
      ],
      "columns": [
        {"name": "AIRLINE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"companyCode": "$.value"}]}, "expr": "hashS({0})",
          "meta": {"description": {"value": "Hash of the functional key (iataCode)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "AIRLINE_IATA_CODE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"companyCode": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_PASSENGER_TYPE",
    "table-selectors": ["PNR_CORE"],
    "mapping": {
      "description": {"description": "Lists the different passenger types used on PNR passengers", "granularity": "1 passenger type"},
      "merge": {
        "key-columns": ["PASSENGER_TYPE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"base": "$.mainResource.current.image.travelers[*]"},{"dim": "$.passengerTypeCode" }]}],
      "columns": [
        {"name": "PASSENGER_TYPE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base":"$.passengerTypeCode"}]}, "expr": "hashS({0})",
          "meta": {"description": {"value": "Hash of the functional key (code)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "PASSENGER_TYPE_CODE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base":"$.passengerTypeCode"}]}},
        {"name": "PASSENGER_TYPE_LABEL", "column-type": "strColumn", "sources": {"blocks": [{"base":"$.passengerTypeCode"}]}
          , "meta": {"description": {"value": "Label corresponding to the passenger type code", "rule": "replace"}, "example": {"value": "Adult", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_SOURCE", "column-type": "strColumn", "sources": {"literal": "DOMAIN_FEED"}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
      },
      "prefiller": [{
        "data-source-key": "PASSENGER_TYPE",
        "column-filler" : [
          {"dim-col" : "PASSENGER_TYPE_ID", "src-col" : "CODE"},
          {"dim-col" : "PASSENGER_TYPE_CODE", "src-col" : "CODE"},
          {"dim-col" : "PASSENGER_TYPE_LABEL", "src-col" : "NAME"}
        ]
      }]
  },
  {
    "name": "DIM_BOOKING_STATUS",
    "table-selectors": ["PNR_CORE"],
    "mapping": {
      "description": {"description": "Lists the booking statuses used on segment bookings or service bookings", "granularity": "1 booking status"},
      "merge": {
        "key-columns": ["BOOKING_STATUS_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        { "blocks": [{"statusCode": "$.mainResource.current.image.keywords[*].status"}]},
        { "blocks": [{"statusCode": "$.mainResource.current.image.travelers[*].identityDocuments[*].status"}]},
        { "blocks": [{"statusCode": "$.mainResource.current.image.ticketingReferences[*].referenceStatusCode"}]},
        { "blocks": [{"statusCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.bookingStatusCode"}]},
        { "blocks": [{"statusCode": "$.mainResource.current.image.products[?(@.subType == 'SERVICE')].service.status"}]},
        { "blocks": [{"statusCode": "$.mainResource.current.image.products[?(@.subType == 'SEATING')].seating.status"}]},
        { "blocks": [{"statusCode": "$.mainResource.current.image.loyaltyRequests[*].status"}]}
      ],
      "columns": [
        {"name": "BOOKING_STATUS_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"statusCode": "$.value"}]}, "expr": "hashS({0})",
          "meta": {"description": {"value": "Hash of the functional key (code)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "BOOKING_STATUS_CODE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"statusCode": "$.value"}]}},
        {"name": "BOOKING_STATUS_LABEL", "column-type": "strColumn", "sources": {"blocks": [{"statusCode": "$.value"}]}
          , "meta": {"description": {"value": "Label corresponding to the booking status code", "rule": "replace"}, "example": {"value": "Holding Confirmed", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "BOOKING_MACRO_STATUS", "column-type": "strColumn", "sources": {"literal": "UNKNOWN"}
          , "meta": {"description": {"value": "Macro Status corresponding to the booking status code", "rule": "replace"}, "example": {"value": "Confirmed", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "BOOKING_STATUS_TYPE", "column-type": "strColumn", "sources": {"literal": "UNKNOWN"}
          , "meta": {"description": {"value": "Status type corresponding to the booking status code", "rule": "replace"}, "example": {"value": "Status, Action", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_SOURCE", "column-type": "strColumn", "sources": {"literal": "DOMAIN_FEED"}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
      },
      "prefiller": [{
        "data-source-key": "BOOKING_STATUS",
        "column-filler" : [
          {"dim-col" : "BOOKING_STATUS_ID", "src-col" : "CODE"},
          {"dim-col" : "BOOKING_STATUS_CODE", "src-col" : "CODE"},
          {"dim-col" : "BOOKING_STATUS_LABEL", "src-col" : "LABEL"},
          {"dim-col" :  "BOOKING_MACRO_STATUS", "src-col" : "MACRO_STATUS"},
          {"dim-col" :  "BOOKING_STATUS_TYPE", "src-col" : "TYPE"}
        ]
      }]
  },
  {
    "name": "DIM_AIRPORT",
    "table-selectors": ["PNR_CORE"],
    "mapping": {
      "description": {"description": "Lists the airports used in departure/arrival airports of flight segments or legs", "granularity": "1 airport"},
      "merge": {
        "key-columns": ["AIRPORT_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        { "blocks": [{"airport": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.departure"}]},
        { "blocks": [{"airport": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.arrival"}]},
        { "blocks": [{"airport": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].flightSegment.departure"}]},
        { "blocks": [{"airport": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].flightSegment.arrival"}]},
        { "blocks": [{"airport": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].departure"}]},
        { "blocks": [{"airport": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].arrival"}]}
      ],
      "columns": [
        {"name": "AIRPORT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"airport": "$.iataCode"}]}, "expr": "hashS({0})",
          "meta": {"description": {"value": "Hash of the functional key (iataCode)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "AIRPORT_IATA_CODE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"airport": "$.iataCode"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_AIRPORT_TERMINAL",
    "table-selectors": ["PNR_CORE"],
    "mapping": {
      "description": {"description": "Lists the airport terminals used in departure/arrival of flight segments and legs", "granularity": "1 terminal in 1 airport"},
      "merge": {
        "key-columns": ["AIRPORT_TERMINAL_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        //@TODO: check if first two blocks fit well for terminal info from FACT_AIR_SEGMENT_PAX (departure, arrival)
        {"blocks": [{"loc": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.departure"}, {"termId": "$.terminal"}]},
        {"blocks": [{"loc": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.arrival"}, {"termId": "$.terminal"}]},
        {"blocks": [{"loc": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].flightSegment.departure"}, {"termId": "$.terminal"}]},
        {"blocks": [{"loc": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].flightSegment.arrival"}, {"termId": "$.terminal"}]},
        {"blocks": [{"loc": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].departure"}, {"termId": "$.terminal"}]},
        {"blocks": [{"loc": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].arrival"}, {"termId": "$.terminal"}]}
      ],
      "columns": [
        {
          "name": "AIRPORT_TERMINAL_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"loc": "$.iataCode"}, {"loc": "$.terminal"}]}, "expr": "hashS({0})",
            "meta": {"description": {"value": "Hash of the functional key (airportIataCode-terminal)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "AIRPORT_IATA_CODE", "column-type": "strColumn", "sources": {"blocks": [{"loc": "$.iataCode"}]}},
        {"name": "TERMINAL", "column-type": "strColumn", "sources": {"blocks": [{"loc": "$.terminal"}]}},
        {
          "name": "AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"loc": "$.iataCode"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_SERVICE",
    "table-selectors": ["PNR_CORE"],
    "mapping": {
      "description": {"description": "Lists the various service codes used in the PNR bookings (SSR/SVC, loyalty requests, keywords, ...)", "granularity": "1 service code"},
      "merge": {
        "key-columns": ["SERVICE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        { "blocks": [{"service": "$.mainResource.current.image.keywords[*]"}, {"code": "$.code"}]},
        { "blocks": [{"service": "$.mainResource.current.image.travelers[*].identityDocuments[*]"}, {"code": "$.code"}]},
        { "blocks": [{"service": "$.mainResource.current.image.products[?(@.subType == 'SERVICE')].service"}, {"code": "$.code"}]},
        { "blocks": [{"service": "$.mainResource.current.image.products[?(@.subType == 'SEATING')].seating"}, {"code": "$.code"}]},
        { "blocks": [{"service": "$.mainResource.current.image.loyaltyRequests[*]"}, {"code": "$.code"}]}
      ],
      "columns": [
        {"name": "SERVICE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"service": "$.code"}]}, "expr": "hashS({0})",
          "meta": {"description": {"value": "Hash of the functional key (code)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "SERVICE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"service": "$.code"}]}},
        {"name": "SERVICE_SUBTYPE", "column-type": "strColumn", "sources": {"blocks": [{"service": "$.subType"}]}},
        {"name": "SERVICE_LABEL", "column-type": "strColumn", "sources": {"blocks": [{"service": "$.code"}]}
          , "meta": {"description": {"value": "Label corresponding to the service code", "rule": "replace"}, "example": {"value": "Excess baggage", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_SOURCE", "column-type": "strColumn", "sources": {"literal": "DOMAIN_FEED"}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
      },
      "prefiller": [{
        "data-source-key": "SSR_CODE",
        "column-filler" : [
          {"dim-col" : "SERVICE_ID", "src-col" : "CODE"},
          {"dim-col" : "SERVICE_CODE", "src-col" : "CODE"},
          {"dim-col" : "SERVICE_SUBTYPE", "src-col" : "SERVICE_SUBTYPE"},
          {"dim-col" : "SERVICE_LABEL", "src-col" : "LABEL"}
        ]
      }]
  },
  {
    "name": "DIM_REASON_FOR_ISSUANCE",
    "table-selectors": ["PNR_CORE"],
    "mapping": {
      "description": {"description": "Lists the various reasons for issuance (RFIC) used in service bookings, seat bookings and coupons", "granularity": "1 reason code"},
      "merge": {
        "key-columns": ["REASON_FOR_ISSUANCE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        { "blocks": [{"rfi": "$.mainResource.current.image.products[?(@.subType == 'SERVICE')].service.priceCategory"}, {"code": "$.code"}]},
        { "blocks": [{"rfi": "$.mainResource.current.image.products[?(@.subType == 'SEATING')].seating.priceCategory"}, {"code": "$.code"}]},
        { "blocks": [{"rfi": "$.mainResource.current.image.ticketingReferences[*].documents[*].coupons[*].reasonForIssuance"}, {"code": "$.code"}]}
      ],
      "columns": [
        {"name": "REASON_FOR_ISSUANCE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"rfi": "$.code"}]}, "expr": "hashS({0})",
          "meta": {"description": {"value": "Hash of the functional key (rfic)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REASON_FOR_ISSUANCE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"rfi": "$.code"}]}},
        {"name": "REASON_FOR_ISSUANCE_LABEL", "column-type": "strColumn", "sources": {"blocks": [{"rfi": "$.code"}]}
          , "meta": {"description": {"value": "Label corresponding to the reason for issuance code", "rule": "replace"}, "example": {"value": "Financial Impact", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_SOURCE", "column-type": "strColumn", "sources": {"literal": "DOMAIN_FEED"}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
      },
      "prefiller": [{
        "data-source-key": "REASON_FOR_ISSUANCE",
        "column-filler" : [
          {"dim-col" : "REASON_FOR_ISSUANCE_ID", "src-col" : "CODE"},
          {"dim-col" : "REASON_FOR_ISSUANCE_CODE", "src-col" : "CODE"},
          {"dim-col" : "REASON_FOR_ISSUANCE_LABEL", "src-col" : "NAME"}
        ]
      }]
  },

  {
    "name": "DIM_FARE_CALC_PRICING_INDICATOR",
    "table-selectors": ["PNR_CORE"],
    "mapping": {
      "description": {"description": "Lists the various fare calc pricing indicators", "granularity": "1 fare calc pricing indicator"},
      "merge": {
        "key-columns": ["FARE_CALC_PRICING_INDICATOR_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"quot": "$.quotations[*].pricingConditions.fareCalculation.pricingIndicator"}]}
      ],
      "columns": [
        {"name": "FARE_CALC_PRICING_INDICATOR_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"quot": "$.value"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "FARE_CALC_PRICING_INDICATOR_CODE", "column-type": "strColumn", "sources": {"blocks": [{"quot": "$.value"}]}},
        {"name": "FARE_CALC_PRICING_INDICATOR_LABEL", "column-type": "strColumn", "sources": {"blocks": [{"quot": "$.value"}]}
          , "meta": {"description": {"value": "Label corresponding to the fare calc pricing indicator code", "rule": "replace"}, "example": {"value": "Automatically priced", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_SOURCE", "column-type": "strColumn", "sources": {"literal": "DOMAIN_FEED"}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
      },
      "prefiller": [{
        "data-source-key": "FARE_CALC_PRICING_INDICATOR",
        "column-filler" : [
          {"dim-col" : "FARE_CALC_PRICING_INDICATOR_ID", "src-col" : "CODE"},
          {"dim-col" : "FARE_CALC_PRICING_INDICATOR_CODE", "src-col" : "CODE"},
          {"dim-col" : "FARE_CALC_PRICING_INDICATOR_LABEL", "src-col" : "LABEL"}
        ]
      }]
  },
  {
    "name": "DIM_FARE_ELEMENT_TYPE",
    "table-selectors": ["PNR_CORE"],
    "mapping": {
      "description": {"description": "Lists the various fare element types", "granularity": "1 fare element type"},
      "merge": {
        "key-columns": ["FARE_ELEMENT_TYPE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        { "name": "pay", "rs" :{ "blocks": [{"base": "$.mainResource.current.image"}, {"fareElem": "$.paymentMethods[*]"},{"dim": "$.code"}]}},
        { "name": "fare", "rs" : { "blocks": [{"base": "$.mainResource.current.image"}, {"fareElem": "$.fareElements[*]"},{"dim": "$.code"}]}}
      ],
      "columns": [
        {"name": "FARE_ELEMENT_TYPE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"fareElem": "$.code"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "FARE_ELEMENT_TYPE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"fareElem": "$.code"}]}},
        {"name": "FARE_ELEMENT_TYPE_LABEL", "column-type": "strColumn", "sources": {"blocks": [{"fareElem": "$.code"}]}
          , "meta": {"description": {"value": "Label corresponding to the fare element type code", "rule": "replace"}, "example": {"value": "Accounting Information", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_SOURCE", "column-type": "strColumn", "sources": {"literal": "DOMAIN_FEED"}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
      },
      "prefiller": [{
        "data-source-key": "FARE_ELEMENT",
        "column-filler" : [
          {"dim-col" : "FARE_ELEMENT_TYPE_ID", "src-col" : "CODE"},
          {"dim-col" : "FARE_ELEMENT_TYPE_CODE", "src-col" : "CODE"},
          {"dim-col" : "FARE_ELEMENT_TYPE_LABEL", "src-col" : "LABEL"}
        ]
      }]
  },
  {
    "name": "DIM_TAX_CODE",
    "table-selectors": ["PNR_CORE"],
    "mapping": {
      "description": {"description": "Lists the various tax codes", "granularity": "1 tax code"},
      "merge": {
        "key-columns": ["TAX_CODE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        { "name": "quot", "rs" :{ "blocks": [ {"base": "$.mainResource.current.image"}, {"quotation": "$.quotations[*]"}, {"tax": "$.price.taxes[*]"},{"dim": "$.code"}]}},
        { "name": "fee", "rs" : { "blocks": [ {"base": "$.mainResource.current.image"}, {"quotation": "$.quotations[*]"}, {"fees": "$.price.fees[*]"}, {"tax": "$.taxes[*]"},{"dim": "$.code"}]}}
      ],
      "columns": [
        {"name": "TAX_CODE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"tax": "$.code"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "TAX_CODE", "column-type": "strColumn", "sources": {"blocks": [{"tax": "$.code"}]}},
        {"name": "TAX_CODE_LABEL", "column-type": "strColumn", "sources": {"blocks": [{"tax": "$.code"}]}
          , "meta": {"description": {"value": "Label corresponding to the tax code", "rule": "replace"}, "example": {"value": "Safety Charge (International) - GABON", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_SOURCE", "column-type": "strColumn", "sources": {"literal": "DOMAIN_FEED"}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
      },
      "prefiller": [{
        "data-source-key": "TAX_CODE",
        "column-filler" : [
          {"dim-col" : "TAX_CODE_ID", "src-col" : "CODE"},
          {"dim-col" : "TAX_CODE", "src-col" : "CODE"},
          {"dim-col" : "TAX_CODE_LABEL", "src-col" : "NAME"}
        ]
        },
        {
          "data-source-key": "COUNTRY",
          "column-filler" : [
            {"dim-col" : "TAX_CODE_ID", "src-col" : "ISO2"},
            {"dim-col" : "TAX_CODE", "src-col" : "ISO2"},
            {"dim-col" : "TAX_CODE_LABEL", "src-col" : "LABEL"}
          ]
        }]
  },
  {
    "name": "DIM_FORM_OF_PAYMENT_TYPE",
    "table-selectors": ["PNR_CORE"],
    "mapping": {
      "description": {"description": "Lists the various form of payment types", "granularity": "1 form of payment type"},
      "merge": {
        "key-columns": ["FORM_OF_PAYMENT_TYPE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        { "name": "pay", "rs" :{ "blocks": [ {"base": "$.mainResource.current.image"}, {"fareElem": "$.paymentMethods[*]"}, {"fop": "$.formsOfPayment[*]"},{"dim": "$.code"} ] }},
        { "name": "fare", "rs" :{ "blocks": [ {"base": "$.mainResource.current.image"}, {"fareElem": "$.fareElements[*]"}, {"fop": "$.formsOfPayment[*]"} ,{"dim": "$.code"} ]}}
      ],
      "columns": [
        {"name": "FORM_OF_PAYMENT_TYPE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"fop": "$.code"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "FORM_OF_PAYMENT_TYPE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"fop": "$.code"}]}},
        {"name": "FORM_OF_PAYMENT_TYPE_LABEL", "column-type": "strColumn", "sources": {"blocks": [{"fop": "$.code"}]}
          , "meta": {"description": {"value": "Label corresponding to the form of payment type code", "rule": "replace"}, "example": {"value": "Account payment", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_SOURCE", "column-type": "strColumn", "sources": {"literal": "DOMAIN_FEED"}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
      },
      "prefiller": [{
        "data-source-key": "FORM_OF_PAYMENT_TYPE",
        "column-filler" : [
          {"dim-col" : "FORM_OF_PAYMENT_TYPE_ID", "src-col" : "CODE"},
          {"dim-col" : "FORM_OF_PAYMENT_TYPE_CODE", "src-col" : "CODE"},
          {"dim-col" : "FORM_OF_PAYMENT_TYPE_LABEL", "src-col" : "NAME"}
        ]
      }]
  },
  {
    "name": "DIM_CARD_VENDOR",
    "table-selectors": ["PNR_CORE"],
    "mapping": {
      "description": {"description": "Lists the various card vendors", "granularity": "1 card vendor"},
      "merge": {
        "key-columns": ["CARD_VENDOR_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        { "name": "pay", "rs" :{ "blocks": [ {"base": "$.mainResource.current.image"}, {"fareElem": "$.paymentMethods[*]"}, {"fop": "$.formsOfPayment[*].paymentCard"},{"dim": "$.vendorCode"} ] }},
        { "name": "fare", "rs" :{ "blocks": [ {"base": "$.mainResource.current.image"}, {"fareElem": "$.fareElements[*]"}, {"fop": "$.formsOfPayment[*].paymentCard"},{"dim": "$.vendorCode"} ]}}
      ],
      "columns": [
        {"name": "CARD_VENDOR_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"fop": "$.vendorCode"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CARD_VENDOR_CODE", "column-type": "strColumn", "sources": {"blocks": [{"fop": "$.vendorCode"}]}},
        {"name": "CARD_VENDOR_LABEL", "column-type": "strColumn", "sources": {"blocks": [{"fop": "$.vendorCode"}]}
          , "meta": {"description": {"value": "Label corresponding to the card vendor code", "rule": "replace"}, "example": {"value": "Visa", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_SOURCE", "column-type": "strColumn", "sources": {"literal": "DOMAIN_FEED"}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
      },
      "prefiller": [{
        "data-source-key": "CARD_VENDOR",
        "column-filler" : [
          {"dim-col" : "CARD_VENDOR_ID", "src-col" : "CODE"},
          {"dim-col" : "CARD_VENDOR_CODE", "src-col" : "CODE"},
          {"dim-col" :  "CARD_VENDOR_LABEL", "src-col" : "NAME"}
        ]
      }]

  },

  {
    "name": "ASSO_AIR_SEGMENT_PAX_KEYWORD",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "latest": {
      "histo-table-name": "ASSO_AIR_SEGMENT_PAX_KEYWORD_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "ASSO_AIR_SEGMENT_PAX_KEYWORD_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "mapping": {
      "description": {"description": "Contains the relationships between booked segments and keywords", "granularity": "1 relationship between 1 segment booking and 1 keyword"},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "AIR_SEGMENT_PAX_ID", "KEYWORD_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
         {"blocks": [
             {"base": "$.mainResource.current.image"},
             {"kw": "$.keywords[*]"},
             {
                 "cartesian": [
                     [{"seg": "$.products[*]"}],
                     [{"trv": "$.travelers[*]"}]
                 ]
             }
         ]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.reference"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "AIR_SEGMENT_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.id"}, {"trv": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_AIR_SEGMENT_PAX_HISTO", "column": "AIR_SEGMENT_PAX_ID"}]},
        {"name": "KEYWORD_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"kw": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_KEYWORD_HISTO", "column": "KEYWORD_ID"}]},
        {"name": "REFERENCE_KEY_AIR_SEGMENT_PAX", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.id"}, {"trv": "$.id"}]},
          "meta": {"description": {"value": "Functional key of the related segment booking", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-ST-1-ABCDEF-2019-10-05-PT-1", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "REFERENCE_KEY_KEYWORD", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"kw": "$.id"}]},
          "meta": {"description": {"value": "Functional key of the related keyword", "rule": "replace"}, "example": {"value": "ABCDEF-2019-05-04-OT-5", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_RESERVATION_HISTO", "column": "RESERVATION_ID"}],
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_LOCATOR", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.reference"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"description": {"value": "Envelope/version of the PNR message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]},
          "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "ASSO_AIR_SEGMENT_PAX_REMARK",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "latest": {
      "histo-table-name": "ASSO_AIR_SEGMENT_PAX_REMARK_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "ASSO_AIR_SEGMENT_PAX_REMARK_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "mapping": {
      "description": {"description": "Contains the relationships between booked segments and remarks", "granularity": "1 relationship between 1 segment booking and 1 remark"},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "AIR_SEGMENT_PAX_ID", "REMARK_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
                 {"blocks": [
                     {"base": "$.mainResource.current.image"},
                     {"rem": "$.remarks[*]"},
                     {
                         "cartesian": [
                             [{"seg": "$.products[*]"}],
                             [{"trv": "$.travelers[*]"}]
                         ]
                     }
                 ]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.reference"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "AIR_SEGMENT_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.id"}, {"trv": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_AIR_SEGMENT_PAX_HISTO", "column": "AIR_SEGMENT_PAX_ID"}]},
        {"name": "REMARK_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"rem": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_REMARK_HISTO", "column": "REMARK_ID"}]},
        {"name": "REFERENCE_KEY_AIR_SEGMENT_PAX", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.id"}, {"trv": "$.id"}]},
          "meta": {"description": {"value": "Functional key of the related segment booking", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-ST-1-ABCDEF-2019-10-05-PT-1", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "REFERENCE_KEY_REMARK", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"rem": "$.id"}]},
          "meta": {"description": {"value": "Functional key of the related remark", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-OT-36", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_RESERVATION_HISTO", "column": "RESERVATION_ID"}],
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_LOCATOR", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.reference"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"description": {"value": "Envelope/version of the PNR message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]},
          "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "ASSO_AIR_SEGMENT_PAX_FLIGHT_TRANSFER",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "latest": {
      "histo-table-name": "ASSO_AIR_SEGMENT_PAX_FLIGHT_TRANSFER_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "ASSO_AIR_SEGMENT_PAX_FLIGHT_TRANSFER_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "mapping": {
      "description": {"description": "Contains the relationships between booked segments and flight transfers (from-segment, to-segment)", "granularity": "1 relationship between 1 segment booking and 1 flight transfer"},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "AIR_SEGMENT_PAX_ID", "FLIGHT_TRANSFER_ID", "RELATES_TO", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        { "name": "from", "rs" :{ "blocks": [
          {"base": "$.mainResource.current.image"},
          {"ft": "$.flightTransfer"},
          {
            "cartesian" : [
              [{"seg": "$.fromSegments[*]"}, {"id": "$.id"}],
              [{"traveler": "$.travelers[*]"}, {"id2": "$.id"}]
          ]}]}},
        { "name": "to", "rs" :{ "blocks": [
          {"base": "$.mainResource.current.image"},
          {"ft": "$.flightTransfer"},
          {
            "cartesian" : [
              [{"seg": "$.toSegments[*]"}, {"id": "$.id"}],
              [{"traveler": "$.travelers[*]"}, {"id2": "$.id"}]
          ]}]}}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.reference"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "AIR_SEGMENT_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.id"},{"traveler": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_AIR_SEGMENT_PAX_HISTO", "column": "AIR_SEGMENT_PAX_ID"}]},
        //No need to put the RELATES_TO in the key as the tattoo ids will be different
        {"name": "FLIGHT_TRANSFER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"},{"ft": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_FLIGHT_TRANSFER_HISTO", "column": "FLIGHT_TRANSFER_ID"}]},
        {"name": "REFERENCE_KEY_AIR_SEGMENT_PAX", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.id"},{"traveler": "$.id"}]},
          "meta": {"description": {"value": "Functional key of the related segment booking", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-ST-1-ABCDEF-2019-10-05-PT-1", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "REFERENCE_KEY_FLIGHT_TRANSFER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"},{"ft": "$.id"}]},
          "meta": {"description": {"value": "Functional key of the related flight transfer", "rule": "replace"}, "example": {"value": "ABCDEF-2023-01-01-32101860", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "RELATES_TO", "column-type": "strColumn",
          "sources": {"root-specific": [
            {"rs-name": "from", "literal": "FROM_SEGMENT"},
            {"rs-name": "to", "literal": "TO_SEGMENT"}
          ]},
          "meta": {"description": {"value": "Indicates which role the related flight segment played in the disruption handling ('from' for being a old/replaced segment, 'to' for being a new/replacing segment)", "rule": "replace"}, "example": {"value": "FROM_SEGMENT", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_RESERVATION_HISTO", "column": "RESERVATION_ID"}],
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_LOCATOR", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.reference"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"description": {"value": "Envelope/version of the PNR message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]},
          "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "ASSO_AIR_SEGMENT_PAX_SERVICE",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "ASSO_AIR_SEGMENT_PAX_SERVICE_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "ASSO_AIR_SEGMENT_PAX_SERVICE_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "description": {"description": "Contains the relationships between booked segments and booked services", "granularity": "1 relationship between 1 segment booking and 1 service booking"},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "AIR_SEGMENT_PAX_ID", "SERVICE_PAX_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [
          {"base": "$.mainResource.current.image"},
          {"srv": "$.products[?(@.subType == 'SERVICE' && @.service.code != 'CLID' && @.service.code != 'FQTU')]"},
          {
            "cartesian": [
              [{"seg": "$.products[*]"}],
              [{"trv": "$.travelers[*]"}]
           ]}
        ]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.reference"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "AIR_SEGMENT_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.id"}, {"trv": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_AIR_SEGMENT_PAX_HISTO", "column": "AIR_SEGMENT_PAX_ID"}]},
        {"name": "SERVICE_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"srv": "$.id"}, {"trv": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_SERVICE_PAX_HISTO", "column": "SERVICE_PAX_ID"}]},
        {"name": "REFERENCE_KEY_AIR_SEGMENT_PAX", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.id"}, {"trv": "$.id"}]},
          "meta": {"description": {"value": "Functional key of the related segment booking", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-ST-1-ABCDEF-2019-10-05-PT-1", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "REFERENCE_KEY_SERVICE_PAX", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"srv": "$.id"}, {"trv": "$.id"}]},
          "meta": {"description": {"value": "Functional key of the related service booking", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-OT-5-ABCDEF-2019-10-05-PT1", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_RESERVATION_HISTO", "column": "RESERVATION_ID"}],
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_LOCATOR", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.reference"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"description": {"value": "Envelope/version of the PNR message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]},
          "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "ASSO_AIR_SEGMENT_PAX_YIELD",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "latest": {
      "histo-table-name": "ASSO_AIR_SEGMENT_PAX_YIELD_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "ASSO_AIR_SEGMENT_PAX_YIELD_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "mapping": {
      "description": {"description": "Contains the relationships between booked segments and segment yields", "granularity": "1 relationship between 1 booked segment and 1 segment yield"},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "AIR_SEGMENT_PAX_ID", "SEGMENT_YIELD_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"name": "ope", "rs" :{"blocks": [
          {"base": "$.mainResource.current.image"},
          {
            "cartesian": [
              [{"seg": "$.products[?(@.subType == 'AIR')]"}, {"yield": "$.airSegment.operating.bookingClass.subClass.yields[*]"}],
              [{"trv": "$.travelers[*]"}]
           ]}
        ]}},
        {"name": "mkt", "rs" :{"blocks": [
          {"base": "$.mainResource.current.image"},
          {
            "cartesian": [
              [{"seg": "$.products[?(@.subType == 'AIR')]"}, {"yield": "$.airSegment.marketing.bookingClass.subClass.yields[*]"}],
              [{"trv": "$.travelers[*]"}]
           ]}
        ]}}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.reference"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "AIR_SEGMENT_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.id"}, {"trv": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_AIR_SEGMENT_PAX_HISTO", "column": "AIR_SEGMENT_PAX_ID"}]},
        {"name": "SEGMENT_YIELD_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.id"}, {"yield": "$.elementaryPriceType"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_SEGMENT_YIELD_HISTO", "column": "SEGMENT_YIELD_ID"}]},
        {"name": "REFERENCE_KEY_AIR_SEGMENT_PAX", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.id"}, {"trv": "$.id"}]},
          "meta": {"description": {"value": "Functional key of the related segment booking", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-ST-1-ABCDEF-2019-10-05-PT-1", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "REFERENCE_KEY_SEGMENT_YIELD", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.id"}, {"yield": "$.elementaryPriceType"}]},
          "meta": {"description": {"value": "Functional key of the related segment yield", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-ST-1-ADJUSTED_YIELD", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_RESERVATION_HISTO", "column": "RESERVATION_ID"}],
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_LOCATOR", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.reference"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"description": {"value": "Envelope/version of the PNR message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]},
          "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "ASSO_AIR_SEGMENT_PAX_SEATING",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "latest": {
      "histo-table-name": "ASSO_AIR_SEGMENT_PAX_SEATING_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "ASSO_AIR_SEGMENT_PAX_SEATING_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "mapping": {
      "description": {"description": "Contains the relationships between booked segments and booked seats", "granularity": "1 relationship between 1 segment booking and 1 seat booking"},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "AIR_SEGMENT_PAX_ID", "SEATING_PAX_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [
          {"base": "$.mainResource.current.image"},
          {"seat": "$.products[?(@.subType == 'SEATING')]"},
          {
            "cartesian": [
              [{"seg": "$.products[*]"}],
              [{"trv": "$.travelers[*]"}]
           ]}
        ]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.reference"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "AIR_SEGMENT_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.id"}, {"trv": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_AIR_SEGMENT_PAX_HISTO", "column": "AIR_SEGMENT_PAX_ID"}]},
        {"name": "SEATING_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"seat": "$.id"}, {"trv": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_SEATING_PAX_HISTO", "column": "SEATING_PAX_ID"}]},
        {"name": "REFERENCE_KEY_AIR_SEGMENT_PAX", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.id"}, {"trv": "$.id"}]},
          "meta": {"description": {"value": "Functional key of the related segment booking", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-ST-1-ABCDEF-2019-10-05-PT-1", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "REFERENCE_KEY_SEATING_PAX", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"seat": "$.id"}, {"trv": "$.id"}]},
          "meta": {"description": {"value": "Functional key of the related seating booking", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-OT-5-ABCDEF-2019-10-05-PT1", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_RESERVATION_HISTO", "column": "RESERVATION_ID"}],
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_LOCATOR", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.reference"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"description": {"value": "Envelope/version of the PNR message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]},
          "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "ASSO_AIR_SEGMENT_PAX_AUTOMATED_PROCESS",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "latest": {
      "histo-table-name": "ASSO_AIR_SEGMENT_PAX_AUTOMATED_PROCESS_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "ASSO_AIR_SEGMENT_PAX_AUTOMATED_PROCESS_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "mapping": {
      "description": {"description": "Contains the relationships between booked segments and automated ticketing processes", "granularity": "1 relationship between 1 segment booking and 1 automated process"},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "AIR_SEGMENT_PAX_ID", "AUTOMATED_PROCESS_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources":
        [
          {"blocks": [
            {"base": "$.mainResource.current.image"},
            {"pro": "$.automatedProcesses[*]"},
            {"cartesian": [
              [{"seg": "$.products[*]"}],
              [{"trv": "$.travelers[*]"}]
            ]}
          ]}
        ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.reference"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "AIR_SEGMENT_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.id"}, {"trv": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_AIR_SEGMENT_PAX_HISTO", "column": "AIR_SEGMENT_PAX_ID"}]},
        {"name": "AUTOMATED_PROCESS_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"pro": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_AUTOMATED_PROCESS_HISTO", "column": "AUTOMATED_PROCESS_ID"}]},
        {"name": "REFERENCE_KEY_AIR_SEGMENT_PAX", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.id"}, {"trv": "$.id"}]},
          "meta": {"description": {"value": "Functional key of the related segment booking", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-ST-1-ABCDEF-2019-10-05-PT-1", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "REFERENCE_KEY_AUTOMATED_PROCESS", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"pro": "$.id"}]},
          "meta": {"description": {"value": "Functional key of the related automated process", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-OT-37", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_RESERVATION_HISTO", "column": "RESERVATION_ID"}],
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_LOCATOR", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.reference"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"description": {"value": "Envelope/version of the PNR message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]},
          "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "ASSO_TRAVEL_DOCUMENT_AIR_SEGMENT_PAX",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "latest": {
      "histo-table-name": "ASSO_TRAVEL_DOCUMENT_AIR_SEGMENT_PAX_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "ASSO_TRAVEL_DOCUMENT_AIR_SEGMENT_PAX_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "mapping": {
      "description": {"description": "Contains the relationships between travel documents (tickets) and booked segments", "granularity": "1 relationship between 1 ticket and 1 segment booking"},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "TRAVEL_DOCUMENT_ID", "AIR_SEGMENT_PAX_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [
          {"base": "$.mainResource.current.image"},
          {"ref": "$.ticketingReferences[*]"},
          {"cartesian": [
            [{"seg": "$.products[*]"}],
            [{"doc": "$.documents[*]"}]
          ]}
        ]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.reference"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [ {"ref": "$.id"}, {"doc": "$.primaryDocumentNumber"}, {"doc": "$.documentNumber"}]}, "expr": hashM(${createConsolidatedDocumentNumberReferenceKey}),
          "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}]},
        {"name": "AIR_SEGMENT_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.id"}, {"ref": "$.traveler.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_AIR_SEGMENT_PAX_HISTO", "column": "AIR_SEGMENT_PAX_ID"}]},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [ {"ref": "$.id"}, {"doc": "$.primaryDocumentNumber"}, {"doc": "$.documentNumber"}]}, "expr": ${createConsolidatedDocumentNumberReferenceKey},
          "meta": {"description": {"value": "Functional key of the related travel document", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-OT18-123456789123", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "REFERENCE_KEY_AIR_SEGMENT_PAX", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.id"}, {"ref": "$.traveler.id"}]},
          "meta": {"description": {"value": "Functional key of the related segment booking", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-ST-1-ABCDEF-2019-10-05-PT-1", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_RESERVATION_HISTO", "column": "RESERVATION_ID"}],
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_LOCATOR", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.reference"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"description": {"value": "Envelope/version of the PNR message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]},
          "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "ASSO_TRAVEL_DOCUMENT_SERVICE_PAX",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "latest": {
      "histo-table-name": "ASSO_TRAVEL_DOCUMENT_SERVICE_PAX_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "ASSO_TRAVEL_DOCUMENT_SERVICE_PAX_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "mapping": {
      "description": {"description": "Contains the relationships between travel documents (EMDs) and booked services", "granularity": "1 relationship between 1 EMD and 1 service booking"},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "TRAVEL_DOCUMENT_ID", "SERVICE_PAX_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [
          {"base": "$.mainResource.current.image"},
          {"ref": "$.ticketingReferences[*]"},
          {"cartesian": [
            [{"srv": "$.products[*]"}],
            [{"doc": "$.documents[*]"}]
          ]}
        ]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.reference"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [ {"ref": "$.id"}, {"doc": "$.primaryDocumentNumber"}, {"doc": "$.documentNumber"}]}, "expr": hashM(${createConsolidatedDocumentNumberReferenceKey}),
          "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}]},
        {"name": "SERVICE_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"srv": "$.id"}, {"ref": "$.traveler.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_SERVICE_PAX_HISTO", "column": "SERVICE_PAX_ID"}]},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [ {"ref": "$.id"}, {"doc": "$.primaryDocumentNumber"}, {"doc": "$.documentNumber"}]}, "expr": ${createConsolidatedDocumentNumberReferenceKey},
          "meta": {"description": {"value": "Functional key of the related travel document", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-OT18-123456789123", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "REFERENCE_KEY_SERVICE_PAX", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"srv": "$.id"}, {"ref": "$.traveler.id"}]},
          "meta": {"description": {"value": "Functional key of the related service booking", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-OT-5-ABCDEF-2019-10-05-PT1", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_RESERVATION_HISTO", "column": "RESERVATION_ID"}],
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_LOCATOR", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.reference"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"description": {"value": "Envelope/version of the PNR message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]},
          "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "ASSO_TRAVEL_DOCUMENT_SEATING_PAX",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "latest": {
      "histo-table-name": "ASSO_TRAVEL_DOCUMENT_SEATING_PAX_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "ASSO_TRAVEL_DOCUMENT_SEATING_PAX_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "mapping": {
      "description": {"description": "Contains the relationships between travel documents (EMDs) and booked seats", "granularity": "1 relationship between 1 EMD and 1 seat booking"},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "TRAVEL_DOCUMENT_ID", "SEATING_PAX_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [
          {"base": "$.mainResource.current.image"},
          {"ref": "$.ticketingReferences[*]"},
          {"cartesian": [
            [{"seat": "$.products[*]"}],
            [{"doc": "$.documents[*]"}]
          ]}
        ]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.reference"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [ {"ref": "$.id"}, {"doc": "$.primaryDocumentNumber"}, {"doc": "$.documentNumber"}]}, "expr": hashM(${createConsolidatedDocumentNumberReferenceKey}),
          "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}]},
        {"name": "SEATING_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"seat": "$.id"}, {"ref": "$.traveler.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_SEATING_PAX_HISTO", "column": "SEATING_PAX_ID"}]},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [ {"ref": "$.id"}, {"doc": "$.primaryDocumentNumber"}, {"doc": "$.documentNumber"}]}, "expr": ${createConsolidatedDocumentNumberReferenceKey},
          "meta": {"description": {"value": "Functional key of the related travel document", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-OT18-123456789123", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "REFERENCE_KEY_SEATING_PAX", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"seat": "$.id"}, {"ref": "$.traveler.id"}]},
          "meta": {"description": {"value": "Functional key of the related seating booking", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-OT-5-ABCDEF-2019-10-05-PT1", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_RESERVATION_HISTO", "column": "RESERVATION_ID"}],
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_LOCATOR", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.reference"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"description": {"value": "Envelope/version of the PNR message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]},
          "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  },
  {
    "name": "ASSO_TRAVELER_CONTACT",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "latest": {
      "histo-table-name": "ASSO_TRAVELER_CONTACT_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "ASSO_TRAVELER_CONTACT_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_CORE"],
    "mapping": {
      "description": {"description": "Contains the relationships between travelers and contacts (postal addresses, phones, emails)", "granularity": "1 relationship between 1 traveler and 1 contact"},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "TRAVELER_ID", "ADDRESS_ID", "PHONE_ID", "EMAIL_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"name": "addressRS", "rs": { "blocks": [{"base": "$.mainResource.current.image"}, {"contact": "$.contacts[?(@.address)]"}, {"traveler": "$.travelerRefs[*]"}]}},
        {"name": "phoneRS", "rs": { "blocks": [{"base": "$.mainResource.current.image"}, {"contact": "$.contacts[?(@.phone)]"}, {"traveler": "$.travelerRefs[*]"}]}},
        {"name": "emailRS", "rs": { "blocks": [{"base": "$.mainResource.current.image"}, {"contact": "$.contacts[?(@.email)]"}, {"traveler": "$.travelerRefs[*]"}]}}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.reference"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "TRAVELER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"traveler": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_TRAVELER_HISTO", "column": "TRAVELER_ID"}]},
        {"name": "ADDRESS_ID", "column-type": "binaryStrColumn", "sources": {"root-specific": [{"rs-name": "addressRS", "blocks": [{"contact": "$.id"}]}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_ADDRESS_HISTO", "column": "ADDRESS_ID"}]},
        {"name": "PHONE_ID", "column-type": "binaryStrColumn", "sources": {"root-specific": [{"rs-name": "phoneRS", "blocks": [{"contact": "$.id"}]}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_PHONE_HISTO", "column": "PHONE_ID"}]},
        {"name": "EMAIL_ID", "column-type": "binaryStrColumn", "sources": {"root-specific": [{"rs-name": "emailRS", "blocks": [{"contact": "$.id"}]}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_EMAIL_HISTO", "column": "EMAIL_ID"}]},
        {"name": "REFERENCE_KEY_TRAVELER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"traveler": "$.id"}]},
          "meta": {"description": {"value": "Functional key of the related traveler", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-PT-1", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "REFERENCE_KEY_ADDRESS", "column-type": "strColumn", "sources": {"root-specific": [{"rs-name": "addressRS", "blocks": [{"contact": "$.id"}]}]},
          "meta": {"description": {"value": "Functional key of the related address", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-OT-14", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "REFERENCE_KEY_PHONE", "column-type": "strColumn", "sources": {"root-specific": [{"rs-name": "phoneRS", "blocks": [{"contact": "$.id"}]}]},
          "meta": {"description": {"value": "Functional key of the related phone", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-OT-10", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "REFERENCE_KEY_EMAIL", "column-type": "strColumn", "sources": {"root-specific": [{"rs-name": "emailRS", "blocks": [{"contact": "$.id"}]}]},
          "meta": {"description": {"value": "Functional key of the related email", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-OT-10", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_RESERVATION_HISTO", "column": "RESERVATION_ID"}],
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_LOCATOR", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.reference"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"description": {"value": "Envelope/version of the PNR message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]},
          "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
    }
  }
]
