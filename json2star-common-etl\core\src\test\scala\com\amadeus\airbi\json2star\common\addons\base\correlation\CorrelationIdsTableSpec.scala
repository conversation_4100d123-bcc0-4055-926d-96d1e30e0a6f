package com.amadeus.airbi.json2star.common.addons.base.correlation

import com.amadeus.airbi.datalake.common.spark.SparkSqlSpecification
import com.amadeus.airbi.json2star.common.addons.base.correlation.CorrelationPipeline.{DomainA, DomainB}
import com.amadeus.airbi.rawvault.common.testfwk.{DataFrameComparison, TmpDir}
import io.delta.tables.DeltaTable
import org.apache.spark.sql.DataFrame
import org.scalatest.BeforeAndAfterEach

import java.util.TimeZone

class CorrelationIdsTableSpec extends SparkSqlSpecification with BeforeAndAfterEach with TmpDir {

  import spark.implicits._

  override def beforeAll: Unit = {
    super.beforeAll
    TimeZone.setDefault(TimeZone.getTimeZone("UTC"))
    spark.conf.set("spark.databricks.delta.allowArbitraryProperties.enabled", "true")
    spark.catalog.setCurrentDatabase(outputDatabase)
  }

  override def beforeEach: Unit = {}

  def createTable(table: String, idCol: String): Unit = {
    DeltaTable
      .create(spark)
      .tableName(table)
      .property("delta.enableChangeDataFeed", "true")
      .addColumn(idCol, "STRING")
      .addColumn("VERSION", "STRING")
      .addColumn("VALUE", "INT")
      .execute()
  }

  def manualDelete(table: String, idCol: String, idValue: String): Unit = {
    DeltaTable.forName(table).delete(s"$idCol = '$idValue'")
  }

  def mergeTable(table: String, idCol: String, values: Seq[(String, String, Int)], rewrite: Boolean): Unit = {
    val input = values.toDF(idCol, "VERSION", "VALUE")
    DeltaTable
      .forName(table)
      .as("dst")
      .merge(input.as("src"), s"src.$idCol == dst.$idCol and src.VERSION == dst.VERSION")
      .whenMatched
      .updateAll()
      .whenNotMatched
      .insertAll()
      .execute()

    if (rewrite) {
      // simulate the file rewrite - this is triggered by the delta auto-compact or running the optimize with z-order
      // --> optimize to have all versions for the same ID in the same file
      DeltaTable
        .forName(table)
        .optimize()
        .executeZOrderBy(idCol)
    }
  }

  def checkNumFiles(table: String): Long = {
    val numFiles = DeltaTable
      .forName(table)
      .detail()
      .select("numFiles")
      .first()
      .getLong(0)
    numFiles
  }

  def checkTable(actualTable: String, expected: DataFrame): Unit = {
    val actual = spark.sql(s"SELECT * FROM $actualTable ORDER BY DOMAIN_A_ID, DOMAIN_B_ID")
    DataFrameComparison.compareData(actualTable, actual, expected)
  }

  // simulate the reading of the corr ids table
  // check the content in a streaming context
  def checkBatchContent(table: String, expected: DataFrame)(actualBatch: DataFrame, batchId: Long): Unit = {
    val actual = actualBatch.orderBy("DOMAIN_A_ID", "DOMAIN_B_ID")
    DataFrameComparison.compareData(s"batch_${batchId}_${table}", actual, expected)
  }

  def clean(tables: Seq[String]): Unit = {
    tables.foreach { table =>
      spark.sql(s"DROP TABLE $table")
    }
  }

  "CorrelationIdsTable" should "create the internal IDS table to track input streams from 2 domains" in withTmpDir {
    tmpDir =>
      val producerIdsCheckpoint = tmpDir.toAbsolutePath + "checkpoint"
      val consumerIdsCheckpoint = tmpDir.toAbsolutePath + "checkpoint_corr_ids"

      val targetTable = "ASSO_PAX_WITH_RESERVATION"
      val corrIds = new CorrelationIdsTable(outputDatabase, targetTable)
      val corrIdsTable = corrIds.TableName
      corrIds.createTableIfNotExists(spark)
      // Example
      // Inputs - Producers
      // - table A
      // - table B
      // Output - Consumer
      // - stream from table A into table IDS
      // - stream from table B into table IDS

      val srcTableA = "FACT_PASSENGER_HISTO"
      val srcIdColA = "PASSENGER_ID"

      val srcTableB = "FACT_RESERVATION_HISTO"
      val srcIdColB = "RESERVATION_ID"

      val inputRewrite = true
      // producer: generate srcTableA with 3 rows
      createTable(srcTableA, srcIdColA)
      mergeTable(srcTableA, srcIdColA, Seq(("PAX_ID_1", "v1", 20)), inputRewrite)
      mergeTable(srcTableA, srcIdColA, Seq(("PAX_ID_2", "v1", 21)), inputRewrite)
      mergeTable(srcTableA, srcIdColA, Seq(("PAX_ID_3", "v1", 22)), inputRewrite)

      // producer: generate srcTableB with 2 rows
      createTable(srcTableB, srcIdColB)
      mergeTable(srcTableB, srcIdColB, Seq(("RES_ID_1", "v1", 10)), inputRewrite)
      mergeTable(srcTableB, srcIdColB, Seq(("RES_ID_2", "v1", 11)), inputRewrite)

      // consumer: read srcTableA as stream --> insert into IDS table
      corrIds.insertCorrIds(spark, srcTableA, srcIdColA, DomainA, producerIdsCheckpoint)

      // consumer: read srcTableB as stream --> insert into IDS table
      corrIds.insertCorrIds(spark, srcTableB, srcIdColB, DomainB, producerIdsCheckpoint)

      // note: it checks that IDs are extracted correctly from 2 input domains A and B

      // run the stream to consume the corr ids
      // check if the read within the batch match the exepected content
      corrIds.streamCorrIds(
        spark,
        consumerIdsCheckpoint,
        checkBatchContent(
          table = corrIdsTable,
          expected = Seq(
            (null, "RES_ID_1"),
            (null, "RES_ID_2"),
            ("PAX_ID_1", null),
            ("PAX_ID_2", null),
            ("PAX_ID_3", null)
          ).toDF("DOMAIN_A_ID", "DOMAIN_B_ID")
        )
      )

      // clean up tables
      clean(Seq(srcTableA, srcTableB, corrIdsTable))
  }

  /** Run a scenario with multiple updates in the upstream table
    * It simulates the normal data flows in J2S tables with multiple insert/update operations and multiple batches
    *
    * Processing Steps are:
    * 1) J2S step streams JSON to compute the FACT Table        (code mocked as INSERT/UPDATE operations on the table)
    * 2) CorrIds step streams Fact Table to compute IDS table   (code called)
    * 3) Corr step streams IDS table to compute Corr table      (code called, but checked directly the content of the batch)
    *
    * note: this test checks that only modified IDS in upstream are then re-computed in step 3
    *  otherwise more IDS than necessary are reprocessed in 3) impacting overall performance read/write amplification effect
    *
    * Data samples are:
    *  - PAX_ID_1: insert v1                          | before starting the step2 - stream of CorrIds
    *  - PAX_ID_2: insert v1 + update v1 + insert v2  | before starting the step2 - stream of CorrIds
    *  - PAX_ID_3: insert v1                          | after starting the step2 - stream of CorrIds
    *  - PAX_ID_4: insert v1 + update v1 + insert v2  | after starting the step 2 - stream of CorrIds
    *
    * @param inputRewrite flag to simulate the file rewrite in the upstream tables (e.g. optimize, auto-compaction)
    * @param expectedInputNumFilesAfterUpdate expected number of files in the upstream table after each operation
    * @param expectedCorrIds expected content of the correlation ids batch as read in te the Corr step
    */
  def runScenario(
    inputRewrite: Boolean,
    expectedInputNumFilesAfterUpdate: Seq[Int],
    expectedCorrIds: DataFrame
  ): Unit = {
    withTmpDir { tmpDir =>
      val producerIdsCheckpoint = tmpDir + "checkpoint"
      val consumerIdsCheckpoint = tmpDir + "checkpoint_corr_ids"

      val targetTable = "ASSO_PAX_WITH_RESERVATION"
      val corrIds = new CorrelationIdsTable(outputDatabase, targetTable)
      val corrIdsTable = corrIds.TableName
      corrIds.createTableIfNotExists(spark)

      val srcTableA = "FACT_PASSENGER_HISTO"
      val srcIdColA = "PASSENGER_ID"

      // Step 1 - Producer: generate srcTableA
      createTable(srcTableA, srcIdColA)

      // INPUT scenario 1: insert in input before starting the stream
      mergeTable(srcTableA, srcIdColA, Seq(("PAX_ID_1", "v1", 20)), inputRewrite)
      checkNumFiles(srcTableA) should be(expectedInputNumFilesAfterUpdate(0))

      // INPUT scenario 2: update in input before starting the stream
      mergeTable(srcTableA, srcIdColA, Seq(("PAX_ID_2", "v1", 20)), inputRewrite)
      checkNumFiles(srcTableA) should be(expectedInputNumFilesAfterUpdate(1))
      mergeTable(srcTableA, srcIdColA, Seq(("PAX_ID_2", "v1", 22), ("PAX_ID_2", "v2", 23)), inputRewrite)
      checkNumFiles(srcTableA) should be(expectedInputNumFilesAfterUpdate(1))

      // Step 2 - Consumer of 1st step
      // CORR start the stream over srcTableA and consumes directly the latest delta snapshot (only 1 rows is received for each ID,Version)
      corrIds.insertCorrIds(spark, srcTableA, srcIdColA, DomainA, producerIdsCheckpoint)

      // Step 3 - Consumer of 2nd step - check content of the batch
      corrIds.streamCorrIds(
        spark,
        consumerIdsCheckpoint,
        checkBatchContent(
          table = corrIdsTable,
          expected = Seq(
            ("PAX_ID_1", null),
            ("PAX_ID_2", null)
          ).toDF("DOMAIN_A_ID", "DOMAIN_B_ID")
        )
      )

      // Step 1 - Producer: generate srcTableA
      // INPUT scenario 3: insert in input after starting the stream
      mergeTable(srcTableA, srcIdColA, Seq(("PAX_ID_3", "v1", 21)), inputRewrite)
      checkNumFiles(srcTableA) should be(expectedInputNumFilesAfterUpdate(2))

      // INPUT scenario 4: update in input after starting the stream
      mergeTable(srcTableA, srcIdColA, Seq(("PAX_ID_4", "v1", 21)), inputRewrite)
      checkNumFiles(srcTableA) should be(expectedInputNumFilesAfterUpdate(3))
      mergeTable(srcTableA, srcIdColA, Seq(("PAX_ID_4", "v1", 22), ("PAX_ID_4", "v2", 23)), inputRewrite)
      checkNumFiles(srcTableA) should be(expectedInputNumFilesAfterUpdate(3))

      // Step 2 - Consumer of 1st step
      // CORR continue the stream over srcTableA and consumes all delta snapshots
      corrIds.insertCorrIds(spark, srcTableA, srcIdColA, DomainA, producerIdsCheckpoint)

      // Step 3 - Consumer of 2nd step - check content of the batch
      corrIds.streamCorrIds(
        spark,
        consumerIdsCheckpoint,
        checkBatchContent(
          table = corrIdsTable,
          expected = expectedCorrIds
        )
      )

      clean(Seq(srcTableA, corrIdsTable))
    }
  }

  /** It simulates a delete in upstream table
    * Example: manual operation to delete records
    * The current J2S logic performs only insert/update operations
    *
    * @param inputRewrite flag to simulate the file rewrite in the upstream tables (e.g. optimize, auto-compaction)
    * @param expectedInputNumFilesAfterUpdate expected number of files in the upstream table after each operation
    * @param expectedCorrIds expected content of the correlation ids batch as read in te the Corr step
    */
  def runDeleteScenario(
    inputRewrite: Boolean,
    expectedInputNumFilesAfterUpdate: Seq[Int],
    expectedCorrIds: DataFrame
  ): Unit = {
    withTmpDir { tmpDir =>
      val producerIdsCheckpoint = tmpDir + "checkpoint"
      val consumerIdsCheckpoint = tmpDir + "checkpoint_corr_ids"

      val targetTable = "ASSO_PAX_WITH_RESERVATION"
      val corrIds = new CorrelationIdsTable(outputDatabase, targetTable)
      val corrIdsTable = corrIds.TableName
      corrIds.createTableIfNotExists(spark)

      val srcTableA = "FACT_PASSENGER_HISTO"
      val srcIdColA = "PASSENGER_ID"

      // Step 1 - Producer: generate srcTableA
      createTable(srcTableA, srcIdColA)

      // INPUT scenario 1: insert and delete in input before starting the stream
      mergeTable(srcTableA, srcIdColA, Seq(("PAX_ID_1", "v0", 20)), inputRewrite)
      mergeTable(srcTableA, srcIdColA, Seq(("PAX_ID_DEL1", "v1", 20)), inputRewrite)
      checkNumFiles(srcTableA) should be(expectedInputNumFilesAfterUpdate(0))
      manualDelete(srcTableA, srcIdColA, "PAX_ID_DEL1")
      checkNumFiles(srcTableA) should be(expectedInputNumFilesAfterUpdate(1))

      // Step 2 - Consumer of 1st step
      // CORR start the stream over srcTableA and consumes directly the latest delta snapshot (only 1 rows is received for each ID,Version)
      corrIds.insertCorrIds(spark, srcTableA, srcIdColA, DomainA, producerIdsCheckpoint)

      // Step 3 - Consumer of 2nd step - check content of the batch
      // note: PAX_ID_DEL1 is not present
      corrIds.streamCorrIds(
        spark,
        consumerIdsCheckpoint,
        checkBatchContent(
          table = corrIdsTable,
          expected = Seq(
            ("PAX_ID_1", null)
          ).toDF("DOMAIN_A_ID", "DOMAIN_B_ID")
        )
      )

      // Step 1 - Producer: generate srcTableA
      // Input scenario DEL2: insert and delete in input after starting the stream
      mergeTable(srcTableA, srcIdColA, Seq(("PAX_ID_2", "v0", 20)), inputRewrite)
      mergeTable(srcTableA, srcIdColA, Seq(("PAX_ID_DEL2", "v1", 20)), inputRewrite)
      checkNumFiles(srcTableA) should be(expectedInputNumFilesAfterUpdate(2))
      manualDelete(srcTableA, srcIdColA, "PAX_ID_DEL2")
      checkNumFiles(srcTableA) should be(expectedInputNumFilesAfterUpdate(3))

      // Step 2 - Consumer of 1st step
      // CORR continues the stream over srcTableA and consumes all delta snapshots
      corrIds.insertCorrIds(spark, srcTableA, srcIdColA, DomainA, producerIdsCheckpoint)

      // Step 3 - Consumer of 2nd step - check content of the batch
      // note: without file rewrite in input, unchanged records are not emitted again
      corrIds.streamCorrIds(
        spark,
        consumerIdsCheckpoint,
        checkBatchContent(
          table = corrIdsTable,
          expected = expectedCorrIds
        )
      )

      clean(Seq(srcTableA, corrIdsTable))
    }
  }

  it should s"create the internal IDS table to track input streams - multiple updates in upstream - WITH file rewrite" in {
    runScenario(
      inputRewrite = true,
      expectedInputNumFilesAfterUpdate = Seq(1, 1, 1, 1), // always 1 file because of the rewrite
      expectedCorrIds = Seq(
        ("PAX_ID_3", null), // only new rows are emitted even with file rewrite, thanks to cdf
        ("PAX_ID_4", null)
      ).toDF("DOMAIN_A_ID", "DOMAIN_B_ID")
    )
  }

  it should s"create the internal IDS table to track input streams - multiple updates in upstream - NO file rewrite" in {
    runScenario(
      inputRewrite = false,
      expectedInputNumFilesAfterUpdate = Seq(1, 2, 3, 4), // 1 file is added in input after each operation
      expectedCorrIds = Seq(
        ("PAX_ID_3", null), // only new rows are emitted no file rewrite, thanks to cdf
        ("PAX_ID_4", null)
      ).toDF("DOMAIN_A_ID", "DOMAIN_B_ID")
    )
  }

  it should s"create the internal IDS table to track input streams - manual delete patch - WITH file rewrite" in {
    runDeleteScenario(
      inputRewrite = true,
      expectedInputNumFilesAfterUpdate = Seq(1, 1, 1, 1),
      expectedCorrIds = Seq(
        ("PAX_ID_2", null),
        ("PAX_ID_DEL2", null)
      ).toDF("DOMAIN_A_ID", "DOMAIN_B_ID")
    )
  }

  it should s"create the internal IDS table to track input streams - manual delete patch - NO file rewrite" in {
    runDeleteScenario(
      inputRewrite = false,
      expectedInputNumFilesAfterUpdate = Seq(2, 1, 3, 2),
      expectedCorrIds = Seq(
        ("PAX_ID_2", null),
        ("PAX_ID_DEL2", null)
      ).toDF("DOMAIN_A_ID", "DOMAIN_B_ID")
    )
  }
}
