package com.amadeus.airbi.json2star.common.addons.mapping

import com.amadeus.airbi.common.utils.CommonSpec
import com.amadeus.airbi.json2star.common.addons.base.mapping.OriginResolver
import com.amadeus.airbi.json2star.common.{MappingType, Origin, TableDef, TablesDef}
import com.amadeus.airbi.json2star.common.app.Json2StarApp.readMappingConfig
import com.amadeus.airbi.rawvault.common.testfwk.ImplicitConversions.OriginConverters

class OriginResolverSpec extends CommonSpec {

  OriginResolver.getClass.getName should "resolve columns origin (synthetic mapping file)" in {
    val mp = readMappingConfig("views/sample_4/mapping.conf")
    val tables = TablesDef.consolidate(mp).tables

    getOrigins(tables, "FACT_RESERVATION_HISTO") should ===(
      List(
        (
          "RESERVATION_ID",
          List("$.mainResource.current.image.id", "$.mainResource.current.image.dummy.id").map(_.toOrigin("hashM"))
        ),
        (
          "VERSION",
          List("$.mainResource.current.image.version", "$.mainResource.current.image.dummy.version").map(_.toOrigin)
        ),
        (
          "PNR_CREATION_DATE",
          List("$.mainResource.current.image.creation.dateTime", "$.mainResource.current.image.dummy.creation.dateTime")
            .map(_.toOrigin)
        ),
        (
          "DATE_BEGIN",
          List(
            "$.mainResource.current.image.lastModification.dateTime",
            "$.mainResource.current.image.dummy.lastModification.dateTime"
          ).map(_.toOrigin)
        ),
        ("DATE_END", List()),
        ("IS_LAST_VERSION", List()),
        ("LOAD_DATE", List())
      )
    )

    getOrigins(tables, "DIM_POINT_OF_SALE") should ===(
      List(
        (
          "COMMON_COLUMN",
          List(
            "$.mainResource.current.image.owner.office.id",
            "$.mainResource.current.image.creation.pointOfSale.office.id",
            "$.mainResource.current.image.lastModification.pointOfSale.office.id"
          ).map(_.toOrigin)
        ),
        (
          "POINT_OF_SALE_ID",
          List(
            "$.mainResource.current.image.owner.office.id - $.mainResource.current.image.owner.login.cityCode",
            "$.mainResource.current.image.creation.pointOfSale.office.id - $.mainResource.current.image.creation.pointOfSale.login.cityCode",
            "$.mainResource.current.image.lastModification.pointOfSale.office.id - $.mainResource.current.image.lastModification.pointOfSale.login.iataNumber"
          ).map(_.toOrigin("hashM"))
        ),
        ("COL_PRESENT_ONLY_IN_A_ROOT", List("$.mainResource.current.image.owner.office.id").map(_.toOrigin)),
        ("LOAD_DATE", List())
      )
    )

    getOrigins(tables, "DIM_CARRIER") should ===(
      List(
        (
          "CARRIER_ID",
          List(
            "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.marketing.flightDesignator.carrierCode.value",
            "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.operating.flightDesignator.carrierCode.value",
            "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].flightSegment.marketing.flightDesignator.carrierCode.value"
          ).map(_.toOrigin("hashXS"))
        ),
        (
          "CARRIER",
          List(
            "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.marketing.flightDesignator.carrierCode.value",
            "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.operating.flightDesignator.carrierCode.value",
            "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].flightSegment.marketing.flightDesignator.carrierCode.value"
          ).map(_.toOrigin)
        ),
        ("LOAD_DATE", List())
      )
    )

  }

  it should "resolve columns origin from different variants of cartesian product based tables" in {
    val mp = readMappingConfig("views/sample_8/mapping.conf")
    val tables = TablesDef.consolidate(mp).tables

    getOrigins(tables, "DIM_TWO_BRANCHES") should ===(
      List(
        ("BASE", List("$.mainResource.current.image.baseId").map(_.toOrigin)),
        ("PRODUCT", List("$.mainResource.current.image.products[*].productId").map(_.toOrigin)),
        ("TRAVELER", List("$.mainResource.current.image.travelers[*].travelerId").map(_.toOrigin)),
        ("LOAD_DATE", List())
      )
    )

    getOrigins(tables, "DIM_THREE_BRANCHES") should ===(
      List(
        ("BASE", List("$.mainResource.current.image.baseId").map(_.toOrigin)),
        ("PRODUCT", List("$.mainResource.current.image.products[*].productId").map(_.toOrigin)),
        ("TRAVELER", List("$.mainResource.current.image.travelers[*].travelerId").map(_.toOrigin)),
        ("LEG", List("$.mainResource.current.image.legs[*].legId").map(_.toOrigin)),
        ("LOAD_DATE", List())
      )
    )

    getOrigins(tables, "DIM_THREE_BRANCHES_MULTI_BLOCK") should ===(
      List(
        ("BASE", List("$.mainResource.current.image.baseId").map(_.toOrigin)),
        ("PRODUCT", List("$.mainResource.current.image.products[*].productId").map(_.toOrigin)),
        ("TRAVELER", List("$.mainResource.current.image.travelers[*].travelerId").map(_.toOrigin)),
        ("LEG", List("$.mainResource.current.image.legs[*].legId").map(_.toOrigin)),
        ("CABIN", List("$.mainResource.current.image.legs[*].cabins[*].cabinId").map(_.toOrigin)),
        ("LOAD_DATE", List())
      )
    )

    getOrigins(tables, "DIM_SUB_CARTESIAN") should ===(
      List(
        ("BASE", List("$.mainResource.current.image.baseId").map(_.toOrigin)),
        ("PRODUCT", List("$.mainResource.current.image.products[*].productId").map(_.toOrigin)),
        ("LEG", List("$.mainResource.current.image.legs[*].legId").map(_.toOrigin)),
        ("BAG_GROUP", List("$.mainResource.current.image.legs[*].baggageGroups[*].bagGroupId").map(_.toOrigin)),
        ("BAG", List("$.mainResource.current.image.legs[*].baggageGroups[*].baggage[*].bagId").map(_.toOrigin)),
        ("SERVICE", List("$.mainResource.current.image.legs[*].services[*].serviceId").map(_.toOrigin)),
        ("LOAD_DATE", List())
      )
    )

  }

  private def getOrigins(tables: List[TableDef], tableName: String): Seq[(String, Seq[Origin])] = {
    val tableDef = tables.filter(_.schema.name == tableName).head
    tableDef.schema.columns.map(c => (c.name, c.origins))
  }
}
