CREATE TABLE IF NOT EXISTS MY_DB.FACT_SERVICE_HISTO (
	CODE STRING,
	PASSENGER_ID STRING,
	RECORD_LOCATOR STRING,
	VERSION STRING,
	<PERSON><PERSON><PERSON>_BEGIN TIMESTAMP,
	DATE_<PERSON><PERSON> TIMESTAMP,
	IS_LAST_VERSION BOOLEAN,
	SERVICE_ID STRING,
	REFERENCE_KEY STRING,
	LEG_DELIVERY_ID STRING,
	SEGMENT_DELIVERY_ID STRING,
	SERVICE_SOURCE STRING,
	LOAD_DATE TIMESTAMP
)
USING DELTA PARTITIONED BY(IS_LAST_VERSION)
TBLPROPERTIES(delta.autoOptimize.optimizeWrite = true, delta.autoOptimize.autoCompact = true, delta.enableChangeDataFeed = true);
