package com.amadeus.airbi.json2star.common.extdata

import com.amadeus.airbi.rawvault.common.testfwk.Json2StarSpec

class PorsExtDataSpec extends Json2StarSpec {
  val dataDir = "datasets/proration/extdata/"
  val optdPath = s"src/test/resources/${dataDir}/input_optd_por_public_sample.csv"

  it should s"correctly parse the external data for OPTD (geographical reference file)" in {
    val rc = rootConfig(dataDir, inputDatabase, isLight = DefaultIsLight)
    val porsExtData = PorsExtData.load(
      rc.copy(etl = rc.etl.copy(common = rc.etl.common.copy(refDataOptdLocation = Some(optdPath)))),
      spark
    )

    porsExtData shouldBe a[PorsExtData]
    // Updated the test to use the allAirports method instead of directly accessing the private airports field.
    // This ensures the test adheres to the encapsulation principle and uses the public API of the PorsRef class.
    porsExtData.asInstanceOf[PorsExtData].data.allAirports("LHR") should not be empty
    porsExtData.asInstanceOf[PorsExtData].data.allAirports("LGW") should not be empty
  }

  it should s"throw exception if refDataOptdLocation path is missing" in {
    val rc = rootConfig(dataDir, inputDatabase, isLight = DefaultIsLight)

    val thrown = intercept[Exception] {
      val porsExtData = PorsExtData.load(
        rc.copy(etl = rc.etl.copy(common = rc.etl.common.copy(refDataOptdLocation = None))),
        spark
      )
      porsExtData
    }

    thrown.getMessage should include("OPTD path is not set in the application.conf file")
  }

}
