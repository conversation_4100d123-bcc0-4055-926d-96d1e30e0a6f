package com.amadeus.airbi.json2star.common.addons.base.mapping

import com.amadeus.airbi.datalake.common.spark.MergeHelper.dedupBatchRecordsRowNumber
import com.amadeus.airbi.json2star.common.addons.base.mapping.MappingMerge.{mergeWithStackableAddons, smartMerge}
import com.amadeus.airbi.json2star.common.addons.base.mapping.MappingPipeline.TableMapping
import com.amadeus.airbi.json2star.common.addons.stackable.StackableAddonConfig
import com.amadeus.airbi.json2star.common.app.TemporaryTable.{
  dropDefaultDeltaTable,
  temporaryTableName,
  toDefaultDeltaTable
}
import com.amadeus.airbi.rawvault.common.RootConfig
import com.amadeus.airbi.rawvault.common.application.config.{Merge, PitTransformation}
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{<PERSON><PERSON><PERSON>, DataFrame, SparkSession}

/** Object factorizing the pit logic common parts.
  */
object PitLogic {

  /** Enumeration for the merge flavor:
    * - MergeFlavorNew, aka Merge 1, it's the merge updating the latest partition
    * - MergeFlavorOld, aka Merge 2, it's the merge updating the historical partition
    */
  sealed trait PitMergeFlavor
  case object MergeFlavorNew extends PitMergeFlavor
  case object MergeFlavorOld extends PitMergeFlavor

  private def isLastVal(flavor: PitMergeFlavor): Option[Boolean] = {
    flavor match {
      case MergeFlavorNew => Some(true)
      case MergeFlavorOld => None
    }
  }

  private def generateTemporaryTableName(flavor: PitMergeFlavor): String = {
    flavor match {
      case MergeFlavorNew => temporaryTableName("PITTED_NEW_VERSIONS")
      case MergeFlavorOld => temporaryTableName("PITTED_ALL_VERSIONS_OLD_KEYS")
    }
  }

  /** Compute the dataframe containing the rows to update or add with the PIT logic.
    * This dataframe does not contain any duplicate, as per merge keys.
    *
    * @param toUpdate Dataframe containing rows coming from the target delta table,
    *                 that potentially need to be updated with PIT logic.
    * @param batch Dataframe containing rows coming from the input batch, that should be included in the PIT logic.
    * @param merge merge configuration for the table, including merge keys and dedup strategy
    * @return      A dataframe containing rows potentially to be added or updated with PIT logic.
    *              This dataframe does not have any dupes.
    */
  def unionAndDedup(toUpdate: DataFrame, batch: DataFrame, merge: Merge): DataFrame = {
    val schema = toUpdate.schema.fieldNames.map(c => col(c))
    val unioned = toUpdate.union(batch.select(schema: _*))
    dedupBatchRecordsRowNumber(
      unioned,
      merge.keyColumns,
      merge.ifDupeTakeHigher.map(e => expr(e).desc_nulls_last)
    )
  }

  class MasterPitLogic(
    val cfg: TableMapping,
    val pit: PitTransformation,
    val tableName: String,
    val merge: Merge
  ) {

    private val tableAccess: TableAccess = TableAccess(cfg)
    private val schema: Seq[Column] = tableAccess.columns

    // Storing these values in the class to be able to do the cleanup at the end
    private val TmpTableNameBatchWithLatest = temporaryTableName("BATCH_WITH_LATEST")
    private val tmpTableNames: Map[PitMergeFlavor, String] = Map(
      MergeFlavorNew -> generateTemporaryTableName(MergeFlavorNew),
      MergeFlavorOld -> generateTemporaryTableName(MergeFlavorOld)
    )

    /** PIT general config consistency assertions for master pit table
      */
    def validatePitConfigMaster(): Unit = {
      assert(
        Set(pit.pitKey, pit.pitVersion).subsetOf(merge.keyColumns.toSet),
        s"Master pit config validation error: the master pit key should contain ${Set(pit.pitKey, pit.pitVersion)}"
      )
    }

    /** Consolidate the rows in the input batch B with the latest partition L and create a split
      * between NEW_VERSIONS and OLD_VERSIONS.
      *
      * NEW_VERSIONS and OLD_VERSIONS are defined as per [[MasterSplit]] documentation.
      *
      * L: latest partition from master pit table
      * B: batch from the input feed data (it can have 1 or more versions for the same key)
      * -------------------     -------------------       -------------------     -------------------
      * case default:            case re-run 1:           case re-run 2:          case out of order:
      * L: v2                    L: v2                    L: v3                   L: v2
      * B: v3                    B: v2                    B: v1,v2,v3             B: v1
      *
      * The Split separates rows in NEW_VERSIONS and OLD_VERSIONS datasets as follows:
      * -------------------      -------------------     -------------------      -------------------
      * NEW_VERSIONS: v2,v3      NEW_VERSIONS: -         NEW_VERSIONS: -          NEW_VERSIONS: -
      * OLD_VERSIONS: -          OLD_VERSIONS: v2        OLD_VERSIONS: v1,v2,v3   OLD_VERSIONS: v1,v2
      *
      * Note:
      * - the default case is the regular processing run when new data arrives.
      *   For performance reason it is necessary that the version from the latest partition and versions from the batch
      *   are kept in the same dataset (NEW_VERSIONS) to update in a single transaction the LATEST partition
      * - the re-run case (1,2) is the recovery processing when a previous run failed.
      *   The failed run updated only Master Table, but not all Secondary Tables, causing inconsistency between tables.
      *   In case of re-run the split is recomputed, it uses the same batch, but a different latest partition as already updated.
      *   For recovery reason it is necessary that the version from the latest partition and versions from the batch
      *   are kept in the OLD_VERSIONS dataset to update the full table (LATEST and HISTORICAL partitions)
      * - the out of order case is the processing of out of order data, meaning versions are older than latest.
      *   In this case, rows are kept in the OLD_VERSIONS dataset to trigger the full update.
      *
      * @param batch the input batch for the master pit table
      * @param batchDistinctPitKeys the distinct pit keys in the input batch
      * @param spark spark session
      * @return two dataframes corresponding, respectively, to the NEW_VERSIONS and the OLD_VERSIONS as defined
      *         in [[MasterSplit]]
      */
    def splitNewAndOldVersions(batch: DataFrame, batchDistinctPitKeys: DataFrame)(implicit
      spark: SparkSession
    ): MasterSplit = {
      /* Load BATCH with LATEST partition */
      // Load latest partition rows for distinct keys in batch
      val latestPart = tableAccess.toDF.filter(col(pit.isLast))
      // rows that exist in latest partition, matching the keys in the batch
      val toUpdate = latestPart.toDF.join(batchDistinctPitKeys, pit.pitKey).select(schema: _*)

      // Union those rows from latest partition with the batch and dedup
      // based on the merge provided configuration
      // (e.g. when duplicated version, keep the row with the most recent load date)
      val toUpdateAndAdd = PitLogic.unionAndDedup(toUpdate, batch, merge)

      /* Split in NEW_VERSIONS and OLD_VERSIONS */

      // for each key get its latest version (-1 if not present) in order to split
      val latestPitVersionCol = s"LATEST_${pit.pitVersion}"
      val latestVersions = toUpdate.select(col(pit.pitKey), col(pit.pitVersion).as(latestPitVersionCol))

      // for each key get its minimum version in the batch to handle re-run cases (recovery case)
      val batchFirstPitVersionCol = s"BATCH_FIRST_${pit.pitVersion}"
      val batchFirstVersions = batch.groupBy(col(pit.pitKey)).agg(min(pit.pitVersion).as(batchFirstPitVersionCol))

      // for each key add its latest version and its first version in the batch in order to split
      val toUpdateAndAddWithLatestVersion = toUpdateAndAdd
        .join(latestVersions, pit.pitKey, "left")
        .withColumn(latestPitVersionCol, coalesce(col(latestPitVersionCol), lit(-1)))
        .join(batchFirstVersions, pit.pitKey, "left")
        .withColumn(batchFirstPitVersionCol, coalesce(col(batchFirstPitVersionCol), lit(Long.MaxValue)))

      // persist on disk to avoid spark re-evaluation of the split
      val toUpdateAndAddWithLatestVersionPersisted =
        toDefaultDeltaTable(toUpdateAndAddWithLatestVersion, TmpTableNameBatchWithLatest)

      // NEW_VERSIONS: input for merge 1
      // Note: this also includes the current latest versions to use the smartMerge for the default case
      val toUpdateAndAddNewVersions = toUpdateAndAddWithLatestVersionPersisted
        .filter(
          // keep new versions (newer than the latest version in the table)
          col(pit.pitVersion) > col(latestPitVersionCol) ||
            // keep the same version when it is a default case
            // 'default case' condition: the latest version in the table is older than the first version in the batch
            (col(pit.pitVersion) === col(latestPitVersionCol) && col(pit.pitVersion) < col(batchFirstPitVersionCol))
        )
        .drop(latestPitVersionCol)
        .drop(batchFirstPitVersionCol)

      // OLD_VERSIONS: input for merge 2
      // Note: batchOldVersions is not a "toUpdateAndAddOldVersions" yet (meaning that is not the dataframes that can be
      // used directly for pit logic computation), because in batchOldVersions there are only versions that were present
      // in the batch. For merge 2, we need to compute pit using *all* the historical versions for the keys in batchOldVersions.
      val batchOldVersions = toUpdateAndAddWithLatestVersionPersisted
        .filter(
          // keep old versions (older than the latest version in the table)
          col(pit.pitVersion) < col(latestPitVersionCol) ||
            // keep the same version when it is a re-run case
            // 're-run case' condition: the latest version in the table is newer or equal than the first version in the batch
            (col(pit.pitVersion) === col(latestPitVersionCol) && col(pit.pitVersion) >= col(batchFirstPitVersionCol))
        )
        .drop(latestPitVersionCol)
        .drop(batchFirstPitVersionCol)
      new MasterSplit(toUpdateAndAddNewVersions, batchOldVersions)
    }

    /** Apply the pit logic to the passed dataframe.
      *
      * This encompasses calculating the end date and the is last flag for the different records, using a window function.
      * @param df input dataframe, having the pit columns (date begin, date end, etc.)
      * @return the pitted dataframe
      */
    def applyMasterPitLogic(df: DataFrame): DataFrame = {
      val validToTmp = pit.validTo + "_tmp"
      val groupByKeyOrderByVersion = Window.partitionBy(pit.pitKey).orderBy(desc(pit.pitVersion))
      df
        .drop(pit.validTo)
        .drop(pit.isLast)
        // redefine the end of the validity (that went obsolete with new records in the batch)
        .withColumn(validToTmp, lag(col(pit.validFrom), 1).over(groupByKeyOrderByVersion))
        // to avoid date_begin > date_end
        .withColumn(
          pit.validTo,
          when(col(pit.validFrom) > col(validToTmp), col(pit.validFrom)).otherwise(col(validToTmp))
        )
        .withColumn(pit.isLast, col(pit.validTo).isNull)
        .drop(validToTmp)
        .select(schema: _*)
    }

    /** Persist the pitted dataframe on delta, merge it into the target table using a strategy that depend on the flavor,
      * and return the persisted dataframe, projected on the pit schema
      *
      * Note:
      * - For Merge 1, the pitted dataframe includes only the NEW_VERSIONS, as defined by the split method above.
      * - For Merge 2, the pitted dataframe includes *all the versions* for the keys that are present in OLD_VERSIONS,
      *   as defined by the split method above.
      *
      * @param patch pitted dataframe
      * @param stackableAddons list of stackable addon configs to apply to the table
      * @param flavor merge flavor (merge 1 or merge 2)
      * @param rootConfig root configuration
      * @param spark spark session
      * @return the pitted dataframe, read from delta, projected on the pit columns
      */
    def persistMergeAndProject(
      patch: DataFrame,
      stackableAddons: List[StackableAddonConfig],
      flavor: PitMergeFlavor,
      rootConfig: RootConfig
    )(implicit spark: SparkSession): DataFrame = {
      // Persist pitted versions to avoid re-evaluation - used for secondary tables
      val pittedVersions = toDefaultDeltaTable(patch, tmpTableNames(flavor))
      // Pass directly pittedVersions as it is a Dataframe read from Delta (optimization)
      mergeWithStackableAddons(
        rootConfig,
        tableName,
        pittedVersions,
        merge,
        stackableAddons,
        smartMerge(pit.isLast, isLastVal(flavor)),
        tableAccess
      )
      // Project pitted versions on pit schema
      // This will be used by Secondary Merge - Note: enable DeltaCache to avoid multiple read (optimization)
      pittedVersions.selectExpr(pit.pitSchema: _*)
    }

    /** Cleanup the temporary tables stored during master pit processing and used by secondary pit.
      *
      * @param spark spark session
      */
    def cleanupTemporaryTables(implicit spark: SparkSession): Unit = {
      tmpTableNames.values.foreach(dropDefaultDeltaTable)
      dropDefaultDeltaTable(TmpTableNameBatchWithLatest)
    }
  }

  class SecondaryPitLogic(
    val pit: PitTransformation,
    val columns: Seq[String],
    val tableName: String
  ) {

    private val pitJoinKey: Seq[String] = Seq(pit.pitKey, pit.pitVersion)
    private val schema: Seq[Column] = columns.map(c => col(c))

    /** PIT general config consistency assertions for secondary pit table
      *
      * @param merge merge configuration for the secondary table
      */
    def validatePitConfigSecondary(merge: Merge): Unit = {
      val baseErrorMessage = "Secondary pit config validation error"
      assert(
        pit.pitSchema.toSet.subsetOf(columns.toSet),
        s"$baseErrorMessage: secondary pit table $tableName schema should contain ${pit.pitSchema}"
      )
      assert(
        merge.keyColumns.contains(pit.pitVersion),
        s"$baseErrorMessage: secondary pit table $tableName PK should contain ${pit.pitVersion}"
      )
    }

    /** Secondary PIT table logic.
      *
      * For secondary tables, applying pit means joining on the pitted versions coming from the master.
      *
      * @param df data frame to be pitted
      * @param masterVersions pitted versions (old or new) coming from master
      * @return pitted data frame
      */
    def applySecondaryPitLogic(df: DataFrame, masterVersions: DataFrame): DataFrame = {
      df
        .drop(pit.validFrom, pit.validTo, pit.isLast)
        .join(masterVersions, pitJoinKey)
        .select(schema: _*)
    }

  }

  /** Dataframes corresponding to the split between old and new versions done by master pit logic, according to the
    * following definitions.
    *
    * - NEW_VERSIONS: rows whose version is more recent than the one currently in the latest partition
    *    - These rows include the current latest version (if any)
    * - OLD_VERSIONS: rows whose version is older or equal than the one currently in the latest partition
    *    - These rows include both out of order and duplicates
    */
  class MasterSplit(
    // NEW_VERSIONS
    val splitNewVersions: DataFrame,
    // OLD_VERSIONS
    val splitOldVersions: DataFrame
  )

  /** Dataframes corresponding to the pitted new versions and old versions for the master pit table.
    * The new versions are persisted in the temporary PITTED_NEW_VERSIONS delta table.
    * The dataframe pittedNewVersions here is a projection of such table on the PIT schema.
    *
    * The old versions are persisted in the temporary PITTED_ALL_VERSIONS_OLD_KEYS delta table.
    * The dataframe pittedAllVersionsOldKeys here is a projection of such table on the PIT schema.
    *
    * @param pittedNewVersions Dataframe corresponding to the master pit table pitted rows for the new versions,
    *                          projected on the PIT schema. It contains all (and only) the versions in NEW_VERSIONS,
    *                          as defined by the split.
    * @param pittedAllVersionsOldKeys Dataframe corresponding to the master pit table pitted rows for the old versions,
    *                                 projected on the PIT schema. It contains all the versions for the keys that are
    *                                 present in OLD_VERSIONS, as defined by the split.
    */
  class PittedMasterRows(
    // PITTED_NEW_VERSIONS projected on PIT schema <=> same versions as NEW_VERSIONS
    val pittedNewVersions: DataFrame,
    // PITTED_ALL_VERSIONS_OLD_KEYS projected on PIT schema <=> all versions for keys in OLD_VERSIONS
    val pittedAllVersionsOldKeys: DataFrame
  )

}
