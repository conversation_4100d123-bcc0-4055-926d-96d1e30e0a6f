package com.amadeus.airbi.json2star.common.snowflake

import com.google.common.base.CaseFormat
import com.typesafe.scalalogging.Logger
import org.apache.commons.text.{CharacterPredicates, RandomStringGenerator}
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions._
import org.apache.spark.sql.streaming.{StreamingQuery, Trigger}
import org.apache.spark.sql.types.StructType
import org.apache.spark.sql.{DataFrame, SparkSession}
import org.slf4j.LoggerFactory

class SnowflakeConnector(rootConfig: SnowflakeRootConfig, queryRunner: <PERSON>f<PERSON>QueryRunner, spark: SparkSession)
    extends Serializable {

  @transient lazy val logger: Logger = Logger(LoggerFactory.getLogger(getClass.getName))

  val options: Map[String, String] = rootConfig.outputConnectorOptions
  val streamOptions: Map[String, String] = rootConfig.streamOptions
  val inputDatabaseName: String = rootConfig.mirror.database

  val trigger: Trigger = rootConfig.sink.trigger.toLowerCase match {
    case "availablenow" => Trigger.AvailableNow()
    case interval => Trigger.ProcessingTime(interval)
  }


  /**
   * In case of cloned table, we need to start from the version of the clone + 1 to avoid reprocessing.
   * This function is called only in the context of a cloned table in initial jobs.
   * @param tableName: Name of the table to get the starting version from
   * @return The version of the clone + 1
   */
  def getStartingVersion(tableName: String): Long = {
    val historyDf = spark.sql(s"DESCRIBE HISTORY $tableName")
    val cloneVersion = historyDf
      .filter(col("operation") === "CLONE")
      .orderBy(col("version").asc)
      .select("version")
      .collect()
      .headOption
      .map(_.getLong(0))
      .getOrElse(throw new RuntimeException(s"No CLONE operation found for table: $tableName."))
    cloneVersion + 1
  }

  def mirrorTable(
    tableName: String,
    primary_key: Set[String],
    isCloned: Boolean = false,
    excludedColumns: List[String] = List()
  ): StreamingQuery = {
    // isClone is only true in case of initial jobs.
    val updatedStreamOptions = if (isCloned) {
      val startingVersion = getStartingVersion(s"$inputDatabaseName.$tableName")
      streamOptions + ("startingVersion" -> s"$startingVersion")
    } else {
      streamOptions
    }
    val tableDf = readTableStreamed(s"$inputDatabaseName.$tableName", updatedStreamOptions)
    mirrorTable(tableDf, tableName, primary_key, excludedColumns)
  }

  private def mirrorTable(
    inputDf: DataFrame,
    tableName: String,
    primaryKey: Set[String],
    excludedColumns: List[String]
  ): StreamingQuery = {
    val queryName = CaseFormat.UPPER_UNDERSCORE.to(CaseFormat.UPPER_CAMEL, tableName)
    val tableCleanDf = (excludedColumns match {
      case Nil => None
      case l: List[String] => Some(l)
    }).foldLeft(inputDf)((df, column_names) => df.drop(column_names: _*))

    tableCleanDf.writeStream
      .trigger(trigger)
      .option("checkpointLocation", rootConfig.sink.checkpointLocation + '/' + queryName)
      .queryName(queryName)
      .foreachBatch(batchProcess(tableName, primaryKey) _)
      .start()
  }

  def readTableStreamed(tableName: String, options: Map[String, String] = Map()): DataFrame = {
    spark.readStream
      .options(options ++ Map("readChangeFeed" -> "true"))
      .format("delta")
      .table(tableName)
  }

  def batchProcess(tableName: String, key: Set[String])(
    increment0: DataFrame,
    batchID: Long
  ): Unit = {

    val schema = increment0.schema

    logger.info("Schema: ")
    logger.info(s"${schema.toDDL}")
    // filter to ignore images before update of a record
    val increment =
      increment0.where(increment0(CDFColumns._change_type._name) =!= ChangeTypeValue.update_preimage.toString)

    val inputIncrement = dedupBatchRecordsRowNumberByLastCommitVersion(increment, key.toSeq)

    val appId = increment0.sparkSession.sparkContext.applicationId
    val stagingTableName = queryRunner.createTransientStagingTable(options, tableName, appId)

    // adding CDF columns
    CDFColumns.values.foreach(c =>
      queryRunner.runQuery(options, s"ALTER TABLE $stagingTableName ADD COLUMN ${c._name} ${c._type} ")
    )

    val lastCopyLoad = queryRunner.appendToStagingTable(options, inputIncrement, stagingTableName)
    logger.info(s"Wrote table using request: $lastCopyLoad")

    mergeStagingTableBySF(schema, key, stagingTableName, tableName)
  }

  /** Deduplicate batch records on the passed key:
    * - keeping the last commit version for a given key
    * - keeping inserts over deletes for a given key
    */
  def dedupBatchRecordsRowNumberByLastCommitVersion(
    batch: DataFrame,
    deduplicateKey: Seq[String]
  ): DataFrame = {
    // When the same commit version, sort by change_type (keep insert above delete)
    val windowSpec = Window
      .partitionBy(deduplicateKey.map(col): _*)
      .orderBy(
        col(CDFColumns._commit_version._name).desc_nulls_last,
        col(CDFColumns._change_type._name).desc_nulls_last
      )
    batch
      .withColumn("row_number", row_number().over(windowSpec))
      .filter("row_number = 1")
      .drop("row_number")
  }

  def mergeStagingTableBySF(
    schema: StructType,
    key: Set[String],
    stagingTableName: String,
    tableName: String
  ): Unit = {
    val outputTableName = queryRunner.getTargetTableName(tableName)
    val mergeQuery = queryGen(schema, key, stagingTableName, outputTableName)

    logger.info(s"""Merge query between "$tableName" and "$stagingTableName":\n {}""", mergeQuery)
    val result = queryRunner.runQuery(options, mergeQuery)
    queryRunner.logResultSet(tableName, result, logger)

    /* drop staging table */
    logger.info(s"Dropping temporary table $stagingTableName")
    queryRunner.runQuery(options, s"drop table $stagingTableName")
  }

  def queryGen(
    schema: StructType,
    key: Set[String],
    stagingTableName: String,
    outputTableName: String
  ): String = {
    val fieldNamesArr = schema.fieldNames.filterNot(name => CDFColumns.names.contains(name))
    val insertColumns: String = fieldNamesArr.iterator.mkString("(", ",", ")")
    val insertValues: String = fieldNamesArr.iterator.map(x => "src." + x).mkString("(", ",", ")")
    val updateFields = fieldNamesArr.iterator
      .filterNot(x => key.contains(x))
      .map(x => s"dst.$x = src.$x")
      .mkString(",")
    val key_condition = key.iterator.map(k => s"EQUAL_NULL(dst.$k, src.$k)").mkString("(", " AND ", ")")

    val mergeQuery = s"""merge
         |    into  $outputTableName dst
         |    using $stagingTableName src
         |     on
         |        $key_condition
         |    when matched AND (src._change_type='${ChangeTypeValue.update_postimage}' ) then
         |    update set $updateFields
         |    when matched AND src._change_type='${ChangeTypeValue.delete}' then
         |    delete
         |    when matched AND (src._change_type='${ChangeTypeValue.insert}') then
         |    update set $updateFields
         |    when not matched AND (src._change_type='${ChangeTypeValue.insert}' OR src._change_type='${ChangeTypeValue.update_postimage}') then
         |    insert $insertColumns values $insertValues
         |""".stripMargin

    mergeQuery
  }
}

object SnowflakeConnector {
  val randomStringGenerator: RandomStringGenerator = new RandomStringGenerator.Builder()
    .withinRange('0', 'z')
    .filteredBy(CharacterPredicates.LETTERS, CharacterPredicates.DIGITS)
    .build()

  def apply(
    rootConfig: SnowflakeRootConfig,
    spark: SparkSession,
    queryRunner: SnowflakeQueryRunner
  ): SnowflakeConnector =
    new SnowflakeConnector(rootConfig, queryRunner, spark)
}
