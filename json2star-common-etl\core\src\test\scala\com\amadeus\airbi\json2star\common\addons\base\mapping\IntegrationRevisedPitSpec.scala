package com.amadeus.airbi.json2star.common.addons.base.mapping

import com.amadeus.airbi.json2star.common.app.{Json2StarApp, TemporaryTable}
import com.amadeus.airbi.json2star.common.resize.DefaultDatabricksUtils
import com.amadeus.airbi.rawvault.common.testfwk._
import com.jayway.jsonpath.DocumentContext
import org.scalatest.tags.Slow

import java.io.File
import scala.reflect.io.Directory

@Slow
class IntegrationRevisedPitSpec extends Json2StarAllSpec with JsonCustomizer {
  val srcTestPath = s"src/test/resources/datasets/revised_pit"

  // Flag to write input files in test resource folder from the JSON template using the custom generator
  val createInputFlag = true

  // Flags to write expected files in resource folder from the output results - key is batch number and value is flag
  override val RewriteExpectedForFailingTests: Boolean = false
  val createExpectedBatchFlags: Map[Int, Boolean] = Map(
    1 -> false,
    2 -> false
  )

  override val runAllFlag: Boolean = false
  // multiple batches can't be light
  override def isLight: Boolean = false

  override def saveResultsFlag: Boolean = true

  // Test scenarios definition, the value corresponds to the test-scenario folder in revised_pit/<test-scenario>/data
  // The test key (e.g. s1) is added as prefix to the ID of the JSON records to guarantee uniqueness between scenario
  override val testDef: TestDef = TestDef(
    mappingFile = s"${srcTestPath}/revised_pit_mapping.conf",
    testScenarios = Set(
      TestScenario("s1", "s1-new_key", 2),
      TestScenario("s2", "s2-new_versions_without_table_histo", 2),
      TestScenario("s3", "s3-new_versions_with_table_histo", 2),
      TestScenario("s4", "s4-same_versions_no_histo", 2),
      TestScenario("s5", "s5-same_versions_with_histo", 2),
      TestScenario("s6", "s6-dup_histo_no_prev", 2),
      TestScenario("s7", "s7-dup_histo_with_prev", 2),
      TestScenario("s8", "s8-old_versions_without_table_histo", 2),
      TestScenario("s9", "s9-old_versions_with_table_histo", 2)
    )
  )

  override def expectedResultsPath(scenario: String, batch: Int): String = {
    s"${srcTestPath}/${scenario}/data/batch${batch}/expected_results/"
  }

  override def inputPath(scenario: String, batch: Int): String = {
    s"datasets/revised_pit/${scenario}/data/batch${batch}/input/"
  }

  val displayMainEventsParam: Boolean = true

  val AKey = "KEY A"
  val AnotherKey = "KEY B"

  // functional date (last modification date field of the document)
  val Date1 = "2023-08-30T08:35:00Z"
  val Date2 = "2023-08-30T10:41:00Z"
  val Date3 = "2023-09-02T12:30:00Z"

  // technical date (load date of event message)
  val LoadDate1 = "08-31-2023"
  val LoadDate2 = "09-03-2023"

  private def check(scenario: String, batch: Int, readFromTable: Boolean = runEachFlag): Unit = {
    checkTablesContent(
      testDef.tableNames,
      expectedResultsPath(scenario, batch),
      createExpected = createExpectedBatchFlags(batch),
      testKey = Some(scenario),
      actualContentFile = if (readFromTable) { _ => None } else {
        (s: String) => Some(actualResultsPath(scenario, batch) + s + ".csv")
      }
    )
  }

  // disable formatter on test cases to keep one line for each generated test input file
  // @formatter:off
  it should "compute PIT for nominal scenario - input batch (new KEY)" taggedAs(SlowTestTag) in {
    val Scenario = "s1-new_key"

    // 1st batch has 1 key
    inputFile(Scenario, batch = 1, key = AKey, version = "1", beginDate = Date1, loadDate = LoadDate1, removeProduct0 = false, createInput = createInputFlag)

    // 2nd batch has another key with 2 versions
    inputFile(Scenario, batch = 2, key = AnotherKey, version = "1", beginDate = Date2, loadDate = LoadDate2, removeProduct0 = false,  createInput = createInputFlag)
    inputFile(Scenario, batch = 2, key = AnotherKey, version = "2", beginDate = Date3, loadDate = LoadDate2, removeProduct0 = false,  createInput = createInputFlag)

    // Process batch 1
    runBatch(batch = 1, inputPath(Scenario, batch = 1), runFlag = runEachFlag)
    // Table has
    //  LATEST:
    //    Key A - v1  (inserted)
    //  HISTO: empty
    check(Scenario, batch = 1)

    // Process batch 2
    // LATEST has data for another key
    // HISTO has no data
    runBatch(batch = 2, inputPath(Scenario, batch = 2), runFlag = runEachFlag)
    // Table has
    //  LATEST:
    //    Key A - v1
    //    KEY B - v2  (inserted)
    //  HISTO:
    //    KEY B - v1  (inserted)
    check(Scenario, batch = 2)
  }

  it should "compute PIT for nominal scenario - input batch (NEW versions) - no HISTO" taggedAs(SlowTestTag) in {
    val Scenario = "s2-new_versions_without_table_histo"

    // 1st batch has 1 key
    inputFile(Scenario, batch = 1, key = AKey, version = "1", beginDate = Date1, loadDate = LoadDate1, removeProduct0 = false, createInput = createInputFlag)

    // 2nd batch has the SAME key with 2 NEW versions
    inputFile(Scenario, batch = 2, key = AKey, version = "2", beginDate = Date2, loadDate = LoadDate2, removeProduct0 = true, createInput = createInputFlag)
    inputFile(Scenario, batch = 2, key = AKey, version = "3", beginDate = Date3, loadDate = LoadDate2, removeProduct0 = false, createInput = createInputFlag)

    // Process batch 1
    runBatch(batch = 1, inputPath(Scenario, batch = 1), runFlag = runEachFlag)
    // Table has
    //  LATEST:
    //    Key A - v1  (inserted)
    //  HISTO: empty
    check(Scenario, batch = 1)

    // Process batch 2
    // LATEST has data for the same key
    // HISTO has no data
    runBatch(batch = 2, inputPath(Scenario, batch = 2), runFlag = runEachFlag)
    // Table has
    //  LATEST:
    //    Key A - v3  (inserted)
    //  HISTO:
    //    Key A - v2  (inserted)
    //    Key A - v1  (updated from previous Latest)
   check(Scenario, batch = 2)
  }

  it should "compute PIT for nominal scenario - input batch (NEW versions) - with HISTO" taggedAs(SlowTestTag) in {
    val Scenario = "s3-new_versions_with_table_histo"

    // 1st batch has 1 key - with 2 NEW versions
    inputFile(Scenario, batch = 1, key = AKey, version = "1", beginDate = Date1, loadDate = LoadDate1, removeProduct0 = false, createInput = createInputFlag)
    inputFile(Scenario, batch = 1, key = AKey, version = "2", beginDate = Date2, loadDate = LoadDate1, removeProduct0 = true, createInput = createInputFlag)

    // 2nd batch has the SAME key with 1 NEW version
    inputFile(Scenario, batch = 2, key = AKey, version = "3", beginDate = Date3, loadDate = LoadDate2, removeProduct0 = false, createInput = createInputFlag)

    // Process batch 1
    runBatch(batch = 1, inputPath(Scenario, batch = 1), runFlag = runEachFlag)

    // Table has
    //  LATEST:
    //    Key A - v2  (inserted)
    //  HISTO:
    //    Key A - v1  (inserted)
    check(Scenario, batch = 1)

    // Process batch 2
    // LATEST has data for the same key
    // HISTO has data for the same key
    runBatch(batch = 2, inputPath(Scenario, batch = 2), runFlag = runEachFlag)
    // Table has
    //  LATEST:
    //    Key A - v3  (inserted)
    //  HISTO:
    //    Key A - v2  (updated from previous Latest)
    //    Key A - v1
   check(Scenario, batch = 2)
  }

  it should "compute PIT for duplicate scenario in LATEST - input BATCH (SAME version) - no HISTO" in {
    val Scenario = "s4-same_versions_no_histo"

    // 1st batch has a record
    inputFile(Scenario, batch = 1, key = AKey, version = "1", beginDate = Date1, loadDate = LoadDate1, removeProduct0 = false,createInput = createInputFlag)

    // 2nd batch has the SAME version with different values
    inputFile(Scenario, batch = 2, key = AKey, version = "1", beginDate = Date2, loadDate = LoadDate2, removeProduct0 = false, createInput = createInputFlag)

    // Process batch 1
    runBatch(batch = 1, inputPath(Scenario, batch = 1), runFlag = runEachFlag)
    // Table has
    //  LATEST:
    //    Key A - v1  (inserted)
    //  HISTO: empty
    check(Scenario, batch = 1)

    // Process batch 2
    // LATEST has data for the same key and same version
    // HISTO has no data
    runBatch(batch = 2, inputPath(Scenario, batch = 2), runFlag = runEachFlag)
    // Table has
    //  LATEST:
    //    Key A - v1 (updated)
    //  HISTO: empty
   check(Scenario, batch = 2)
  }

  it should "compute PIT for duplicate scenario in LATEST - input BATCH (SAME version) - with HISTO" in {
    val Scenario = "s5-same_versions_with_histo"

    // 1st batch
    inputFile(Scenario, batch = 1, key = AKey, version = "1", beginDate = Date1, loadDate = LoadDate1, removeProduct0 = false, createInput = createInputFlag)
    inputFile(Scenario, batch = 1, key = AKey, version = "2", beginDate = Date2, loadDate = LoadDate1, removeProduct0 = false, createInput = createInputFlag)

    // 2nd batch has the SAME version of LATEST with different values Date3
    inputFile(Scenario, batch = 2, key = AKey, version = "2", beginDate = Date3, loadDate = LoadDate2, removeProduct0 = false, createInput = createInputFlag)

    // Process batch 1
    runBatch(batch = 1, inputPath(Scenario, batch = 1), runFlag = runEachFlag)
    // Table has
    //  LATEST:
    //    Key A - v2  (inserted)
    //  HISTO:
    //    Key A - v1  (inserted)
    check(Scenario, batch = 1)

    // Process batch 2
    // LATEST has data for the same key and version
    // HISTO has data for the same key
    runBatch(batch = 2, inputPath(Scenario, batch = 2), runFlag = runEachFlag)
    // Table has
    //  LATEST:
    //    Key A - v2  (updated)
    //  HISTO:
    //    Key A - v1  (updated)
    // NOTE: the end date of V1 in HISTO is also updated with the Date3
   check(Scenario, batch = 2)
  }

  it should "compute PIT for duplicate scenario in HISTO - input BATCH (OLD versions) - no previous in HISTO" in {
    val Scenario = "s6-dup_histo_no_prev"

    // 1st batch
    inputFile(Scenario, batch = 1, key = AKey, version = "1", beginDate = Date2, loadDate = LoadDate1, removeProduct0 = false, createInput = createInputFlag)
    inputFile(Scenario, batch = 1, key = AKey, version = "2", beginDate = Date3, loadDate = LoadDate1, removeProduct0 = true, createInput = createInputFlag)

    // 2nd batch
    inputFile(Scenario, batch = 2, key = AKey, version = "1", beginDate = Date1, loadDate = LoadDate2, removeProduct0 = false, createInput = createInputFlag)

    // Process batch 1
    runBatch(batch = 1, inputPath(Scenario, batch = 1), runFlag = runEachFlag)
    // Table has
    //  LATEST:
    //    Key A - v2  (inserted)
    //  HISTO:
    //    Key A - v1  (inserted)
    check(Scenario, batch = 1)

    // Process batch 2
    // LATEST has data for the same key and same version
    // HISTO has data for the same version
    runBatch(batch = 2, inputPath(Scenario, batch = 2), runFlag = runEachFlag)
    // Table has
    //  LATEST:
    //    Key A - v2
    //  HISTO:
    //    Key A - v1  (updated)
   check(Scenario, batch = 2)
  }

  it should "compute PIT for duplicate scenario in HISTO - input BATCH (OLD versions) - with previous in HISTO" in {
    val Scenario = "s7-dup_histo_with_prev"

    // 1st batch
    inputFile(Scenario, batch = 1, key = AKey, version = "1", beginDate = Date1, loadDate = LoadDate1, removeProduct0 = false, createInput = createInputFlag)
    inputFile(Scenario, batch = 1, key = AKey, version = "2", beginDate = Date2, loadDate = LoadDate1, removeProduct0 = true, createInput = createInputFlag)
    inputFile(Scenario, batch = 1, key = AKey, version = "3", beginDate = Date3, loadDate = LoadDate1, removeProduct0 = true, createInput = createInputFlag)

    // 2nd batch - beginDate is changed Date1
    inputFile(Scenario, batch = 2, key = AKey, version = "2", beginDate = Date1, loadDate = LoadDate2, removeProduct0 = false, createInput = createInputFlag)

    // Process batch 1
    runBatch(batch = 1, inputPath(Scenario, batch = 1), runFlag = runEachFlag)
    // Table has
    //  LATEST:
    //    Key A - v3  (inserted)
    //  HISTO:
    //    Key A - v2  (inserted)
    //    Key A - v1  (inserted)
    check(Scenario, batch = 1)

    // Process batch 2
    // LATEST has data for the same key and same version
    // HISTO has data for the same version
    runBatch(batch = 2, inputPath(Scenario, batch = 2), runFlag = runEachFlag)
    // Table has
    //  LATEST:
    //    Key A - v3
    //  HISTO:
    //    Key A - v2  (updated)
    //    Key A - v1  (updated)
   check(Scenario, batch = 2)
  }

  it should "compute PIT for missing scenario in HISTO - input batch (OLD versions) - no HISTO" in {
    val Scenario = "s8-old_versions_without_table_histo"

    // 1st batch has a key
    inputFile(Scenario, batch = 1, key = AKey, version = "2", beginDate = Date2, loadDate = LoadDate2, removeProduct0 = true, createInput = createInputFlag)

    // 2nd batch has the SAME key with an OLD version (1) and NEW version (3)
    inputFile(Scenario, batch = 2, key = AKey, version = "1", beginDate = Date1, loadDate = LoadDate1, removeProduct0 = false, createInput = createInputFlag)
    inputFile(Scenario, batch = 2, key = AKey, version = "3", beginDate = Date3, loadDate = LoadDate2, removeProduct0 = false, createInput = createInputFlag)
    // e.g. processing of a record that has arrived late (out of order transaction)

    // Process batch 1
    runBatch(batch = 1, inputPath(Scenario, batch = 1), runFlag = runEachFlag)
    // Table has
    //  LATEST:
    //    Key A - v2  (inserted)
    //  HISTO: empty
    check(Scenario, batch = 1)

    // Process batch 2
    // LATEST has data for the same key
    // HISTO has no data
    runBatch(batch = 2, inputPath(Scenario, batch = 2), runFlag = runEachFlag)
    // Table has
    //  LATEST:
    //    Key A - v3  (inserted)
    //  HISTO:
    //    Key A - v2  (updated from previous Latest)
    //    Key A - v1  (inserted)
   check(Scenario, batch = 2)
  }

  it should "compute PIT for missing scenario in HISTO - input batch (OLD versions) - with HISTO" in {
    val Scenario = "s9-old_versions_with_table_histo"

    // 1st batch has a key
    inputFile(Scenario, batch = 1, key = AKey, version = "2", beginDate = Date2, loadDate = LoadDate2, removeProduct0 = true, createInput = createInputFlag)
    inputFile(Scenario, batch = 1, key = AKey, version = "3", beginDate = Date3, loadDate = LoadDate2, removeProduct0 = false, createInput = createInputFlag)

    // 2nd batch has the SAME key with an OLD version (1)
    inputFile(Scenario, batch = 2, key = AKey, version = "1", beginDate = Date1, loadDate = LoadDate1, removeProduct0 = false, createInput = createInputFlag)
    // e.g. processing of a record that has arrived late (out of order transaction)

    // Process batch 1
    runBatch(batch = 1, inputPath(Scenario, batch = 1), runFlag = runEachFlag)
    // Table has
    //  LATEST:
    //    Key A - v3  (inserted)
    //  HISTO:
    //    Key A - v2  (inserted)
    check(Scenario, batch = 1)

    // Process batch 2
    // LATEST has data for the same key
    // HISTO has data for the same key
    runBatch(batch = 2, inputPath(Scenario, batch = 2), runFlag = runEachFlag)
    // Table has
    //  LATEST:
    //    Key A - v3
    //  HISTO:
    //    Key A - v2  (updated from previous Histo)
    //    Key A - v1  (inserted)
   check(Scenario, batch = 2)
  }

  //@formatter:on

  private def assertNoTemporaryTable(): Unit = {
    val temporaryTables = spark
      .sql(s"show tables in ${TemporaryTable.TmpTablesDB} like '${TemporaryTable.TmpTablePrefix}*'")
      .select("tableName")
      .collect()
      .map(_.getAs[String]("tableName"))
    temporaryTables shouldBe Array.empty
  }

  private def inputFile(
                         scenario: String,
                         batch: Int,
                         version: String, // 1
                         beginDate: String, // 2022-08-31T08:35:00Z
                         removeProduct0: Boolean = false,
                         loadDate: String = "01-01-2018",
                         key: String = "OP27AX-2022-05-22",
                         createInput: Boolean
                       ): Unit = {
    if (createInput) {
      val jsonTemplate = s"${srcTestPath}/template.json"
      val testKey = testDef.byTestName(scenario).id

      def customize(d: DocumentContext): JsonAsString = {
        d
          .set(".mainResource.current.image.version", version)
          .set(".mainResource.current.image.lastModification.dateTime", beginDate)
        if (removeProduct0) {
          d.delete(".mainResource.current.image.products[0]")
        }
        // replace the ID with
        // - the key used for the functional scenario
        // - the test key used to use the test fwk to run all test together
        d.jsonString().replaceAll("OP27AX-2022-05-22", s"${testKey}-${key}")
      }

      newJsonFile(
        jsonTemplate = jsonTemplate,
        targetDir = "src/test/resources/" + inputPath(scenario, batch),
        description = s"${key}-v${version}-${beginDate.replace(":", "-")}-${removeProduct0}",
        jsonCustomization = customize _,
        loadDate = loadDate
      )
    }
  }


  def runBatch(batch: Int, inputPath: String): Unit = {
    runBatch(batch, inputPath, runFlag = true)
  }

  private def runBatch(batch: Int,
                       inputPath: String,
                       resizeCfg: Option[ResizeConfigContext] = None,
                       runFlag: Boolean): Unit = {
    if (runFlag) {
      logger.info(s"Run batch: $batch")
      val baseRootConfig = rootConfig(inputPath, inputDatabase, displayMainEventsParam = displayMainEventsParam, isLight = isLight)
      val rc = resizeCfg.map(_.rootConfigWithResize(baseRootConfig)).getOrElse(baseRootConfig)
      val dbxUtils = resizeCfg.map(_.dbxUtils).getOrElse(DefaultDatabricksUtils)
      val checkpointLocation = rc.etl.stream.sink.checkpointLocation
      val directory = new Directory(new File(checkpointLocation))
      directory.deleteRecursively()

      Json2StarApp.run(spark, rc, testDef.mapping, dbx = dbxUtils)
      // Note: for simple feed, we are simulating streaming running batches on different folders
      // so the checkpoint location MUST BE cleaned at each batch
      directory.deleteRecursively()
    } else {
      logger.info(s"Skip run")
    }
  }
}
