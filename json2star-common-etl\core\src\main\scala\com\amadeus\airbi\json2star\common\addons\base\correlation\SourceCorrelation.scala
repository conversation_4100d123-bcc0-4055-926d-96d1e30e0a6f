package com.amadeus.airbi.json2star.common.addons.base.correlation

import com.amadeus.airbi.json2star.common.addons.base.Addon
import com.amadeus.airbi.json2star.common.{ColumnDef, Origin, Schema, SourceCorrelationType}
import com.amadeus.airbi.rawvault.common.application.config.SourceCorrelation.AssoTable
import com.amadeus.airbi.rawvault.common.application.config.{TableConfig, TableDescription, TablesConfig}

object SourceCorrelation extends Addon[AssoTable] {
  override def getConfig(t: TableConfig): Option[AssoTable] = t.sourceCorrelation

  override def enrichSchema(c: TablesConfig, t: TableConfig, a: AssoTable, l: Schema): Schema = {
    val origins = Seq(
      Origin(
        List(a.domainA.name, a.domainA.tableName).mkString("-"),
        None,
        None,
        sourceType = SourceCorrelationType
      )
    )

    def isPKkey(columnName: String) = {
      columnName == a.target.domainAKey || columnName == a.target.domainBKey || columnName == a.target.domainAVersion || columnName == a.target.domainBVersion
    }

    Schema(
      name = t.name,
      columns = a.target.columns
        .map(c => ColumnDef(c.name, c.columnType, c.isMandatory, isPKkey(c.name), origins, c.meta, c.fk, None, None))
        .toList,
      description = Some(
        TableDescription(
          description = Some(
            s"Source Correlation table between ${a.domainA.name} and ${a.domainB}"
          ),
          granularity = Some(
            s"1 row for each different tuple (${a.target.domainAKey}, ${a.target.domainBKey}, ${a.target.domainAVersion}, " +
              s"${a.target.domainBVersion})"
          )
        )
      ),
      partitionColumn = Some(a.target.isLast),
      kind = Schema.Materialized,
      subdomain = l.subdomain,
      subdomainMainTable = l.subdomainMainTable
    )
  }

  override def validate(c: TablesConfig, t: TableConfig): Unit = {
    // TODO
  }

}
