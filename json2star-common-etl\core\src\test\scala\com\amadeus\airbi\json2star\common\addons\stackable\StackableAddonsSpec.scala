package com.amadeus.airbi.json2star.common.addons.stackable

import com.amadeus.airbi.common.utils.CommonSpec
import com.amadeus.airbi.json2star.common._
import com.amadeus.airbi.json2star.common.addons.base.correlation.Correlation
import com.amadeus.airbi.json2star.common.addons.base.latest.Latest
import com.amadeus.airbi.json2star.common.addons.base.mapping.Mapping
import com.amadeus.airbi.json2star.common.addons.stackable.dummy.DummyAddon
import com.amadeus.airbi.json2star.common.app.Json2StarApp.readMappingConfig
import com.amadeus.airbi.rawvault.common.application.config.{TableDescription, TablesConfig}
import com.amadeus.airbi.rawvault.common.config.ColumnMetadataRule.Replace
import com.amadeus.airbi.rawvault.common.config.ColumnType._
import com.amadeus.airbi.rawvault.common.config.{ColumnMetadata, ColumnMetadataValue, GDPRZone}
import com.amadeus.airbi.rawvault.common.testfwk.SpecHelper

class StackableAddonsSpec extends CommonSpec with SpecHelper {

  val mappingFile = "stackable/dummy_mapping.conf"
  val mapping: TablesConfig = readMappingConfig(mappingFile)

  import StackableAddonsSpec._

  "StackableAddons" should "aggregate validations an all stackable addons for a table" in {
    // no exception
    TablesDef.consolidate(getTableDef(mapping, "FACT_OK_HISTO").general)

    // exception because of dummy addon failing twice
    val caught = intercept[StackableAddonValidationException] { // Result type: IndexOutOfBoundsException
      TablesDef.consolidate(mapping.copy(tables = mapping.tables.filter(_.name == "FACT_FAIL_HISTO")))
    }
    val expected =
      """|Validations in error:
         |- expected_error
         |- expected_error""".stripMargin
    caught.getMessage shouldBe expected
  }

  "StackableAddons" should "apply the schema metadata enrichment provided by a stackable addon" in {

    // Base schema
    val baseValue = Some("base")
    val baseOrigins = Seq(Origin("base", None, None, sourceType = MappingType))
    val colMetadataValueBase = Some(ColumnMetadataValue("base", Replace))
    val baseDescription = Some(
      TableDescription(
        description = baseValue,
        granularity = None
      )
    )
    val baseSchema = Schema(
      name = "FACT_RESERVATION_HISTO",
      columns = List(
        columnDef(
          "column_1",
          strColumn,
          isMandatory = true,
          belongsToPK = true,
          origins = Seq.empty
        ),
        columnDef(
          "column_2",
          timestampColumn,
          isMandatory = false,
          belongsToPK = false,
          origins = baseOrigins,
          meta = Some(
            ColumnMetadata(
              description = colMetadataValueBase,
              example = None,
              piiType = colMetadataValueBase,
              gdprZone = Some(GDPRZone.Red)
            )
          )
        )
      ),
      description = baseDescription,
      kind = Schema.Materialized
    )

    // Enrichment
    val colMetadataValueEnriched = Some(ColumnMetadataValue("a", Replace))
    val enrichedMetadata = ColumnMetadata(
      description = colMetadataValueEnriched,
      example = colMetadataValueEnriched,
      piiType = colMetadataValueEnriched,
      gdprZone = Some(GDPRZone.Red)
    )
    val enrichedValue = Some("a")
    val enrichedOrigins = Seq(Origin("a", enrichedValue, enrichedValue, MappingType))
    val enrichment = EnrichedSchemaMetadata(
      columnsMetadata = Map(
        // fill everything for column_1
        "column_1" -> EnrichedColumnMetadata(
          origins = Some(enrichedOrigins),
          meta = Some(enrichedMetadata)
        ),
        // fill only example and replace piiType for column_2
        "column_2" -> EnrichedColumnMetadata(
          meta = Some(
            ColumnMetadata(
              example = colMetadataValueEnriched,
              piiType = colMetadataValueEnriched
            )
          )
        )
      ),
      description = Some(
        TableDescription(
          description = enrichedValue,
          granularity = enrichedValue
        )
      )
    )

    // Integrate enrichment in base schema
    // - override what's present
    // - add what's missing

    val expectedSchema = Schema(
      name = "FACT_RESERVATION_HISTO",
      columns = List(
        columnDef(
          "column_1",
          strColumn,
          isMandatory = true,
          belongsToPK = true,
          origins = enrichedOrigins, // set
          meta = Some(enrichedMetadata) // set
        ),
        columnDef(
          "column_2",
          timestampColumn,
          isMandatory = false,
          belongsToPK = false,
          origins = baseOrigins, // not present in enrichment, so kept
          meta = Some( // replaced by enrichment
            ColumnMetadata(
              description = None,
              example = colMetadataValueEnriched,
              piiType = colMetadataValueEnriched,
              gdprZone = None
            )
          )
        )
      ),
      description = Some( // replaced by enrichment
        TableDescription(
          description = enrichedValue,
          granularity = enrichedValue
        )
      ),
      kind = Schema.Materialized
    )

    // integrate enrichment
    StackableAddons.integrateMetadataInSchema(enrichment, baseSchema) should ===(expectedSchema)

    // integrate enrichment (don't touch schema description)
    StackableAddons.integrateMetadataInSchema(
      enrichment.copy(description = None),
      baseSchema
    ) should ===(expectedSchema.copy(description = baseDescription))

  }

  it should "apply the schema metadata enrichment for all the stackable addons of a table" in {
    val tables = mapping.copy(tables = mapping.tables.filter(_.name == "FACT_OK_HISTO"))
    val tableDefs = TablesDef.consolidate(tables)
    val tableOk = tables.tables.head
    val enrichedSchema =
      StackableAddons.enrichSchemaMetadata(tables, tableOk, tableDefs.tables.head.schema, tableOk.stackableAddons)

    enrichedSchema.description shouldBe (Some(DummyAddon.enrichedTableDescription("enrich_table")))
    enrichedSchema.columns.filter(_.name == "LOAD_DATE").flatMap(_.meta).head.description shouldBe Some(
      ColumnMetadataValue(DummyAddon.enrichedDescription("enrich_load_date"), Replace)
    )
  }

  it should "correctly check whether all stackable addons are compatible with a base addon" in {
    val tableDefOk = getTableDef(mapping, "FACT_OK_HISTO").table
    StackableAddons.areStackableAddonsCompatibleWith(tableDefOk.stackableAddons, Mapping) shouldBe true
    StackableAddons.areStackableAddonsCompatibleWith(tableDefOk.stackableAddons, Latest) shouldBe false
    StackableAddons.areStackableAddonsCompatibleWith(tableDefOk.stackableAddons, Correlation) shouldBe false
  }

  it should "make TableDef creation fail in case of stackable asson incompatible with a base addon" in {
    assertThrows[IllegalArgumentException](
      getTableDef(mapping, "FACT_FAIL")
    )
  }

  it should "correctly get the compatible stackable addons for a base addon" in {
    val tableDefOk = getTableDef(mapping, "FACT_OK_HISTO")
    StackableAddons.getCompatibleStackableAddons(tableDefOk, Mapping).length shouldBe 2
  }

  it should "determine the addon name from the config class name" in {
    StackableAddons.stackableAddonName(WeightConversion(List.empty)) shouldBe "weight-conversion"
    StackableAddons.stackableAddonName(CurrencyConversion(List.empty)) shouldBe "currency-conversion"
    StackableAddons.stackableAddonName(Dummy("")) shouldBe "dummy"
  }

  it should "keep only not disabled stackable addons in table configurations" in {
    def names(config: TablesConfig): List[String] =
      config.tables.head.stackableAddons.map(c => StackableAddons.stackableAddonName(c))

    val tables = mapping.copy(tables = mapping.tables.filter(_.name == "FACT_MANY_ADDONS"))

    names(StackableAddons.filterStackableAddons(tables, Set.empty)) should contain theSameElementsAs List(
      "dummy",
      "weight-conversion",
      "currency-conversion"
    )

    names(StackableAddons.filterStackableAddons(tables, Set("dummy"))) should contain theSameElementsAs List(
      "weight-conversion",
      "currency-conversion"
    )

    names(
      StackableAddons.filterStackableAddons(tables, Set("weight-conversion", "currency-conversion"))
    ) should contain theSameElementsAs List("dummy")
  }

}

object StackableAddonsSpec {
  def getTableDef(mapping: TablesConfig, name: String): TableDef = {
    TablesDef.consolidate(mapping.copy(tables = mapping.tables.filter(_.name == name))).tables.head
  }
}
