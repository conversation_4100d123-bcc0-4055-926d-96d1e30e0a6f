{
  "tables": [
    {
        "name": "FACT_TABLE1",
        "mapping" : {
          "merge": {
            "key-columns": ["RESERVATION_ID", "VERSION"],
            "if-dupe-take-higher": ["LOAD_DATE"]
          },
          "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}]}],
          "columns": [
            {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
            {"name": "VERSION", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.version"}]}},

            {"name": "VERSION_INT", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.version"}]}},
            {"name": "VERSION_STRING", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.version"}]}},
            {"name": "PNR_CREATION_DATE", "column-type": "dateColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}]}},
            {"name": "PNR_CREATION_TIMESTAMP", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}]}},
            //{"name": "ID_BINARY", "column-type": "binaryColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"}, bugged
            {"name": "ID_BINARY_STRING", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
            {"name": "IS_ACTIVE_BOOLEAN", "column-type": "booleanColumn", "sources": {"blocks": [{"base": "$.isActive"}]}},

            {"name": "VERSION_LONG", "column-type": "longColumn", "sources": {"blocks": [{"base": "$.version"}]}},
            {"name": "VERSION_FLOAT", "column-type": "floatColumn", "sources": {"blocks": [{"base": "$.version"}]}},

            {"name": "OWNER_JSON_STRING", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.owner"}]}},

            {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
            {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
            {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
          ],
          "pit": {
            "type": "master-pit-table",
            "master": {
              "pit-key": "RESERVATION_ID",
              "pit-version": "VERSION",
              "valid-from": "DATE_BEGIN",
              "valid-to": "DATE_END",
              "is-last": "IS_LAST_VERSION"
            }
          }
        }
    }
  ]
}
