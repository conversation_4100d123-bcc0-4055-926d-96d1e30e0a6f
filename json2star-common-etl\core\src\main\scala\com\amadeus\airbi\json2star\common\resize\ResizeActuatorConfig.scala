package com.amadeus.airbi.json2star.common.resize

import com.amadeus.airbi.json2star.common.config.AppConfig.Secret
import com.amadeus.airbi.json2star.common.config.{AppConfig, ResizeParams}
import com.amadeus.airbi.rawvault.common.RootConfig

case class ResizeActuatorConfig(
  workspaceUrl: String,
  workspaceToken: String,
  resizeParams: ResizeParams,
  displayEvents: Boolean
)

object ResizeActuatorConfig {

  def secretValue(secret: Secret, dbx: DatabricksUtils): String = {
    dbx.getSecret(secret.scope, secret.key)
  }

  def fromRootConfig(rootConfig: RootConfig, dbx: DatabricksUtils): Option[ResizeActuatorConfig] = {
    for {
      resizeParams <- rootConfig.processingParams.resizeParams
      workspaceSecrets <- rootConfig.workspaceSecrets
      workspaceUrlSecret <- workspaceSecrets.get("workspace-url")
      workspaceTokenSecret <- workspaceSecrets.get("workspace-token")
    } yield ResizeActuatorConfig(
      workspaceUrl = secretValue(workspaceUrlSecret, dbx),
      workspaceToken = secretValue(workspaceTokenSecret, dbx),
      resizeParams = resizeParams,
      displayEvents = rootConfig.processingParams.displayMainEvents
    )
  }

  def fromAppConfig(appConfig: AppConfig, dbx: DatabricksUtils): Option[ResizeActuatorConfig] = {
    for {
      resizeParams <- appConfig.processingParams.flatMap(_.resizeParams)
      workspaceSecrets <- appConfig.workspaceSecrets
      workspaceUrlSecret <- workspaceSecrets.get("workspace-url")
      workspaceTokenSecret <- workspaceSecrets.get("workspace-token")
    } yield ResizeActuatorConfig(
      workspaceUrl = secretValue(workspaceUrlSecret, dbx),
      workspaceToken = secretValue(workspaceTokenSecret, dbx),
      resizeParams = resizeParams,
      displayEvents = appConfig.processingParams.exists(_.displayMainEvents)
    )
  }

}
