package com.amadeus.airbi.json2star.common

import com.amadeus.airbi.rawvault.common.config.GDPRZone.GDPRZoneType
import com.amadeus.airbi.rawvault.common.config.{ColumnMetadata, ColumnType, FKeyRelationship, GDPRZone}

/** Column definition
  *
  * This definition will be used to generate views like .sql or documentation for the table.
  *
  * @param name name of the column
  * @param columnType type of the column (string, date, ...)
  * @param isMandatory if this column is guaranteed to be always present
  * @param belongsToPK if this column belongs to the primary keys
  * @param origins sources from which this column is calculated (for instance: json mapping for Mapping tables)
  * @param meta metadata information for the description, example, piiType, gdpr zone of the column (override or concatenate the default values given by the DIH YAML Data Dictionaries)
  * @param fk foreign key relationships
  * @param preExpr Spark expression used (if any) to generate this column value before any stackable addon
  * @param postExpr Spark expression used (if any) to generate this column value after any stackable addon
  */
case class ColumnDef(
  name: String,
  columnType: ColumnType.Value,
  isMandatory: Boolean,
  belongsToPK: Boolean,
  origins: Seq[Origin],
  meta: Option[ColumnMetadata],
  fk: Option[Seq[FKeyRelationship]],
  preExpr: Option[String],
  postExpr: Option[String]
) {

  /** Consolidate the column metadata.
    *
    * This business logic is implemented here for the moment.
    * In the future, when it will be possible to stack addons, it should be extracted in a dedicated addon
    * run at the end.
    */
  def consolidatedMeta(tableName: String): Option[ColumnMetadata] = {
    def desc(): Option[String] = {
      if (name == "LOAD_DATE") {
        Some("Technical field: date/time UTC when the source message was received in the Amadeus Big Data Platform")
      } else if (fk.isDefined && name.endsWith(ColumnDef.IdSuffix)) {
        Some("Hashed foreign key")
      } else {
        None
      }
    }

    def gdpr(tableName: String): Option[GDPRZoneType] = {
      GDPRZone.getGDPRZone(meta.flatMap(_.gdprZone), name, tableName)
    }

    if (meta.isEmpty) {
      Some(ColumnMetadata.from(description = desc(), gdprZone = gdpr(tableName), example = None, piiType = None))
    } else {
      meta.map(m => m.consolidate(desc(), gdpr(tableName)))
    }
  }

  def consolidatedGdprZone(tableName: String): Option[GDPRZoneType] =
    consolidatedMeta(tableName).flatMap(_.gdprZone)

}

object ColumnDef {
  val IdSuffix = "_ID"
}

/** Origin definition
  *
  * @param raw raw origin without any transformation
  * @param transformation optional transformation
  * @param transformed optional transformed view of the origin
  * @param sourceType the type of the source (e.g. Literal, Mapping, ...)
  */
case class Origin(
  raw: String,
  transformation: Option[String],
  transformed: Option[String],
  sourceType: SourceType
)

sealed trait SourceType
case object LiteralType extends SourceType
case object MappingType extends SourceType
case object SourceCorrelationType extends SourceType
