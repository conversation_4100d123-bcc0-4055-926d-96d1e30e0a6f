package com.amadeus.airbi.json2star.common.validation.generators

import com.amadeus.airbi.json2star.common.{ColumnDef, TableDef, TablesDef}
import com.amadeus.airbi.json2star.common.validation.config.ValidationConfig
import com.amadeus.airbi.json2star.common.validation.executors.ValidationSchemaExecutor.logger

/** It generates the SQL DDL statements to create Delta Databricks Tables from the mapping file
  */
object ValidationForeignKeyGenerator {

  val tmpValueNotFound: String = "No column found"
  /** Generate the SQL statement to initialize a Table
    *
    * @param conf     a Table Config - ongoing HubConfigBean
    * @param database database name
    * @return a init SQL
    */

  def toCreateValidationRequest(
    conf: TablesDef,
    database: String,
    validParams: ValidationConfig
  ): List[String] = {
    val masterTable = conf.tables
      .filter(table =>
        table.table.mapping.isDefined
          && table.table.mapping.get.masterPit.isDefined
      ).head

    val allTables = conf.tables.map(_.schema.name)
    conf.tables.flatMap { table =>
      val foreignKeyTableName = table.schema.name
      if (
        (foreignKeyTableName.startsWith("FACT_") || foreignKeyTableName.startsWith("ASSO")) & foreignKeyTableName
          .endsWith("_HISTO")
      ) {
        table.schema.columns.flatMap { column =>
          val foreignKeyColumnName = column.name
          if (column.fk.isDefined || foreignKeyColumnName.endsWith(ColumnDef.IdSuffix)) {
            val primaryTableName = findPrimaryTable(foreignKeyColumnName, column, allTables)
            val primaryKeyColumnName = findTablePrimaryKeyColumnName(column, conf, primaryTableName,masterTable)
            if (!(primaryTableName == foreignKeyTableName) & (primaryKeyColumnName != tmpValueNotFound)) {
              List(
                buildForeignKeyCheck(
                  validParams,
                  database,
                  foreignKeyTableName,
                  foreignKeyColumnName,
                  primaryTableName,
                  primaryKeyColumnName,
                  masterTable.schema.name
                )
              )
            } else {
              None
            }
          } else {
            None
          }
        }
      } else {
        None
      }
    }
  }

  def findPrimaryTable(idColumn: String, column: ColumnDef, allTables: List[String]): String = {
    if (column.fk.isDefined) {
      val tmp = column.fk match {
        case Some(i) => i(0).table
      }
      if(tmp.startsWith("FACT") && !tmp.endsWith("HISTO")) {
        tmp + "_HISTO"
      }
      else {
        tmp
      }
    } else {
      val tableEnd = idColumn.substring(0, idColumn.length - 3)
      val factTableName = "FACT_" + tableEnd + "_HISTO"
      val dimTableName = "DIM_" + tableEnd
      if (allTables.contains(factTableName)) {
        factTableName
      } else if (allTables.contains(dimTableName)) {
        dimTableName
      } else {
        logger.info(s"No FACT or DIM table found for: ${idColumn}")
        "No table found"
      }
    }
  }

  def findTablePrimaryKeyColumnName(column: ColumnDef, conf: TablesDef, tableName: String, masterTable : TableDef): String = {
    val keyColPos = if (tableName.startsWith("FACT")) 1 else 0
    val notIncludes = List("VERSION", "INTERNAL_ZORDER")
    if (tableName == "No table found" || notIncludes.contains(column.name)) {
      tmpValueNotFound
    } else if (column.fk.isDefined) {
      if (column.fk.head.head.column.isDefined) {
        column.fk.head.head.column.head
      } else {
        val findPrimTableDef = conf.tables.filter(table => table.schema.name == tableName)
        if(findPrimTableDef.isEmpty){
          tmpValueNotFound
        } else {
          findPrimTableDef.head.schema.keyColumns(keyColPos).name
        }
      }
    } else if (tableName == masterTable.schema.name) {
      masterTable.schema.keyColumns(keyColPos).name
    } else {
      conf.tables.filter(table => table.schema.name == tableName).head.schema.keyColumns(keyColPos).name
    }
  }


  def buildForeignKeyCheck(
    validParams: ValidationConfig,
    database: String,
    foreignKeyTableName: String,
    foreignKeyColumnName: String,
    primaryTableName: String,
    primaryColumnName: String,
    masterTableName : String
  ): String = {
    val endRequest = if (primaryTableName.endsWith("_HISTO")) {
      s"""and primary_table.version = foreign_table.version, checkReferenceTime
        |where  to_timestamp(unix_timestamp(foreign_table.LOAD_DATE, "M/d/y h:m:s a"))  <= checkReferenceTime.reftime
        |and to_timestamp(unix_timestamp(foreign_table.LOAD_DATE, "M/d/y h:m:s a")) >= checkReferenceTime.reftime - INTERVAL ${validParams.daysBack} DAYS );"""
        .stripMargin
    } else {
      s""", checkReferenceTime
        |where  to_timestamp(unix_timestamp(foreign_table.LOAD_DATE, "M/d/y h:m:s a"))  <= checkReferenceTime.reftime
        |and to_timestamp(unix_timestamp(foreign_table.LOAD_DATE, "M/d/y h:m:s a")) >= checkReferenceTime.reftime - INTERVAL ${validParams.daysBack} DAYS );"""
        .stripMargin
    }
    val request =
      s"""WITH
        |checkReferenceTime as (select max(load_date) - INTERVAL 1 HOUR as refTime from ${database}.${masterTableName})
        |insert into ${validParams.validationDatabase}.${validParams.validationTablename}
        |(select "${validParams.domain}" as domain,
        |"${validParams.domainVersion}" as domain_version,
        |"${validParams.customer}" as customer,
        |"${validParams.phase}" as phase,
        |'${foreignKeyTableName}-${foreignKeyColumnName}--${primaryTableName}-${primaryColumnName}' as foreign_key,
        |count (*) as nb_rows,
        |sum(decode(foreign_table.${foreignKeyColumnName}, null, 0, 1)) / count(*) as ratio_of_foreign_key,
        |sum(decode(primary_table.${primaryColumnName}, null, 0, 1)) / sum(decode(foreign_table.${foreignKeyColumnName}, null, 0, 1)) as quality_of_foreign_key,
        |now() as test_datetime
        |from ${database}.${foreignKeyTableName} foreign_table
        |left outer join ${database}.${primaryTableName} primary_table
        |on foreign_table.${foreignKeyColumnName} = primary_table.${primaryColumnName}
        |${endRequest}""".stripMargin
   // println(request)
    request
  }
}
