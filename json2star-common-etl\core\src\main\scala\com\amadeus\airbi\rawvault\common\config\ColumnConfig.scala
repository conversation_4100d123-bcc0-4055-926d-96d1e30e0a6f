package com.amadeus.airbi.rawvault.common.config

import com.amadeus.airbi.rawvault.common.config.Blocks.Block
import com.amadeus.airbi.rawvault.common.config.ColSources.{BlocksSource, ColSource, LiteralSource}
import com.amadeus.airbi.rawvault.common.config.ColumnMetadataRule.ColumnMetadataRule
import com.amadeus.airbi.rawvault.common.config.GDPRZone.GDPRZoneType
import com.amadeus.airbi.rawvault.common.processors.BlockResults.BlockJsonAliasRoot
import com.amadeus.airbi.rawvault.common.vault.generators.PreRowQuery
import pureconfig.ConfigReader
import pureconfig.generic.semiauto.deriveEnumerationReader

import scala.util.Try

object ColumnType extends Enumeration {
  type ColumnType = Value
  val intColumn, strColumn, dateColumn, timestampColumn, binaryColumn, binaryStrColumn, booleanColumn, longColumn,
    floatColumn = Value
}

case class SourceWithRoot(
  rsName: String,
  blocks: Option[List[PreRowQuery]],
  literal: Option[String]
) {

  /** Builds a source without a root (`BlockSource` or `LiteralSource`) from this source with root.
    */
  def asSourceWithoutRoot(): ColSource = {
    (blocks, literal) match {
      case (Some(b), None) => BlocksSource(b)
      case (None, Some(l)) => LiteralSource(l)
      case _ => throw new IllegalStateException(s"You must define one and only one between `blocks` and `literal`")
    }
  }
}

object SourceWithRoot {
  def validate(b: SourceWithRoot): Try[SourceWithRoot] = Try {
    b.asSourceWithoutRoot()
    b
  }
}

sealed trait RootSource

case class BlocksRootSource(blocks: List[Block]) extends RootSource

case class NamedRootSource(name: String, rs: RootSource) extends RootSource

object RootSource {

  def rootSourceAsBlocks(rs: RootSource): Blocks = rs match {
    case NamedRootSource(_, rs) => asBlocks(rs)
    case rs => asBlocks(rs)
  }

  private def asBlocks(rs: RootSource): Blocks = rs match {
    case BlocksRootSource(blocks) => Blocks.from(blocks)
    case _: NamedRootSource => throw new IllegalArgumentException("Not expected to have NamedRootSource here")
  }
}

object BlocksRootSource {
  def validate(b: BlocksRootSource): Try[BlocksRootSource] = Try {
    val allAliasesDefined = b.blocks.flatMap(_.aliases)
    if (allAliasesDefined.exists(_ == BlockJsonAliasRoot)) {
      throw new IllegalArgumentException(s"Can't use '${BlockJsonAliasRoot}' as alias")
    }
    val thereAreDupes = allAliasesDefined.size != allAliasesDefined.distinct.size
    if (thereAreDupes) {
      throw new IllegalArgumentException(s"Dupes found in root source: ${allAliasesDefined}")
    }
    b
  }
}

case class ColumnConfig(
  name: String,
  columnType: ColumnType.Value,
  sources: ColSource,
  hasVariable: Boolean = false,
  isMandatory: Boolean = false,
  expr: Option[String] = None,
  postExpr: Option[String] = None,
  createFk: Option[FKeyConfig] = None,
  fk: Option[Seq[FKeyRelationship]] = None,
  meta: Option[ColumnMetadata] = None
) {
  def getFkeyColumn: ColumnConfig = createFk match {
    case Some(f) =>
      val fKeyName = f.name.getOrElse(name.concat("_ID"))
      ColumnConfig(
        fKeyName,
        f.columnType,
        sources,
        hasVariable,
        isMandatory,
        f.expr,
        None,
        None,
        f.fk,
        meta = f.meta.orElse(
          Some(
            ColumnMetadata(
              description =
                if (f.isHashed) Some(ColumnMetadataValue("Hashed Foreign Key", ColumnMetadataRule.Replace)) else None,
              example = None,
              piiType = None,
              gdprZone = if (f.isHashed) Some(GDPRZone.Green) else meta.flatMap(_.gdprZone)
            )
          )
        )
      )
    case None => throw new IllegalArgumentException("Not supported None")
  }
} // TODO: Only strColumn can have multiple sources: add an assert?

case class FKeyRelationship(
  schema: Option[String] = None, // if None, it means the schema for the current feed
  table: String,
  column: Option[String] = None // if None, it means the column name respects a naming convention
) {

  /** Add '_ID' to table name, drop the table prefix ('FACT_', 'DIM_', ..) and the `_HISTO` suffix if present.
    * If no prefix is present in the table name, it will just return 'ID'.
    */
  def columnName: String = column.getOrElse(
    table.replaceAll("_HISTO$", "").concat("_ID").dropWhile(_ != '_').drop(1)
  )
}

case class FKeyConfig(
  name: Option[String] = None,
  columnType: ColumnType.Value,
  expr: Option[String] = None,
  fk: Option[Seq[FKeyRelationship]] = None,
  meta: Option[ColumnMetadata] = None // if None, it means the column use the generated metadata values for a FK
) {
  def isHashed: Boolean = expr.exists(_.toLowerCase.startsWith("hash"))
}

object ColumnMetadataRule {
  sealed trait ColumnMetadataRule
  case object Replace extends ColumnMetadataRule
  case object Concat extends ColumnMetadataRule

  implicit val colMetaConverter: ConfigReader[ColumnMetadataRule] = deriveEnumerationReader[ColumnMetadataRule]

}

case class ColumnMetadataValue(value: String, rule: ColumnMetadataRule)
case class ColumnMetadata(
  description: Option[ColumnMetadataValue] = None,
  example: Option[ColumnMetadataValue] = None,
  piiType: Option[ColumnMetadataValue] = None,
  gdprZone: Option[GDPRZoneType] = None
) {

  def consolidate(desc: Option[String], gdpr: Option[GDPRZoneType]): ColumnMetadata = {
    this.copy(
      description = description.orElse(desc.map(v => ColumnMetadataValue(v, ColumnMetadataRule.Replace))),
      gdprZone = gdprZone.orElse(gdpr)
    )
  }

}

object ColumnMetadata {

  def from(
    description: Option[String],
    example: Option[String],
    piiType: Option[String],
    gdprZone: Option[GDPRZoneType]
  ): ColumnMetadata = {
    ColumnMetadata(
      description.map(v => ColumnMetadataValue(v, ColumnMetadataRule.Replace)),
      example.map(v => ColumnMetadataValue(v, ColumnMetadataRule.Replace)),
      piiType.map(v => ColumnMetadataValue(v, ColumnMetadataRule.Replace)),
      gdprZone
    )
  }

}
