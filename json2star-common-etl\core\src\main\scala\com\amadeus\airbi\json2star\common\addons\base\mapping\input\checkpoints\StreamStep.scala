package com.amadeus.airbi.json2star.common.addons.base.mapping.input.checkpoints

import com.amadeus.airbi.json2star.common.addons.base.mapping.input.checkpoints.StaticStartStep.{
  Batch,
  FormatterYyyyMmDd
}

import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import java.time.{LocalDate, YearMonth}
import scala.collection.immutable

/** Represents a processing step in the pipeline.
  * It can be either an autoloader step or a static start step.
  */
sealed trait StreamStep

/** Represents an autoloader step used in static-start mode.
 * It is completely defined by the input paths and the corresponding month.
 */
case class AutoloaderMonthStep(inputPaths: Seq[String], yearMonth: YearMonth) extends StreamStep

/** Represents an autoloader step used when we are autoloader-start mode.
 * It is completely defined by the input paths.
 */
case class AutoloaderStartStep(inputPaths: Seq[String]) extends StreamStep

/** Represents a static start step in the pipeline.
  *
  * @param numberOfDaysPerBatch number of days to include in a batch
  * @param paths input paths
  * @param startDate start date (inclusive)
  * @param endDate end date (inclusive)
  */
case class StaticStartStep(
  numberOfDaysPerBatch: Int,
  paths: Seq[StaticStartStepPath],
  startDate: LocalDate,
  endDate: LocalDate
) extends StreamStep {
  def batches: Seq[Batch] = {
    val nroOutputDays = ChronoUnit.DAYS.between(startDate, endDate).toInt
    val dates = Range.inclusive(0, nroOutputDays).map(n => startDate.plusDays(n))
    val groupedDates = dates.sliding(numberOfDaysPerBatch, numberOfDaysPerBatch).toSeq
    groupedDates.map { g =>
      val files = g.flatMap(d => paths.flatMap(_.pathsForDate(d)))
      Batch(g.head, g.last, files)
    }
  }

}

/** Represents a resolved input path configuration for a static start step.
  * @param basePath to use in paths to be created
  * @param partitions partitions detected (integer range with no holes)
  * @param pathSuffix suffix to use in paths to be created
  */
case class StaticStartStepPath(
  basePath: String,
  partitions: Seq[Int],
  pathSuffix: String = "*/*"
) {
  def pathsForDate(d: LocalDate): Seq[String] = {
    partitions.map(p => s"${basePath}/$p/${d.format(FormatterYyyyMmDd)}/$pathSuffix")
  }
}

object StaticStartStep {
  val FormatterYyyyMmDd: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy/MM/dd")
  type Path = String
  case class Batch(start: LocalDate, end: LocalDate, paths: Seq[Path])
}

object AutoloaderMonthStep {
  import java.time.{LocalDate, YearMonth}

  private def getLastDayOfMonth(date: LocalDate): LocalDate = {
    val yearMonth = YearMonth.from(date)
    yearMonth.atEndOfMonth()
  }

  def from(
    staticStartStep: StaticStartStep,
    lastAutoloaderDate: Option[LocalDate],
    currentDate: LocalDate
  ): Seq[AutoloaderMonthStep] = {
    from(
      staticStartEndDate = staticStartStep.endDate,
      staticStartPaths = staticStartStep.paths,
      lastAutoloaderDate = lastAutoloaderDate,
      currentDate = currentDate
    )
  }

  /** Builds an autoloader step when the input config was in mode StaticStart
    * Limitations:
    * - this only works with structures like: basePath/part/year/month/day
    * - the partitions are assumed to be integers and there are no holes in the range
    */
  def from(
    staticStartEndDate: LocalDate,
    staticStartPaths: Seq[StaticStartStepPath],
    lastAutoloaderDate: Option[LocalDate],
    currentDate: LocalDate
  ): Seq[AutoloaderMonthStep] = {
    val fromDate = lastAutoloaderDate.getOrElse(staticStartEndDate).plusDays(1) // date already processed
    val toDate = getLastDayOfMonth(currentDate) // date whose entire month is to process

    // code that generates autoloader paths given these dates
    // input: fromDate = 2024-11-28, toDate = 2024-12-07
    // output: List("2024/11/{28,29,30}", "2024/12")
    // generate the code that transforms the input in the expected output

    val dateRange = asDateRange(fromDate, toDate)
    val groupedByYearMonth = dateRange.groupBy(date => (date.getYear, date.getMonthValue))
    val autoloaderSteps = groupedByYearMonth.map { case ((year, month), dates) =>
      val isFullMonth = !(year == staticStartEndDate.getYear && month == staticStartEndDate.getMonthValue)
      val daysGlob = if (isFullMonth) "" else dates.map(_.getDayOfMonth).map(d => f"$d%02d").mkString("{", ",", "}")
      val domainsPaths = staticStartPaths.flatMap { path =>
        path.partitions.map(partition => f"${path.basePath}/${partition}/${year}%04d/${month}%02d/$daysGlob")
      }
      AutoloaderMonthStep(domainsPaths, YearMonth.of(year, month))
    }.toSeq
    autoloaderSteps
  }

  private def asDateRange(fromDate: LocalDate, toDate: LocalDate): Seq[LocalDate] = {
    val nroOutputDays = ChronoUnit.DAYS.between(fromDate, toDate).toInt
    val dateRange = Range.inclusive(0, nroOutputDays).map(n => fromDate.plusDays(n))
    dateRange
  }
}
