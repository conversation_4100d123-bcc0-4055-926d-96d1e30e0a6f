package com.amadeus.airbi.json2star.common.addons.base

import com.amadeus.airbi.json2star.common.addons.stackable.StackableAddonConfig
import com.amadeus.airbi.json2star.common.addons.stackable.StackableAddons.getCompatibleStackableAddons
import com.amadeus.airbi.json2star.common.{TableDef, TablesDef}
import com.amadeus.airbi.rawvault.common.application.config.{TableConfig, TablesConfig}

/** This is a view of table config explicitly giving access to the base addon specific config
  * (on top of the table definition) + the stackable addons that can be applied to this base addon
  *
  * @param tableDef table definition
  * @param addonConfig addon specific configuration
  *  @param stackableAddons stackable addons that can be applied to this table, with their config
  * @tparam T concrete addon config type
  */
class AddonTable[T <: AddonConfig](
  val tableDef: TableDef,
  val addonConfig: T,
  val stackableAddons: List[StackableAddonConfig]
) {
  def table: TableConfig = tableDef.table
  def general: TablesConfig = tableDef.general
  def getOtherTableByName(n: String): TableDef = tableDef.getOtherTableByName(n)
}

object AddonTable {
  def fromTablesDef[T <: AddonConfig](addon: Addon[T])(tables: TablesDef): List[AddonTable[T]] =
    fromTableDefs[T](addon)(tables.tables)

  def fromTableDefs[T <: AddonConfig](addon: Addon[T])(tables: List[TableDef]): List[AddonTable[T]] =
    tables.flatMap { t =>
      addon.getConfig(t.table).map(new AddonTable(t, _, getCompatibleStackableAddons(t, addon)))
    }

  // TODO is this method actually needed?
  def fromAddonTables[T <: AddonConfig, U <: AddonConfig](addon: Addon[T])(
    tables: List[AddonTable[U]]
  ): List[AddonTable[T]] =
    tables.flatMap { i =>
      addon
        .getConfig(i.tableDef.table)
        .map(c =>
          new AddonTable(
            tableDef = i.tableDef,
            addonConfig = c,
            getCompatibleStackableAddons(i.tableDef, addon)
          )
        )
    }
}
