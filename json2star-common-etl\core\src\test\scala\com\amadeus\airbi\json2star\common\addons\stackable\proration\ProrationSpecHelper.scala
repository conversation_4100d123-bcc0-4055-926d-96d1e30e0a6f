package com.amadeus.airbi.json2star.common.addons.stackable.proration

import com.amadeus.airbi.json2star.common.addons.base.mapping.Table.KeyValueRow
import com.amadeus.airbi.json2star.common.addons.stackable.proration.ProrationColumns._
import com.amadeus.airbi.rawvault.common.processors.JsonProcessor
import com.jayway.jsonpath.DocumentContext
import net.minidev.json.JSONArray

import java.net.URLDecoder
import java.util
import scala.collection.JavaConverters._

object ProrationSpecHelper {

  /** Build a DocumentContext from a json file
    */
  def getJsonDocument(filePath: String): DocumentContext = {
    val fullPath = URLDecoder.decode(getClass.getResource(filePath).getPath, "UTF-8")
    val source = scala.io.Source.fromFile(fullPath)
    val jsonString =
      try source.mkString
      finally source.close()
    JsonProcessor.parseJsonString(
      jsonString,
      JsonProcessor.SINGLE_VALUE_CONFIGURATION
    )
  }

  /** Fetch a projection of FACT_COUPON_HISTO rows over the proration mandatory input columns from the json document
    */
  def fetchCouponRows(jsonRoot: DocumentContext): List[KeyValueRow] = {
    val issueDate = jsonRoot.read[String]("$.mainResource.current.image.creation.dateTime")
    val coupons = jsonRoot
      .read[JSONArray]("$.mainResource.current.image.coupons[*]")
      .asScala
      .toList
      .map(_.asInstanceOf[util.LinkedHashMap[String, Any]].asScala.toMap)

    def fetchAirport(c: Map[String, Any], kind: String) = {
      c("soldSegment")
        .asInstanceOf[util.LinkedHashMap[String, Any]]
        .get(kind)
        .asInstanceOf[util.LinkedHashMap[String, String]]
        .get("iataCode")
    }

    val rows: List[KeyValueRow] = coupons.map(c => {
      Map(
        DOCUMENT_CREATION_DATE -> issueDate,
        SEQUENCE_NUMBER -> c("sequenceNumber").toString,
        SOLD_DEPARTURE_AIRPORT -> fetchAirport(c, "departure"),
        SOLD_ARRIVAL_AIRPORT -> fetchAirport(c, "arrival")
      )
    })
    rows
  }

}
