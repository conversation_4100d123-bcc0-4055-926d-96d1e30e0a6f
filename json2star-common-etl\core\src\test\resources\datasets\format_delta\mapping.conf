{"tables": [{"name": "FACT_TABLE1", "mapping": {"merge": {"key-columns": ["PERIOD_ID", "VERSION"], "if-dupe-take-higher": ["LOAD_DATE"]}, "root-sources": [{"blocks": [{"base": "$"}]}], "columns": [{"name": "PERIOD_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"}, {"name": "VERSION", "column-type": "intColumn", "sources": {"literal": "1"}}, {"name": "PERIOD_START", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.aggregatedPeriod.start"}]}}, {"name": "PERIOD_END", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.aggregatedPeriod.end"}]}}, {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.aggregatedPeriod.start"}]}}, {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}}, {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}], "pit": {"type": "master-pit-table", "master": {"pit-key": "PERIOD_ID", "pit-version": "VERSION", "valid-from": "DATE_BEGIN", "valid-to": "DATE_END", "is-last": "IS_LAST_VERSION"}}}}]}