model-conf-file = "datasets/sfpush/mapping.conf"

stream {
  sink {
    checkpoint-location = "target/test/resources/checkpoints/"
    trigger = "once"
    delta-options = {}
  }
}

spark-conf {
  "spark.app.name" = Foo bar
  "spark.driver.memory" = 2g
}

common {
  domain = "DOMAIN"
  output-database = "DB_DOMAIN_1_0_1"
  domain-version = "1_0_1"
  output-path = "abfss://container-name@storage-account/tmp/json2star/output/path"
  shard = "6X"
}

cloud-files-conf {
  use-notifications = true
  backfill-interval = 1 day
  include-existing-files = false
}

processing-params {
  hash-debug = true
  enable-table-metrics = true
  resize-params {
    num-workers-default: 5
    num-workers-sf-push: 1
  }
}

tables-selectors = [ "TEST_CORE" ]


dim-prefiller-params = {
folder-path = "toto",
input-files = {
  "KEY1" = {
    options = {
      header : "true",
      delimiter : "^"
    },
    format = "CSV",
    path = "path10"
  },
  "KEY2" = {
    options = {
      header : "true",
      delimiter : "^"
    },
    format = "CSV",
    path = "path10"
  }
}
}




