# Readme

## test multiple root sources for a DIM table

The files in `data/` represents 2 pnrs, with 3 versions each.

This data has been extracted from EY UAT data.

Goal is to test th extraction of 2 Dimension tables (DIM_CARRIER and DIM_POINT_OF_SALES), from multiple root-sources in the input data:
- DIM_POINT_OF_SALES has specific columns for each root-sources
- DIM_CARRIER has 2 common columns coming from multiple root-sources.

