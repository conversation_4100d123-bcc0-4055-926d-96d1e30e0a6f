package com.amadeus.airbi.json2star.common.app

import com.amadeus.airbi.rawvault.common.RootConfig
import com.typesafe.scalalogging.Logger

import java.time.format.DateTimeFormatter
import java.time.{Instant, ZoneId, ZonedDateTime}

object Display {
  def formatDuration(startedMSec: Long, finishedMSec: Long): String = s"${(finishedMSec - startedMSec) / 1000}s"

  def display(s: String, rootConfig: RootConfig, logger: Logger): Unit = {
    display(s, rootConfig.processingParams.displayMainEvents, logger)
  }

  def display(s: String, displayMainEvents: Boolean, logger: Logger): Unit = {
    logger.info(s)
    if (displayMainEvents) {
      println(s) // scalastyle:ignore
    }
  }

  def displayDuration[T](msgToFill: String => String, logger: Logger, displayMainEvents: Boolean)(code: => T): T = {
    val t0 = System.currentTimeMillis()
    val result = code
    val t1 = System.currentTimeMillis()
    val duration = t1 - t0
    val tempSec = duration / 1000
    val sec = tempSec                % 60
    val min = (tempSec / 60)         % 60
    val hour = (tempSec / (60 * 60)) % 24

    val dateTimeFormatter = DateTimeFormatter.ISO_OFFSET_DATE_TIME
    val start = dateTimeFormatter.format(ZonedDateTime.ofInstant(Instant.ofEpochMilli(t0), ZoneId.of("UTC")))
    val end = dateTimeFormatter.format(ZonedDateTime.ofInstant(Instant.ofEpochMilli(t1), ZoneId.of("UTC")))

    val timeStr = s"Start = ${start} | End = ${end}"
    val msg = msgToFill(f" | FINISHED: took ${tempSec} ss - ${hour}%02d:${min}%02d:${sec}%02d | ${timeStr}")
    logger.info(msg)
    if (displayMainEvents) {
      println(msg) // scalastyle:ignore
    }
    result
  }
}
