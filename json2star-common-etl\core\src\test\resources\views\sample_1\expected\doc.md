
### Tables overview
#### Fact Tables
| Table | Description | GDPR Zone | Granularity | Primary key | Subdomain |
| ---- | ---- | ---- | ---- | ---- | ---- |
| FACT_PASSENGER | Latest view of FACT_PASSENGER_HISTO |  | Same as FACT_PASSENGER_HISTO, considering only the latest version | PASSENGER_ID | Passenger |
| FACT_PASSENGER_HISTO |  |  |  | PASSENGER_ID-VERSION | Passenger |
| FACT_SECONDARY_HISTO |  |  |  | FACT_SECONDARY_ID-VERSION | Passenger |

#### Dimension Tables
No Table is available
#### Association Tables
No Table is available
### Tables details with fields
#### Fact Tables Fields
| Table | Field | Description | Example | GDPR Zone | Format | Mandatory | In FK to | Source Path |
| ---- | ---- | ---- | ---- | ---- | ---- | ---- | ---- | ---- |
| FACT_PASSENGER | INTERNAL_ZORDER |  |  |  | STRING | Y |  | <p>hashM(.id)</p> |
| FACT_PASSENGER | PASSENGER_ID |  |  | <p>green</p> | STRING | PK |  | <p>hashM(.id)</p> |
| FACT_PASSENGER | REFERENCE_KEY |  |  |  | STRING | Y |  | <p>.id</p> |
| FACT_PASSENGER | CPR_FEED_TYPE |  |  |  | STRING | N |  | <p>.cprFeedType</p> |
| FACT_PASSENGER | ETAG |  |  |  | STRING | N | FACT_DUMMY | <p>.etag</p> |
| FACT_PASSENGER | GROUP_NAME |  |  |  | STRING | N |  | <p>.groupName</p> |
| FACT_PASSENGER | IS_MASTER_RECORD |  |  |  | BOOLEAN | N |  | <p>.isMasterRecord</p> |
| FACT_PASSENGER | IS_SAME_PHYSICAL_CUSTOMER |  |  |  | BOOLEAN | N |  | <p>.isSamePhysicalCustomer</p> |
| FACT_PASSENGER | IS_SYSTEM_MARKED_SPC |  |  |  | BOOLEAN | N |  | <p>.isSystemMarkedSPC</p> |
| FACT_PASSENGER | TYPE |  |  |  | STRING | N |  | <p>.type</p> |
| FACT_PASSENGER | BIRTH_DATE |  |  |  | DATE | N |  | <p>.passenger.dateOfBirth[\*].timings[?(!(@.qualifier=~/.\*ZULU/))]</p> |
| FACT_PASSENGER | BIRTH_PLACE |  |  |  | STRING | N |  | <p>.passenger.placeOfBirth</p> |
| FACT_PASSENGER | PAX_TYPE |  |  |  | STRING | N |  | <p>.passenger.flightPassengerType</p> |
| FACT_PASSENGER | GENDER |  |  |  | STRING | N |  | <p>.passenger.gender</p> |
| FACT_PASSENGER | NATIONALITY |  |  |  | STRING | N |  | <p>.passenger.nationality</p> |
| FACT_PASSENGER | SPECIAL_SEAT |  |  |  | STRING | N |  | <p>.passenger.specialSeat</p> |
| FACT_PASSENGER | FIRST_NAME |  |  |  | STRING | N |  | <p>.passenger.name.firstName</p> |
| FACT_PASSENGER | LAST_NAME |  |  |  | STRING | N |  | <p>.passenger.name.lastName</p> |
| FACT_PASSENGER | TITLE |  |  |  | STRING | N |  | <p>.passenger.name.title</p> |
| FACT_PASSENGER | RESIDENCE_COUNTRY |  |  |  | STRING | N |  | <p>.passenger.countryOfResidence</p> |
| FACT_PASSENGER | AGE |  |  |  | INT | N |  | <p>.passenger.age</p> |
| FACT_PASSENGER | STAFF_CATEGORY |  |  |  | STRING | N |  | <p>.staff.category</p> |
| FACT_PASSENGER | STAFF_COMPANY_CODE |  |  |  | STRING | N |  | <p>.staff.companyCode</p> |
| FACT_PASSENGER | STAFF_COMPANY_NAME |  |  |  | STRING | N |  | <p>.staff.companyName</p> |
| FACT_PASSENGER | STAFF_ID |  |  | <p>green</p> | STRING | N |  | <p>.staff.id</p> |
| FACT_PASSENGER | STAFF_BOOKING_TYPE |  |  |  | STRING | N |  | <p>.staff.idType</p> |
| FACT_PASSENGER | STAFF_JOINING_DATE |  |  |  | TIMESTAMP | N |  | <p>.staff.joiningDate</p> |
| FACT_PASSENGER | STAFF_RELATIONSHIP |  |  |  | STRING | N |  | <p>.staff.relationshipType</p> |
| FACT_PASSENGER | STAFF_RETIREMENT_DATE |  |  |  | TIMESTAMP | N |  | <p>.staff.retirementDate</p> |
| FACT_PASSENGER | STAFF_TRANSFER_DAYS |  |  |  | INT | N |  | <p>.staff.transferDays</p> |
| FACT_PASSENGER | STAFF_TRANSFERS_DURING_DAYS |  |  |  | INT | N |  | <p>.staff.transfersDuringDay</p> |
| FACT_PASSENGER | RECORD_LOCATOR |  |  |  | STRING | N |  | <p>.recordLocator</p> |
| FACT_PASSENGER | VERSION |  |  | <p>green</p> | STRING | N |  | <p>.version</p> |
| FACT_PASSENGER | LOAD_DATE | <p>Technical field: date/time UTC when the source message was received in the Amadeus Big Data Platform</p> |  | <p>green</p> | TIMESTAMP | N |  |  |
| FACT_PASSENGER_HISTO | DATE_BEGIN |  |  |  | TIMESTAMP | N |  | <p>.lastModification.dateTime</p> |
| FACT_PASSENGER_HISTO | DATE_END |  |  |  | TIMESTAMP | N |  |  |
| FACT_PASSENGER_HISTO | IS_LAST_VERSION |  |  |  | BOOLEAN | N |  |  |
| FACT_SECONDARY_HISTO | FACT_SECONDARY_ID |  |  | <p>green</p> | STRING | PK |  | <p>hashM(.id)</p> |
| FACT_SECONDARY_HISTO | REFERENCE_KEY |  |  |  | STRING | Y |  | <p>.id</p> |
| FACT_SECONDARY_HISTO | VERSION |  |  | <p>green</p> | STRING | PK |  | <p>.version</p> |
| FACT_SECONDARY_HISTO | DATE_BEGIN |  |  |  | TIMESTAMP | N |  | <p>.lastModification.dateTime</p> |
| FACT_SECONDARY_HISTO | DATE_END |  |  |  | TIMESTAMP | N |  |  |
| FACT_SECONDARY_HISTO | IS_LAST_VERSION |  |  |  | BOOLEAN | N |  |  |
| FACT_SECONDARY_HISTO | LOAD_DATE | <p>Technical field: date/time UTC when the source message was received in the Amadeus Big Data Platform</p> |  | <p>green</p> | TIMESTAMP | N |  |  |

#### Dimension Tables Fields
No Table is available
#### Association Tables Fields
No Table is available

