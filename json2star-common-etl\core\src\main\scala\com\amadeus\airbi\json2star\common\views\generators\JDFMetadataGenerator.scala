package com.amadeus.airbi.json2star.common.views.generators

import com.amadeus.airbi.json2star.common.config.AppConfig
import com.amadeus.airbi.json2star.common.views.generators.JDFStatus.JDFStatusValue
import com.amadeus.airbi.json2star.common.views.lookups.YamlLookup
import com.amadeus.airbi.json2star.common.{MappingType, Origin, Schema, TablesDef}
import com.amadeus.airbi.rawvault.common.application.config.ModelConfigLoader
import com.amadeus.airbi.rawvault.common.config.GDPRZone
import org.json4s.DefaultFormats
import org.json4s.jackson.Serialization.{writePretty => asJson}
import org.rogach.scallop.{singleArgConverter, ValueConverter}

object JDFStatus {
  sealed trait JDFStatusValue
  case object Activated extends JDFStatusValue

  /**
   * Preview status is used to indicate that the metadata is not yet activated.
   * It is used to enable clients to work on a pre-release version of the dataset.
   * See BDS-27143 for more details.
   */
  case object Preview extends JDFStatusValue
  case object Decommissioned extends JDFStatusValue

  implicit val converter: ValueConverter[JDFStatusValue] = singleArgConverter[JDFStatusValue]((s: String) =>
    s.trim.toLowerCase() match {
      case "preview" => Preview
      case "activated" => Activated
      case "decommissioned" => Decommissioned
      case _ => throw new Exception(s"Not a valid value - use preview, activated, decommissioned")
    }
  )
  def toString(s: JDFStatusValue): String = s match {
    case Preview => "PREVIEW"
    case Activated => "ACTIVATED"
    case Decommissioned => "DECOMMISSIONED"
  }

}

case class JDFMetadata(
  version: String,
  path: String,
  shard: String,
  schemaName: String,
  status: String,
  tables: Seq[JDFTableMetadata]
)

case class JDFTableMetadata(
  name: String,
  `type`: String,
  description: String,
  gdprZone: String,
  kind: String,
  query: Option[String],
  columns: Seq[JDFColumnMetadata]
)

case class JDFColumnMetadata(
  name: String,
  description: String,
  `type`: String,
  primaryKey: Boolean,
  gdprZone: Option[String],
  piiType: String,
  sourceSystem: Option[String],
  example: Option[String],
  fkRelationships: Seq[JDFFKeyRelationship],
  sourcePaths: Option[Seq[String]]
)

case class JDFFKeyRelationship(
  schemaName: Option[String] = None,
  tableName: String,
  columnName: String
)

object JDFMetadataGenerator {

  case class Config(
    modelConfFile: String,
    tablesSelectors: Set[String],
    disabledStackableAddons: Set[String],
    common: AppConfig.Common,
    yamlFilePath: Option[String] = None,
    inputDatabases: Map[String, String],
    status: Option[JDFStatusValue]
  )

  private val EmptyValue = ""
  private val ConcatenateValue = ","

  protected def from(config: Config): JDFMetadata = {
    // Load J2S Json Config
    val tablesConfig =
      ModelConfigLoader.defaultLoad(config.modelConfFile, config.tablesSelectors, config.disabledStackableAddons)
    // Build Tables Definition
    val allTablesDef = TablesDef.consolidate(tablesConfig)
    // Load Metadata YAML config
    val yamlLookup = config.yamlFilePath.map(fp => YamlLookup(fp, tablesConfig.jsonToYamlPaths))

    val tables = allTablesDef.tables.map { td =>
      val columns = td.schema.columns.map { col =>
        val sourcePathList = col.origins.map {
          case Origin(_, _, Some(transformed), _) => transformed
          case Origin(raw, _, _, MappingType) => raw
          case _ => EmptyValue
        }.distinct
        val colDetails = ColumnDetails.from(yamlLookup, col, td.schema.name)
        JDFColumnMetadata(
          name = col.name,
          description = colDetails.descriptions.mkString("\n"),
          `type` = DeltaSchemaGenerator.toTableType(col.columnType),
          primaryKey = col.belongsToPK,
          gdprZone = col.consolidatedGdprZone(td.schema.name).map(GDPRZone.toString),
          piiType = colDetails.piiTypes.mkString(ConcatenateValue),
          sourceSystem = None,
          example = if (colDetails.examples.isEmpty) None else Some(colDetails.examples.mkString(ConcatenateValue)),
          fkRelationships = col.fk
            .map { frs =>
              frs.map { fr =>
                JDFFKeyRelationship(
                  schemaName = fr.schema.map(s => config.inputDatabases.getOrElse(s, s"[$s]")),
                  tableName = fr.table,
                  columnName = fr.columnName
                )
              }
            }
            .getOrElse(Seq()),
          sourcePaths = if (sourcePathList == List("") || sourcePathList.isEmpty) None else Some(sourcePathList)
        )
      }
      JDFTableMetadata(
        name = td.schema.name,
        `type` = td.schema.name.split("_").head,
        description = td.schema.description.flatMap(_.description).getOrElse(EmptyValue),
        gdprZone = td.schema.gdprZone.map(GDPRZone.toString).getOrElse(EmptyValue),
        td.schema.kind.name,
        td.schema.kind match {
          case Schema.Materialized => None
          case Schema.View(query) => Some(query)
        },
        columns = columns
      )
    }

    val outputPathUri = new java.net.URI(config.common.outputPath)
    JDFMetadata(
      config.common.domainVersion,
      outputPathUri.getPath.replaceFirst("/", ""),
      config.common.shard,
      config.common.outputDatabase + ".db",
      JDFStatus.toString(config.status.getOrElse(JDFStatus.Activated)),
      tables
    )
  }

  def jsonFrom(config: Config): String = {
    val jdfMeta = from(config)
    asJson(jdfMeta)(DefaultFormats)
  }

  def viewsFrom(config: Config): String = {
    val jdfMeta = from(config)
    jdfMeta.tables
      .map(t => (t.name, t.query))
      .flatMap {
        case (name, Some(query)) => Some(s"CREATE VIEW $name AS $query;")
        case _ => None
      }
      .mkString("\n")
  }

}
