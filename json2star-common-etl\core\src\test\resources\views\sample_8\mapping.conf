{
  // TODO add tests with cartesian: 1. 2 branches, 2. 3 branches, 3. 3 branches with one multi block, 4. 2 branches with nested cartesian
  "tables": [
    {
      "name": "DIM_TWO_BRANCHES",
      "mapping": {
        "merge": {
          "key-columns": ["BASE"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          {
            "blocks": [
              {"base": "$.mainResource.current.image"},
              {
                "cartesian": [
                  [{"prod": "$.products[*]"}],
                  [{"trv": "$.travelers[*]"}]
                ]
              }
            ]
          }
        ],
        "columns": [
          { "name": "BASE", "column-type": "strColumn","sources": {"blocks": [{"base": "$.baseId"}]} },
          { "name": "PRODUCT", "column-type": "strColumn","sources": {"blocks": [{"prod": "$.productId"}]} },
          { "name": "TRAVELER", "column-type": "strColumn","sources": {"blocks": [{"trv": "$.travelerId"}]} }
        ],
        "pit": {
          "type": "no-pit-table"
        }}
    },
    {
      "name": "DIM_THREE_BRANCHES",
      "mapping": {
        "merge": {
          "key-columns": ["ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          {
            "blocks": [
              {"base": "$.mainResource.current.image"},
              {
                "cartesian": [
                  [{"prod": "$.products[*]"}],
                  [{"trv": "$.travelers[*]"}],
                  [{"leg": "$.legs[*]"}]
                ]
              }
            ]
          }
        ],
        "columns": [
          { "name": "BASE", "column-type": "strColumn","sources": {"blocks": [{"base": "$.baseId"}]} },
          { "name": "PRODUCT", "column-type": "strColumn","sources": {"blocks": [{"prod": "$.productId"}]} },
          { "name": "TRAVELER", "column-type": "strColumn","sources": {"blocks": [{"trv": "$.travelerId"}]} },
          { "name": "LEG", "column-type": "strColumn","sources": {"blocks": [{"leg": "$.legId"}]} }
        ],
        "pit": {
          "type": "no-pit-table"
        }}
    },
    {
      "name": "DIM_THREE_BRANCHES_MULTI_BLOCK",
      "mapping": {
        "merge": {
          "key-columns": ["BASE"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          {
            "blocks": [
              {"base": "$.mainResource.current.image"},
              {
                "cartesian": [
                  [{"prod": "$.products[*]"}],
                  [{"trv": "$.travelers[*]"}],
                  [{"leg": "$.legs[*]"},{"cabin": "$.cabins[*]"}]
                ]
              }
            ]
          }
        ],
        "columns": [
          { "name": "BASE", "column-type": "strColumn","sources": {"blocks": [{"base": "$.baseId"}]} },
          { "name": "PRODUCT", "column-type": "strColumn","sources": {"blocks": [{"prod": "$.productId"}]} },
          { "name": "TRAVELER", "column-type": "strColumn","sources": {"blocks": [{"trv": "$.travelerId"}]} },
          { "name": "LEG", "column-type": "strColumn","sources": {"blocks": [{"leg": "$.legId"}]} },
          { "name": "CABIN", "column-type": "strColumn","sources": {"blocks": [{"cabin": "$.cabinId"}]} }
        ],
        "pit": {
          "type": "no-pit-table"
        }}
    },
    {
      "name": "DIM_SUB_CARTESIAN",
      "mapping": {
        "merge": {
          "key-columns": ["BASE"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          {
            "blocks": [
              {"base": "$.mainResource.current.image"},
              {
                "cartesian": [
                  [{"prod": "$.products[*]"}],
                  [
                    {"leg": "$.legs[*]"},
                    {"cartesian": [
                      [{"baggroup": "$.baggageGroups[*]"},{"bags": "$.baggage[*]"}],
                      [{"service": "$.services[*]"}]
                    ]}
                  ]
                ]
              }
            ]
          }
        ],
        "columns": [
          { "name": "BASE", "column-type": "strColumn","sources": {"blocks": [{"base": "$.baseId"}]} },
          { "name": "PRODUCT", "column-type": "strColumn","sources": {"blocks": [{"prod": "$.productId"}]} },
          { "name": "LEG", "column-type": "strColumn","sources": {"blocks": [{"leg": "$.legId"}]} },
          { "name": "BAG_GROUP", "column-type": "strColumn","sources": {"blocks": [{"baggroup": "$.bagGroupId"}]} },
          { "name": "BAG", "column-type": "strColumn","sources": {"blocks": [{"bags": "$.bagId"}]} },
          { "name": "SERVICE", "column-type": "strColumn","sources": {"blocks": [{"service": "$.serviceId"}]} }
        ],
        "pit": {
          "type": "no-pit-table"
        }}
    }
  ]
}