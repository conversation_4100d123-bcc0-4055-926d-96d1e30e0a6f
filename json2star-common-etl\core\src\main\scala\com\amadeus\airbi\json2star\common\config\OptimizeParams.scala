package com.amadeus.airbi.json2star.common.config

/** Object providing default values for Optimize configuration
  */
object OptimizeParams {
  val DefaultParallelism: Int = 5
  val DefaultPeriodDays: Int = 30
  val DefaultAllowedHourMin: Int = 0
  val DefaultAllowedHourMax: Int = 24
  val DefaultRunReorg: Boolean = false
  val DefaultRunAnalyze: Boolean = true

  val Default: OptimizeParams = OptimizeParams()
}

/** Optimize configuration
  *
  * @param parallelism maximum number of tables that can be optimized in parallel
  * @param periodDays minimum number of day between 2 optimize, 0 means that optimize is not subject to period check
  * @param allowedHourMin minimum hour of the day (0-24) when the optimize can take place (default: 0)
  * @param allowedHourMax maximum hour of the day (0-24) when the optimize can take place (default: 24)
  * @param runReorg enable the run of REORG TABLE .. APPLY (PURGE) after the optimize
  * @param runAnalyze enable the run of ANALYZE TABLE ... COMPUTE STATISTICS NOSCAN after the optimize
  */
case class OptimizeParams(
  parallelism: Int = OptimizeParams.DefaultParallelism,
  periodDays: Int = OptimizeParams.DefaultPeriodDays, // 0 means always
  allowedHourMin: Int = OptimizeParams.DefaultAllowedHourMin, // 0-24
  allowedHourMax: Int = OptimizeParams.DefaultAllowedHourMax, // 0-24
  runReorg: Boolean = OptimizeParams.DefaultRunReorg, // false
  runAnalyze: Boolean = OptimizeParams.DefaultRunAnalyze // true
)
