package com.amadeus.airbi.json2star.common.views.generators

import com.amadeus.airbi.json2star.common.views.generators.SchemaGenerator.Statements
import com.amadeus.airbi.json2star.common.{ColumnDef, Schema, TableDef}
import com.amadeus.airbi.rawvault.common.application.config.TableSnowflake
import com.amadeus.airbi.rawvault.common.config.ColumnType
import com.amadeus.airbi.rawvault.common.config.ColumnType.ColumnType

/** It generates the SQL DDL statements to create Snowflake Tables from the mapping file
  */
object SnowflakeSchemaGenerator extends SchemaGenerator {

  private[views] def toTableType(columnType: ColumnType): String = columnType match {
    case ColumnType.intColumn => "INTEGER"
    case ColumnType.strColumn => "STRING"
    case ColumnType.dateColumn => "DATE"
    case ColumnType.timestampColumn => "TIMESTAMP(6)"
    case ColumnType.binaryColumn => "BINARY"
    case ColumnType.binaryStrColumn => "BINARY"
    case ColumnType.booleanColumn => "BOOLEAN"
    case ColumnType.longColumn => "BIGINT"
    case ColumnType.floatColumn => "DECIMAL(20,2)"
  }

  private[views] def toColumnDDL(col: ColumnDef): Seq[String] = {
    Seq(
      Some(col.name),
      Some(toTableType(col.columnType)),
      if (col.isMandatory) Some("NOT NULL") else None
    ).flatten
  }

  private[views] def createInternalTable: Boolean = false

  def toCreateTableSql(
    conf: TableDef,
    database: String,
    options: Map[String, String]
  ): Option[Statements] = {
    val maybeSf = conf.table.tableSnowflake
    conf.schema.kind match {
      case Schema.Materialized =>
        Some(toCreateTableSql(
          td = conf.schema,
          conf = maybeSf,
          database = database,
          options = options
        ))
      case Schema.View(query) => Some(Statements(Seq(s"CREATE VIEW ${database}.${conf.schema.name} AS ${query};")))
    }
  }

  private[views] def toCreateTableSql(
    td: Schema,
    conf: Option[TableSnowflake],
    database: String,
    options: Map[String, String]
  ): Statements = {

    val clusterBy = (td.partitionColumn, conf) match {
      // if both defined, partition column from Schema (is-last for pit tables) has priority
      case (Some(c), Some(s)) => c :: s.clusterBy
      case (Some(c), None) => Seq(c)
      case (None, Some(s)) => s.clusterBy
      case (None, None) => Seq()
    }
    val clusterByStr = if (clusterBy.nonEmpty) Some(s"CLUSTER BY (${clusterBy.mkString(",")})") else None

    val createSql = Some(
      s"""|CREATE TABLE ${database}.${td.name} (
          |${formatDDL(td.columns)}
          |)
          |${clusterByStr.getOrElse("")}
          |""".stripMargin.trim
    )

    val pks = td.keyColumns.map(_.name)
    val alterSql = if (pks.nonEmpty) {
      Some(s"""ALTER TABLE ${database}.${td.name} ADD CONSTRAINT ${td.name}_PK PRIMARY KEY (${pks.mkString(", ")})""")
    } else {
      None
    }

    val statements = Seq(createSql, alterSql).flatten.map(q => s"$q;")
    Statements(statements)
  }

}
