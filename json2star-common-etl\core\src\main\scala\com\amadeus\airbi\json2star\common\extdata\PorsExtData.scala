package com.amadeus.airbi.json2star.common.extdata

import com.amadeus.airbi.rawvault.common.RootConfig
import com.amadeus.ti.models.por.{Por, PorsRef}
import com.amadeus.ti.parsers.por.optd.PorParser
import org.apache.spark.sql.SparkSession

import java.time.LocalDate

case class PorsExtData(data: PorsRef) extends ExtDataValue
object PorsExtData {
  val ERROR_MSG_PREFIX = "OPTD"

  def load(rootConfig: RootConfig, sparkSession: SparkSession): ExtDataValue = {
    val inputPath = rootConfig.etl.common.refDataOptdLocation
    inputPath match {
      case Some(path) =>
        // OPTD por is 10 MB - it can be collected on the driver
        val src = sparkSession.sparkContext.textFile(path).collect()
        val header :: lines = src.toList
        val porsRef = PorParser.toPorsRef(header, lines)
        PorsExtData(porsRef)
      case None =>
        throw new Exception(
          "OPTD path is not set in the application.conf file. Check common.refDataOptdLocation"
        )
    }
  }

  def getDistance(pors: PorsRef, bCode: String, oCode: String, date: LocalDate): Either[String, Double] = {
    applyFunc(pors, bCode, oCode, date, (b: Por, o: Por) => b.distance(o))
  }

  def applyFunc[T](
    pors: PorsRef,
    bCode: String,
    oCode: String,
    date: LocalDate,
    func: (Por, Por) => T
  ): Either[String, T] = {
    val board = pors.any(bCode, date)
    val off = pors.any(oCode, date)
    (board, off) match {
      case (Some(b), Some(o)) => Right(func(b, o))
      case (Some(_b), None) => Left(s"${ERROR_MSG_PREFIX}: city is not found for ${oCode}")
      case (None, Some(_o)) => Left(s"${ERROR_MSG_PREFIX}: city is not found for ${bCode}")
      case (None, None) => Left(s"${ERROR_MSG_PREFIX}: city is not found for ${bCode} and for ${oCode}")
    }
  }

}
