package com.amadeus.airbi.json2star.common.optimize

import com.amadeus.airbi.json2star.common.Schema.Materialized
import com.amadeus.airbi.json2star.common.TablesDef
import com.amadeus.airbi.json2star.common.app.Display.displayDuration
import com.amadeus.airbi.json2star.common.app.{Display, PipelineContext}
import com.typesafe.scalalogging.Logger
import org.slf4j.LoggerFactory

import java.time.{LocalDateTime, ZoneId}
import scala.concurrent.duration.Duration
import scala.concurrent.{Await, Future}
import scala.util.{Failure, Success, Try}

object Optimize {

  sealed trait OptimizeStatus {
    def toDebugString: String
  }

  case class OptimizeOk(tableName: String) extends OptimizeStatus {
    override def toDebugString: String = s"Optimize OK for table: $tableName"
  }

  case class OptimizeFailed(tableName: String, reason: String, exception: Throwable) extends OptimizeStatus {
    override def toDebugString: String = s"Optimize failed for table: $tableName: $reason"
  }

}

class Optimize(ctx: PipelineContext) {

  import Optimize._
  import ctx.xc

  lazy val logger: Logger = Logger(LoggerFactory.getLogger(getClass.getName))

  /** Entry point.
    *
    * @param tables list of tables to optimize, if configured so and the time conditions apply
    */
  def optimize(tables: TablesDef): Unit = {
    // Set current database
    val database = ctx.rootConfig.etl.common.outputDatabase
    if (ctx.sparkSession.catalog.databaseExists(database)) {
      ctx.sparkSession.catalog.setCurrentDatabase(database)
    } else {
      throw new IllegalStateException(
        s"Database '$database' does not exist: please create database and tables first"
      )
    }
    val internalMetadata = InternalOptimizeMetadata(ctx.sparkSession)
    if (shouldOptimize(internalMetadata)) {
      OptimizeResizeLogic.handleClusterResize(ctx.sparkSession, ctx.resizeActuator)
      displayDuration("[optimize]" + _, logger, ctx.rootConfig.processingParams.displayMainEvents) {
        logger.info(s"Launching optimize")
        optimizeTables(tables)
        internalMetadata.logOptimize()
      }
    } else {
      logger.info(s"Optimize not launched")
    }
  }

  /** We should optimize if:
    * - the period since last optimize is elapsed AND
    * - the hour is in the right interval (TODO OR we are at a specific batch)
    */
  def shouldOptimize(internalMetadata: InternalOptimizeMetadata): Boolean = {
    val now = LocalDateTime.now(ZoneId.of("UTC"))
    if (periodElapsed(now, internalMetadata)) {
      val hourInInterval = hourOk(now)
      Display.display(s"[optimize] Period elapsed: true, hour in interval: $hourInInterval", ctx.rootConfig, logger)
      hourInInterval
    } else {
      Display.display(s"[optimize] Not doing optimize because period not elapsed", ctx.rootConfig, logger)
      false
    }
  }

  def periodElapsed(now: LocalDateTime, internalMetadata: InternalOptimizeMetadata): Boolean = {
    val internalMetadata = InternalOptimizeMetadata(ctx.sparkSession)
    val lastOptimize = internalMetadata.getLastOptimizeTimestamp.toLocalDateTime
    val duration = java.time.Duration.between(lastOptimize, now).toDays
    val elapsed = duration >= ctx.rootConfig.processingParams.optimizeParams.periodDays
    logger.info(
      s"lastOptimize: $lastOptimize, now: $now, duration: $duration days, " +
        s"configured period: ${ctx.rootConfig.processingParams.optimizeParams.periodDays} days, " +
        s"period elapsed: $elapsed"
    )
    elapsed
  }

  def hourOk(now: LocalDateTime): Boolean = {
    def inInterval(h: Int, s: Int, e: Int): Boolean = {
      val ok = h >= s && h <= e
      logger.info(s"In interval: $h, [$s, $e], $ok")
      ok
    }

    val currentHour = now.getHour
    val min = ctx.rootConfig.processingParams.optimizeParams.allowedHourMin
    val max = ctx.rootConfig.processingParams.optimizeParams.allowedHourMax
    if (min < max) {
      inInterval(currentHour, min, max)
    } else {
      // 2 intervals to check: [min, 24]; [0, max]
      inInterval(currentHour, min, 24) || inInterval(currentHour, 0, max) // scalastyle:ignore
    }
  }

  private def optimizeTables(tables: TablesDef): Unit = {
    val tablesToOptimize = tables.tables
      .filter(_.schema.kind == Materialized)
      .map(t => (t.table.name, t.table.zorderColumns))
    val tot = tablesToOptimize.size
    val optimizeFutures = tablesToOptimize.zipWithIndex.map { case ((table, col), i) =>
      optimizeTable(table, col, i, tot)
    }
    val optimizeStatuses = Await.result(Future.sequence(optimizeFutures), Duration.Inf)
    val optimizeFailures = logOptimizeStatuses(optimizeStatuses)
    logger.info(s"Optimize failures count: ${optimizeFailures.size}")
    optimizeFailures.foreach { exception => throw exception }
  }

  def shutdown(): Unit = {
    if (xc != null) {
      xc.shutdown()
    }
  }

  private def optimizeTable(name: String, zorderCols: List[String], i: Int, tot: Int): Future[OptimizeStatus] = Future {
    ctx.sparkSession.sparkContext.setJobGroup(
      s"OPTIMIZE",
      s"TYPE=OPTIMIZE TABLE=$name (${i + 1}/$tot)"
    )

    val optimizeStatement = if (zorderCols.isEmpty) {
      s"OPTIMIZE $name"
    } else {
      s"OPTIMIZE $name ZORDER BY (${zorderCols.mkString(", ")})"
    }

    val reorgStatement = s"REORG TABLE $name APPLY (PURGE)"
    val analyzeStatement = s"ANALYZE TABLE $name COMPUTE STATISTICS NOSCAN"

    Try {
      ctx.sparkSession.sql(optimizeStatement)
      logger.info(s"Running REORG: ${ctx.rootConfig.processingParams.optimizeParams.runReorg}")
      if (ctx.rootConfig.processingParams.optimizeParams.runReorg) {
        ctx.sparkSession.sql(reorgStatement)
      }
      logger.info(s"Running ANALYZE: ${ctx.rootConfig.processingParams.optimizeParams.runAnalyze}")
      if (ctx.rootConfig.processingParams.optimizeParams.runAnalyze) {
        ctx.sparkSession.sql(analyzeStatement)
      }
    } match {
      case Success(_) => OptimizeOk(name)
      case Failure(exception) => OptimizeFailed(name, exception.getMessage, exception)
    }
  }

  private def logOptimizeStatuses(statuses: Seq[OptimizeStatus]) = {
    statuses.flatMap {
      case ok: OptimizeOk =>
        logger.trace(s"Optimize succeeded: ${ok.toDebugString}")
        None
      case failed: OptimizeFailed =>
        logger.error(s"Unexpected Optimize failure: ${failed.toDebugString}", failed.exception)
        Some(new RuntimeException(s"Unexpected Optimize failure ${failed.toDebugString}", failed.exception))
    }
  }

}
