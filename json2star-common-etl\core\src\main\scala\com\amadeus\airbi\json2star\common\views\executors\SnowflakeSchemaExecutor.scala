package com.amadeus.airbi.json2star.common.views.executors

import com.amadeus.airbi.json2star.common.TablesDef
import com.amadeus.airbi.json2star.common.views.generators.SchemaGenerator.Statements
import com.amadeus.airbi.json2star.common.views.generators.{SnowflakeSchemaGenerator, SnowflakeTableCopySchemaGenerator}
import com.amadeus.airbi.rawvault.common.application.config.ModelConfigLoader
import com.typesafe.scalalogging.Logger
import net.snowflake.spark.snowflake.Utils.runQuery
import org.rogach.scallop.{ScallopConf, ScallopOption}
import org.slf4j.LoggerFactory

import java.io.{BufferedWriter, File, FileWriter}

case class SnowflakeSchemaExecutorScallopConf(arguments: Seq[String]) extends ScallopConf(arguments) {
  val appConfigFile: ScallopOption[String] = opt[String](required = true)
  val command: ScallopOption[String] = opt[String](required = true)
  val dryMode: ScallopOption[String] = opt[String](required = false)

  verify()
}

object SnowflakeSchemaExecutor {
  object Command extends Enumeration {
    type Command = Value
    val CREATE, DROP, COPY = Value
  }

  @transient
  implicit lazy val logger: Logger = Logger(LoggerFactory.getLogger(getClass.getName))

  def main(args: Array[String]): Unit = {
    val sConfig = SnowflakeConfig(args)
    run(sConfig)
  }

  def run(sConfig: SnowflakeConfig): Unit = {
    Command.withName(sConfig.command) match {
      case Command.CREATE => runCreate(sConfig)
      case Command.DROP => runDrop(sConfig)
      case Command.COPY => runCopy(sConfig)
    }
  }

  /** Run the query
    * According to the value of dryMode - It supports
    * - when None                     --> it runs the query with Snowflake connector
    * - when a file name is provided  --> it writes the file (supports only local filesystem)
    * - when the DEBUG is provided    --> it prints to stdout
    *
    * @param query   query to run
    * @param sConfig a SnowflakeConfig
    */
  def run(query: String, sConfig: SnowflakeConfig): Unit = {
    logger.info(s"Run query: $query")
    sConfig.dryMode match {
      case None => runQuery(sConfig.options, query)
      // To print the content of SQL without executing it
      case Some(outFile) if outFile.toUpperCase == "DEBUG" => println(query) // scalastyle:ignore
      case Some(outFile) =>
        // Append to a file
        val bw = new BufferedWriter(new FileWriter(new File(outFile), true))
        bw.write(query + "\n")
        bw.close()
    }
  }

  private def runCreate(sConfig: SnowflakeConfig): Unit = {
    val mappingConf = ModelConfigLoader.defaultLoad(
      sConfig.appConfig.modelConfFile,
      sConfig.appConfig.tablesSelectors,
      sConfig.appConfig.disabledStackableAddons
    )
    val options = sConfig.options

    val initStmts = Statements(Seq(
      s"CREATE DATABASE IF NOT EXISTS ${options("sfDatabase")};",
      s"CREATE SCHEMA IF NOT EXISTS ${sConfig.dbSchema};",
      s"USE ${sConfig.dbSchema};"
    ))
    val tablesDef = TablesDef.consolidate(mappingConf)
    val createTableStmts =
      SnowflakeSchemaGenerator.toInitSql(tablesDef, sConfig.dbSchema, options = Map.empty[String, String])
    if (createTableStmts.isEmpty) {
      logger.error(s"Wrong SQL generated from the file ${sConfig.appConfig.modelConfFile}")
      logger.error(s"Wrong SQL content is ${createTableStmts.mkString("\n")}")
      throw new Exception("ERROR INIT SQL GENERATOR")
    }

    val createStmts = Seq(initStmts) ++ createTableStmts
    createStmts.foreach { st =>
      st.statements.foreach(s => run(s, sConfig))
    }
  }

  private def runDrop(sConfig: SnowflakeConfig): Unit = {
    try {
      val st = s"DROP SCHEMA IF EXISTS ${sConfig.dbSchema};"
      run(st, sConfig)
    } catch {
      case _: net.snowflake.client.jdbc.SnowflakeSQLException =>
        // required because an exception is raised when DB doesn't exist: it's just ignored
        val msg = s"WARNING: database ${sConfig.options("sfDatabase")} does not exist"
        logger.warn(msg)
    }
  }

  private def runCopy(sConfig: SnowflakeConfig): Unit = {
    val mappingConf = ModelConfigLoader.defaultLoad(
      sConfig.appConfig.modelConfFile,
      sConfig.appConfig.tablesSelectors,
      sConfig.appConfig.disabledStackableAddons
    )
    val options = sConfig.options
    val tablesDef = TablesDef.consolidate(mappingConf)
    val generator = new SnowflakeTableCopySchemaGenerator(sConfig)
    val copyStmts = generator.toInitSql(tablesDef, sConfig.dbSchema, options = Map.empty[String, String])

    if (copyStmts.isEmpty) {
      logger.error(s"Wrong SQL generated from the file ${sConfig.appConfig.modelConfFile}")
      logger.error(s"Wrong SQL content is ${copyStmts.mkString("\n")}")
      throw new Exception("ERROR COPY SQL GENERATOR")
    }

    logger.info(s"${getClass.getName} - Copying ${copyStmts.size} tables...")
    copyStmts.par.foreach { st =>
      st.statements.foreach(s => run(s, sConfig))
    }
  }
}
