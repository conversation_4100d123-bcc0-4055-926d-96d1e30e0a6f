package com.amadeus.airbi.json2star.common.app

import com.amadeus.airbi.json2star.common.TablesDef
import com.amadeus.airbi.json2star.common.addons.base.AddonTable
import com.amadeus.airbi.json2star.common.addons.base.mapping.Mapping
import com.amadeus.airbi.json2star.common.app.Display.{display, displayDuration}
import com.amadeus.airbi.json2star.common.app.Json2StarApp.readMappingConfig
import com.amadeus.airbi.json2star.common.config.{AppConfig, ProcessingParams}
import com.amadeus.airbi.json2star.common.purge.PurgeParams
import com.amadeus.airbi.rawvault.common.application.config.{MappingConfig, MasterPitTable, TablesConfig}
import com.amadeus.airbi.rawvault.common.vault.spark.EventsListener
import com.typesafe.scalalogging.Logger
import org.apache.spark.sql.SparkSession
import org.json4s.DefaultFormats
import org.json4s.jackson.Serialization.{write => asJson}
import org.rogach.scallop.{ScallopConf, ScallopOption}
import org.slf4j.LoggerFactory

import java.time.LocalDate
import java.time.format.DateTimeFormatter

case class PurgeAppScallopConf(arguments: Seq[String]) extends ScallopConf(arguments) {
  val appConfigFile: ScallopOption[String] = opt[String](required = true)
  val dryRun: ScallopOption[String] = opt[String](required = false)
  verify()
}

case class PurgeInternalContext(
  spark: SparkSession,
  purgeParams: PurgeParams,
  masterPitTableName: String,
  masterPitTableKey: String,
  masterPitTableDateBegin: String,
  masterPitTableIsLast: String,
  secPitTables: List[String],
  dryRun: Boolean,
  refDate: String
)

object PurgeApp {

  @transient
  lazy val logger: Logger = Logger(LoggerFactory.getLogger(getClass.getName))

  def main(args: Array[String]): Unit = {
    val (appConfig, purgeParams, model, dryRun) = buildConfigs(args)

    logger.info(s"--- Starting Purge pipeline ---")
    val spark: SparkSession = SparkSession
      .builder()
      .getOrCreate()

    val eventsListener = new EventsListener(appConfig.processingParams.getOrElse(ProcessingParams()))
    spark.sparkContext.addSparkListener(eventsListener)

    run(spark, appConfig.common.outputDatabase, purgeParams, model, dryRun)
    eventsListener.report()
  }

  def run(
    spark: SparkSession,
    database: String,
    purgeParams: PurgeParams,
    tablesConf: TablesConfig,
    dryRun: Boolean,
    refDate: Option[String] = None
  ): Unit = {

    val validRefDate = refDate.getOrElse(LocalDate.now().format(DateTimeFormatter.ISO_DATE))
    display(s"The reference date for the purge is: $validRefDate", displayMainEvents = true, logger)

    val purgeContext: PurgeInternalContext =
      buildPurgeInternalContext(spark, database, purgeParams, tablesConf, dryRun, validRefDate)

    val diffBefore = getDiffBtwOldestDataVsThreshold(purgeContext)
    display(
      s"Before the purge, the difference between the oldest latest version begin date and the threshold is: $diffBefore",
      displayMainEvents = true,
      logger
    )

    purge(purgeContext)

    if (!dryRun) {
      val diffAfter = getDiffBtwOldestDataVsThreshold(purgeContext)
      diffAfter match {
        case x if x > 0 =>
          logger.warn(s"Some data is still eligible to be purged (by $diffAfter minutes)")
        case _ =>
          display(
            s"After the purge, the difference between the oldest latest version begin date and the threshold is: " +
              s"$diffAfter. So, no data to purge anymore.",
            displayMainEvents = true,
            logger
          )
      }
    }
  }

  def buildPurgeInternalContext(
    spark: SparkSession,
    database: String,
    purgeParams: PurgeParams,
    tablesConf: TablesConfig,
    dryRun: Boolean,
    validRefDate: String
  ): PurgeInternalContext = {
    val tablesDef = TablesDef.consolidate(tablesConf)
    val mappingTables = AddonTable.fromTablesDef(Mapping)(tablesDef)
    val (pitMas, pitMasCf) = mappingTables
      .map(i => (i, i.addonConfig.pit))
      .collect { case (j, i: MasterPitTable) => (j, i) }
      .head // we assume only one pit master for now

    val purgeContext: PurgeInternalContext = PurgeInternalContext(
      spark = spark,
      purgeParams = purgeParams,
      masterPitTableName = s"$database.${pitMas.table.name}",
      masterPitTableKey = pitMasCf.master.pitKey,
      masterPitTableDateBegin = pitMasCf.master.validFrom,
      masterPitTableIsLast = pitMasCf.master.isLast,
      secPitTables = mappingTables.filter(_.addonConfig.secondaryPit.isDefined).map(t => s"$database.${t.table.name}"),
      dryRun = dryRun,
      refDate = validRefDate
    )
    purgeContext
  }

  private def purge(purgeContext: PurgeInternalContext): Unit = {
    val spark = purgeContext.spark
    val displayFlag = purgeContext.purgeParams.displayEvents
    val purgeThresholdMinutes = purgeContext.purgeParams.purgeThresholdMinutes
    val masterPitTableDateBegin = purgeContext.masterPitTableDateBegin
    val masterPitTableKey = purgeContext.masterPitTableKey
    val masterPitTableName = purgeContext.masterPitTableName
    val refDate = purgeContext.refDate
    val masterPitPurgeSql =
      s"""select ${masterPitTableKey}, ${masterPitTableDateBegin},
          datediff(MINUTE, ${masterPitTableDateBegin}, "${refDate}") as MINUTES_OLD
    from ${masterPitTableName}
    where ${purgeContext.masterPitTableIsLast} = true
    and datediff(MINUTE, ${masterPitTableDateBegin}, "${refDate}") >= ${purgeThresholdMinutes};
     """

    displayDuration(s"Compute MASTER PIT TABLE keys to purge" + _, logger, displayFlag) {
      // create view masterPitPurge and cache it / persist it
      val masterPitPurge = spark.sql(masterPitPurgeSql)
      masterPitPurge.cache().createOrReplaceTempView("masterPitPurge")
      display(
        s"Entities eligible for purge: ${masterPitPurge.count()} (older than ${purgeThresholdMinutes / 60 / 24} days)",
        displayMainEvents = true,
        logger
      )
    }

    if (purgeContext.dryRun) {
      display(
        s"DRY RUN: no data will be purged",
        displayMainEvents = true,
        logger
      )
    } else { // do the purge
      def mergeQuery(tableName: String, key: String): String = {
        s"""
        MERGE INTO $tableName AS target
        USING masterPitPurge AS source
        ON target.$key = source.$key
        WHEN MATCHED THEN DELETE
      """
      }

      displayDuration(s"Purge SECONDARY PIT TABLES" + _, logger, displayFlag) {
        purgeContext.secPitTables.par.foreach(secTable => {
          display(s"Purging ${secTable}", displayFlag, logger)
          spark.sparkContext.setJobDescription(secTable)
          spark.sql(mergeQuery(secTable, masterPitTableKey))
          display(s"Purge done for ${secTable}", displayFlag, logger)
        })
      }

      displayDuration(s"Purge MASTER PIT TABLE ${masterPitTableName}" + _, logger, displayFlag) {
        spark.sparkContext.setJobDescription(masterPitTableName)
        spark.sql(mergeQuery(masterPitTableName, masterPitTableKey))
      }
    }
  }

  // Comparison between the oldest data, and threshold at which purge would take place
  // If positive, some data is eligible to be purged (by x minutes)
  // If negative, no data is to be purged
  def getDiffBtwOldestDataVsThreshold(ctxt: PurgeInternalContext): Long = {
    val diffColName = "data_vs_threshold_diff"
    ctxt.spark
      .sql(s"""
      select max(datediff(MINUTE, ${ctxt.masterPitTableDateBegin}, "${ctxt.refDate}")) - ${ctxt.purgeParams.purgeThresholdMinutes} as $diffColName
      from ${ctxt.masterPitTableName}
      where ${ctxt.masterPitTableIsLast} = true;
    """)
      .first()
      .getAs[Long](diffColName)
  }

  private def isDryRun(s: String): Boolean = {
    if (s.equalsIgnoreCase("true")) { true }
    else if (s.equalsIgnoreCase("false")) { false }
    else {
      throw new IllegalArgumentException(s"Invalid value for dryRun: $s. Expected 'true' or 'false'.")
    }
  }

  def buildConfigs(args: Array[String]): (AppConfig, PurgeParams, TablesConfig, Boolean) = {
    val argsSc = PurgeAppScallopConf(args)
    val appConfigFile = argsSc.appConfigFile()
    val dryRun = isDryRun(argsSc.dryRun())

    val appConfig = AppConfig(appConfigFile)
    val purgeParams = appConfig.assumePurgeParams()
    val model = readMappingConfig(appConfig.modelConfFile, appConfig.tablesSelectors)

    logger.info(s"### PURGE DRY RUN: $dryRun")
    logger.info(s"### PURGE PARAMS: ${asJson(purgeParams)(DefaultFormats)}")

    (appConfig, purgeParams, model, dryRun)
  }

}
