{
  "tables": [
    {
      "name": "FACT_BAGS_GROUP_HISTO",
      "mapping": {
        "description": {"description": "Contains information of a baggage group (a set of bags travelling together on an identical itinerary), such as number and weight of checked bags and hand bags, and the responsible passenger.", "granularity": "1 bags group"},
        "merge": {
          "key-columns": ["BAGS_GROUP_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}]}],
        "columns": [
          {"name": "BAGS_GROUP_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
          {"name": "VERSION", "column-type": "longColumn", "sources": {"blocks": [{"base": "$.version"}]}, "expr": "bigint({0})"},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
        ],
        "pit": {
          "type": "master-pit-table",
          "master": {
            "pit-key": "BAGS_GROUP_ID",
            "pit-version": "VERSION",
            "valid-from": "DATE_BEGIN",
            "valid-to": "DATE_END",
            "is-last": "IS_LAST_VERSION"
          }
        }
      }
    },
    {
      "name": "FACT_BAG_HISTO",
      "mapping": {
        "description": {"description": "Contains information of an individual bag", "granularity": " 1 bag in 1 bags group"},
        "merge": {
          "key-columns": ["BAG_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          {"blocks": [{"base": "$.mainResource.current.image"}, {"bag": "$.bags[*]"}]}
        ],
        "columns": [
          {"name": "BAG_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"bag": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"bag": "$.id"}]}},
          {"name": "BAGS_GROUP_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "BAG_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"bag": "$.bagType"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_BAG_TYPE"}]}},
          {"name": "WEIGHT_VALUE_ORIGINAL", "column-type": "floatColumn", "sources": {"blocks": [{"bag": "$.weight.value"}]}},
          {"name": "WEIGHT_VALUE_EXTRA_PART", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.weight.value"}]}},
          {"name": "WEIGHT_UNIT_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"bag": "$.weight.unit"}]}},
          {"name": "WEIGHT_VALUE", "column-type": "floatColumn", "sources": {}}, // column is present but not filled
          {"name": "WEIGHT_UNIT", "column-type": "strColumn", "sources": {}},   // column is present but not filled
          {"name": "WEIGHT_VALUE_ORIGINAL_2", "column-type": "strColumn", "sources": {"blocks": [{"bag": "$.weight.value"}]}, "meta": {"gdpr-zone": "red"}},
          {"name": "WEIGHT_UNIT_ORIGINAL_2", "column-type": "strColumn", "sources": {"blocks": [{"bag": "$.weight.unit"}]}},
          {"name": "WEIGHT_VALUE_2", "column-type": "strColumn", "sources": {}}, // column is present but not filled
          {"name": "WEIGHT_UNIT_2", "column-type": "strColumn", "sources": {}},   // column is present but not filled
          {"name": "VERSION", "column-type": "longColumn", "sources": {"blocks": [{"base": "$.version"}]}, "expr": "bigint({0})"},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
        ],
        "pit": {
          "type": "secondary-pit-table"
        }
      },
      "stackable-addons": [
        {
          "type": "weight-conversion",
          "conversions": [
            {"src-col": "WEIGHT_VALUE_ORIGINAL", "src-unit-col": "WEIGHT_UNIT_ORIGINAL", "dst-col": "WEIGHT_VALUE", "dst-unit-col": "WEIGHT_UNIT"},
            {"src-col": "WEIGHT_VALUE_ORIGINAL_2", "src-unit-col": "WEIGHT_UNIT_ORIGINAL_2", "dst-col": "WEIGHT_VALUE_2", "dst-unit-col": "WEIGHT_UNIT_2"}
          ]
        }
      ]
    },
    {
      "name": "FACT_1_CURRENCY_CONVERSION",
      "latest": {
        "histo-table-name": "FACT_1_CURRENCY_CONVERSION_HISTO"
      },
      "table-snowflake": {
        "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
      }
    },
    {
      "name": "FACT_1_CURRENCY_CONVERSION_HISTO",
      "mapping": {
        "description": {"description": "Contains information on an individual bag involved in a excess baggage charge, such as calculation airport and details (weight, charge, rate), and references to the chargeable document", "granularity": "1 excess item for 1 excess charge"},
        "merge": {
          "key-columns": ["EXCESS_BAGGAGE_ITEM_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          {"blocks": [{"base": "$.mainResource.current.image"}, {"exc": "$.excessCharges[*]"}, {"calc": "$.calculations[*]"}, {"item": "$.items[*]"}]}
        ],
        "columns": [
          {"name": "EXCESS_BAGGAGE_ITEM_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"exc": "$.id"}, {"calc": "$.id"}, {"item": "$.id"}]}, "expr": "hashM({0})"
            , "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"exc": "$.id"}, {"calc": "$.id"}, {"item": "$.id"}]}
            , "meta": {"description": {"value": "Functional key: bagsGroupId-excessChargeId-excessCalcId-excessItemId", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "CALCULATION_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"calc": "$.id"}]}
            , "meta": {"gdpr-zone": "orange"}},
          {"name": "ITEM_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"item": "$.id"}]}
            , "meta": {"gdpr-zone": "orange"}},
          {"name": "WEIGHT_VALUE", "column-type": "floatColumn", "sources": {}
            , "meta": { "gdpr-zone": "green"}},
          {"name": "WEIGHT_UNIT", "column-type": "strColumn", "sources": {}
            , "meta": {"gdpr-zone": "green"}},
          {"name": "WEIGHT_VALUE_ORIGINAL", "column-type": "floatColumn", "sources": {"blocks": [{"item": "$.weight.value"}]}
            , "meta": {"example": {"value": "24.9", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "WEIGHT_UNIT_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"item": "$.weight.unit"}]}
            , "meta": {"gdpr-zone": "green"}},
          {"name": "CHARGE_AMOUNT", "column-type": "floatColumn", "sources": {}
            , "meta": {"description": {"value": "The charge amount of the excess baggage item, in home currency", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "CHARGE_CURRENCY", "column-type": "strColumn", "sources": {}
            , "meta": {"description": {"value": "The airline's home currency", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "CHARGE_AMOUNT_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"item": "$.charge.total"}]}
            , "meta": {"gdpr-zone": "green"}},
          {"name": "CHARGE_CURRENCY_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"item": "$.charge.currency"}]}
            , "meta": {"gdpr-zone": "green"}},
          {"name": "RATE_AMOUNT", "column-type": "floatColumn", "sources": {}
            , "meta": {"description": {"value": "The rate amount of the excess baggage item, in home currency", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "RATE_CURRENCY", "column-type": "strColumn", "sources": {}
            , "meta": {"description": {"value": "The airline's home currency", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "EXCHANGE_RATE_DATE_NEEDED", "column-type": "dateColumn", "sources": {}
            , "meta": {"description": {"value": "The date for which the home currency conversion rate was needed", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "EXCHANGE_RATE_DATE_TAKEN", "column-type": "dateColumn", "sources": {}
            , "meta": {"description": {"value": "The date for which the home currency conversion rate was found and taken", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "RATE_AMOUNT_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"item": "$.rate.total"}]}
            , "meta": {"gdpr-zone": "green"}},
          {"name": "RATE_CURRENCY_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"item": "$.rate.currency"}]}
            , "meta": {"gdpr-zone": "green"}},
          {"name": "BAG_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.bag.id"}]}, "expr": "hashM({0})"},
          {"name": "EXCESS_BAGGAGE_CHARGE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"exc": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "BAGS_GROUP_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "VERSION", "column-type": "longColumn", "sources": {"blocks": [{"base": "$.version"}]}, "expr": "bigint({0})"
            , "meta": {"description": {"value": "Version of the DCS Baggage message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}
            , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}
            , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
            , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
        ],
        "pit": {
          "type": "secondary-pit-table"
        }
      },
      "stackable-addons": [
        {
          "type": "weight-conversion",
          "conversions": [
            {"src-col": "WEIGHT_VALUE_ORIGINAL", "src-unit-col": "WEIGHT_UNIT_ORIGINAL", "dst-col": "WEIGHT_VALUE", "dst-unit-col": "WEIGHT_UNIT"}
          ]
        },
        {
          "type": "currency-conversion",
          "conversions": [
            {"src-col": "RATE_AMOUNT_ORIGINAL", "src-unit-col": "RATE_CURRENCY_ORIGINAL", "src-date-col" : "EXCHANGE_RATE_DATE_NEEDED", "dst-col": "RATE_AMOUNT", "dst-unit-col": "RATE_CURRENCY","dst-date-col" :"EXCHANGE_RATE_DATE_TAKEN"}
          ]
        }
      ],
      "table-snowflake": {
        "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
      }
    },

    {
      "name": "FACT_2_CURRENCY_CONVERSIONS_HISTO",
      "mapping": {
        "description": {"description": "Contains information on an individual bag involved in a excess baggage charge, such as calculation airport and details (weight, charge, rate), and references to the chargeable document", "granularity": "1 excess item for 1 excess charge"},
        "merge": {
          "key-columns": ["EXCESS_BAGGAGE_ITEM_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          {"blocks": [{"base": "$.mainResource.current.image"}, {"exc": "$.excessCharges[*]"}, {"calc": "$.calculations[*]"}, {"item": "$.items[*]"}]}
        ],
        "columns": [
          {"name": "EXCESS_BAGGAGE_ITEM_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"exc": "$.id"}, {"calc": "$.id"}, {"item": "$.id"}]}, "expr": "hashM({0})"
            , "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"exc": "$.id"}, {"calc": "$.id"}, {"item": "$.id"}]}
            , "meta": {"description": {"value": "Functional key: bagsGroupId-excessChargeId-excessCalcId-excessItemId", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "CALCULATION_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"calc": "$.id"}]}
            , "meta": {"gdpr-zone": "orange"}},
          {"name": "ITEM_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"item": "$.id"}]}
            , "meta": {"gdpr-zone": "orange"}},
          {"name": "WEIGHT_VALUE", "column-type": "floatColumn", "sources": {}
            , "meta": { "gdpr-zone": "green"}},
          {"name": "WEIGHT_UNIT", "column-type": "strColumn", "sources": {}
            , "meta": {"gdpr-zone": "green"}},
          {"name": "WEIGHT_VALUE_ORIGINAL", "column-type": "floatColumn", "sources": {"blocks": [{"item": "$.weight.value"}]}
            , "meta": {"example": {"value": "24.9", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "WEIGHT_UNIT_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"item": "$.weight.unit"}]}
            , "meta": {"gdpr-zone": "green"}},
          {"name": "CHARGE_AMOUNT", "column-type": "floatColumn", "sources": {}
            , "meta": {"description": {"value": "The charge amount of the excess baggage item, in home currency", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "CHARGE_CURRENCY", "column-type": "strColumn", "sources": {}
            , "meta": {"description": {"value": "The airline's home currency", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "CHARGE_EXCHANGE_RATE_DATE_TAKEN", "column-type": "dateColumn", "sources": {}
            , "meta": {"description": {"value": "The date for which the home currency conversion rate was found and taken", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "CHARGE_AMOUNT_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"item": "$.charge.total"}]}
            , "meta": {"gdpr-zone": "green"}},
          {"name": "CHARGE_CURRENCY_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"item": "$.charge.currency"}]}
            , "meta": {"gdpr-zone": "green"}},
          {"name": "CHARGE_EXCHANGE_RATE_DATE_NEEDED", "column-type": "dateColumn", "sources": {"blocks": [{"item": "$.boardPoint.localDateTime"}]}
            , "meta": {"description": {"value": "The date for which the home currency conversion rate was needed", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "RATE_AMOUNT", "column-type": "floatColumn", "sources": {}
            , "meta": {"description": {"value": "The rate amount of the excess baggage item, in home currency", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "RATE_CURRENCY", "column-type": "strColumn", "sources": {}
            , "meta": {"description": {"value": "The airline's home currency", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "RATE_EXCHANGE_RATE_DATE_TAKEN", "column-type": "dateColumn", "sources": {}
            , "meta": {"description": {"value": "The date for which the home currency conversion rate was found and taken", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "RATE_AMOUNT_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"item": "$.rate.total"}]}
            , "meta": {"gdpr-zone": "green"}},
          {"name": "RATE_AMOUNT_EXTRA_PART", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.rate.total"}]}
            ,"meta": {"example": {"value": "A", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "RATE_CURRENCY_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"item": "$.rate.currency"}]}
            , "meta": {"gdpr-zone": "green"}},
          {"name": "RATE_EXCHANGE_RATE_DATE_NEEDED", "column-type": "dateColumn", "sources": {"blocks": [{"item": "$.boardPoint.localDateTime"}]}
            , "meta": {"description": {"value": "The date for which the home currency conversion rate was needed", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "BAG_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.bag.id"}]}, "expr": "hashM({0})"},
          {"name": "EXCESS_BAGGAGE_CHARGE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"exc": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "BAGS_GROUP_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "VERSION", "column-type": "longColumn", "sources": {"blocks": [{"base": "$.version"}]}, "expr": "bigint({0})"
            , "meta": {"description": {"value": "Version of the DCS Baggage message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}
            , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}
            , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
            , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
        ],
        "pit": {
          "type": "secondary-pit-table"
        }
      },
      "stackable-addons": [
        {
          "type": "weight-conversion",
          "conversions": [
            {"src-col": "WEIGHT_VALUE_ORIGINAL", "src-unit-col": "WEIGHT_UNIT_ORIGINAL", "dst-col": "WEIGHT_VALUE", "dst-unit-col": "WEIGHT_UNIT"}
          ]
        },
        {
          "type": "currency-conversion",
          "conversions": [
            {"src-col": "RATE_AMOUNT_ORIGINAL", "src-unit-col": "RATE_CURRENCY_ORIGINAL", "src-date-col" : "RATE_EXCHANGE_RATE_DATE_NEEDED", "dst-col": "RATE_AMOUNT", "dst-unit-col": "RATE_CURRENCY","dst-date-col" :"RATE_EXCHANGE_RATE_DATE_TAKEN"}
            {"src-col": "CHARGE_AMOUNT_ORIGINAL", "src-unit-col": "CHARGE_CURRENCY_ORIGINAL", "src-date-col" : "CHARGE_EXCHANGE_RATE_DATE_NEEDED", "dst-col": "CHARGE_AMOUNT", "dst-unit-col": "CHARGE_CURRENCY","dst-date-col" :"CHARGE_EXCHANGE_RATE_DATE_TAKEN"}
          ]
        }
      ],
      "table-snowflake": {
        "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
      }
    }

    {
      "name": "FACT_3_CURRENCY_CONVERSIONS_HISTO",
      "mapping": {
        "description": {"description": "Contains information on an individual bag involved in a excess baggage charge, such as calculation airport and details (weight, charge, rate), and references to the chargeable document", "granularity": "1 excess item for 1 excess charge"},
        "merge": {
          "key-columns": ["EXCESS_BAGGAGE_ITEM_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          {"blocks": [{"base": "$.mainResource.current.image"}, {"exc": "$.excessCharges[*]"}, {"calc": "$.calculations[*]"}, {"item": "$.items[*]"}]}
        ],
        "columns": [
          {"name": "EXCESS_BAGGAGE_ITEM_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"exc": "$.id"}, {"calc": "$.id"}, {"item": "$.id"}]}, "expr": "hashM({0})"
            , "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "RATE_AMOUNT", "column-type": "floatColumn", "sources": {}
            , "meta": {"description": {"value": "The rate amount of the excess baggage item, in home currency", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "RATE_CURRENCY", "column-type": "strColumn", "sources": {}
            , "meta": {"description": {"value": "The airline's home currency", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "RATE_EXCHANGE_RATE_DATE_TAKEN", "column-type": "dateColumn", "sources": {}
            , "meta": {"description": {"value": "The date for which the home currency conversion rate was found and taken", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "RATE_AMOUNT_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"item": "$.rate.total"}]}
            , "meta": {"gdpr-zone": "green"}},
          {"name": "RATE_AMOUNT_ADDITIONAL_PART", "column-type": "strColumn", "sources": {}
            , "meta": {"description": {"value": "The rate amount additional part of the excess baggage item, in home currency", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "RATE_CURRENCY_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"item": "$.rate.currency"}]}
            , "meta": {"gdpr-zone": "green"}},
          {"name": "RATE_EXCHANGE_RATE_DATE_NEEDED", "column-type": "dateColumn", "sources": {"blocks": [{"item": "$.boardPoint.localDateTime"}]}
            , "meta": {"description": {"value": "The date for which the home currency conversion rate was needed", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "BAGS_GROUP_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "VERSION", "column-type": "longColumn", "sources": {"blocks": [{"base": "$.version"}]}, "expr": "bigint({0})"
            , "meta": {"description": {"value": "Version of the DCS Baggage message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}
            , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}
            , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
            , "meta": {"description": {"value": "Indidates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
        ],
        "pit": {
          "type": "secondary-pit-table"
        }
      },
      "stackable-addons": [
        {
          "type": "currency-conversion",
          "conversions": [
            {"src-col": "RATE_AMOUNT_ORIGINAL", "src-unit-col": "RATE_CURRENCY_ORIGINAL", "src-date-col" : "RATE_EXCHANGE_RATE_DATE_NEEDED", "dst-col": "RATE_AMOUNT", "dst-unit-col": "RATE_CURRENCY","dst-date-col" :"RATE_EXCHANGE_RATE_DATE_TAKEN", "dst-incr-flag-col": "RATE_AMOUNT_ADDITIONAL_PART"}
          ]
        }
      ],
      "table-snowflake": {
        "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
      }
    }
  ]
}