# Development Environment Setup for VS Code

This guide will help you set up your development environment for the json2star-common-etl project using VS Code instead of IntelliJ.

## Prerequisites

Please see [How-to] Setup the development environment from Performance insight team for general tools.

## SSH Setup

Add an SSH key to connect simply and safely to repositories.

- How to create SSH keys
- How to setup SSH for personal use

## HTTP TOKEN

To be able to clone and update GOB repositories (e.g. blueprint), you'll need to create an HTTP access token.

To do so, go to View Profile -> Manage account -> HTTP access tokens -> Create token.

Save the newly created token, it will be asked as password for git actions.

## Windows Setup

### Java JDK 1.8

Install JDK 1.8 latest version:

1. Download from [Oracle JDK 8](https://www.oracle.com/java/technologies/javase/javase8-archive-downloads.html) or [OpenJDK 8](https://adoptium.net/temurin/releases/?version=8)
2. Install the downloaded package
3. Set JAVA_HOME environment variable:
   - Open System Properties → Advanced → Environment Variables
   - Add new system variable: `JAVA_HOME` = `C:\Program Files\Java\jdk1.8.0_xxx` (adjust path as needed)
   - Add `%JAVA_HOME%\bin` to your PATH variable

### SBT (Scala Build Tool)

1. Download and install SBT from https://www.scala-sbt.org/1.x/docs/Installing-sbt-on-Windows.html
2. Use the MSI installer for Windows
3. Verify installation by running `sbt --version` in command prompt

### VS Code Setup

1. Download and install [Visual Studio Code](https://code.visualstudio.com/)

2. Install the following essential extensions:
   - **Metals** (Scala Language Server) - `scalameta.metals`
   - **Scala Syntax (official)** - `scala-lang.scala`
   - **SBT** - `lightbend.vscode-sbt-scala`
   - **Java Extension Pack** - `vscjava.vscode-java-pack`

3. Install additional helpful extensions:
   - **GitLens** - `eamodio.gitlens`
   - **Error Lens** - `usernamehw.errorlens`
   - **Bracket Pair Colorizer 2** - `coenraads.bracket-pair-colorizer-2`
   - **Path Intellisense** - `christian-kohler.path-intellisense`
   - **TODO Highlight** - `wayou.vscode-todo-highlight`

### VS Code Configuration

Create or update your VS Code settings (`.vscode/settings.json` in project root):

```json
{
  "files.eol": "\n",
  "files.insertFinalNewline": true,
  "files.trimTrailingWhitespace": true,
  "metals.javaHome": "C:\\Program Files\\Java\\jdk1.8.0_xxx",
  "java.configuration.runtimes": [
    {
      "name": "JavaSE-1.8",
      "path": "C:\\Program Files\\Java\\jdk1.8.0_xxx"
    }
  ],
  "metals.sbtScript": "sbt.bat",
  "metals.bloopSbtAlreadyInstalled": true
}
```

### Artifactory Credentials

Follow Json2Star ReadMe instructions to add your credentials to access artifactory:

1. Create a `.artifactory` folder in your user home directory
2. Create a `credentials` file with your artifactory credentials
3. This is mandatory to download dependencies

### P-Account Setup

MUCMSPDOM (P-Account) - user creation procedure

### Hadoop WinUtils (for Spark on Windows)

Install winutils for Hadoop 3.2.0:

In order to run the Spark program on windows, you would need Hadoop winutils.exe file as windows doesn't support HDFS and winutils provides a wrapper.

If you don’t have winutils.exe installed, please download the wintils.exe and hadoop.dll files from https://github.com/steveloughran/winutils or https://github.com/cdarlint/winutils/ (select the Hadoop version you are using as winutils are specific to Hadoop versions)

Copy the downloaded files to a folder on your system, for example, let’s say you have copied these files to c:/hadoop/bin now, set the below environment variables.


set HADOOP_HOME=c:/hadoop
set PATH=%PATH%;%HADOOP_HOME%/bin;

Bash

Restart VS Code to initialize these variables.

Note: You may need to configure environment variables in VS Code launch configurations if needed

Some times you may also need to put hadoop.dll file into the C:/Windows/System32 folder. (need ADMIN rights)

Now run your spark program and issue “Windows.access0(Ljava/lang/String;I)Z” should disappear.

however, If it still doesn’t work, try to restart your system as some of the above settings would get the effect with the restart.

### DBeaver (Optional Database Tool)

Follow the Databricks documentation: https://docs.databricks.com/dev-tools/dbeaver.html

Setup steps:
1. Download and install DBeaver
2. Download JDBC/ODBC driver for Databricks
3. Configure connection with:
   - Spark cluster JDBC URL (from cluster advanced settings)
   - Databricks access token (from user settings)

This provides SQL access to Delta tables.

---

## Linux Setup
### Java JDK 1.8

Install OpenJDK 8:

```bash
sudo apt install openjdk-8-jdk
```

If you have multiple Java installations, configure the default version:

```bash
sudo update-alternatives --config java
```

Example output:
```
There are 3 choices for the alternative java (providing /usr/bin/java).

  Selection    Path                                            Priority   Status
------------------------------------------------------------
* 0            /usr/lib/jvm/java-11-openjdk-amd64/bin/java      1101      auto mode
  1            /usr/lib/jvm/java-11-openjdk-amd64/bin/java      1101      manual mode
  2            /usr/lib/jvm/java-8-openjdk-amd64/jre/bin/java   1081      manual mode
  3            /usr/lib/jvm/java-8-oracle/jre/bin/java          1081      manual mode
```

Choose the number associated with Java 8 or press ENTER to keep current settings.

Set JAVA_HOME environment variable:

```bash
sudo nano /etc/environment
```

Add this line (adjust path as needed):
```
JAVA_HOME="/usr/lib/jvm/java-8-openjdk-amd64"
```

Reload the environment:
```bash
source /etc/environment
echo $JAVA_HOME
```

### SBT (Scala Build Tool)

Install SBT on Linux:

```bash
# Ubuntu/Debian
echo "deb https://repo.scala-sbt.org/scalasbt/debian all main" | sudo tee /etc/apt/sources.list.d/sbt.list
echo "deb https://repo.scala-sbt.org/scalasbt/debian /" | sudo tee /etc/apt/sources.list.d/sbt_old.list
curl -sL "https://keyserver.ubuntu.com/pks/lookup?op=get&search=0x2EE0EA64E40A89B84B2DF73499E82A75642AC823" | sudo apt-key add
sudo apt-get update
sudo apt-get install sbt
```

### VS Code Setup for Linux

1. Install VS Code:
   ```bash
   # Ubuntu/Debian
   wget -qO- https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor > packages.microsoft.gpg
   sudo install -o root -g root -m 644 packages.microsoft.gpg /etc/apt/trusted.gpg.d/
   sudo sh -c 'echo "deb [arch=amd64,arm64,armhf signed-by=/etc/apt/trusted.gpg.d/packages.microsoft.gpg] https://packages.microsoft.com/repos/code stable main" > /etc/apt/sources.list.d/vscode.list'
   sudo apt update
   sudo apt install code
   ```

2. Install the same VS Code extensions as mentioned in Windows setup:
   - Metals (Scala Language Server)
   - Scala Syntax (official)
   - SBT
   - Java Extension Pack

3. Configure VS Code settings (`.vscode/settings.json`):
   ```json
   {
     "files.eol": "\n",
     "files.insertFinalNewline": true,
     "files.trimTrailingWhitespace": true,
     "metals.javaHome": "/usr/lib/jvm/java-8-openjdk-amd64",
     "java.configuration.runtimes": [
       {
         "name": "JavaSE-1.8",
         "path": "/usr/lib/jvm/java-8-openjdk-amd64"
       }
     ]
   }
   ```

### Artifactory Credentials

Follow Json2Star ReadMe instructions to add your credentials to access artifactory.

### Databricks CLI

Follow these instructions: Browse DIH Data Lake / dih-datalake-toolkit - Amadeus Central Bitbucket

---

## macOS Setup

### Java JDK 1.8

Install OpenJDK 8:

```bash
brew install openjdk@8
```

Add to your shell profile (`.zshrc` or `.bash_profile`):
```bash
export JAVA_HOME=$(/usr/libexec/java_home -v 1.8)
export PATH="$JAVA_HOME/bin:$PATH"
```

### SBT

```bash
brew install sbt
```

### VS Code Setup for macOS

1. Download and install VS Code from https://code.visualstudio.com/
2. Install the same extensions as mentioned above
3. Configure VS Code settings (`.vscode/settings.json`):
   ```json
   {
     "files.eol": "\n",
     "files.insertFinalNewline": true,
     "files.trimTrailingWhitespace": true,
     "metals.javaHome": "/Library/Java/JavaVirtualMachines/adoptopenjdk-8.jdk/Contents/Home",
     "java.configuration.runtimes": [
       {
         "name": "JavaSE-1.8",
         "path": "/Library/Java/JavaVirtualMachines/adoptopenjdk-8.jdk/Contents/Home"
       }
     ]
   }
   ```

### Databricks CLI

```bash
brew tap databricks/tap
brew install databricks
```

---

## Project Setup in VS Code

### Opening the Project

1. Open VS Code
2. File → Open Folder → Select the `json2star-common-etl` directory
3. VS Code will detect the Scala project and prompt to import it
4. Allow Metals to import the build (this may take several minutes)

### Building and Testing

Use the integrated terminal in VS Code:

```bash
# Compile the project
sbt compile

# Run tests
sbt test

# Generate assembly JAR
sbt assembly

# Run specific test
sbt "testOnly *YourTestClass*"
```

### Debugging

1. Set breakpoints by clicking in the gutter next to line numbers
2. Use the Debug panel (Ctrl+Shift+D / Cmd+Shift+D)
3. Create launch configurations in `.vscode/launch.json` if needed

### VS Code Configuration Files

The project includes pre-configured VS Code settings in the `.vscode/` directory:

- **settings.json**: Project-specific settings for Scala development
- **extensions.json**: Recommended extensions that will be suggested when opening the project
- **launch.json**: Debug configurations for running and testing Scala applications
- **tasks.json**: Pre-defined tasks for common SBT commands

These files will automatically configure VS Code for optimal Scala development experience.

### Useful VS Code Shortcuts for Scala Development

- **Ctrl+Shift+P** (Cmd+Shift+P): Command palette
- **F12**: Go to definition
- **Shift+F12**: Find all references
- **Ctrl+.** (Cmd+.): Quick fix/code actions
- **Ctrl+Space**: Trigger IntelliSense
- **F2**: Rename symbol
- **Ctrl+Shift+O** (Cmd+Shift+O): Go to symbol in file
- **Ctrl+Shift+\`** (Cmd+Shift+\`): Open integrated terminal
- **Ctrl+Shift+B** (Cmd+Shift+B): Run build task
- **F5**: Start debugging

### Running Tasks

Use **Ctrl+Shift+P** → "Tasks: Run Task" to access pre-configured SBT tasks:
- `sbt compile`: Compile the project
- `sbt test`: Run all tests
- `sbt assembly`: Create assembly JAR
- `sbt clean`: Clean build artifacts
- `sbt dependencyTree`: Show dependency tree
- `sbt scalafmt`: Format Scala code

### Troubleshooting

**Metals not starting:**
1. Check that Java 8 is properly installed and JAVA_HOME is set
2. Restart VS Code
3. Use Command Palette → "Metals: Restart server"

**Import build fails:**
1. Ensure SBT is installed and accessible from command line
2. Check artifactory credentials are properly configured
3. Try running `sbt compile` from terminal first

**Slow performance:**
1. Increase VS Code memory: Add `"metals.javaOpts": ["-Xmx4G"]` to settings
2. Exclude target directories from file watching (already configured in settings.json)
