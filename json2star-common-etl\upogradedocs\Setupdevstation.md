Step-by-step Microsoft guide:
General tools
Please see [How-to] Setup the development environment from Performance insight team.

Git
Install gitbash : https://gitforwindows.org/

Configure git

 

run> git config --global user.name "<yourWinUser>"

run> git config --global user.email "<your1AEmail>"

run> git config --global core.autocrlf true

SSH
Add an SSH key to connect simply and safely to repositories.

How to create SSH keys

How to setup SSH for personal use

HTTP TOKEN
To be able to clone and update GOB repositories (e.g. blueprint), you'll need to create an HTTP access token.

To do so, go to View Profile -> Manage account -> HTTP acces tokens -> Create token.

Save the newly created token, it will be asked as password for git actions.

jdk 
Install jdk 1.8 latest version from 


sbt 
Download msi installer and install it from https://www.scala-sbt.org/1.x/docs/Installing-sbt-on-Windows.html

Follow Json2Star ReadMe instruction to add your credentials to access artifactory

IntelliJ
Using JetBrains licenses

 

Follow the steps in this page to create a credential folder. It is mandatory in order to be able to download dependencies.

To get the encrypted password, you should use this procedure (the curl's way works fine).

 

Tips :

Change line separator to Linux :

File > Settings > Editor > Code Style > Line Separator > "Unix and maxOS (\n)"

File > File Properties > Line Separators > "LF - Unix and maxOS (\n)"

Paccount
MUCMSPDOM (P-Account) - user creation procedure

hadoop
Install winutils for hadoop 3.2.0 :

In order to run the Spark program on windows, you would need Hadoop winutils.exe file as windows doesn't support HDFS and winutils provides a wrapper.

If you don’t have winutils.exe installed, please download the wintils.exe and hadoop.dll files from https://github.com/steveloughran/winutils or https://github.com/cdarlint/winutils/ (select the Hadoop version you are using as winutils are specific to Hadoop versions)

Copy the downloaded files to a folder on your system, for example, let’s say you have copied these files to c:/hadoop/bin now, set the below environment variables.


set HADOOP_HOME=c:/hadoop
set PATH=%PATH%;%HADOOP_HOME%/bin;

Bash

Close and reload IntelliJ to initialize these variables.

Some times you'll  need to force use of env variables in intelliJ run configuration

Some times you may also need to put hadoop.dll file into the C:/Windows/System32 folder. (need ADMIN rights)

Now run your spark program and issue “Windows.access0(Ljava/lang/String;I)Z” should disappear.

however, If it still doesn’t work, try to restart your system as some of the above settings would get the effect with the restart.

DBeaver (optional)
Follow the Databricks documentation how-to: https://docs.databricks.com/dev-tools/dbeaver.html#

There are mainly 3 steps:

download DBeaver application here and launch it.

retrieve the JDBC/ODBC driver for Databricks here and configure a Databricks connection with it.

To finalize the configuration, you will need a Spark cluster JDBC URL value and a Databricks access token. In the Databricks UI, the URL is in the advanced settings of a cluster and a token can be generated in the user settings (visible only once at creation time):

 


 

In the end, you'll get access to Delta tables as simply as any other SQL table:

 


Step by Step Linux Guide:
Java jdk 1.8 :

https://www.digitalocean.com/community/tutorials/how-to-install-java-with-apt-on-ubuntu-18-04-fr

sudo apt install openjdk-8-jdk

Vous pouvez avoir plusieurs installations Java sur un même serveur. Vous pouvez configurer la version utilisée par défaut sur la ligne de commande en utilisant la commande update-alternatives.

 

sudo update-alternatives --config java

 

Voici à quoi ressemblerait la sortie si vous aviez installé toutes les versions de Java de ce tutoriel :

 

Output

There are 3 choices for the alternative java (providing /usr/bin/java).

  Selection    Path                                            Priority   Status
------------------------------------------------------------
* 0            /usr/lib/jvm/java-11-openjdk-amd64/bin/java      1101      auto mode
  1            /usr/lib/jvm/java-11-openjdk-amd64/bin/java      1101      manual mode
  2            /usr/lib/jvm/java-8-openjdk-amd64/jre/bin/java   1081      manual mode
  3            /usr/lib/jvm/java-8-oracle/jre/bin/java          1081      manual mode

Choisissez le numéro associé à la version Java afin de l'utiliser par défaut ou appuyez sur ENTER pour conserver les paramètres actuels.

Copiez le chemin de votre installation souhaitée. Puis, ouvrez /etc/environment en utilisant nano ou votre éditeur de texte préféré :

 

sudo nano /etc/environment

 

À la fin de ce fichier, ajoutez la ligne suivante, en veillant à remplacer le chemin surligné par votre propre chemin copié :
/etc/environment

JAVA_HOME="/usr/lib/jvm/java-8-oracle/jre/bin/"

La modification de ce fichier définira le chemin JAVA_HOME pour tous les utilisateurs de votre système.

Enregistrez le fichier et quittez l'éditeur.

Rechargez maintenant ce fichier pour appliquer les changements à votre session actuelle :

 

source /etc/environment

 

Vérifiez que la variable d'environnement est définie :

 

echo $JAVA_HOME

 

Vous verrez le chemin que vous venez de définir :

 

/usr/lib/jvm/java-8-openjdk-amd64/jre/bin/

For Mac OS: 
To install the java 8 jdk run

 



brew install openjdk
 

IntelliJ : https://www.jetbrains.com/fr-fr/idea/download/#section=linux
Sur Ubuntu ?

IntelliJ IDEA est également disponible sous forme de paquet snap. Si vous êtes sous Ubuntu 16.04 ou version ultérieure, vous pouvez installer IntelliJ IDEA à partir de la ligne de commande.

sudo snap install intellij-idea-community --classic

ou

sudo snap install intellij-idea-ultimate --classic

ou

sudo snap install intellij-idea-educational --classic

Add scala plugin

Configure IntelliJ to use jdk 1.8

SBT:
https://www.scala-sbt.org/1.x/docs/Installing-sbt-on-Linux.html

Follow Json2Star ReadMe instruction to add your credentials to access artifactory

Git:
if not already installed : https://doc.ubuntu-fr.org/git

Databricks CLI:
Follow these instructions: Browse DIH Data Lake / dih-datalake-toolkit - Amadeus Central Bitbucket

To install databricks on Mac OS terminal:



brew tap databricks/tap
brew install databricks
 