package com.amadeus.airbi.json2star.common.addons.base.mapping

import com.amadeus.airbi.json2star.common.addons.base.Addon
import com.amadeus.airbi.json2star.common.{ColumnDef, Schema}
import com.amadeus.airbi.rawvault.common.application.config._
import com.amadeus.airbi.rawvault.common.config.ColSources.{BlocksSource, ColSource, RootSpecificSource}
import com.amadeus.airbi.rawvault.common.config.{ColumnConfig, FKeyRelationship}
import com.amadeus.airbi.rawvault.common.vault.generators. PreRowQuery

import java.util.regex.{Matcher, Pattern}

object Mapping extends Addon[MappingConfig] {
  private val regexVariable = Pattern.compile("\\{(.*?)\\}")

  private def master(c: TablesConfig): PitTransformation = {
    c.tables
      .flatMap(_.mapping)
      .map(_.pit)
      .flatMap {
        case MasterPitTable(master) => Some(master)
        case _ => None
      }
      .head // we assume one and only one pit master for now
  }

  override def enrichSchema(c: TablesConfig, t: TableConfig, a: MappingConfig, l: Schema): Schema = {
    val otherTables = c.tables.filterNot(_.name == t.name)
    val cols = a.allOrderedColumnsWithLoadDate.toList.map { colConfig =>
      ColumnDef(
        name = colConfig.name,
        columnType = colConfig.columnType,
        origins = OriginResolver.resolve(a, colConfig),
        isMandatory = colConfig.isMandatory,
        belongsToPK = a.merge.keyColumns.contains(colConfig.name),
        meta = colConfig.meta,
        fk = Option(colConfig.fk.getOrElse(findForeignTables(colConfig, a, otherTables))).filter(_.nonEmpty),
        preExpr = colConfig.expr,
        postExpr = colConfig.postExpr
      )
    }

    val partition = a.pit match {
      case MasterPitTable(master) => Some(master.isLast)
      case SecondaryPitTable() => Some(master(c).isLast)
      case _ => None
    }

    Schema(
      name = t.name,
      columns = cols,
      description = generateDescription(a.description, t),
      partitionColumn = partition,
      kind = Schema.Materialized,
      subdomain = l.subdomain,
      subdomainMainTable = l.subdomainMainTable
    )
  }

  def getConfig(t: TableConfig): Option[MappingConfig] = t.mapping

  //detect all variables in a list of PreRowQuery
  private def detectVariables(sources: List[PreRowQuery]): Seq[String] = {
    class VariableIterator(m: Matcher) extends Iterator[String] {
      override def hasNext: Boolean = m.find()

      override def next(): String = m.group(1)
    }
    val variablesList = sources.flatMap { source =>
      val regexMatcher = regexVariable.matcher(source.path)
      val variables = new VariableIterator(regexMatcher).toList
      variables
    }
    variablesList
  }

  //detect all variables in a ColSource if needed, else return an empty list or throw an error if colSource is the wrong type
  private def detectVariablesAllColSourceType(
    tableName: String,
    colName: String,
    hasVariable: Boolean,
    colSource: ColSource
  ): Seq[String] = {
    if (hasVariable) {
      colSource match {
        case RootSpecificSource(sources) =>
          sources
            .flatMap(sourceWithRoot => {
              sourceWithRoot.blocks.map(source => detectVariables(source))
            })
            .flatten
        case BlocksSource(sources) => detectVariables(sources)
        case _ =>
          throw new IllegalArgumentException(
            s"Table ${tableName},Column ${colName} Only BlockSource supported when variables are configured"
          )
      }
    } else {
      Seq()
    }
  }

  private def validateColumnWithVariable(
    tableName: String,
    mapping: MappingConfig,
    parentColumns: Seq[ColumnConfig],
    colToBeValidated: ColumnConfig
  ): Unit = {
    //get all variables from the columnto be validated
    def variables = detectVariablesAllColSourceType(
      tableName,
      colToBeValidated.name,
      colToBeValidated.hasVariable,
      colToBeValidated.sources
    )
    variables.foreach { variable =>
      //case variable not defined in the mapping configuration
      if (!mapping.allOrderedColumnsWithLoadDate.map(_.name).contains(variable)) {
        throw new NoSuchFieldException(
          s"Table ${tableName}, Column Variable ${variable} in column ${colToBeValidated.name} " +
            s"is not defined in mapping configuration"
        )
      }
      //case cyclic dependency between columns
      else if (parentColumns.map(_.name).contains(variable)) {
        throw new IllegalAccessException(
          s"Table ${tableName}, Column Variable ${variable} in column ${colToBeValidated.name} " +
            s"is caught in a cyclic dependency"
        )
      }
      // else validate dependencies of the child column
      else {
        //validate child column
        val childCol = mapping.allOrderedColumnsWithLoadDate.find(_.name == variable).get
        validateColumnWithVariable(tableName, mapping, parentColumns ++ Seq(colToBeValidated), childCol)
      }
    }
  }

  override def validate(c: TablesConfig, t: TableConfig): Unit = {
    t.mapping.foreach { m =>
      m.columns.foreach { col => validateColumnWithVariable(t.name, m, Seq(), col) }
    }

  }

  case class MappingTableKeyInfo(
    name: String,
    keyColumns: Seq[String]
  )

  def findForeignTables(
    col: ColumnConfig,
    a: MappingConfig,
    otherTables: List[TableConfig]
  ): Seq[FKeyRelationship] = {
    otherTables
      // we only keep other mapping tables
      .filter(t => Mapping.getConfig(t).isDefined)
      // we keep only table name and key columns
      .map(t => MappingTableKeyInfo(t.name, Mapping.getConfig(t).get.merge.keyColumns))
      // we keep only the tables where our column is part of the PK
      .filter(t => t.keyColumns.contains(col.name))
      // we keep only the tables where all the PK is present in out table's schema
      // (meaning that our table contains the complete FK for that table)
      .filter(t => t.keyColumns.forall(keyCol => a.allOrderedColumnsWithLoadDate.map(_.name).contains(keyCol)))
      .map(t => FKeyRelationship(table = t.name))
  }

  private def generateDescription(tdOpt: Option[TableDescription], t: TableConfig): Option[TableDescription] = {
    val hasPrefiller = t.prefiller.nonEmpty
    val enrichedTableDesc = if (hasPrefiller) {
      val TableDescSuffix = "This table is preloaded with static referential data coming from Amadeus"
      tdOpt match {
        case Some(td) => Some(td.copy(description = Some(td.description.getOrElse("").trim + " - " + TableDescSuffix)))
        case None => Some(TableDescription(Some(TableDescSuffix), None))
      }
    } else {
      tdOpt
    }
    enrichedTableDesc
  }

}
