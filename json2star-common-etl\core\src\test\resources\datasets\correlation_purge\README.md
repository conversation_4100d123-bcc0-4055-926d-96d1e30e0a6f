# Readme

The files in the data/pnr and data/tktemd folders correspond:

- to the following versions of PNR USCK5S and TKT 6072160023101:

```
PNR 8
TKT 0
TKT 3
PNR 11
TKT 4
```

- to the following versions of PNR AAAAAA and TKT 6666666666666:

```
PNR 8
TKT 0
```

In the test scenario using this dataset:
- version 8 of USCK5S-2022-06-29 is manually deleted.
- PNR AAAAAA is completely purged (but not the corresponding ticket 6666666666666).

Correlation algorithm correctly reflects the delete operations in those 2 scenarios.
Note that a purge on both PNR AAAAAA and TKT 6666666666666 would not be mirrored.
