@startuml star-schema_TEST_v1_0_0

' Header
hide circle
hide <<assotable>> stereotype
hide <<dimtable>> stereotype
hide <<maintable>> stereotype
hide <<mainsubdomain>> stereotype
hide methods
left to right direction

!define TABLE_GRADIENT_BACKGROUND #F2F2F2-fcffd6
!define MAIN_TABLE_GRADIENT_HEADER Orange-%lighten("Yellow", 60)

!function $pk($content)
!return "<color:#ff0000>" + $content + "</color>"
!endfunction

!function $fk($content)
!return "<color:#0000ff>" + $content + "</color>"
!endfunction

skinparam {
    DefaultFontName Monospaced
    ranksep 250
    linetype polyline
    tabSize 4
    HyperlinkUnderline false
    HyperlinkColor #0000ff
}

skinparam frame {
    FontSize 28
    FontSize<<mainsubdomain>> 34
    BorderThickness<<mainsubdomain>> 3
}

skinparam class {
    BackgroundColor TABLE_GRADIENT_BACKGROUND
    HeaderBackgroundColor %lighten("Crimson", 40)
    HeaderBackgroundColor<<maintable>> MAIN_TABLE_GRADIENT_HEADER
    HeaderBackgroundColor<<dimtable>> LightBlue
    HeaderBackgroundColor<<assotable>> %lighten("LimeGreen", 30)
    ColorArrowSeparationSpace Red
    BorderColor Black
    BorderColor<<maintable>> MediumBlue
    BorderThickness<<maintable>> 3
    ArrowColor Blue
    FontSize 16
    FontSize<<maintable>> 20
    FontStyle Bold
}

' Frames
frame "Passenger"<<mainsubdomain>> #E4C7FF {

entity "FACT_PASSENGER_HISTO"<<maintable>> {
$pk("PASSENGER_ID                 Binary     NN  PK")
REFERENCE_KEY                String     NN    
CPR_FEED_TYPE                String           
$fk("[[#{DUMMY_S.FACT_DUMMY_HISTO} ETAG]]                         String         FK")
GROUP_NAME                   String           
IS_MASTER_RECORD             Boolean          
IS_SAME_PHYSICAL_CUSTOMER    Boolean          
IS_SYSTEM_MARKED_SPC         Boolean          
TYPE                         String           
BIRTH_DATE                   Date             
BIRTH_PLACE                  String           
PAX_TYPE                     String           
GENDER                       String           
NATIONALITY                  String           
SPECIAL_SEAT                 String           
FIRST_NAME                   String           
LAST_NAME                    String           
TITLE                        String           
RESIDENCE_COUNTRY            String           
AGE                          Integer          
STAFF_CATEGORY               String           
STAFF_COMPANY_CODE           String           
STAFF_COMPANY_NAME           String           
STAFF_ID                     String           
STAFF_BOOKING_TYPE           String           
STAFF_JOINING_DATE           Timestamp        
STAFF_RELATIONSHIP           String           
STAFF_RETIREMENT_DATE        Timestamp        
STAFF_TRANSFER_DAYS          Integer          
STAFF_TRANSFERS_DURING_DAYS  Integer          
RECORD_LOCATOR               String           
$pk("VERSION                      String         PK")
DATE_BEGIN                   Timestamp        
DATE_END                     Timestamp        
IS_LAST_VERSION              Boolean          
LOAD_DATE                    Timestamp        
}

entity "FACT_SECONDARY_HISTO" {
$pk("FACT_SECONDARY_ID  Binary     NN  PK")
REFERENCE_KEY      String     NN    
$pk("VERSION            String         PK")
DATE_BEGIN         Timestamp        
DATE_END           Timestamp        
IS_LAST_VERSION    Boolean          
LOAD_DATE          Timestamp        
}
}

frame "DUMMY_S" #DDDDDD {

entity "FACT_DUMMY_HISTO" {
$pk("DUMMY_C  String  NN  PK")
}
}

' Free entities


' Relationships
FACT_PASSENGER_HISTO::ETAG --> DUMMY_S.FACT_DUMMY_HISTO::DUMMY_C

@enduml
