package com.amadeus.airbi.json2star.common.addons.base.correlation

import com.amadeus.airbi.json2star.common.addons.base.Addon
import com.amadeus.airbi.json2star.common.{ColumnDef, Schema}
import com.amadeus.airbi.rawvault.common.application.config.Correlation.{AssoTable, CorrelationColumnConfig}
import com.amadeus.airbi.rawvault.common.application.config.{TableConfig, TableDescription, TablesConfig}
import com.amadeus.airbi.rawvault.common.config.{ColumnMetadata, ColumnType, GDPRZone}

object Correlation extends Addon[AssoTable] {
  override def getConfig(t: TableConfig): Option[AssoTable] = t.correlation
  override def enrichSchema(c: TablesConfig, t: TableConfig, a: AssoTable, l: Schema): Schema = {
    val noOrigins = Seq()
    val noMeta = None
    val noFk = None
    Schema(
      t.name,
      List(
        ColumnDef(
          a.target.domainAKey.name,
          ColumnType.binaryStrColumn,
          isMandatory = false,
          belongsToPK = true,
          origins = noOrigins,
          a.target.domainAKey.meta,
          a.target.domainAKey.fk,
          preExpr = None,
          postExpr = None
        ),
        ColumnDef(
          a.target.domainBKey.name,
          ColumnType.binaryStrColumn,
          isMandatory = false,
          belongsToPK = true,
          origins = noOrigins,
          a.target.domainBKey.meta,
          a.target.domainBKey.fk,
          preExpr = None,
          postExpr = None
        ),
        ColumnDef(
          a.target.domainAVersion.name,
          ColumnType.longColumn,
          isMandatory = true,
          belongsToPK = true,
          origins = noOrigins,
          meta = a.target.domainAVersion.meta,
          fk = a.target.domainAVersion.fk,
          preExpr = None,
          postExpr = None
        ),
        ColumnDef(
          a.target.domainBVersion.name,
          ColumnType.longColumn,
          isMandatory = true,
          belongsToPK = true,
          origins = noOrigins,
          meta = a.target.domainBVersion.meta,
          fk = a.target.domainBVersion.fk,
          preExpr = None,
          postExpr = None
        ),
        ColumnDef(
          a.target.startDate,
          ColumnType.timestampColumn,
          isMandatory = false,
          belongsToPK = false,
          origins = noOrigins,
          meta = Some(
            ColumnMetadata.from(
              description = Some(s"Validity start date of correlation"),
              example = None,
              piiType = None,
              gdprZone = Some(GDPRZone.Green)
            )
          ),
          fk = noFk,
          preExpr = None,
          postExpr = None
        ),
        ColumnDef(
          a.target.endDate,
          ColumnType.timestampColumn,
          isMandatory = false,
          belongsToPK = false,
          origins = noOrigins,
          meta = Some(
            ColumnMetadata.from(
              description = Some(s"Validity end date of correlation"),
              example = None,
              piiType = None,
              gdprZone = Some(GDPRZone.Green)
            )
          ),
          fk = noFk,
          preExpr = None,
          postExpr = None
        ),
        ColumnDef(
          a.target.isLast,
          ColumnType.booleanColumn,
          isMandatory = false,
          belongsToPK = false,
          origins = noOrigins,
          meta = Some(
            ColumnMetadata.from(
              description = Some(s"True if it is the last correlation otherwise False"),
              example = None,
              piiType = None,
              gdprZone = Some(GDPRZone.Green)
            )
          ),
          fk = noFk,
          preExpr = None,
          postExpr = None
        )
      ) ++ a.target.assoAttributesSeq
        .map(c =>
          ColumnDef(
            c.name,
            ColumnType.strColumn,
            isMandatory = false,
            belongsToPK = false,
            origins = noOrigins,
            meta = c.meta,
            fk = c.fk,
            preExpr = None,
            postExpr = None
          )
        ),
      description = Some(
        TableDescription(
          description = Some(
            s"Correlation table between ${factHistoName(a.target.domainAKey.name)} and ${factHistoName(a.target.domainBKey.name)}"
          ),
          granularity = Some(
            s"1 row for each different tuple (${a.target.domainAKey.name}, ${a.target.domainBKey.name}, ${a.target.domainAVersion.name}, " +
              s"${a.target.domainBVersion.name})"
          )
        )
      ),
      partitionColumn = Some(a.target.isLast),
      kind = Schema.Materialized,
      subdomain = l.subdomain,
      subdomainMainTable = l.subdomainMainTable
    )
  }

  override def validate(c: TablesConfig, t: TableConfig): Unit = {
    val assoTable =
      getConfig(t).getOrElse(throw new IllegalArgumentException(s"Correlation config not found for table ${t.name}"))

    val msgPrefix = s"[${t.name}]"

    // Partial keys consistency
    assert(
      assoTable.domainA.partial.partialCorrKey == assoTable.domainB.partial.partialCorrSecondaryKey,
      s"$msgPrefix Partial keys mismatch: ${assoTable.domainA.partial.partialCorrKey} != ${assoTable.domainB.partial.partialCorrSecondaryKey}"
    )
    assert(
      assoTable.domainB.partial.partialCorrKey == assoTable.domainA.partial.partialCorrSecondaryKey,
      s"$msgPrefix Partial keys mismatch: ${assoTable.domainB.partial.partialCorrKey} != ${assoTable.domainA.partial.partialCorrSecondaryKey}"
    )

    if (assoTable.target.domainAKey.source.isEmpty) {
      // Domain A key coming directly from the partial table
      assert(
        assoTable.target.domainAKey.name == assoTable.domainA.partial.partialCorrKey,
        s"$msgPrefix Domain A key mismatch: ${assoTable.target.domainAKey.name} != ${assoTable.domainA.partial.partialCorrKey}"
      )
    } else {
      // Standalone correlation, with domain A key coming from PIT
      assert(
        assoTable.correlationFieldBToA == assoTable.domainA.partial.partialCorrKey,
        s"$msgPrefix Corr-B-to-A mismatch: ${assoTable.correlationFieldBToA} != ${assoTable.domainA.partial.partialCorrKey}"
      )
      // the ID used to correlate with domain A is among the asso attributes coming from B partial
      assert(
        assoTable.target.assoAttributesSeq.exists(_.name == assoTable.correlationFieldBToA),
        s"$msgPrefix Corr-B-to-A (${assoTable.correlationFieldBToA}) not found in the asso attributes"
      )
    }

    if (assoTable.target.domainBKey.source.isEmpty) {
      // Domain B key coming directly from the partial table
      assert(
        assoTable.target.domainBKey.name == assoTable.domainB.partial.partialCorrKey,
        s"$msgPrefix Domain B key mismatch: ${assoTable.target.domainBKey.name} != ${assoTable.domainB.partial.partialCorrKey}"
      )
    } else {
      // Standalone correlation with domain B key coming from PIT
      assert(
        assoTable.correlationFieldAToB == assoTable.domainB.partial.partialCorrKey,
        s"$msgPrefix Corr-A-to-B mismatch: ${assoTable.correlationFieldAToB} != ${assoTable.domainB.partial.partialCorrKey}"
      )
      // the ID used to correlate with domain B is among the asso attributes coming from A partial
      assert(
        assoTable.target.assoAttributesSeq.exists(_.name == assoTable.correlationFieldAToB),
        s"$msgPrefix Corr-A-to-B (${assoTable.correlationFieldAToB}) not found in the asso attributes"
      )
    }
  }

  /** Builds the FACT HISTO table name from the ID
    *
    * This relies on the following naming convention:
    *
    * FACT HISTO table name: FACT_XXXXX_HISTO
    * FACT HISTO table ID: XXXXX_ID
    *
    * @param id FACT HISTO table ID
    * @return the FACT HISTO table name
    */
  private def factHistoName(id: String): String = {
    val HistoSuffix = "_HISTO"
    val IdSuffix = "_ID"
    val FactPrefix = "FACT_"
    s"$FactPrefix${id.stripSuffix(IdSuffix)}$HistoSuffix"
  }

}
