# Readme

## PNR and TKT have distinct correlations

The files in the data/pnr and data/tkt folders correspond to the following versions of PNR NP84R4 and TKT 6072401543235 :

```
PNR v11
PNR v12
TKT v1
```

This data has been extracted from EY UAT data.

We have this correlation block into PNR raw data
```
"corrTktPnr": {
  "items": [
    {
      "pnrAirSegmentId": "NP84R4-2022-08-19-ST-3",
      "pnrTicketingReferenceId": "NP84R4-2022-08-19-OT-28",
      "pnrTravelerId": "NP84R4-2022-08-19-PT-2",
      "ticketCouponId": "6072401543235-2022-08-19-1"
    }
  ]
}
```

And the below one in TKT block, different from the previous
```
  "corrTktPnr": {
    "items": [
      {
        "pnrAirSegmentId": "NP84R4-2022-08-19-ST-2",
        "pnrTicketingReferenceId": "NP84R4-2022-08-19-OT-19",
        "pnrTravelerId": "NP84R4-2022-08-19-PT-2",
        "ticketCouponId": "6072401543235-2022-08-19-2"
      }
    ]
  }
```

This final result should append the two correlations


PS : This case will also test the situation when we have begin_date = end_date for a single version
